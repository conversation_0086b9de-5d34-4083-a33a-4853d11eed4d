<?php

namespace App\Listeners;

use App\Events\PaymentAccountCreated;
use App\Models\Store;
use App\Services\StoreService;

class SendPaymentAccountConfirmNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct() {}

    /**
     * Handle the event.
     *
     * @param PaymentAccountCreated $event
     * @return void
     */
    public function handle(PaymentAccountCreated $event)
    {
        $user = $event->user;
        $paymentAccount = $event->paymentAccount;
        try {
            $dataSendMailLog = [
                'sellerId' => $user->id ?? null
            ];
            sendEmail([
                'to' => $user->email,
                'template' => 'seller.payment_account_confirmation',
                'data' => [
                    'subject' => 'New Payment Account Confirmation',
                    'name' => $user->name,
                    'email' => $user->email,
                    'payment' => [
                        'payment_type' => $paymentAccount->payment_type,
                        'account_id' => $paymentAccount->account_id,
                        'account_name' => $paymentAccount->account_name
                    ],
                    'confirm_url' => (
                        config('senprints.base_url_seller') .
                        'settings/payment-account?confirm_token=' .
                        $paymentAccount->confirm_token
                    ),
                    'store_info' => StoreService::getStoreInfo(Store::SENPRINTS_STORE_ID)
                ],
                'hash' => gen_unique_hash(),
                'sendMailLog' => $dataSendMailLog
            ]);
        } catch (\Throwable $e) {
            logException($e, 'SendPaymentAccountConfirmNotification::handle');
        }
    }
}
