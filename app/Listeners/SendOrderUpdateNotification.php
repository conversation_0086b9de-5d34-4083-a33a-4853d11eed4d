<?php

namespace App\Listeners;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Events\OrderUpdated;
use App\Http\Controllers\SmsController;
use App\Models\Order;
use App\Services\StoreService;

class SendOrderUpdateNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param OrderUpdated $event
     * @return void
     */
    public function handle(OrderUpdated $event)
    {
        $orderId = $event->orderId;
        $order = Order::query()->find($orderId);
        if (is_null($order)) {
            return;
        }
        if ($order->type !== OrderTypeEnum::REGULAR) {
            return;
        }

        $storeInfo = StoreService::getStoreInfo($order->store_id);
        $email = $order->customer_email;
        $name = $order->customer_name;
        $message = 'Your order has just received a new update';
        switch ($order->fulfill_status) {
            case OrderFulfillStatus::FULFILLED:
                // if seller paid for insurance, send sms to customer
                if ($order->insurance_fee > 0) {
                    (new SmsController())->processInsuranceSms($order);
                }
                $message = 'All items order have been shipped';
                break;
            case OrderFulfillStatus::PROCESSING:
                $message = 'Your order is being processed for shipping';
                break;
            case OrderFulfillStatus::CANCELLED:
                $message = 'Your order has been canceled';
                break;
            default:
                break;
        }

        $dataSendMailLog = [
            'sellerId' => $order->seller_id ?? null,
            'storeId' => $order->store_id ?? null,
            'orderId' => $order->id ?? null
        ];

        $data = [
            'to' => $email,
            'template' => 'buyer.order_update',
            'data' => [
                'subject' => 'Your order #' . $order->order_number . ' has been updated',
                'order_id' => $order->order_number,
                'message' => $message . ', please click the button below to view details.',
                'name' => $name,
                'order_url' => $order->status_url,
                'store_info' => $storeInfo
            ],
            'sendMailLog' => $dataSendMailLog
        ];
        $result = sendEmail($data);
        if (!$result) {
            logToDiscord('Order ID: ' . $orderId . ' | Cannot send order updated email.');
        }
    }
}
