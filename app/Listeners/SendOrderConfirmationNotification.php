<?php

namespace App\Listeners;

use App\Events\OrderPaymentCompleted;
use App\Jobs\SendBuyerOrderConfirmationJob;

class SendOrderConfirmationNotification
{
    /**
     * Handle the event.
     *
     * @param OrderPaymentCompleted $event
     * @return void
     */
    public function handle(OrderPaymentCompleted $event)
    {
        /**
         * Send email order confirmation to customer
         */
        try {
            $order = $event->order;
            if ($order->isServiceOrder() || $order->isCustomServiceOrder() || $order->isFulfillmentOrder()) {
                return;
            }
            // make sure the data of order is sync success, then send email after 30 seconds
            SendBuyerOrderConfirmationJob::dispatch($order)->delay(now()->addSeconds(30));
        } catch (\Exception $ex) {
            logException($ex);
        }
    }
}
