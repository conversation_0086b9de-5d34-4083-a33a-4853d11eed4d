<?php

namespace App\Listeners;

use App\Enums\OrderStatus;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Events\CampaignCreated;
use App\Jobs\ScanCampaignCopyright;
use App\Models\Order;
use App\Models\SystemConfig;
use Illuminate\Contracts\Queue\ShouldQueue;

class CheckDesignFiles implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param CampaignCreated $event
     * @return void
     */
    public function handle(CampaignCreated $event): void
    {
        // disabled?
        if (!SystemConfig::query()
            ->where('key', 'scan_trademark')
            ->value('value')) {
            return;
        }

        $campaignId = $event->campaignId;
        $seller = $event->sellerInfo;

        // ignore trusted sellers or designer
        if ($seller->status === UserStatusEnum::TRUSTED
            || $seller->role === UserRoleEnum::DESIGNER
            || $seller->custom_payment) {
            $debugMsg = 'Campaign ID: ' . $campaignId . "\n";
            $debugMsg .= 'Seller ID: ' . $seller->id . "\n";
            $debugMsg .= 'Seller Status: ' . $seller->status . "\n";
            $debugMsg .= 'Seller Role: ' . $seller->role . "\n-----";
            graylogInfo("CheckDesignFiles stopped.", [
                'category' => 'scan_trademark',
                'data' => $debugMsg,
            ]);
            return;
        }

        // ignore if sellers have at least 1 completed order
        $orderCount = Order::query()
            ->where([
                'seller_id' => $seller->id,
                'status' => OrderStatus::COMPLETED
            ])
            ->count();

        if ($orderCount > 0) {
            $debugMsg = 'Campaign ID: ' . $campaignId . "\n";
            $debugMsg .= 'Seller ID: ' . $seller->id . "\n";
            $debugMsg .= 'Order Count: ' . $orderCount . "\n-----";
            graylogInfo("CheckDesignFiles stopped.", [
                'category' => 'scan_trademark',
                'data' => $debugMsg,
            ]);
            return;
        }
        ScanCampaignCopyright::dispatch($campaignId, sellerId: $seller->id);
    }
}
