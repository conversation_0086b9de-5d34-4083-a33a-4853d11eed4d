<?php

namespace App\Listeners;

use App\Events\StoreContacted;
use App\Http\Controllers\EmailController;

class SendContactSellerNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param StoreContacted $event
     * @return void
     */
    public function handle(StoreContacted $event): void
    {
        $store = $event->store;
        $form = $event->form;
        try {
            $emailController = new EmailController();
            $emailController->emailContactSeller($store, $form);
        } catch (\Exception $ex) {
            logException($ex);
        }
    }
}
