<?php

namespace App\Listeners;

use App\Enums\UserRoleEnum;
use App\Events\InviteMember;
use App\Models\Store;
use App\Models\User;
use App\Services\StoreService;

class SendInviteMemberEmail
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  InviteMember  $event
     * @return void
     */
    public function handle(InviteMember $event)
    {
        $email = $event->email ?? '';
        $message = $event->message ?? '';
        $dataSendMailLog = [];
        $dataSendMail = [
            'to' => $email,
            'template' => 'seller.invite_member_email',
            'data' => [
                'subject' => 'You got an invite to join team',
                'message' => $message,
                'store_info' => StoreService::getStoreInfo(Store::SENPRINTS_STORE_ID)
            ]
        ];
        $seller = User::query()
            ->select('id')
            ->where('email', $email)
            ->role(UserRoleEnum::SELLER, 'user')
            ->first();
        if (!is_null($seller)) {
            $dataSendMailLog = [
                'sellerId' => $seller->id ?? null
            ];
            if (!is_null($dataSendMail['data']['store_info'])) {
                $dataSendMailLog['storeId'] = $dataSendMail['data']['store_info']->id ?? null;
            }
        }
        $dataSendMail['sendMailLog'] = $dataSendMailLog;
        $success = sendEmail($dataSendMail);
        if (!$success) {
            logToDiscord('seller.invite_member_email failed @Tech', 'email');
        }
    }
}
