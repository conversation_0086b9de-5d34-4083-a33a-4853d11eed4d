<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Slug
 *
 * @property int $seller_id
 * @property int $campaign_id
 * @property string $slug
 * @method static \Illuminate\Database\Eloquent\Builder|Slug newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Slug newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Slug query()
 * @property-read \App\Models\User|null $seller
 * @method static \Illuminate\Database\Eloquent\Builder|Slug whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Slug whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Slug whereSlug($value)
 * @mixin \Eloquent
 */
class Slug extends Model
{
    use HasFactory;

    const SLUG_PREFIX_LIMIT = 500;

    protected $fillable = [
        'seller_id',
        'campaign_id',
        'slug',
    ];

    public $timestamps = false;

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function isInvalid(String $slug): bool
    {
        return self::query()->where('slug', $slug)
            ->exists();
    }

    public static function isValid(String $slug): bool
    {
        return !self::isInvalid($slug);
    }

    public static function genSlugIfNecessary(string $slug): string
    {
        // Nếu không có slug nào trùng với slug hiện tại thì trả về luôn
        if (self::isValid($slug)) {
            return $slug;
        }

        // Tìm số lớn nhất của slug có dạng slug-<số>
        $max = self::findMaxSlugSuffix($slug . '-');

        return self::genSlugLowPerformance($slug, $max + 1);
    }

    public static function findMaxSlugSuffix(string $slug): int
    {
        $max = 0;
        $offset = 0;

        while (true) {
            $slugs = self::slugPrefixesBy($slug, $offset);

            foreach ($slugs as $_slug) {
                if (preg_match('/^' . $slug . '-(\d+)$/', $_slug, $matches)) {
                    $max = max($max, (int)$matches[1]);
                }
            }

            if ($slugs->count() > self::SLUG_PREFIX_LIMIT) {
                $offset += self::SLUG_PREFIX_LIMIT;
                continue;
            }

            return $max;
        }
    }

    public static function slugPrefixesBy(string $slug, int $offset = 0, int $limit = self::SLUG_PREFIX_LIMIT): \Illuminate\Support\Collection
    {
        return self::query()
            ->where('slug', 'like', $slug . '%')
            ->offset($offset)
            ->take($limit + 1)
            ->get('slug')
            ->pluck('slug');
    }

    public static function genSlugLowPerformance(string $slug, int $start = 1): string
    {
        do {
            $newSlug = $slug . '-' . $start++;
        } while (self::isInvalid($newSlug));

        return $newSlug;
    }
}
