<?php

namespace App\Models;

use App\Enums\ProductType;
use App\Traits\ScopeFilterDateRangeTrait;
use App\Traits\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\IndexCampaign
 *
 * @property int $id
 * @property string|null $slug
 * @property int|null $campaign_id
 * @property int|null $supplier_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $company_id
 * @property int|null $template_id
 * @property string $name
 * @property string|null $name_ts
 * @property string|null $thumb_url
 * @property mixed|null $options
 * @property string $mockup_type mockup type
 * @property string|null $default_option
 * @property string|null $print_spaces
 * @property string $currency_code
 * @property string|null $market_location
 * @property float $base_cost
 * @property string|null $base_costs
 * @property float $price
 * @property float $old_price
 * @property float $shipping_cost
 * @property string $status
 * @property string $product_type
 * @property string|null $description
 * @property string|null $description_ts
 * @property float $extra_print_cost Extra print cost
 * @property string|null $start_time
 * @property \Illuminate\Support\Carbon|null $end_time
 * @property int $show_countdown
 * @property int|null $default_product_id
 * @property string|null $tracking_code
 * @property string $tm_status
 * @property int|null $sync_status Sync campaign info to elastic-search
 * @property string|null $elastic_document_id
 * @property string|null $sku
 * @property float $score
 * @property float $time_score
 * @property int $sales_score
 * @property int|null $priority
 * @property string $pricing_mode
 * @property string $public_status
 * @property string|null $google_category_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $is_deleted
 * @property string|null $attributes
 * @property float $pre_discounted_price
 * @property string $render_mode Campaign render mode
 * @property int $personalized
 * @property int $full_printed
 * @property int $allow_bulk 1:allow_bulk
 * @property int $temp_status
 * @property string|null $visited_at
 * @property string $system_type
 * @property int $google_crawled_status
 * @property int $archived
 * @property \Illuminate\Database\Eloquent\Collection|\App\Models\IndexProduct[] $products
 * @method static \Illuminate\Database\Eloquent\Builder|IndexCampaign addFilterAnalytic(array $arrayFilter, array $dateRanges)
 * @method static \Database\Factories\CampaignFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|IndexCampaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IndexCampaign newQuery()
 * @method static \Illuminate\Database\Query\Builder|IndexCampaign onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|IndexCampaign query()
 * @method static \Illuminate\Database\Query\Builder|IndexCampaign withTrashed()
 * @method static \Illuminate\Database\Query\Builder|IndexCampaign withoutTrashed()
 * @mixin \Eloquent
 */
class IndexCampaign extends Model
{
    use HasFactory;
    use SoftDeletes;
    use ScopeFilterDateRangeTrait;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'product';
    protected $fillable = [
        'name',
        'name_ts',
        'thumb_url',
        'description',
        'description_ts',
        'seller_id',
        'auth_id',
        'slug',
        'start_time',
        'end_time',
        'show_countdown',
        'default_product_id',
        'template_id',
        'tracking_code',
        'price',
        'old_price',
        'status',
        'render_mode',
        'tm_status',
        'personalized',
        'product_type',
        'public_status',
        'currency_code',
        'market_location',
        'mockup_type',
        'default_option',
        'options',
        'system_type',
        'temp_status',
    ];

    // format to restore html5 datetime-local input
    protected $casts = [
        'end_time' => 'datetime:Y-m-d\TH:i',
    ];

    /**
     * @param array $attributes
     * @param bool $batch
     */
    public function __construct(array $attributes = [], $batch = false)
    {
        $this->batch = $batch;
        if($this->useSingleStoreConnection()) {
            $this->connection = 'singlestore';
        }
        parent::__construct($attributes);
    }

    protected static function booted()
    {
        static::addGlobalScope('campaign_type', function ($query) {
            $query->whereIn('product_type', [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ]);
        });
    }

    public function products(): HasMany
    {
        return $this->hasMany(IndexProduct::class, 'campaign_id', 'id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }
}
