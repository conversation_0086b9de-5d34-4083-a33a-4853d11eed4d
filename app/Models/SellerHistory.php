<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\SellerHistory
 *
 * @property int $seller_id
 * @property string $action
 * @property string $seller_status
 * @property int $campaign_id
 * @property int $order_id
 * @property string $details
 * @property int $staff_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property-read \App\Models\User $seller
 * @method static \Illuminate\Database\Eloquent\Builder|SellerHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerHistory whereSellerId($value)
 * @mixin \Eloquent
 */

class SellerHistory extends Model
{
    use HasFactory;
    protected $table = 'seller_history';

    protected $fillable = [
        'seller_id',
        'action',
        'seller_status',
        'campaign_id',
        'order_id',
        'details',
        'staff_id',
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return HasOne
     */
    public function staff(): HasOne
    {
        return $this->hasOne(Staff::class, 'id', 'staff_id')->select(['id', 'email', 'name', 'status']);
    }

    /**
     * @return HasOne
     */
    public function campaign(): HasOne
    {
        return $this->hasOne(Campaign::class, 'id', 'campaign_id')->select(['id', 'name']);
    }

    /**
     * @return HasOne
     */
    public function order(): HasOne
    {
        return $this->hasOne(Order::class, 'id', 'order_id')->select(['id', 'order_number']);
    }
}
