<?php

namespace App\Models;

use App\Traits\ScopeFilterAnalyticTrait;
use App\Traits\ScopeOrderByArray;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\StatsOrder
 *
 * @property int $id
 * @property int|null $seller_id
 * @property int|null $store_id
 * @property int|null $campaign_id
 * @property int $order_id
 * @property int $product_id
 * @property int $items
 * @property float $sales
 * @property float $seller_profit
 * @property string|null $country
 * @property string|null $device
 * @property string|null $device_detail
 * @property string $timestamp
 * @property string|null $datestamp
 * @property int $timezone
 * @property string $ad_source
 * @property string $ad_campaign
 * @property string $ad_medium
 * @property int|null $ref_id
 * @property int|null $template_id
 * @property string|null $color
 * @property string|null $size
 * @property-read \App\Models\Campaign|null $campaign
 * @property-read \App\Models\SystemLocation|null $country_info
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\User|null $seller
 * @property-read \App\Models\Store|null $store
 * @property-read \App\Models\Template|null $template_product
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder addExcludeAnalytic(array $arrExclude)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder addFilterAnalytic(array $arrayFilter, array $dateRanges, $sellerId = null, $arrUnsetFilter = [])
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder calculateItems()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder calculateOrders()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder calculateProfits()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder calculateSales()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder countActiveSellers()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder getAnalyticOverview()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder orderByArray($arrId, $column, $isGroupBy = true, $typeArr = 'int')
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder query()
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereAdCampaign($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereAdMedium($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereAdSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereDatestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereDevice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereDeviceDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereItems($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereRefId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereSales($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereSellerProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereTimestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StatsOrder whereTimezone($value)
 * @mixin \Eloquent
 */
class StatsOrder extends Model
{
    use ScopeFilterAnalyticTrait;
    use ScopeOrderByArray;

    public const FILTER_COLUMN_DATE = 'timestamp';

    protected $connection = 'pgsql';
    protected $table = 'stats_order';
    protected $guarded = [];
    public $timestamps = false;

    public function scopeCalculateOrders($query)
    {
        return $query->selectRaw('COALESCE(COUNT(distinct order_id), 0) AS orders');
    }

    public function scopeCalculateItems($query)
    {
        return $query->selectRaw('COALESCE(SUM(items), 0) AS items');
    }

    public function scopeCalculateProfits($query)
    {
        return $query->selectRaw('COALESCE(SUM(seller_profit), 0) AS profits');
    }

    public function scopeCalculateSales($query)
    {
        return $query->selectRaw('COALESCE(SUM(sales),0) AS sales');
    }

    public function scopeCountActiveSellers($query)
    {
        return $query->selectRaw('COUNT(distinct seller_id) AS active_sellers');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function template_product(): BelongsTo
    {
        return $this->belongsTo(Template::class, 'template_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id');
    }

    public function country_info(): BelongsTo
    {
        return $this->belongsTo(SystemLocation::class, 'country', 'code');
    }

    public function scopeGetAnalyticOverview($query)
    {
        return $query
            ->calculateOrders()
            ->calculateItems()
            ->calculateSales()
            ->calculateProfits();
    }
}
