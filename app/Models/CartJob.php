<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use Illuminate\Database\Eloquent\InvalidCastException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Collection;
use InvalidArgumentException;

/**
 * App\Models\CartJob
 *
 * @property int $id
 * @property int $order_id
 * @property string $start_time
 * @property string|null $next_email
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\NotificationSetting[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \App\Models\Order|null $order
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob query()
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob whereNextEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartJob whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CartJob extends Model
{

    protected $table = 'cart_jobs';

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function notifications(): HasManyThrough
    {
        return $this->hasManyThrough(
            NotificationSetting::class,
            Order::class,
            'id',
            'store_id',
            'order_id',
            'store_id'
        );
    }

    /**
     * @param Collection $orderIds
     * @return mixed
     * @throws InvalidCastException
     * @throws InvalidArgumentException
     */
    public static function abandonedOrderIds(Collection $orderIds)
    {
        if (empty($orderIds)) {
            return null;
        }
        $orderIds          = $orderIds->toArray();
        $abandonedOrderIds = Order::query()
            ->whereIn('id', $orderIds)
            ->where('status', OrderStatus::PENDING)
            ->where('type', OrderTypeEnum::REGULAR)
            ->get();
        if ($abandonedOrderIds->isEmpty()) {
            return null;
        }
        return $abandonedOrderIds;
    }
}
