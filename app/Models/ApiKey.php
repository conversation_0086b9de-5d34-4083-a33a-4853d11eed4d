<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * App\Models\ApiKey
 *
 * @property int $id
 * @property int $reference_id Reference id of supplier, seller or store
 * @property string $type
 * @property string $access_token
 * @property int|null $user_id ID for seller
 * @property int $limit
 * @property string|null $expired
 * @property int $status
 * @property string|null $note
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereAccessToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereExpired($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereUserId($value)
 * @mixin \Eloquent
 */
class ApiKey extends Model
{
    use HasFactory;

    protected $table = 'api_keys';

    protected $fillable = [
        'reference_id',
        'type',
        'access_token',
        'user_id',
        'limit',
        'expired',
        'status',
    ];
}
