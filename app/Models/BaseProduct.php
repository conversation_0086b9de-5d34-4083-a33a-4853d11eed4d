<?php

namespace App\Models;

use App\Enums\ProductPrintType;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

/**
 * @property int $full_printed
 */
class BaseProduct extends Model
{
    /**
     * @return bool
     */
    public function isPrint2D(): bool
    {
        return in_array($this->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::PRINT_2D_FULL], true);
    }
    /**
     * @return bool
     */
    public function isFullPrintedType(): bool
    {
        return !in_array($this->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY, ProductPrintType::HANDMADE], true);
    }

    /**
     * @return bool
     */
    public function isHandMadeType(): bool
    {
        return $this->full_printed === ProductPrintType::HANDMADE;
    }

    /**
     * @return bool
     */
    public function isNotHandMade(): bool
    {
        return !$this->isHandMadeType();
    }

    /**
     * @return bool
     */
    public function isEmbroidery(): bool
    {
        return $this->full_printed === ProductPrintType::EMBROIDERY;
    }

    /**
     * @return bool
     */
    public function isNotFulfillFBA(): bool
    {
        return $this->system_type !== ProductSystemTypeEnum::FULFILL_FBA;
    }
}
