<?php

namespace App\Models;

use App\Data\User\CustomerData;
use App\Enums\UserRoleEnum;
use App\Traits\ScopeFilterDateRangeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Customer
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Customer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Customer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Customer query()
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereSettable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Customer whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Customer extends Model
{
    use ScopeFilterDateRangeTrait;
    use HasFactory;
    use SoftDeletes;
    protected $dataClass = CustomerData::class;
    protected $table = 'customer';
    // protected $fillable = CustomerData::getFillableAttributes();

    protected $fillable  = [];
    public $incrementing = true;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->fillable = CustomerData::getFillableAttributes();
    }
    public function sellers()
    {
        return $this->belongsToMany(
            User::class,
            'seller_customer',
            'customer_id',
            'seller_id',
        )->where('role', UserRoleEnum::SELLER);
    }
    /*
    * Setters
    */

    public function setEmail ($value) {
        $this->email = $value;
    }
    public function setName ($value) {
        $this->name = $value;
    }
    public function setId ($value) {
        $this->id = $value;
    }

    public function unsubscribeEmail(): void
    {
        $this->email_subscribed = 0;
        $this->save();
    }
    /*
    * Getters
    */
    public function getEmail () {
        return $this->email;
    }
    public function getName () {
        return $this->name;
    }
    public function getId () {
        return $this->id;
    }

    public function recent_store()
    {
        return $this->hasOneThrough(
            Store::class,
            SellerCustomer::class,
            'customer_id',
            'id',
            'id',
            'store_id'
        )->orderBy('seller_customer.created_at', 'desc');
    }

    public function isEmailSubscribed(): bool
    {
        return $this->email_subscribed === 1;
    }

    public function isSmsSubscribed(): bool
    {
        return $this->sms_subscribed === 1;
    }

    public function unsubscribeSms(): void
    {
        $this->sms_subscribed = 0;
        $this->save();
    }

}
