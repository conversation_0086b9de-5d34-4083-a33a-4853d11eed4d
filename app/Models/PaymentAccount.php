<?php

namespace App\Models;

use App\Enums\PaymentAccountTypeEnum;

/**
 * App\Models\PaymentAccount
 *
 * @property int $id
 * @property int $seller_id User ID
 * @property string $payment_type
 * @property string $account_name
 * @property string $account_id account email/number
 * @property string|null $additional_info
 * @property string $status 'unverified','verified','used','archived','pending'
 * @property string $confirm_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $payment_name
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereAdditionalInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereConfirmToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount wherePaymentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentAccount whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class PaymentAccount extends Model
{

    protected $guarded = [];

    protected $appends = ['payment_name'];

    /**
     * @return string
     * @throws \JsonException
     */
    public function getPaymentNameAttribute(): string
    {
        if (($this->payment_type === 'bank') && !empty($this->additional_info)) {
            $jsonData = json_decode($this->additional_info, true, 512, JSON_THROW_ON_ERROR);
            return $jsonData['bank_name'];
        }

        return ucfirst($this->payment_type);
    }

    /**
     * Check if payment account used in db
     *
     * @return bool
     */
    public function isUsed(): bool
    {
        $payoutId = $this->id;

        $count = SellerBilling::query()->where('payment_account_id', $payoutId)->count();

        return $count > 0;
    }

    /**
     * @return string|null
     */
    public function pingPongBizId(): ?string
    {
        if (($this->payment_type === PaymentAccountTypeEnum::PINGPONG) && !empty($this->additional_info)) {
            $jsonData = json_decode($this->additional_info, true);
            return $jsonData['biz_id'] ?? null;
        }
        return null;
    }
}
