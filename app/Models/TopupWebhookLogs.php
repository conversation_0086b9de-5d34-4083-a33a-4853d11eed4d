<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

/**
 * App\Models\TopupWebhookLogs
 *
 * @property string $id
 * @property string $type
 * @property string|null $payment_site
 * @property string|null $code
 * @property int $re_validate
 * @property string|null $parse_message
 * @property string|null $raw_message
 * @property string|null $status
 * @property Carbon|null $created_at
 * @method static Builder|TopupWebhookLogs newModelQuery()
 * @method static Builder|TopupWebhookLogs newQuery()
 * @method static Builder|TopupWebhookLogs query()
 */
class TopupWebhookLogs extends Model
{
    protected $keyType = 'string';
    public $incrementing = false;
    /**
     * table name
     *
     * @var string
     */
    protected $table = 'topup_webhook_logs';
    protected $fillable = ['type', 'payment_site', 'code', 're_validate', 'parse_message', 'raw_message', 'status'];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'string',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * @return void
     */
    protected static function booted(): void
    {
        static::creating(static function (TopupWebhookLogs $model) {
            $model->id = generateUUID();
        });
    }

    /**
     * @param $type
     * @param $paymentSite
     * @param $code
     * @param $reValidate
     * @param $parseMessage
     * @param $rawMessage
     * @return string|null
     */
    public static function insertRow($type, $paymentSite, $code, $reValidate, $parseMessage, $rawMessage) {
        try {
            $created = self::query()->create([
                'type' => $type,
                'payment_site' => $paymentSite,
                'code' => $code,
                're_validate' => (int) $reValidate,
                'parse_message' => is_array($parseMessage) ? json_encode($parseMessage) : $parseMessage,
                'raw_message' => $rawMessage,
            ]);
            return $created->refresh()->id ?? null;
        } catch (\Throwable $e) {
            logException($e, 'TopupWebhookLogs::insertRow');
            return null;
        }
    }

    /**
     * @param $webhookId
     * @param $status
     * @return bool
     */
    public static function updateStatusRow($webhookId, $status) {
        try {
            if (empty($webhookId)) {
                return false;
            }
            $updated = self::query()->where('id', $webhookId)->update([
                'status' => $status,
            ]);
            if ($updated) {
                return true;
            }
            return false;
        } catch (\Throwable $e) {
            logException($e, 'TopupWebhookLogs::updateStatusRow');
            return false;
        }
    }
}
