<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * App\Models\TrademarkList
 *
 * @property int $id
 * @property string $text
 * @property bool $block_logo
 * @property bool $block_text
 * @property bool $accept_logo
 * @property int $supplier_id
 * @method static \Illuminate\Database\Eloquent\Builder|TrademarkList newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrademarkList newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrademarkList query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrademarkList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrademarkList whereText($value)
 * @mixin \Eloquent
 */
class TrademarkList extends Model
{
    use HasFactory;

    protected $table = 'trademark_list';

    protected $fillable = ['text', 'supplier_id', 'block_logo', 'block_text', 'accept_logo'];

    public $timestamps = false;
}
