<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DevGitCommit extends Model
{
    use HasFactory;

    protected $fillable = [
        'jira_id',
        'hash',
    ];

    public function dev(): BelongsTo
    {
        return $this->belongsTo(Dev::class, 'jira_id', 'jira_id');
    }
}
