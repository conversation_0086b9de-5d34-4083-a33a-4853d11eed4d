<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\IndexUserCleanSettings
 *
 * @property int $id
 * @property int|null $seller_id
 * @property string|null $clean_at
 * @property string|null $sent_at
 * @property string|null $type
 * @property string|null $token
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings query()
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings whereCleanAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IndexUserCleanSettings whereType($value)
 * @mixin \Eloquent
 */
class IndexUserCleanSettings extends Model
{
    use HasFactory;

    protected $table = 'user_clean_settings';
    public $timestamps = false;
    protected $connection = 'singlestore';
    protected $guarded = [];

    protected $fillable = [
        'seller_id',
        'clean_at',
        'sent_at',
        'type',
        'options',
        'token',
    ];
}
