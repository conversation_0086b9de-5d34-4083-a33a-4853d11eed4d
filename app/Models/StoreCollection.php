<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\StoreCollection
 *
 * @property int $store_id
 * @property int $collection_id
 * @property int $position
 * @property int $popularity
 * @property string|null $banner_url
 * @property-read \App\Models\Collection|null $collection
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection query()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection whereBannerUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection whereCollectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection wherePopularity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreCollection whereStoreId($value)
 * @mixin \Eloquent
 */
class StoreCollection extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'store_collection';
    protected $fillable = ['store_id', 'collection_id'];

    public $timestamps = false;

    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class, 'collection_id', 'id');
    }
}
