<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\PaymentGatewayStatusEnum;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * App\Models\PaymentGateway
 *
 * @property int $id
 * @property int|null $seller_id Null = system
 * @property int|null $store_id
 * @property string|null $account_id
 * @property string $name
 * @property string $gateway
 * @property string $config
 * @property int|null $checkout_domain
 * @property int $active
 * @property int $verified
 * @property string $location
 * @property string $seller_status
 * @property ?int $weight
 * @property ?int $sale_limit
 * @property \Illuminate\Support\Carbon $hold_at
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property-read \App\Models\Store|null $store
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway isActive()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereCheckoutDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereGateway($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentGateway whereVerified($value)
 * @mixin Builder
 * @mixin \Illuminate\Database\Query\Builder
 */
class PaymentGateway extends Model
{
    use SoftDeletes;

    /**
     * @var \Illuminate\Support\Carbon|mixed
     */
    protected $guarded = [
        'verified',
        'created_at',
        'updated_at',
    ];
    protected $casts = [
        'options' => 'json',
    ];

    protected $table = 'payment_gateways';

    public static function getConfig($gatewayName)
    {
        return self::query()
            ->where([
                'seller_id' => null,
                'gateway' => $gatewayName,
                'active' => PaymentGatewayStatusEnum::ACTIVE,
            ])
            ->value('config');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id')->select('id', 'name');
    }

    public function store_payment_gateways(): HasMany
    {
        return $this->hasMany(StorePaymentGateway::class, 'payment_gateway_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'payment_gateway_id');
    }

    public function scopeIsActive($query)
    {
        return $query->where('active', 1);
    }


    /**
     * @param    $id
     *
     * @return bool
     */
    public function holdPaymentGatewayIfSaleLimit($id): bool
    {
        try {
            $result = $this->getCalculationSaleLimit($id);

            if (! $result || ! $result->sale_limit) {
                return false;
            }

            if ($hold = ($result->total_sale > $result->sale_limit)) {
                $this->hold($id);
                $this->clearCache($result->seller_id);
            }
        } catch (\Throwable $e) {
            logException($e, __FUNCTION__, 'error', true);
        }

        return $hold ?? false;
    }

    /**
     * @param $id
     *
     * @return \App\Models\PaymentGateway|null
     */
    public function getCalculationSaleLimit($id): ?PaymentGateway
    {
        return self::query()
            ->join('order', static function ($join) {
                $join->on('payment_gateways.id', '=', 'order.payment_gateway_id')
                    ->where('order.paid_at', '>=', now()->subDay());
            })
            ->where('payment_gateways.id', $id)
            ->first(['payment_gateways.seller_id', 'payment_gateways.sale_limit', DB::raw('SUM(order.total_amount) as total_sale')]);
    }

    /**
     * @param $id
     *
     * @return void
     */
    public function hold($id): void
    {
        self::query()->where('id', $id)->update(['hold_at' => now()->addHour()]);
    }

    /**
     * @throws Exception
     */
    public static function getPaymentGatewaysBySeller(object $store): \Illuminate\Database\Eloquent\Collection
    {
        return cacheAlt()->remember(
            self::cacheKey($store->custom_payment ? $store->seller_id : ''),
            CacheTime::CACHE_10m,
            fn () => self::freshPaymentGatewayBySeller($store->seller_id, (int) $store->custom_payment)
        );
    }

    /**
     * Lấy cổng thanh toán của seller bao gồm
     * - Cổng của seller nếu seller có cấu hình cổng thanh toán riêng
     * - Cổng của platform nếu seller ko có cấu hình cổng và trạng thái seller là trusted
     * Như vậy seller thông thường sẽ chỉ dùng được 1 trong 2 loại cổng thanh toán là của
     * seller hoặc của platform
     * Ngoại lệ là seller vừa có cấu hình cổng riêng và trusted thì sẽ dùng dc cả 2
     *
     * @param $sellerId
     * @param $isCustomPayment
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function freshPaymentGatewayBySeller($sellerId, $isCustomPayment): \Illuminate\Database\Eloquent\Collection
    {
        return self::query()
            ->where('seller_id', $isCustomPayment ? $sellerId : null)
            ->groupBy('id')
            ->isActive()
            ->orderByDesc('seller_id')
            ->get()
            ->makeHidden([
                'created_at',
                'updated_at',
                'deleted_at',
            ]);
    }

    /**
     * @param $sellerId
     *
     * @return string
     */
    public static function cacheKey($sellerId): string
    {
        return CacheKeys::SYSTEM_PAYMENT_GATEWAYS . $sellerId;
    }

    /**
     * @param $sellerId
     *
     * @return void
     */
    public function clearCache($sellerId): void
    {
        syncClearCache(self::cacheKey($sellerId));
    }
}
