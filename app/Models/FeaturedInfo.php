<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FeaturedInfo extends Model
{
    use HasFactory;

    protected $table = 'featured_info';

    protected $fillable = [
        'id',
        'subject',
        'description',
        'short_desc',
        'banner_url',
        'banner_url_2',
        'article_url',
        'staff_id',
        'status',
        'expired_at',
    ];

    /**
     * @return BelongsTo
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'staff_id', 'id');
    }
}
