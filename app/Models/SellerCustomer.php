<?php

namespace App\Models;

use App\Traits\HasCompositePrimaryKey;
use Awobaz\Compoships\Compoships;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\SellerCustomer
 *
 * @property int $seller_id
 * @property int $customer_id
 * @property int $store_id
 * @property int $status 1:active,0:unsubscribed
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property int $total_orders
 * @property float $total_purchases
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer query()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereTotalOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereTotalPurchases($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerCustomer whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SellerCustomer extends Model
{
    use HasCompositePrimaryKey;
    use Compoships; //Laravel relationships with support for composite/multiple keys

    protected $fillable = ['seller_id', 'customer_id', 'store_id', 'total_orders', 'total_purchases'];

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'seller_customer';
    protected $primaryKey = ['seller_id', 'customer_id'];

    public function recent_store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

}
