<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

/**
 * App\Models\TwillioSms
 *
 * @property int $id
 * @property string $phone
 * @property string|null $region
 * @property string|null $code
 * @property int $status
 * @method static \Illuminate\Database\Eloquent\Builder|TwillioSms newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TwillioSms newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TwillioSms query()
 * @mixin \Eloquent
 */

class TwillioSms extends Model
{
    use HasUuids;
    public $incrementing = false;
    protected $keyType = 'string';
    protected $table = 'twilio_sms';
    protected $fillable = [
        'phone',
        'region',
        'code',
        'status',
    ];
}
