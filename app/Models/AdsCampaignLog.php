<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\AdsCampaignLog
 *
 * @property int $id
 * @property int $ads_campaign_id
 * @property int|null $seller_id
 * @property int $action
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property-read AdsCampaign|null $ads_campaign
 * @method static \Illuminate\Database\Eloquent\Builder|ActivityLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ActivityLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ActivityLog query()
 * @mixin \Eloquent
 */
class AdsCampaignLog extends Model
{
    protected $table = 'ads_campaign_logs';
    protected $fillable = [
        'ads_campaign_id',
        'seller_id',
        'action',
    ];

    /**
     * @return BelongsTo
     */
    public function ads_campaign()
    {
        return $this->belongsTo(AdsCampaign::class, 'ads_campaign_id', 'id');
    }
}
