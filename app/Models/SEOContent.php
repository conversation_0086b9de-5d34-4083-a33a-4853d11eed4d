<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\SEOContent
 *
 * @property int $id
 * @property string $type
 * @property string|null $title
 * @property string|null $description
 * @property string|null $content
 * @property string|null $keywords
 * @property string|null $data_json Data products
 * @property string|null $link_json Data links
 * @property string|null $config_json
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Database\Factories\SEOContentFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent query()
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereConfigJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereDataJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereKeywords($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereLinkJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SEOContent whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SEOContent extends Model
{
    use HasFactory;
    protected $table = 'seo_content_template';
    protected $fillable = [
        'type',
        'title',
        'description',
        'content',
        'keywords',
        'data_json',
        'link_json'
    ];
}
