<?php

namespace App\Models;

/**
 * App\Models\SystemTax
 *
 * @property int $id
 * @property string $country_code
 * @property string $city
 * @property float $tax_rate
 * @property int $status
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax query()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereCountryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereTaxRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTax whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SystemTax extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'system_tax';
}
