<?php

namespace App\Models;

use App\Enums\EventLogsTypeEnum;
use App\Traits\ScopeFilterAnalyticTrait;
use App\Traits\ScopeOrderByArray;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\TempEventLog
 *
 * @property int $id
 * @property string $type visit,add_to_cart,init_checkout
 * @property int|null $seller_id
 * @property int|null $store_id
 * @property int|null $campaign_id
 * @property int|null $product_id
 * @property string $session_id
 * @property string|null $country
 * @property string|null $device
 * @property string|null $device_detail
 * @property string $ad_source
 * @property string $ad_campaign
 * @property string $ad_medium
 * @property string $timestamp
 * @property string $datestamp
 * @property-read \App\Models\SystemLocation|null $country_info
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog addExcludeAnalytic(array $arrExclude)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog addFilterAnalytic(array $arrayFilter, array $dateRanges, $sellerId = null, $arrUnsetFilter = [])
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog calculateCount($alias = 'count')
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog filterVisit()
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog orderByArray($arrId, $column, $isGroupBy = true, $typeArr = 'int')
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereAdCampaign($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereAdMedium($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereAdSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereDatestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereDevice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereDeviceDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereSessionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereTimestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempEventLog whereType($value)
 * @mixin \Eloquent
 */
class TempEventLog extends Model
{
    use ScopeFilterAnalyticTrait;
    use ScopeOrderByArray;

    protected $connection = 'pgsql';
    protected $guarded = [];
    public $timestamps = false;

    public const FILTER_COLUMN_DATE = 'timestamp';

    public function country_info(): HasOne
    {
        return $this->hasOne(SystemLocation::class, 'code', 'country');
    }

    public function scopeCalculateCount($query, $alias = 'count')
    {
        return $query->selectRaw("count(distinct(session_id)) as $alias");
    }

    public function scopeFilterVisit($query)
    {
        return $query->where('type', EventLogsTypeEnum::VISIT);
    }
}
