<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\SystemConfigTypeEnum;
use App\Library\PingPongX\PingPongX;
use App\Traits\Encrypter;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\QueryException;
use Illuminate\Support\Str;

/**
 * App\Models\SystemConfig
 *
 * @property int $id
 * @property string $key
 * @property string|null $value
 * @property string|null $json_data
 * @property int $status
 * @property bool $is_encrypted
 * @property int $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereIsEncrypted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereJsonData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemConfig whereValue($value)
 * @mixin \Eloquent
 */
class SystemConfig extends Model
{
    use Encrypter;

    protected $table = 'system_config';

    protected $fillable = [
        'key',
        'value',
        'json_data',
        'status',
        'type',
        'is_encrypted',
    ];

    protected $casts = [
        'is_encrypted' => 'boolean',
    ];

    public static function backendConfigs(): array
    {
        return cacheAlt()->remember(CacheKeys::SYSTEM_CONFIGS_BACKEND, 86400, static function () {
            return self::loadConfigs(SystemConfigTypeEnum::BACKEND);
        });
    }

    public static function frontendConfigs(): array
    {
        return cacheAlt()->remember(CacheKeys::SYSTEM_CONFIGS_FRONTEND, 86400, static function () {
            return self::loadConfigs(SystemConfigTypeEnum::FRONTEND);
        });
    }

    private static function loadConfigs($type): array
    {
        $configs = [];
        $encrypter = self::encrypter();

        self::query()
            ->select([
                'key',
                'value',
                'is_encrypted',
            ])
            ->where('status', 1)
            ->whereIn('type', [
                $type,
                SystemConfigTypeEnum::DEFAULT,
            ])
            ->get()
            ->map(function ($config) use (&$configs, $encrypter) {
                $value = self::getAndDecryptValueByKey($config, 'value', $encrypter);
                $configs[$config->key] = $value;
            });

        return $configs;
    }

    public static function getConfig(string $key, $defaultValue = null)
    {
        $configs = self::backendConfigs();

        if (array_key_exists($key, $configs)) {
            return $configs[$key];
        }

        return $defaultValue;
    }

    public static function getElasticLog(): array
    {
        return cacheAlt()->remember(
            CacheKeys::SYSTEM_ELASTIC_LOG,
            CacheKeys::CACHE_30D,
            static function () {
                $value = optional(self::getCustomConfig(CacheKeys::SYSTEM_ELASTIC_LOG))->json_data;
                return json_decode($value, true);
            }
        );
    }

    /**
     * @param $key
     * @param bool $cacheResult
     * @return SystemConfig|null
     */
    public static function getCustomConfig($key, bool $cacheResult = true): ?object
    {
        $encrypter = self::encrypter();
        $callback = function () use ($key, $encrypter) {
            $obj = self::query()
                ->select([
                    'value',
                    'json_data',
                    'is_encrypted',
                ])
                ->where('status', 1)
                ->where('key', $key)
                ->first();
            if (!is_null($obj)) {
                $obj->value = self::getAndDecryptValueByKey($obj, 'value', $encrypter);
                $obj->json_data = self::getAndDecryptValueByKey($obj, 'json_data', $encrypter);
            }
            return $obj;
        };
        if ($cacheResult) {
            return cache()->remember('custom_config_' . $key, 60, $callback);
        }
        return $callback();
    }

    public static function getDomainManagementApi(): ?array
    {
        return cache()->remember(CacheKeys::DOMAIN_MANAGEMENT_API, CacheKeys::CACHE_30D, static function () {
            $value = optional(self::getCustomConfig(CacheKeys::DOMAIN_MANAGEMENT_API))->json_data;
            return json_decode($value, true);
        });
    }

    private static function getAndDecryptValueByKey($config, $key, $encrypter): ?string
    {
        if (!$config->is_encrypted) {
            return $config->$key;
        }

        if (empty($config->$key)) {
            return $config->$key;
        }

        try {
            return $encrypter->decrypt($config->$key);
        } catch (DecryptException $e) {
            return 'invalid key decrypt';
        }
    }

    public static function getPingPongXToken($isExpired = false): ?array
    {
        return cache()->remember(CacheKeys::PINGPONGX_AUTHORIZATION, CacheTime::CACHE_29D, static function () use ($isExpired) {
            $value = optional(SystemConfig::getCustomConfig(CacheKeys::PINGPONGX_AUTHORIZATION))->json_data;
            $data = json_decode($value, true);
            if (!is_null($data)) {
                if ($data['access_token_expires'] <= now()->timestamp || $isExpired) {
                    if ($data['refresh_token_expires'] <= now()->timestamp) {
                        return null;
                    }
                    $refreshToken = PingPongX::instance()->refreshToken($data['refresh_token']);
                    if (is_null($refreshToken)) {
                        return null;
                    }
                    $refreshToken['access_token_expires'] = (int)$refreshToken['expires_in'] + now()->subDays(1)->timestamp;
                    $refreshToken['refresh_token_expires'] = (int)$refreshToken['refresh_token_expires_in'] + now()->subDays(1)->timestamp;
                    SystemConfig::query()
                        ->where('key', CacheKeys::PINGPONGX_AUTHORIZATION)
                        ->update([
                            'json_data' => json_encode($refreshToken)
                        ]);
                    return $refreshToken;
                }
            }
            return $data;
        });
    }

    public static function getPingPongXSubscribeNotificationSecretKey()
    {
        return cache()->remember(CacheKeys::PINGPONGX_SUBSCRIBE_NOTIFICATION, CacheKeys::CACHE_30D, static function () {
            $value = optional(SystemConfig::getCustomConfig(CacheKeys::PINGPONGX_SUBSCRIBE_NOTIFICATION))->json_data;
            return json_decode($value, true);
        });
    }

    public static function checkPingPongXWhiteListEnabled()
    {
        return cache()->remember(CacheKeys::PINGPONGX_PAYOUT_WHITELIST_ENABLED, CacheKeys::CACHE_30D, static function () {
            $value = optional(SystemConfig::getCustomConfig(CacheKeys::PINGPONGX_PAYOUT_WHITELIST_ENABLED))->value;
            return ($value === 'true');
        });
    }

    public static function checkPingPongXPayoutQuickTestEnabled()
    {
        return cache()->remember(CacheKeys::PINGPONGX_PAYOUT_QUICK_TESTING_ENABLED, CacheKeys::CACHE_30D, static function () {
            $value = optional(SystemConfig::getCustomConfig(CacheKeys::PINGPONGX_PAYOUT_QUICK_TESTING_ENABLED))->value;
            return ($value === 'true');
        });
    }

    public static function getPayoutPayPalConfig()
    {
        return cache()->remember(CacheKeys::PAYOUT_PAYPAL, CacheTime::CACHE_1Y, static function () {
            $value = optional(SystemConfig::getCustomConfig(CacheKeys::PAYOUT_PAYPAL))->json_data;
            return json_decode($value, true);
        });
    }

    public static function paypalPayoutQuickTestEnabled()
    {
        return cache()->remember(CacheKeys::PAYOUT_PAYPAL_QUICK_TEST, CacheKeys::CACHE_30D, static function () {
            $value = optional(SystemConfig::getCustomConfig(CacheKeys::PAYOUT_PAYPAL_QUICK_TEST))->value;
            return ($value === 'true');
        });
    }

    /**
     * @param $key
     * @param array $values
     * @return false|Builder|Model
     */
    public static function setConfig($key, array $values)
    {
        try {
            if (!empty($values['is_encrypted'])) {
                $encrypter = self::encrypter();

                if (isset($values['value']) && $values['value'] !== '') {
                    $values['value'] = $encrypter->encrypt($values['value']);
                }

                if (isset($values['json_data']) && $values['json_data'] !== '') {
                    $values['json_data'] = $encrypter->encrypt($values['json_data']);
                }
            }
            return self::query()->updateOrCreate(array('key' => $key), $values);
        } catch (QueryException $e) {
            return false;
        }
    }

    /**
     * @return array
     * @throws \Exception
     */
    public static function getShopifyConfig(): array
    {
        return cache()->remember(CacheKeys::SHOPIFY_APP_CONFIG, CacheKeys::CACHE_30D, static function () {
            $settings = self::getCustomConfig(CacheKeys::SHOPIFY_APP_CONFIG);
            if (empty($settings)) {
                return [];
            }

            $value = optional($settings)->json_data;
            return json_decode($value, true) ?: [];
        });
    }

    public static function getTiktokShopConfig(): array
    {
        return cache()->remember(CacheKeys::TIKTOK_SHOP_CONFIG, CacheKeys::CACHE_30D, static function () {
            $settings = self::getCustomConfig(CacheKeys::TIKTOK_SHOP_CONFIG);
            if (empty($settings)) {
                return [];
            }

            $value = optional($settings)->json_data;
            return json_decode($value, true) ?: [];
        });
    }

    public static function getCheckoutFormConfig($country = null): array
    {
        $config = cache()->remember(CacheKeys::CHECKOUT_FORM_CONFIG, CacheTime::CACHE_1Y, static function () {
            $value = optional(self::getCustomConfig(CacheKeys::CHECKOUT_FORM_CONFIG))->json_data;
            return json_decode($value, true) ?: [];
        });

        if (!$country) {
            return $config;
        }

        return $config[$country === 'GB' ? 'UK' : $country] ?? [];
    }

    /**
     * @return \Illuminate\Support\Collection
     * @throws \Throwable
     */
    public static function getCheckoutPaymentGateways()
    {
        $config = cache()->remember(CacheKeys::CHECKOUT_PAYMENT_GATEWAYS, CacheTime::CACHE_24H, static function () {
            $value = optional(self::getCustomConfig(CacheKeys::CHECKOUT_PAYMENT_GATEWAYS))->json_data;
            if (empty($value)) {
                return [];
            }
            if (Str::isJson($value)) {
                return json_decode($value, true, 512, JSON_THROW_ON_ERROR);
            }
            return [];
        });
        return !empty($config) ? collect($config) : collect();
    }
}
