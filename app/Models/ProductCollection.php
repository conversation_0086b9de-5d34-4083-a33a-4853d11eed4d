<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\ProductCollection
 *
 * @property int $product_id
 * @property int $collection_id
 * @property int $seller_id
 * @property-read \App\Models\Collection|null $collection
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\User $seller
 * @method static \Database\Factories\ProductCollectionFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCollection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCollection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCollection query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCollection whereCollectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCollection whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCollection whereSellerId($value)
 * @mixin \Eloquent
 */
class ProductCollection extends Model
{
    use HasFactory;

    protected $table = 'product_collection';
    protected $fillable = ['product_id', 'collection_id', 'seller_id', 'created_by'];
    public $timestamps = false;

    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class, 'collection_id', 'id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * Get collection (id, slug) for sync to elasticsearch by campaign ids
     *
     * @param  array  $campaignIds
     *
     * @return array
     */
    public static function getCollectionsForSyncByCampaignIds(array $campaignIds = []): array
    {
        $campaignCollections = [];
        foreach ($campaignIds as $sellerId => $productIds) {
            $campaignCollectionModel = self::query()
                ->with('collection')
                ->when(!empty($sellerId), function ($query) use ($sellerId) {
                    $query->where('seller_id', $sellerId);
                })
                ->whereIn('product_id', $productIds)
                ->get();
            if ($campaignCollectionModel->count() > 0) {
                foreach ($campaignCollectionModel as $idx => $campaignCollectionModelItem) {
                    $key = $sellerId . '_' .$campaignCollectionModelItem->product_id;
                    if (!isset($campaignCollections[$key])) {
                        $campaignCollections[$key]['ids'] = [(int)$campaignCollectionModelItem->collection_id];
                        $campaignCollections[$key]['name'] = !is_null($campaignCollectionModelItem->collection) ? [$campaignCollectionModelItem->collection->name] : [];
                        $campaignCollections[$key]['slugs'] = !is_null($campaignCollectionModelItem->collection) ? [$campaignCollectionModelItem->collection->slug] : [];
                        $campaignCollectionModel->forget($idx);
                        continue;
                    }

                    if (!in_array((int)$campaignCollectionModelItem->collection_id, $campaignCollections[$key]['ids'], true)) {
                        $campaignCollections[$key]['ids'][] = (int)$campaignCollectionModelItem->collection_id;
                        $campaignCollections[$key]['name'][] = !is_null($campaignCollectionModelItem->collection) ? $campaignCollectionModelItem->collection->name : '';
                        $campaignCollections[$key]['slugs'][] = !is_null($campaignCollectionModelItem->collection) ? $campaignCollectionModelItem->collection->slug : '';
                    }
                    $campaignCollectionModel->forget($idx);
                }
            }
        }
        return $campaignCollections;
    }
}
