<?php

namespace App\Models;

use App\Enums\EventLogsTypeEnum;
use App\Traits\ScopeFilterAnalyticTrait;
use App\Traits\ScopeOrderByArray;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\EventLogs
 *
 * @property int $id
 * @property int|null $seller_id
 * @property int|null $store_id
 * @property int|null $campaign_id
 * @property int|null $product_id
 * @property string $session_id
 * @property string|null $country
 * @property string|null $device
 * @property string|null $device_detail
 * @property string $timestamp
 * @property string|null $datestamp
 * @property string $ad_source
 * @property string $ad_campaign
 * @property string $ad_medium
 * @property string $type visit,add_to_cart,init_checkout
 * @property-read \App\Models\SystemLocation|null $country_info
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs addExcludeAnalytic(array $arrExclude)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs addFilterAnalytic(array $arrayFilter, array $dateRanges, $sellerId = null, $arrUnsetFilter = [])
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs calculateCount($alias = 'count')
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs filterVisit()
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs orderByArray($arrId, $column, $isGroupBy = true, $typeArr = 'int')
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereAdCampaign($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereAdMedium($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereAdSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereDatestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereDevice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereDeviceDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereSessionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereTimestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventLogs whereType($value)
 * @mixin \Eloquent
 */
class EventLogs extends Model
{
    use ScopeFilterAnalyticTrait;
    use ScopeOrderByArray;

    protected $connection = 'pgsql';
    protected $table = 'event_logs';
    protected $guarded = [];
    public $timestamps = false;

    public const FILTER_COLUMN_DATE = 'timestamp';

    public function country_info(): HasOne
    {
        return $this->hasOne(SystemLocation::class, 'code', 'country');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function scopeCalculateCount($query, $alias = 'count')
    {
        return $query->selectRaw("count(distinct(session_id)) as $alias");
    }

    public function scopeFilterVisit($query)
    {
        return $query->where('type', EventLogsTypeEnum::VISIT);
    }
}
