<?php

namespace App\Models;

use App\Enums\StoreDomainStatusEnum;
use App\Library\DomainManagement\DomainClient;
use App\Traits\UuidTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\StoreDomain
 *
 * @property string $id
 * @property int $seller_id
 * @property int $store_id
 * @property string|null $domain
 * @property int $is_default 0:is not default, 1: is default
 * @property string $status 'pending','activated'
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $seller
 * @property-read \App\Models\Store|null $store
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain query()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreDomain whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StoreDomain extends Model
{
    use HasFactory, UuidTrait;

    protected $fillable = [
        'seller_id',
        'store_id',
        'domain',
        'is_default',
        'status',
        'cloudflare_custom_hostname_id',
        'sender_email',
        'sender_name',
        'sender_verified',
        'sender_next_verify_at',
        'sender_dns_records'
    ];

    protected $casts = [
        'sender_dns_records' => 'array',
        'dns_records' => 'array'
    ];


    public static function getStoreDomainsByCurrentSeller()
    {
        return self::query()
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'store_id' => request()->route('storeId'),
            ])
            ->get();
    }

    public static function getCurrentStoreDomainByIdAndStoreId($domainId, $storeId)
    {
        return self::query()->firstWhere([
            'id' => $domainId,
            'store_id' => $storeId,
            'seller_id' => currentUser()->getUserId(),
        ]);
    }

    public static function updateStoreDomainById($Id, $params = []): int
    {
        return self::query()
            ->where('id', $Id)
            ->update($params);
    }

    public static function resetDefaultByStoreId($storeId): int
    {
        return self::query()
            ->where('store_id', $storeId)
            ->update([
                'is_default' => 0
            ]);
    }

    public static function destroyAllByStoreId($storeId)
    {
        try {
            $storeDomains = self::query()
                ->where([
                    'store_id' => $storeId,
                    'status' => StoreDomainStatusEnum::ACTIVATED
                ])
                ->get();

            if (count($storeDomains) > 0) {
                $storeDomains->each(fn($entry) => DomainClient::instance()->destroy($entry->domain));
            }

            return self::query()
                ->where('store_id', $storeId)
                ->forceDelete();
        } catch (\Exception $e) {
            logToDiscord("destroyAllByStoreId in model StoreDomain error! \n - StoreId:  {$storeId} - Line: {$e->getFile()} - Trace Info: {$e->getTraceAsString()}", 'error');
            return null;
        }
    }

    public function store(): HasOne
    {
        return $this->hasOne(Store::class, 'id', 'store_id');
    }

    public function seller(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'seller_id');
    }
}
