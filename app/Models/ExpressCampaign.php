<?php

namespace App\Models;

use App\Enums\FileTypeEnum;
use App\Enums\ProductType;
use App\Traits\HasRelationShipsCustom;
use App\Traits\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * App\Models\ExpressCampaign
 *
 * @property int $id
 * @property string|null $slug
 * @property int|null $campaign_id
 * @property int|null $supplier_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $company_id
 * @property int|null $template_id
 * @property string $name
 * @property string|null $name_ts
 * @property string|null $thumb_url
 * @property string|null $options
 * @property string $mockup_type mockup type
 * @property string|null $default_option
 * @property string|null $print_spaces
 * @property string $currency_code
 * @property string|null $market_location
 * @property float $base_cost
 * @property string|null $base_costs
 * @property float $price
 * @property float $old_price
 * @property float $shipping_cost
 * @property string $status
 * @property string $product_type
 * @property string|null $description
 * @property string|null $description_ts
 * @property float $extra_print_cost Extra print cost
 * @property \Illuminate\Support\Carbon|null $start_time
 * @property \Illuminate\Support\Carbon|null $end_time
 * @property int $show_countdown
 * @property int|null $default_product_id
 * @property int|null $default_mockup_id
 * @property array|null $tracking_code
 * @property string $tm_status
 * @property int|null $sync_status Sync campaign info to elastic-search
 * @property string|null $elastic_document_id
 * @property string|null $sku
 * @property float $score
 * @property float $time_score
 * @property int $sales_score
 * @property int|null $priority
 * @property string $pricing_mode
 * @property string $public_status
 * @property string|null $google_category_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $is_deleted
 * @property string|null $attributes
 * @property float $pre_discounted_price
 * @property string $render_mode Campaign render mode
 * @property int $personalized
 * @property int $full_printed
 * @property int $allow_bulk 1:allow_bulk
 * @property int $temp_status
 * @property string|null $visited_at
 * @property int $google_crawled_status
 * @property string $system_type
 * @property int $archived
 * @property-read \App\Models\Category|null $categories
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Collection> $collections
 * @property-read int|null $collections_count
 * @property-read ExpressCampaign|null $defaultProduct
 * @property-read \App\Models\File|null $design
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $designs
 * @property-read int|null $designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $images
 * @property-read int|null $images_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductReview> $productReviews
 * @property-read int|null $product_reviews_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product> $products
 * @property-read int|null $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PromotionRule> $promotions
 * @property-read int|null $promotions_count
 * @property-read \App\Models\User|null $seller
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\StoreProduct> $store_campaign
 * @property-read int|null $store_campaign_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Store> $stores
 * @property-read int|null $stores_count
 * @property-read ExpressCampaign|null $template_campaign
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product> $template_products
 * @property-read int|null $template_products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Upsell> $upsell
 * @property-read int|null $upsell_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductVariant> $variants
 * @property-read int|null $variants_count
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign query()
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereAllowBulk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereArchived($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereAuthId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereBaseCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereBaseCosts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereCurrencyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereDefaultMockupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereDefaultOption($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereDefaultProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereDescriptionTs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereElasticDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereExtraPrintCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereFullPrinted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereGoogleCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereGoogleCrawledStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereIsDeleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereMarketLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereMockupType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereNameTs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereOldPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePersonalized($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePreDiscountedPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePricingMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePrintSpaces($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereProductType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign wherePublicStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereRenderMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSalesScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereShippingCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereShowCountdown($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSupplierId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSyncStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereSystemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereTempStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereThumbUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereTimeScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereTmStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereTrackingCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign whereVisitedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ExpressCampaign withoutTrashed()
 * @mixin \Eloquent
 */
class ExpressCampaign extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasRelationShipsCustom;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'product';
    protected $fillable = [
        'name',
        'name_ts',
        'thumb_url',
        'description',
        'description_ts',
        'seller_id',
        'auth_id',
        'slug',
        'start_time',
        'end_time',
        'show_countdown',
        'default_product_id',
        'tracking_code',
        'price',
        'old_price',
        'status',
        'render_mode',
        'tm_status',
        'personalized',
        'product_type',
        'public_status',
        'currency_code',
        'market_location',
        'mockup_type',
        'default_option',
        'template_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'tracking_code' => 'json',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'nickname', 'email', 'status', 'custom_payment')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown',
                'nickname' => null
            ]);
    }

    public function stores(): HasManyThrough
    {
        return $this->hasManyThrough(
            Store::class,
            StoreProduct::class,
            'product_id',
            'id',
            'id',
            'store_id'
        );
    }

    public function collections(): HasManyThrough
    {
        return $this->hasManyThrough(
            Collection::class,
            ProductCollection::class,
            'product_id',
            'id',
            'id',
            'collection_id'
        );
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'campaign_id', 'id');
    }

    public function images(): HasMany
    {
        return $this->hasMany(File::class, 'campaign_id', 'id')
            ->select(['id', 'campaign_id', 'file_url', 'file_url_2', 'product_id', 'option', 'token'])
            ->where('type', FileTypeEnum::IMAGE)
            ->orderBy('position')
            ->orderBy('id');
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'campaign_id', 'id');
    }


    public function template_campaign(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'template_id', 'id')
            ->where('product_type', ProductType::CAMPAIGN_TEMPLATE);
    }

    public function template_products(): HasMany
    {
        return $this->hasMany(Product::class, 'campaign_id', 'id')
        ->where('product_type', ProductType::PRODUCT_TEMPLATE);

    }

    public function defaultProduct(): HasOne
    {
        return $this->hasOne(Product::class, 'id', 'default_product_id');
    }

    public function upsell(): HasMany
    {
        return $this->hasMany(Upsell::class, 'product_id', 'id');
    }

    public function promotions(): HasMany
    {
        return $this->hasMany(PromotionRule::class, 'campaign_id', 'id');
    }

    public function designs(): HasMany
    {
        return $this->hasMany(File::class, 'campaign_id', 'id')
            ->select(['id', 'campaign_id', 'file_url', 'file_url_2', 'product_id', 'option', 'token'])
            ->where('type', FileTypeEnum::DESIGN)
            ->orderBy('position')
            ->orderBy('id');
    }

    public function design(): HasOne
    {
        return $this->hasOne(File::class, 'campaign_id', 'id')
            ->select(['id', 'campaign_id', 'file_url', 'file_url_2', 'product_id', 'option', 'type_detail'])
            ->where('type', FileTypeEnum::DESIGN);
    }

    public function productReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    /**
     * Get campaign info with campaign ids
     * $fields[id, name, slug, description]
     * @param array $campaignIds
     * @return array
     */
    public static function get_campaign_info_by_campaign_ids(array $campaignIds = []): array
    {
        $campaigns = self::query()
            ->select([
                'id',
                'name',
                'slug',
                'description',
                'default_product_id',
            ])
            ->whereIn('id', $campaignIds)
            ->get();

        $data = [];

        if ($campaigns->count() > 0) {
            foreach ($campaigns as $campaignIndex => $campaign) {
                if (!isset($data[$campaign->id])) {
                    $data[$campaign->id] = [
                        'name' => $campaign->name,
                        'slug' => $campaign->slug,
                        'description' => $campaign->description,
                        'default_product_id' => $campaign->default_product_id,
                    ];
                }

                unset($campaigns[$campaignIndex]);
            }
        }

        return $data;
    }

    public function categories(): HasOneThrough
    {
        return $this->hasOneThrough(
            Category::class,
            ProductCategory::class,
            'product_id',
            'id',
            'campaign_id',
            'product_id',
        );
    }

    public function getNameAttribute(): string
    {
        // ref: https://stackoverflow.com/a/35184977/3872002
        // https://laravel.com/docs/8.x/eloquent-mutators#defining-a-mutator
        if (is_null($this->attributes['name'])) {
            return 'Draft #' . $this->id;
        }

        return $this->attributes['name'];
    }

    public function store_campaign(): HasMany
    {
        // return parent campaign stores if the product belong to a campaign
        if ($this->campaign_id) {
            return $this->hasMany(StoreProduct::class, 'product_id', 'campaign_id');
        }

        return $this->hasMany(StoreProduct::class, 'product_id', 'id');
    }

    public const FILTER_COLUMN_DATE = 'product.created_at';
}
