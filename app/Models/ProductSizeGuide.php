<?php

namespace App\Models;

use App\Enums\CacheKeys;

/**
 * App\Models\ProductSizeGuide
 *
 * @property int $id
 * @property int $product_id
 * @property string|null $sku
 * @property string|null $size
 * @property float|null $length
 * @property float|null $width
 * @property float|null $sleeve
 * @property float|null $hip
 * @property float|null $waist
 * @property string|null $height
 * @property string|null $weight
 * @property string $unit
 * @property string|null $note
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereHip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereLength($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereSleeve($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereWaist($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductSizeGuide whereWidth($value)
 * @mixin \Eloquent
 */
class ProductSizeGuide extends Model
{
    protected $table = 'product_size_guide';
    protected $fillable = [
        'product_id',
        'sku',
        'size',
        'length',
        'width',
        'sleeve',
        'waist',
        'hip',
        'unit',
        'note',
    ];

    public $timestamps = false;

    public static function allSizeGuides()
    {
        return cacheGet(CacheKeys::SIZE_GUIDE, CacheKeys::CACHE_30D, function () {
            return ProductSizeGuide::all();
        }, [], CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }

}
