<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Enums\FulfillMappingEnum;
use Illuminate\Support\Str;

/**
 * App\Models\FulfillMapping
 *
 * @property int $id
 * @property string $object_id
 * @property string|null $external_id
 * @property int|null $supplier_id
 * @property string|null $location
 * @property string $type
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping query()
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping whereObjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping whereSupplierId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillMapping whereType($value)
 * @mixin \Eloquent
 */
class FulfillMapping extends Model
{
    protected $table = 'fulfill_mapping';
    public $timestamps = false;

    protected $guarded = [];

    protected static function booted(): void
    {
        static::saving(static function ($model) {
            syncClearCache(CacheKeys::getFulfillMapping($model->type));
        });
        static::deleting(static function ($model) {
            syncClearCache(CacheKeys::getFulfillMapping($model->type));
        });
    }

    static private array $cache = [];
    /** @noinspection PhpUnhandledExceptionInspection */
    public static function getAndCache($type)
    {
        if (isset(self::$cache[$type])) {
            return self::$cache[$type];
        }

        $data = cache()->remember(
            CacheKeys::getFulfillMapping($type),
            CacheKeys::CACHE_30D,
            function () use ($type) {
                return self::query()
                    ->where('type', $type)
                    ->orderBy('supplier_id')
                    ->orderByDesc('location')
                    ->get()
                    ->toJson();
            }
        );

        if (is_string($data)) {
            $arr = json_decode($data, true);
            $data = collect($arr)->map(function ($each){
                return self::make($each);
            });
        }
        self::$cache[$type] = $data;

        return $data;
    }

    public static function filterByExcludeLocation($type, ?object $orderLocation, ?string $objectId = null)
    {
        return self::getAndCache($type)
            ->when($objectId, function ($query) use ($objectId) {
                return $query->where('object_id', $objectId);
            })
            ->filter(function ($each) use ($orderLocation) {
                $locations = explode(',', $each->location);
                $checkTrue = false;
                foreach ($locations as $location) {
                    $location = strtolower(trim($location));

                    $mappingLocationStateCode = null;
                    $pattern = '/^[a-zA-Z]{2}-[a-zA-Z]{2}$/';

                    if (preg_match($pattern, $location)) {
                        $locations = explode('-', $location);
                        $mappingLocationStateCode = end($locations);
                    }

                    $address = optional($orderLocation)->address;
                    if ($location === strtolower(optional($orderLocation)->code) ||
                        $location === strtolower(optional($orderLocation)->state) ||
                        (!empty($mappingLocationStateCode) && $mappingLocationStateCode === strtolower(optional($orderLocation)->order_state)) ||
                        (Str::of($location)->explode(' ')->count() > 1 && str_contains($address, $location)))
                    {
                        $checkTrue = true;
                        break;
                    }
                }

                return $checkTrue;
            });
    }

    public static function checkExcludeShipping(?object $location, ?string $templateId): bool
    {
        if (empty($location) || empty($templateId)) {
            return false;
        }

        $excludeMappings = self::filterByExcludeLocation(
            FulfillMappingEnum::PRODUCT_EXCLUDE_LOCATION,
            $location,
            $templateId,
        );

        // if null supplier_id, it mean all supplier
        return $excludeMappings
                ->filter(function ($mapping) {
                    return empty($mapping->supplier_id);
                })->count() > 0;
    }
}
