<?php

namespace App\Models;

use Awobaz\Compoships\Compoships;

/**
 * App\Models\ProductDesignMapping
 *
 * @property int $supplier_id
 * @property string $print_space
 * @property int $product_id
 * @property string $width
 * @property string $height
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping wherePrintSpace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping whereSupplierId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductDesignMapping whereWidth($value)
 * @mixin \Eloquent
 */
class ProductDesignMapping extends Model
{
    use Compoships;

    protected $table = 'product_design_mapping';
    public $timestamps = false;
    public $incrementing = false;
}
