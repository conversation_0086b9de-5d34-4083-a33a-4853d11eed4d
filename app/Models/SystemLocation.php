<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Enums\LocationTypeEnum;
use Illuminate\Support\Collection as CollectionAlias;
use Modules\OrderService\Models\RegionOrders;
/**
 * App\Models\SystemLocation
 *
 * @property string $code
 * @property string $name
 * @property string $region
 * @property string $sub_region
 * @property string $intermediate_region
 * @property string|null $region_code
 * @property string $sub_region_code
 * @property string $intermediate_region_code
 * @property string | null $phone_code
 * @property string|null $type 'country','region','sub_region','intermediate_region','worldwide'
 * @property string|null $default_currency_code
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereDefaultCurrencyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereIntermediateRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereIntermediateRegionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereRegionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereSubRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereSubRegionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemLocation whereType($value)
 * @mixin \Illuminate\Database\Eloquent\Builder
 */
class SystemLocation extends Model
{

    protected $table = 'system_location';
    protected $connection = 'mysql';
    protected $primaryKey = 'code';
    public $incrementing = false;
    public $timestamps = false;
    /**
     * @var null | \Illuminate\Database\Eloquent\Collection<int, SystemLocation>
     */
    static $systemLocations = null;
    /**
     * @var null | \Illuminate\Database\Eloquent\Collection<int, SystemLocation>
     */
    static $systemCountries = null;

    protected static function booted()
    {
        static::addGlobalScope('sort_by_alphabets', function ($query) {
            $query->orderBy('name');
        });
    }

    public static function systemLocations()
    {
        if (is_null(self::$systemLocations)) {
            self::$systemLocations = cacheGet(CacheKeys::LOCATIONS, CacheKeys::CACHE_30D, static function () {
                return SystemLocation::all();
            }, [], CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }
        return self::$systemLocations;
    }

    // Get countries only
    public static function systemCountries(): CollectionAlias
    {
        if (is_null(self::$systemCountries)) {
            self::$systemCountries = cacheGet(CacheKeys::COUNTRIES, CacheKeys::CACHE_30D, static function () {
                $arr = [];
                self::systemLocations()->each(static function ($each) use (&$arr) {
                    if ($each->type === LocationTypeEnum::COUNTRY) {
                        $arr[] = $each;
                    }
                });

                return collect($arr);
            }, [], CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }
        return self::$systemCountries;
    }

    public static function findByCountryCode($countryCode): ?object
    {
        return self::systemLocations()->where('code', $countryCode)->first();
    }

    public static function getCountriesByLocationCode($codes): array
    {
        if (is_string($codes)) {
            $codes = [$codes];
        }
        $codes = array_filter($codes, static function ($each) {
            return $each !== '*';
        });

        $arr = [];
        $locations = self::systemLocations()
            ->whereIn('code', $codes);

        foreach ($locations as $location) {
            switch ($location->type) {
                case LocationTypeEnum::REGION:
                    $column = 'region_code';
                    break;
                case LocationTypeEnum::SUB_REGION:
                    $column = 'sub_region_code';
                    break;
                case LocationTypeEnum::INTERMEDIATE_REGION:
                    $column = 'intermediate_region_code';
                    break;
                default:
                    $column = 'code';
                    break;
            }
            $countries = self::systemLocations()
                ->where($column, $location->code)
                ->where('type', LocationTypeEnum::COUNTRY);

            $arr = array_merge($arr, $countries->toArray());
        }

        return $arr;
    }

    /**
     * @param $orderId
     *
     * @return \App\Models\SystemLocation|null
     */
    public static function ofOrderId($orderId): ?SystemLocation
    {
        if (! $country = Order::whereKey($orderId)->first(['country', 'address'])) {
            return null;
        }

        return self::findByCountryCodeThenSetForAssign($country);
    }

    /**
     * @param Order|RegionOrders|null $order
     *
     * @return SystemLocation|null
     */
    public static function findByCountryCodeThenSetForAssign(Order|RegionOrders|null $order): ?SystemLocation
    {
        if (!$order) {
            return null;
        }
        if (!$location = self::findByCountryCode($order->country)) {
            return null;
        }

        $location->address = $order->address;
        $location->order_state = $order->state;

        return self::cook($location);
    }

    /**
     * @param $location
     * @param Order|RegionOrders $order
     * @return SystemLocation|null
     */
    public static function mappingLocationWithOrder($location, Order|RegionOrders $order): ?SystemLocation
    {
        if (!$location) {
            return null;
        }
        $location->address = $order->address;
        $location->order_state = $order->state;
        return self::cook($location);
    }

    /**
     * @param     \App\Models\SystemLocation     $location
     *
     * @return \App\Models\SystemLocation
     */
    public static function cook(SystemLocation $location): SystemLocation
    {
        $location->state = $location->code . '-' . $location->order_state;
        $location->address = strtolower(
            trim(
                preg_replace(
                    ['/[^a-zA-Z\s]/', '/\s+/',],
                    ['', ' ',],
                    $location->address
                )
            )
        );

        return $location;
    }

    public function getRegionCodes()
    {
        $regionCodes = [];

        $regionCodes[] = $this->code;

        if (!empty($this->sub_region_code)) {
            $regionCodes[] = $this->sub_region_code;
        }

        if (!empty($this->intermediate_region_code)) {
            $regionCodes[] = $this->intermediate_region_code;
        }

        if (!empty($this->region_code)) {
            $regionCodes[] = $this->region_code;
        }
        $regionCodes[] = "*";

        return $regionCodes;
    }

    /**
     * @return bool
     */
    public function isWorldWide(): bool
    {
        return $this->code === '*';
    }
}
