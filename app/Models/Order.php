<?php

namespace App\Models;

use App\Enums\AccessExternalApiType;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\FileTypeEnum;
use App\Enums\FulfillmentStatusEnum;
use App\Enums\NotificationChannelEnum;
use App\Enums\OrderAssigneeEnum;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderSupportStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\ProductPrintType;
use App\Enums\PromotionTypeEnum;
use App\Filters\OrderFilter;
use App\Traits\Filterable;
use App\Traits\HasRelationShipsCustom;
use App\Traits\ScopeFilterAnalyticTrait;
use App\Traits\ScopeFilterDateBeforeRangeTrait;
use App\Traits\ScopeFilterDateBeforeRangeWithSubmonthTrait;
use App\Traits\ScopeFilterDateInRangeWithRoundTrait;
use App\Traits\ScopeFilterDateRangeWithSameDiffInPassTrait;
use App\Traits\ScopeFilterQueryDateInPreviousRangeToCurrentEndDateTrait;
use Awobaz\Compoships\Compoships;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\OrderService\Services\RegionOrderService;
use Modules\OrderService\Traits\HasRegionConnection;
use Modules\OrderService\Traits\UpdatingOrderAttributesTrait;
use Modules\TiktokShop\Models\TiktokShopInfo;
use Propaganistas\LaravelPhone\PhoneNumber;

/**
 * App\Models\Order
 *
 * @property int $id
 * @property string|null $access_token
 * @property int|null $company_id
 * @property int|null $seller_id
 * @property int|null $ref_id
 * @property int|null $store_id
 * @property string|null $store_name
 * @property string|null $store_domain
 * @property int|null $customer_id
 * @property string|null $customer_name
 * @property string|null $customer_email
 * @property string|null $customer_phone
 * @property string|null $order_number
 * @property string|null $region
 * @property string|null $order_number_2
 * @property string|null $name
 * @property int $total_quantity
 * @property float $total_product_amount subtotal
 * @property float $total_shipping_amount
 * @property float $tip_amount
 * @property float $total_discount
 * @property float $total_tax_amount
 * @property float $total_amount
 * @property float $total_paid customer paid
 * @property float $total_refund refund to customer
 * @property float $total_product_cost total product cost pay to suppliers
 * @property float $total_product_base_cost
 * @property float $total_shipping_cost
 * @property float $total_seller_profit
 * @property float $total_artist_profit
 * @property float $total_profit
 * @property float $total_sen_points
 * @property string|null $billing_address
 * @property string|null $shipping_address
 * @property int|null $payment_gateway_id
 * @property string|null $payment_method
 * @property float $payment_discount
 * @property string|null $transaction_id
 * @property float $payment_fee
 * @property float $processing_fee
 * @property float $insurance_fee The insurance fee
 * @property float $total_fulfill_fee
 * @property float $processing_fee_paid
 * @property float $fulfill_fee_paid
 * @property int|null $promotion_rule_id
 * @property string|null $promotion_rule JSON/backup rule
 * @property string|null $discount_code
 * @property string|null $shipping_method
 * @property int $remarketing_status 0:no,1:yes
 * @property string $status ARCHIVED,DRAFT,PENDING,PROCESSING,COMPLETED,ON_HOLD,REFUNDED,CANCELLED,DELETED,PENDING_PAYMENT,SUSPENDED
 * @property string $status_url
 * @property string $payment_status UNPAID,PAID,PARTIALLY_PAID,REFUNDED,PARTIALLY_REFUNDED,AUTHORIZED,FAILED,PENDING
 * @property string|null $payment_log
 * @property string|null $payment_summary
 * @property string $fulfill_status 'unfulfilled','fulfilled','partial_fulfilled','cancelled','processing','on_hold','invalid','no_ship','reviewing','designing','on_delivery'
 * @property string $sen_fulfill_status YES,NO,REVIEW,PENDING
 * @property string $fraud_status 'trusted','flagged','fraud'
 * @property int $stats_status 1: stats updated
 * @property string $type REGULAR,FULFILLMENT,SERVICE
 * @property string|null $address
 * @property string|null $address_2
 * @property string|null $house_number
 * @property string|null $mailbox_number
 * @property string|null $city
 * @property string|null $state
 * @property string|null $postcode
 * @property string $address_verified
 * @property string|null $order_note
 * @property string|null $admin_note
 * @property string|null $ad_source
 * @property string|null $ad_campaign
 * @property string|null $ad_medium
 * @property string|null $ad_id
 * @property string $ip_address
 * @property string|null $ip_location
 * @property string|null $country
 * @property string|null $device
 * @property string|null $device_detail
 * @property int|null $export_id
 * @property \Illuminate\Support\Carbon|null $exported_at
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $fulfilled_at
 * @property string|null $delivered_at
 * @property string|null $received_at
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string $currency_code
 * @property float $currency_rate
 * @property string|null $device_id
 * @property string|null $session_id
 * @property string|null $visit_info
 * @property int $personalized 1:yes,0:no (yes if order has personalized product)
 * @property string|null $fulfill_log
 * @property int $shard_id
 * @property int $support_status OrderSupportStatusEnum
 * @property int|null $assignee
 * @property string $review_request_status
 * @property string|null $flag_log
 * @property string $tm_status
 * @property string|null $datestamp
 * @property string|null $external_order_id
 * @property int $skip_validate_address Skip validate address when create order
 * @property int|null $sync_status Sync order info to singlestore
 * @property string|null $sync_at
 * @property \Illuminate\Support\Carbon|null $region_synced_at
 * @property string|null $shipping_label
 * @property int $temp_status
 * @property int $is_corner_placement
 * @property int $cross_shipping 0: not cross shipping, 1: cross shipping
 * @property int|null $last4_card_number
 * @property float|null $total_fulfill_base_cost Tổng giá sản phẩm của supplier
 * @property float|null $total_fulfill_shipping_cost Tổng phí ship của supplier
 * @property float|null $total_fulfill_profit Tổng lợi nhuận của sen
 * @property string|null $estimate_delivery_date
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AbandonedLog> $abandoned_logs
 * @property-read int|null $abandoned_logs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ApiLog> $api_logs
 * @property-read int|null $api_logs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderAssignSupplierHistory> $assign_supplier_histories
 * @property-read int|null $assign_supplier_histories_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Campaign> $campaigns
 * @property-read int|null $campaigns_count
 * @property-read \App\Models\SystemLocation|null $country_info
 * @property-read \App\Models\Currency|null $currency
 * @property-read \App\Models\Customer|null $customer
 * @property-read \App\Models\CustomerAddress|null $customer_address
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $designs
 * @property-read int|null $designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $files
 * @property-read int|null $files_count
 * @property-read \App\Models\OrderProduct|null $first_order_product
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProduct> $fulfilled_products
 * @property-read int|null $fulfilled_products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Fulfillment> $fulfillments
 * @property-read int|null $fulfillments_count
 * @property-read array|string|null $formatted_phone_number
 * @property-read mixed $line_items_pb
 * @property-read (string|array)[] $order_info_pb
 * @property-read float $platform_fee
 * @property-read float $sen_profit
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderIssue> $issues
 * @property-read int|null $issues_count
 * @property-read \App\Models\OrderHistory|null $latest_auto_resume_order_history
 * @property-read \App\Models\OrderHistory|null $latest_manual_resume_order_history
 * @property-read \App\Models\OrderHistory|null $latest_order_history
 * @property-read \App\Models\SystemLocation|null $location
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $mockups
 * @property-read int|null $mockups_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderDispute> $order_disputes
 * @property-read int|null $order_disputes_count
 * @property-read \App\Models\OrderExport|null $order_export
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderHistory> $order_history
 * @property-read int|null $order_history_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProduct> $order_products
 * @property-read int|null $order_products_count
 * @property-read \App\Models\PaymentGateway|null $payment_gateway
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductReview> $productReviews
 * @property-read int|null $product_reviews_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductPoint> $product_points
 * @property-read int|null $product_points_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProduct> $products
 * @property-read int|null $products_count
 * @property-read \App\Models\PromotionRule|null $promotion
 * @property-read \App\Models\OrderCancelRequest|null $request_cancel
 * @property-read \App\Models\User|null $seller
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SellerBilling> $sellerBilling
 * @property-read int|null $seller_billing_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $sellers
 * @property-read int|null $sellers_count
 * @property-read \App\Models\Staff|null $staff
 * @property-read \App\Models\Store|null $store
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Supplier> $suppliers
 * @property-read int|null $suppliers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product> $templates
 * @property-read int|null $templates_count
 * @property-read TiktokShopInfo|null $tiktok_shop
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TrackingStatus> $tracking_statuses
 * @property-read int|null $tracking_statuses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProduct> $unfulfilled_products
 * @property-read int|null $unfulfilled_products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PgWebhookLogs> $wh_payment_logs
 * @property-read int|null $wh_payment_logs_count
 * @method static Builder|Order addExcludeAnalytic(array $arrExclude)
 * @method static Builder|Order addFilterAnalytic(array $arrayFilter, array $dateRanges, $sellerId = null, $arrUnsetFilter = [], $columnDate = null)
 * @method static Builder|Order addFilterAnalyticBeforeDateRange(array $arrayFilter, array $dateRanges, $sellerId = null, $arrUnsetFilter = [])
 * @method static Builder|Order analytic($columns = null)
 * @method static Builder|Order analyticReferral($dateRange, $startDate, $endDate, $refId = null, $columnGroupBy = '')
 * @method static Builder|Order analyticWithFilterDateRangeBefore($columns = null, $dateRange = null)
 * @method Builder|Order checkMarkWarningOrder()
 * @method static Builder|Order countActiveSellers()
 * @method static Builder|Order customerCancelOrderConditions()
 * @method static Builder|Order excludeTest()
 * @method static \Database\Factories\OrderFactory factory($count = null, $state = [])
 * @method static Builder|Order filter(array $params, $filter)
 * @method Builder|Order filterDateBeforeRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method Builder|Order filterDateBeforeRangeWithSubmonth($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false, $submonth = 0)
 * @method Builder|Order filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false, $submonths = null, $isTimeZone = false)
 * @method Builder|Order filterDateRangeWithSameDiffInPass($dateRange, $startDate = null, $endDate = null, $column = null, $isOrderBy = false)
 * @method Builder|Order filterFulfill($status = null, $fulfillStatus = null, $supportStatus = -1, $isListing = false, $isTrackingLate = false, $isShippingLate = false, $expressShippingLate = false)
 * @method Builder|Order filterFulfillProduct($dateRange, $startDate = null, $endDate = null, $status = null, $fulfillStatus = null, $shippingMethod = null, $supportStatus = -1, $assignee = -2, $dateRangeFilterColumn = 'order.fulfilled_at', $isListing = false, $isTrackingLate = false, $isShippingLate = false, $expressShippingLate = false)
 * @method Builder|Order filterQueryDateInPreviousRangeToCurrentEndDate($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false, $dayRange = null)
 * @method Builder|Order filterQueryDateInRangeWithRound($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false, $dayRange = null, $subMonth = null)
 * @method Builder|Order filterReceivedDateRange($dateRange)
 * @method Builder|Order filterSupport($supportStatus, $assignee)
 * @method Builder|Order filterUnfulfilledSupplierOrder()
 * @method static Builder|Order findByAccessToken(string $accessToken, string $withTrashed = false)
 * @method Builder|Order getChangesDetail()
 * @method Builder|Order getFulfillItems()
 * @method Builder|Order getLastPaidOrder($startTime, $endTime)
 * @method Builder|Order getSenPoints()
 * @method Builder|Order getSoldItems()
 * @method Builder|Order invalidAddressOrderConditions()
 * @method Builder|Order isAbandoned()
 * @method Builder|Order isValidPaidOrder()
 * @method Builder|Order listingExport($storeDomain, $sortBy, $sortDirection)
 * @method static Builder|Order newModelQuery()
 * @method static Builder|Order newQuery()
 * @method static Builder|Order onlyTrashed()
 * @method Builder|Order paymentCompleted(string $totalPaid, string $paymentObjectId = '', bool $isPayPalECheck = false, ?string $currencyCode = null, ?float $currencyRate = null, string $gatewayId = null)
 * @method Builder|Order paymentFailed(?string $failedLog, ?string $paymentObjectId = '', ?int $gatewayId = null)
 * @method Builder|Order updateOrderCondition(array $whereCondition = [])
 * @method Builder|Order paymentPendingReview(?string $paymentObjectId = '')
 * @method static Builder|Order platformOrder()
 * @method static Builder|Order query()
 * @method static Builder|Order selectForHistory($addSelect = '')
 * @method static Builder|Order selectedFieldToFulfill()
 * @method static Builder|Order shippingLate($shippingLate)
 * @method static Builder|Order whereAccessToken($value)
 * @method static Builder|Order whereAdCampaign($value)
 * @method static Builder|Order whereAdId($value)
 * @method static Builder|Order whereAdMedium($value)
 * @method static Builder|Order whereAdSource($value)
 * @method static Builder|Order whereAddress($value)
 * @method static Builder|Order whereAddress2($value)
 * @method static Builder|Order whereAddressVerified($value)
 * @method static Builder|Order whereAdminNote($value)
 * @method static Builder|Order whereAssignee($value)
 * @method static Builder|Order whereBillingAddress($value)
 * @method static Builder|Order whereCity($value)
 * @method static Builder|Order whereCompanyId($value)
 * @method static Builder|Order whereCountry($value)
 * @method static Builder|Order whereCreatedAt($value)
 * @method static Builder|Order whereCrossShipping($value)
 * @method static Builder|Order whereCurrencyCode($value)
 * @method static Builder|Order whereCurrencyRate($value)
 * @method static Builder|Order whereCustomerEmail($value)
 * @method static Builder|Order whereCustomerId($value)
 * @method static Builder|Order whereCustomerName($value)
 * @method static Builder|Order whereCustomerPhone($value)
 * @method static Builder|Order whereDatestamp($value)
 * @method static Builder|Order whereDeletedAt($value)
 * @method static Builder|Order whereDeliveredAt($value)
 * @method static Builder|Order whereDevice($value)
 * @method static Builder|Order whereDeviceDetail($value)
 * @method static Builder|Order whereDeviceId($value)
 * @method static Builder|Order whereDiscountCode($value)
 * @method static Builder|Order whereEstimateDeliveryDate($value)
 * @method static Builder|Order whereExportId($value)
 * @method static Builder|Order whereExportedAt($value)
 * @method static Builder|Order whereExternalOrderId($value)
 * @method static Builder|Order whereFlagLog($value)
 * @method static Builder|Order whereFraudStatus($value)
 * @method static Builder|Order whereFulfillFeePaid($value)
 * @method static Builder|Order whereFulfillLog($value)
 * @method static Builder|Order whereFulfillStatus($value)
 * @method static Builder|Order whereFulfilledAt($value)
 * @method static Builder|Order whereHouseNumber($value)
 * @method static Builder|Order whereId($value)
 * @method static Builder|Order whereInsuranceFee($value)
 * @method static Builder|Order whereIpAddress($value)
 * @method static Builder|Order whereIpLocation($value)
 * @method static Builder|Order whereLast4CardNumber($value)
 * @method static Builder|Order whereMailboxNumber($value)
 * @method static Builder|Order whereName($value)
 * @method static Builder|Order whereOrderNote($value)
 * @method static Builder|Order whereOrderNumber($value)
 * @method static Builder|Order whereOrderNumber2($value)
 * @method static Builder|Order wherePaidAt($value)
 * @method static Builder|Order wherePaymentDiscount($value)
 * @method static Builder|Order wherePaymentFee($value)
 * @method static Builder|Order wherePaymentGatewayId($value)
 * @method static Builder|Order wherePaymentLog($value)
 * @method static Builder|Order wherePaymentMethod($value)
 * @method static Builder|Order wherePaymentStatus($value)
 * @method static Builder|Order wherePersonalized($value)
 * @method static Builder|Order wherePostcode($value)
 * @method static Builder|Order whereProcessingFee($value)
 * @method static Builder|Order whereProcessingFeePaid($value)
 * @method static Builder|Order wherePromotionRule($value)
 * @method static Builder|Order wherePromotionRuleId($value)
 * @method static Builder|Order whereReceivedAt($value)
 * @method static Builder|Order whereRefId($value)
 * @method static Builder|Order whereRegion($value)
 * @method static Builder|Order whereRegionSyncedAt($value)
 * @method static Builder|Order whereRemarketingStatus($value)
 * @method static Builder|Order whereReviewRequestStatus($value)
 * @method static Builder|Order whereSellerId($value)
 * @method static Builder|Order whereSenFulfillStatus($value)
 * @method static Builder|Order whereSessionId($value)
 * @method static Builder|Order whereShardId($value)
 * @method static Builder|Order whereShippingAddress($value)
 * @method static Builder|Order whereShippingLabel($value)
 * @method static Builder|Order whereShippingMethod($value)
 * @method static Builder|Order whereSkipValidateAddress($value)
 * @method static Builder|Order whereState($value)
 * @method static Builder|Order whereStatsStatus($value)
 * @method static Builder|Order whereStatus($value)
 * @method static Builder|Order whereStatusUrl($value)
 * @method static Builder|Order whereStoreDomain($value)
 * @method static Builder|Order whereStoreId($value)
 * @method static Builder|Order whereStoreName($value)
 * @method static Builder|Order whereSupportStatus($value)
 * @method static Builder|Order whereSyncAt($value)
 * @method static Builder|Order whereSyncStatus($value)
 * @method static Builder|Order whereTempStatus($value)
 * @method static Builder|Order whereTipAmount($value)
 * @method static Builder|Order whereTmStatus($value)
 * @method static Builder|Order whereTotalAmount($value)
 * @method static Builder|Order whereTotalArtistProfit($value)
 * @method static Builder|Order whereTotalDiscount($value)
 * @method static Builder|Order whereTotalFulfillBaseCost($value)
 * @method static Builder|Order whereTotalFulfillFee($value)
 * @method static Builder|Order whereTotalFulfillProfit($value)
 * @method static Builder|Order whereTotalFulfillShippingCost($value)
 * @method static Builder|Order whereTotalPaid($value)
 * @method static Builder|Order whereTotalProductAmount($value)
 * @method static Builder|Order whereTotalProductBaseCost($value)
 * @method static Builder|Order whereTotalProductCost($value)
 * @method static Builder|Order whereTotalProfit($value)
 * @method static Builder|Order whereTotalQuantity($value)
 * @method static Builder|Order whereTotalRefund($value)
 * @method static Builder|Order whereTotalSellerProfit($value)
 * @method static Builder|Order whereTotalSenPoints($value)
 * @method static Builder|Order whereTotalShippingAmount($value)
 * @method static Builder|Order whereTotalShippingCost($value)
 * @method static Builder|Order whereTotalTaxAmount($value)
 * @method static Builder|Order whereTransactionId($value)
 * @method static Builder|Order whereType($value)
 * @method static Builder|Order whereUpdatedAt($value)
 * @method static Builder|Order whereVisitInfo($value)
 * @method static Builder|Order withTrashed()
 * @method static Builder|Model withWhereHas(string $relation, \Closure $condition)
 * @method static Builder|Order withoutTrashed()
 */
class Order extends Model
{
    use HasFactory;
    use Compoships;
    use SoftDeletes;
    use ScopeFilterAnalyticTrait;
    use ScopeFilterDateBeforeRangeTrait;
    use ScopeFilterDateBeforeRangeWithSubmonthTrait;
    use ScopeFilterDateInRangeWithRoundTrait;
    use ScopeFilterDateRangeWithSameDiffInPassTrait;
    use ScopeFilterQueryDateInPreviousRangeToCurrentEndDateTrait;
    use Filterable;
    use HasRelationShipsCustom {
        HasRelationShipsCustom::hasOne insteadof Compoships;
        HasRelationShipsCustom::hasMany insteadof Compoships;
        HasRelationShipsCustom::belongsTo insteadof Compoships;
        HasRelationShipsCustom::newHasOne insteadof Compoships;
        HasRelationShipsCustom::newHasMany insteadof Compoships;
        HasRelationShipsCustom::newBelongsTo insteadof Compoships;
    }
    use HasRegionConnection;
    use UpdatingOrderAttributesTrait;

    public const PLATFORM_ORDER_TYPES = [
        OrderTypeEnum::SERVICE,
        OrderTypeEnum::REGULAR,
        OrderTypeEnum::CUSTOM
    ];

    public const CHECKOUT_ASSIGN_SUPPLIER = 'checkout_assign_supplier';

    public const NOT_CHECKOUT_ASSIGN_SUPPLIER = 'not_checkout_assign_supplier';
    public const ORDER_ADDRESS_VERIFIED_STATUS_ALL = 'all';

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'order';

    protected $fillable = [
        'access_token',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'address',
        'address_2',
        'house_number',
        'mailbox_number',
        'city',
        'state',
        'postcode',
        'country',
        'store_id',
        'store_name',
        'store_domain',
        'seller_id',
        'ref_id',
        'tip_amount',
        'discount_code',
        'shipping_method',
        'payment_method',
        'payment_status',
        'payment_log',
        'payment_summary',
        'ip_address',
        'ad_source',
        'ad_campaign',
        'ad_medium',
        'ad_id',
        'device',
        'device_detail',
        'currency_code',
        'currency_rate',
        'fulfilled_at',
        'device_id',
        'session_id',
        'order_number',
        'order_number_2',
        'region',
        'type',
        'status',
        'fulfill_status',
        'fulfill_log',
        'delivered_at',
        'support_status',
        'review_request_status',
        'order_note',
        'received_at',
        'fraud_status',
        'flag_log',
        'ip_location',
        'tm_status',
        'insurance_fee',
        'external_order_id',
        'external_fulfillment_id',
        'skip_validate_address',
        'shipping_label',
        'temp_status',
        'cross_shipping',
        'total_fulfill_base_cost',
        'total_fulfill_shipping_cost',
        'total_fulfill_profit',
        'last4_card_number',
        'estimate_delivery_date',
        'region_synced_at',
        'visit_info',
        'ioss_number',
        'is_corner_placement',
        'approved_at',
    ];

    //Apply delivery insurance for only $0.98
    public const ORDER_INSURANCE_FEE = 0.98;
    public const ORDER_WARNING_AMOUNT = 500;
    public const ORDER_WARNING_QUANTITY = 15;
    public const SYNC_DATA_STATS_ENABLED = 0;
    public const SYNC_DATA_STATS_DISABLED = 1;
    public const SUPPLIER_ORDER_STATUS = [
        OrderProductFulfillStatus::FULFILLED,
        OrderProductFulfillStatus::PROCESSING,
        OrderProductFulfillStatus::ON_DELIVERY,
        OrderProductFulfillStatus::REJECTED,
        OrderProductFulfillStatus::UNFULFILLED,
        OrderProductFulfillStatus::CANCELLED
    ];
    public const FIELD_EXPORT = [
        'order.id',
        'order.store_domain',
        'order.order_number',
        'order.customer_email',
        'order.created_at',
        'order.total_amount',
        'order.customer_name',
        'order.address',
        'order.address_2',
        'order.city',
        'order.state',
        'order.postcode',
        'order.country',
        'order.customer_phone',
        'order.order_note',
    ];
    public const FIELD_FULFILL = [
        'id',
        'order_number',
        'customer_name',
        'customer_email',
        'customer_phone',
        'payment_method',
        'shipping_method',
        'address',
        'address_2',
        'mailbox_number',
        'house_number',
        'state',
        'city',
        'postcode',
        'order_note',
        'admin_note',
        'country',
        'paid_at',
        'currency_code',
        'type',
        'seller_id',
        'paid_at',
        'status',
        'fulfill_status',
        'shipping_label',
        'ioss_number',
    ];
    protected $casts = [
        'paid_at' => 'datetime',
        'region_synced_at' => 'datetime',
        'fulfilled_at' => 'datetime',
        'exported_at' => 'datetime',
    ];
    public $incrementing = false;

    private const FBA_COST_PER_UNIT = 0.5;
    private const FBA_FEE_PER_ORDER = 0.03;
    private const DEFAULT_ORDER_SHIPPING_LATE_DATES = 29;

    private static $orderRegion = null;
    public static function setRegionCheckout($region)
    {
        self::$orderRegion = $region;
        return new static;
    }

    protected static function booted(): void
    {
        static::creating(static function ($model) {
            $orderRegionPrefix = empty(self::$orderRegion) ? null : RegionOrderService::getRegionId(self::$orderRegion);
            $model->id = generateShardId($model, 1000, false, $orderRegionPrefix);
            $model->shard_id = empty($orderRegionPrefix) ? config('senprints.shard_id') : config('region.region_order_shard_id') . $orderRegionPrefix;
            self::$orderRegion = null;
        });
    }

    public function products(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'order_id', 'id');
    }

    public function unfulfilled_products(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'order_id', 'id')
            ->where('order_product.fulfill_status', '=', OrderProductFulfillStatus::UNFULFILLED)
            ->orWhere('order_product.fulfill_status', '=', OrderProductFulfillStatus::PARTIALLY_FULFILLED);
    }

    public function fulfilled_products(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'order_id', 'id')
            ->groupBy('order_product.tracking_code', 'order_product.fulfill_status')
            ->having('order_product.fulfill_status', '=', OrderProductFulfillStatus::FULFILLED);
    }

    public function fulfillments(): HasMany
    {
        return $this->hasMany(Fulfillment::class, 'order_id', 'id')
            ->orderBy('created_at', 'desc');
    }

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(PromotionRule::class, 'promotion_rule_id');
    }

    public function api_logs(): hasMany
    {
        return $this->hasMany(ApiLog::class, 'order_id', 'id')
            ->where('reference_type', AccessExternalApiType::SUPPLIER);
    }

    public function product_points(): HasManyThrough
    {
        return $this->hasManyThrough(
            ProductPoint::class,
            OrderProduct::class,
            'order_id',
            'product_id',
            'id',
            'template_id'
        );
    }

    /**
     * @param $isSeller
     * @param $isCustomer
     * @return array
     * @throws \Throwable
     */
    public function getFulfillments($isSeller = false, $isCustomer = false): array
    {
        $processingStatus = [
            OrderProductFulfillStatus::REJECTED,
            OrderProductFulfillStatus::EXCEPTION
        ];
        $unfulfilledStatus = [
            OrderProductFulfillStatus::INVALID,
            OrderProductFulfillStatus::DESIGNING,
            OrderProductFulfillStatus::REVIEWING,
            OrderProductFulfillStatus::ON_HOLD,
        ];

        $products = $this->products;
        $fulfillments = [];
        if ($products->isEmpty()) {
            return $fulfillments;
        }
        foreach ($products as $product) {
            $key = $product->fulfill_status;
            if (($isSeller || $isCustomer) && in_array($key, $processingStatus, true)) {
                $key = OrderProductFulfillStatus::PROCESSING;
            }
            if ($isCustomer && in_array($key, $unfulfilledStatus, true)) {
                $key = OrderProductFulfillStatus::UNFULFILLED;
            }
            if (in_array($key, [OrderProductFulfillStatus::FULFILLED, OrderProductFulfillStatus::ON_DELIVERY], true)) {
                $key .= '_' . $product->tracking_code;
            }
            if (!isset($fulfillments[$key])) {
                $fulfillments[$key] = [];
            }
            if ($product->tracking_code) {
                $product->tracking_info = [
                    'tracking_code' => explode(',', $product->tracking_code),
                    'shipping_carrier' => explode(',', $product->shipping_carrier),
                    'tracking_url' => explode(',', $product->tracking_url),
                ];
            }
            // check the product has only color white, if has color white, remove color from options
            if ($isCustomer && $product->product && Str::isJson($product->product->options) && Str::isJson($product->options)) {
                $productOptions = json_decode($product->product->options, true, 512, JSON_THROW_ON_ERROR);
                $options = json_decode($product->options, true, 512, JSON_THROW_ON_ERROR);
                if (isset($options['color'], $productOptions['color']) && count($productOptions['color']) === 1 && data_get($productOptions, 'color.0') === 'white') {
                    unset($options['color']);
                    $product->options = json_encode($options, JSON_THROW_ON_ERROR);
                }
                unset($product->product, $product->seller_id);
            }
            $fulfillments[$key][] = $product;
        }
        return $fulfillments;
    }

    public function tracking_statuses(): HasMany
    {
        return $this->hasMany(TrackingStatus::class, 'order_number', 'order_number');
    }

    public function order_products(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'order_id');
    }

    public function first_order_product()
    {
        return $this->hasOne(OrderProduct::class, 'order_id');
    }

    public function order_export(): BelongsTo
    {
        return $this->belongsTo(OrderExport::class, 'export_id');
    }

    public function order_disputes(): HasMany
    {
        return $this->hasMany(OrderDispute::class, 'order_id', 'id');
    }

    public function tiktok_shop(): BelongsTo
    {
        return $this->belongsTo(TiktokShopInfo::class, 'store_id', 'store_id');
    }

    public function getShippingAddressAttribute()
    {
        return collect([
            'name' => $this->customer_name,
            'phone' => $this->customer_phone,
            'address' => $this->address,
            'address_2' => $this->address_2,
            'city' => $this->city,
            'state' => $this->state,
            'postcode' => $this->postcode,
            'country' => $this->country,
        ]);
    }

    public function getBillingAddressAttribute()
    {
        return collect([
            'name' => $this->customer_name,
            'phone' => $this->customer_phone,
            'address' => $this->address,
            'address_2' => $this->address_2,
            'city' => $this->city,
            'state' => $this->state,
            'postcode' => $this->postcode,
            'country' => $this->country,
        ]);
    }

    public function order_history(): HasMany
    {
        return $this->hasMany(OrderHistory::class, 'order_id')->orderByDesc('id');
    }

    public function wh_payment_logs(): HasMany
    {
        return $this->hasMany(PgWebhookLogs::class, 'order_id')->whereNotNull('order_id')->orderByDesc('created_at');
    }

    public function latest_order_history()
    {
        return $this->hasOne(OrderHistory::class)->orderByDesc('id');
    }

    public function latest_manual_resume_order_history()
    {
        return $this->hasOne(OrderHistory::class)->whereIn('action', [OrderHistoryActionEnum::RESUME_FULFILL, OrderHistoryActionEnum::FULFILLMENT_CREATED])->whereNotNull('updated_by')->orderByDesc('id');
    }

    public function latest_auto_resume_order_history()
    {
        return $this->hasOne(OrderHistory::class)->where('action', OrderHistoryActionEnum::FULFILLMENT_CREATED)->whereNull('updated_by')->orderByDesc('id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'assignee');
    }

    public function customer_address(): BelongsTo
    {
        return $this->belongsTo(CustomerAddress::class, 'customer_id', 'user_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class, 'order_id');
    }

    public function mockups(): HasMany
    {
        return $this->hasMany(File::class, 'order_id')->where('file.type', FileTypeEnum::MOCKUP);
    }

    public function designs(): HasMany
    {
        return $this->hasMany(File::class, 'order_id')->where('file.type', FileTypeEnum::DESIGN);
    }

    public function country_info(): HasOne
    {
        return $this->hasOne(SystemLocation::class, 'code', 'country');
    }

    public function payment_gateway(): HasOne
    {
        return $this->hasOne(PaymentGateway::class, 'id', 'payment_gateway_id');
    }

    public function request_cancel(): HasOne
    {
        return $this->hasOne(OrderCancelRequest::class, 'order_id', 'id');
    }

    public function sellerBilling(): HasMany
    {
        return $this->hasMany(SellerBilling::class, 'order_id', 'id');
    }


    public function isOrderCompleted(): bool
    {
        $isCompleted = true;
        $fulfillments = $this->fulfillments->toArray();
        $orderProducts = $this->products->toArray();

        array_map(static function ($fulfillment) use (&$isCompleted) {
            if (
                $fulfillment['status'] !== FulfillmentStatusEnum::FULFILLED
                && $fulfillment['status'] !== FulfillmentStatusEnum::CANCELLED
            ) {
                $isCompleted = false;
            }
        }, $fulfillments);

        array_map(static function ($product) use (&$isCompleted) {
            if (
                $product['fulfill_status'] !== OrderProductFulfillStatus::FULFILLED
                && $product['fulfill_status'] !== OrderProductFulfillStatus::CANCELLED
            ) {
                $isCompleted = false;
            }
        }, $orderProducts);

        return $isCompleted;
    }

    /**
     * @return Builder
     */
    public static function getOrdersByStatStatus(): Builder
    {
        return self::query()
            ->select([
                'id',
                'seller_id',
                'store_id',
                'country',
                'device',
                'device_detail',
                'stats_status',
                'ad_source',
                'ad_campaign',
                'ad_medium',
                'paid_at',
            ])
            ->with(['products' => static function ($query) {
                $query->select([
                    'id',
                    'order_id',
                    'product_id',
                    'quantity',
                    'campaign_id',
                    'template_id',
                    'total_amount',
                    'seller_profit'
                ]);
            }])
            ->whereHas('products')
            ->where('stats_status', self::SYNC_DATA_STATS_ENABLED);
    }

    /**
     * @param array $orderIds
     * @param int $syncStatus
     * @return bool|int
     */
    public static function updateSyncStatusByIds(array $orderIds = [], int $syncStatus = 0)
    {
        return self::query()
            ->whereIn('id', $orderIds)
            ->update(['stats_status' => $syncStatus]);
    }

    /**
     * @param Collection $rules
     * @param array $locationCodes
     * @param OrderProduct $orderProduct
     *
     * @return ShippingRule|null
     */
    private function matchLocation($rules, $locationCodes, $product): ?object
    {
        foreach ($locationCodes as $locationCode) {
            // check location code
            foreach ($rules as $rule) {
                // match product or template
                if ($rule->product_id === $product->id || $rule->product_id === $product->template_id) {
                    if (
                        $rule->location_code === $locationCode
                        && (
                            // check supplier if exist
                            is_null($rule->supplier_id) || $product->supplier_id === $rule->supplier_id
                        )
                        && (
                            // check variant key if exist
                            is_null($rule->variant_key) || $rule->variant_key === getVariantKey($product->options, $product->full_printed)
                        )
                    ) {
                        return $rule;
                    }
                }
            }
        }

        return null;
    }

    public function updatePromotionUsed(): void
    {
        // todo @long: do decrement when cancel order
        if ($this->total_discount > 0) {
            $this->promotion()->increment('used_count');
        }
    }

    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(
            Campaign::class,
            OrderProduct::class,
            'order_id',
            'campaign_id'
        );
    }

    public function scopeListingExport($query, $storeDomain, $sortBy, $sortDirection)
    {
        // todo @long: transform raw to eloquent
        return $query
            ->select('store_domain')
            ->addSelect(DB::raw("COUNT(IF(export_id is null, NULL, 1)) as total_order"))
            ->addSelect(
                DB::raw("COUNT(IF(fulfill_status = '"
                    . FulfillmentStatusEnum::FULFILLED
                    . "' or fulfill_status = '"
                    . FulfillmentStatusEnum::PARTIAL_FULFILLED
                    . "', 1, NULL)) as total_has_tracking")
            )
            ->addSelect(DB::raw("
                sum(
                    (
                        select
                          count(*)
                        from
                          `tracking_status`
                        where
                          `order`.`order_number` = `tracking_status`.`order_number`
                          and `exported_at` is not null
                    )
                ) as `total_exported`
            "))
            ->addSelect(DB::raw("
                sum(
                    (
                        select
                          count(*)
                        from
                          `tracking_status`
                        where
                          `order`.`order_number` = `tracking_status`.`order_number`
                          and `exported_at` is null
                    )
                ) as `total_unexported`
            "))
            ->when(!is_null($storeDomain), function ($query) use ($storeDomain) {
                $query->where('store_domain', 'like', '%' . $storeDomain . '%');
            })
            ->groupBy('store_domain')
            ->when(!is_null($sortBy) && !is_null($sortDirection), function ($query) use ($sortBy, $sortDirection) {
                $query->orderBy($sortBy, $sortDirection);
            });
    }

    public function getStatusUrlAttribute(): string
    {
        return 'https://' . $this->store_domain . '/order/status/' . $this->access_token;
    }

    public function getCheckoutUrl(): string
    {
        $checkoutDomain = ($this->store_domain) ?: getMarketPlaceDomain();
        return 'https://' . $checkoutDomain . '/checkout/' . $this->access_token;
    }

    public const FILTER_COLUMN_DATE = 'order.paid_at';

    public function scopeFilterFulfillProduct(
        $query,
        $dateRange,
        $startDate = null,
        $endDate = null,
        $status = null,
        $fulfillStatus = null,
        $shippingMethod = null,
        $supportStatus = OrderSupportStatusEnum::ALL,
        $assignee = OrderAssigneeEnum::ALL,
        $dateRangeFilterColumn = 'order.fulfilled_at',
        $isListing = false,
        $isTrackingLate = false,
        $isShippingLate = false,
        $expressShippingLate = false,
    )
    {
        return $query
            ->filterDateRange($dateRange, $startDate, $endDate, $dateRangeFilterColumn)
            ->filterSupport($supportStatus, $assignee)
            ->when($shippingMethod, fn($q) => $q->where('order.shipping_method', $shippingMethod))
            ->filterFulfill($status, $fulfillStatus, $supportStatus, $isListing, $isTrackingLate, $isShippingLate, $expressShippingLate);
    }

    public function scopeSelectedFieldToFulfill($query)
    {
        return $query->select([
            'order.id as id',
            'order.store_domain',
            'order.order_number',
            'order.customer_email',
            'order.created_at',
            'order.total_amount',
            'order.customer_name',
            'order.address',
            'order.address_2',
            'order.city',
            'order.state',
            'order.postcode',
            'order.country',
            'order.customer_phone',
            'order.order_note',
        ]);
    }

    /** @noinspection TypeUnsafeComparisonInspection */
    public function scopeFilterSupport($query, $supportStatus, $assignee)
    {
        $query->when(
            $supportStatus != OrderSupportStatusEnum::ALL,
            function ($q) use ($supportStatus) {
                if ($supportStatus === OrderSupportStatusEnum::NEED_SUPPORT) {
                    return $q->where('order.support_status', '>', OrderSupportStatusEnum::NORMAL)
                        ->whereNotIn('order.status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::COMPLETED])
                        ->whereNotIn('order.fulfill_status', [OrderFulfillStatus::CANCELLED, OrderFulfillStatus::FULFILLED, OrderFulfillStatus::ON_DELIVERY])
                        ->whereNotNull('order.admin_note')
                        ->where(function ($q) {
                            $q->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
                            $q->orWhere(function ($q) {
                                $q->whereIn('order.type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                                    ->where('order.payment_status', OrderPaymentStatus::PAID)
                                    ->where('order.total_paid', '>', 0)
                                    ->whereNotNull('order.paid_at');
                            });
                        });
                }

                return $q->where('order.support_status', $supportStatus);
            }
        );

        $query->when(
            $assignee != OrderAssigneeEnum::ALL,
            fn($q) => match ($assignee) {
                OrderAssigneeEnum::ME => $q->where('order.assignee', currentUser()->getUserId()),
                OrderAssigneeEnum::UNASSIGNED => $q->whereNull('order.assignee'),
                default => $q->where('order.assignee', $assignee),
            }
        );

        return $query;
    }

    public function scopeFilterFulfill($query, $status = null, $fulfillStatus = null, $supportStatus = OrderSupportStatusEnum::ALL, $isListing = false, $isTrackingLate = false, $isShippingLate = false, $expressShippingLate = false)
    {
        if ($supportStatus !== OrderSupportStatusEnum::NEED_SUPPORT && !$isTrackingLate && !$expressShippingLate) {
            // default
            if (empty($status)) {
                $status = [
                    OrderStatus::PROCESSING,
                ];
            }
            if (empty($fulfillStatus)) {
                $fulfillStatus = [
                    OrderFulfillStatus::PROCESSING,
                    OrderFulfillStatus::UNFULFILLED,
                    OrderFulfillStatus::ON_HOLD,
                    OrderFulfillStatus::INVALID,
                    OrderFulfillStatus::REVIEWING,
                    OrderFulfillStatus::DESIGNING,
                ];
            }
        } else {
            // get all
            $status = null;
            $fulfillStatus = null;
        }

        if ($isShippingLate) {
            $fulfillStatus = null;
        }

        if ($isListing) {
            $arrSenFulfillStatus = [
                OrderSenFulfillStatus::YES,
                OrderSenFulfillStatus::PENDING,
            ];
        } else {
            $arrSenFulfillStatus = [
                OrderSenFulfillStatus::YES,
            ];
        }

        return $query->where(
            function ($query) use ($status, $fulfillStatus, $arrSenFulfillStatus) {
                if (!empty($status) && $status !== ['1']) {
                    $query->whereIn('order.status', $status);
                }
                if (!empty($fulfillStatus) && $fulfillStatus !== ['1']) {
                    $query->whereIn('order.fulfill_status', $fulfillStatus);
                }

                $query->whereIn('order.sen_fulfill_status', $arrSenFulfillStatus);
            }
        );
    }

    public function getTimeframe(): array
    {
        $validateTime = $this->fulfilled_at ?? now()->addHours(24);
        $printTime = $validateTime->copy()->addDays($this->getPrintingTime() - 1);

        if ($this->status !== OrderStatus::COMPLETED && $printTime->lt(Carbon::now())) {
            $printTime = Carbon::now()->addDays(2);
        }

        $packageTime = $printTime->copy()->addDay();
        $shipTime = $packageTime->copy()->addDay();

        if ($this->delivered_at) {
            $shipTime = Carbon::createFromTimeString($this->delivered_at);
            $packageTime = $shipTime->copy()->subHours(12);
            $printTime = $packageTime->copy()->subDay();
        }

        $delivered = $this->received_at ? Carbon::createFromTimeString($this->received_at) : null;

        return [
            'received' => $this->paid_at,
            'validate' => $validateTime,
            'print' => $printTime,
            'package' => $packageTime,
            'ship' => $shipTime,
            'delivered' => $delivered,
        ];
    }

    public function scopeIsAbandoned($query)
    {
        $limitFromThisDate = Carbon::parse('2021-12-27 21:18:00'); #TODO: @james send order to abandoned after this date

        if (!app()->environment(EnvironmentEnum::PRODUCTION)) {
            $dateForIdentifyAbandoned = now()->subMinute(); // for testing
        } else {
            $dateForIdentifyAbandoned = now()->subHour();
        }

        return $query->where(
            function ($query) use ($dateForIdentifyAbandoned, $limitFromThisDate) {
                return $query->where('status', OrderStatus::PENDING)
                    ->where('payment_status', OrderPaymentStatus::UNPAID)
                    ->where('updated_at', '<', $dateForIdentifyAbandoned) // 5 minutes before
                    ->where('updated_at', '>', now()->subDays(30)) // 30 days before
                    ->where('updated_at', '>', $limitFromThisDate);
            }
        );
    }

    public function abandoned_logs()
    {
        return $this->hasMany(AbandonedLog::class, 'reference_id', 'id');
    }

    public function productReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function scopeGetChangesDetail(): string
    {
        return getChangesDetailForModel($this);
    }

    public function getChangesDetail2(array $oldOrder): string
    {
        return getChangesDetailForOrder($this, $oldOrder);
    }

    public function scopeSelectForHistory($q, $addSelect = '')
    {
        $q->select(OrderHistory::ARR_SELECT_ORDER);
        if (!empty($addSelect)) {
            $q->addSelect($addSelect);
        }

        return $q;
    }

    public function getLineItemsPbAttribute()
    {
        $this->products->map(function ($product) {
            $product->design_pb = $product->design_pb;
        });
    }

    /**
     * order_info_pb attribute for PersonalBridge
     * @return (string|array)[]
     * @throws Exception
     */
    public function getOrderInfoPbAttribute(): array
    {
        $address = [
            'address1' => '458 P. Minh Khai, Hai Ba Trung',
            'address2' => '',
            'city' => 'Hanoi',
            'company' => 'SenPrints',
            'zip' => '10000',
            'first_name' => 'SenPrints',
            'last_name' => 'Order',
            'province_code' => 'Hanoi',
            'phone' => '+***********',
            'country' => 'Vietnam',
            'country_code' => 'VN',
        ];
        return [
            'id' => $this->id,
            'status' => OrderStatus::PENDING,
            'billing_address' => $address,
            'shipping_address' => $address,
            'customer' => [
                'id' => Str::random(12),
                'first_name' => 'SenPrints',
                'last_name' => 'Order',
                'phone' => '+***********',
                'email' => '<EMAIL>'
            ],
            'name' => 'SenPrints Order',
            'line_items' => $this->products->map(fn($product) => $product->design_pb)->toArray()
        ];
    }

    /**
     * @return array|string|null
     */
    public function getFormattedPhoneNumberAttribute()
    {
        try {
            $country = $this->country;
            $phoneNumber = $this->customer_phone;
            if (empty($phoneNumber)) {
                return null;
            }
            if ($this->type === OrderTypeEnum::REGULAR && strlen($country) === 2) {
                return (new PhoneNumber($phoneNumber, $country))->formatE164();
            }
            return null;
        } catch (\Exception $ex) {
            logException($ex);
            $error = [
                'order_id' => $this->id,
                'phone' => $this->customer_phone,
                'country' => $this->country,
                'message' => $ex->getMessage(),
            ];
            graylogError('Error format phone number', array(
                'category' => 'order_logs',
                'order_id' => $this->id,
                'data_error' => json_encode($error),
            ));
            return $error;
        }
    }

    public function scopeFilterReceivedDateRange($query, $dateRange)
    {
        $dateRanges = [
            'column' => 'order.received_at',
            'type' => $dateRange
        ];

        return filterQueryByDateRange($dateRanges, $query);
    }

    public function removeAllDesigns(): void
    {
        Design::where('order_id', $this->id)->forceDelete();
    }


    /**
     * @return NotificationSetting|Builder|mixed|null
     */
    public function getNotificationSetting($channel = NotificationChannelEnum::EMAIL)
    {
        $settings = NotificationSetting::where('store_id', $this->store_id)
            ->where('channel', $channel)
            ->get();
        $updatedAt = Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at);
        $currentTime = Carbon::now();
        $seconds = $currentTime->diffInSeconds($updatedAt);

        if ($settings->count() > 0) {
            $settings = $settings->sortBy('type');
            foreach ($settings as $setting) {
                $sendAfter = $setting->send_after;
                if ($sendAfter >= $seconds) {
                    return $setting;
                }
            }
        }

        return null;
    }

    /**
     * @param string $description
     * @param string $channel
     * @return void
     */
    public function logToDiscord(string $description = ''): void
    {
        $message = "Order ID: {$this->id}\r\n";
        $message .= "Email: {$this->customer_email}\r\n";
        $message .= "Phone: {$this->customer_phone}\r\n";
        $message .= "Description: {$description}\r\n";
        graylogInfo($description, [
            'category' => 'abandoned_success',
            'data' => $message,
        ]);
    }

    public function hasAOPProducts()
    {
        $products = $this->products;
        foreach ($products as $product) {
            if ($product->full_printed === ProductPrintType::PRINT_3D_FULL) {
                return true;
            }
        }
        return false;
    }

    public function scopeGetSoldItems($query)
    {
        return $query->selectRaw("sum(case when `order`.`type` = '" . OrderTypeEnum::REGULAR . "' OR (`order`.`type` = '" . OrderTypeEnum::CUSTOM . "' and `order`.`sen_fulfill_status`='" . OrderSenFulfillStatus::YES . "') then `order_product`.`quantity` else 0 end) as sold_items");
    }

    public function scopeGetFulfillItems($query)
    {
        return $query->selectRaw("sum(case when `order`.`type` = '" . OrderTypeEnum::FULFILLMENT . "' OR `order`.`type` = '" . OrderTypeEnum::FBA . "' then `order_product`.`quantity` else 0 end) as fulfill_items");
    }

    public function scopeGetSenPoints($query)
    {
        return $query->selectRaw("sum(`order_product`.`sen_points`) as sen_points");
    }

    public function scopeIsValidPaidOrder($query)
    {
        return $query
            ->where('order.payment_status', OrderPaymentStatus::PAID)
            ->whereNotIn('order.status', [
                OrderStatus::CANCELLED,
                OrderStatus::REFUNDED,
            ]);
    }

    public function scopeAnalyticReferral($query, $dateRange, $startDate, $endDate, $refId = null, $columnGroupBy = '')
    {
        return $query
            ->getSoldItems()
            ->getFulfillItems()
            ->getSenPoints()
            ->filterDateRange($dateRange, $startDate, $endDate, self::FILTER_COLUMN_DATE, $refId)
            ->join('order_product', 'order.id', 'order_product.order_id')
            ->whereNull('order_product.deleted_at')
            ->when(!is_null($refId), function ($q) use ($refId) {
                $q->where('order.ref_id', $refId);
            })
            ->isValidPaidOrder()
            ->when(!empty($columnGroupBy), function ($q) use ($columnGroupBy) {
                $q->addSelect('order.' . $columnGroupBy);
                $q->groupBy('order.' . $columnGroupBy);
            });
    }

    public function scopeExcludeTest($query)
    {
        return $query->where(function ($q) {
            $q->where('order.customer_email', '!=', '<EMAIL>');
            $q->where('order.customer_email', '!=', '<EMAIL>');
        })->orWhereNull('order.customer_email');
    }

    public function scopeInvalidAddressOrderConditions($query)
    {
        return $query
            ->where('order.type', OrderTypeEnum::CUSTOM)
            ->where(function ($query) {
                $query->whereDoesntHave('request_cancel')
                    ->orWhereHas('request_cancel', function ($query) {
                        $query->whereNotIn('status', [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED]);
                    });
            });
    }

    public function scopeCustomerCancelOrderConditions($query)
    {
        return $query
            ->where('order.type', OrderTypeEnum::CUSTOM)
            ->whereHas('order_history', function ($q) {
                $q->where('action', OrderHistoryActionEnum::HOLD_FULFILL)->where('display_level', OrderHistoryDisplayLevelEnum::ADMIN)->where('admin_detail', OrderHistoryActionEnum::REQUEST_CANCEL_12H_CONFIRMED);
            })
            ->whereHas('request_cancel', function ($query) {
                $query->where('status', OrderCancelRequestStatus::CONFIRMED);
            });
    }

    public function scopeAnalytic($query, $columns = null)
    {
        if (!is_string($columns)) {
            $columns ??= CampaignSortByAllowEnum::getArrayForAnalyticOrder();

            foreach ($columns as $each) {
                $query->analytic($each);
            }
            return $query;
        }
        return match ($columns) {
            CampaignSortByAllowEnum::TOTAL_ITEMS => $query->selectRaw('sum(order.total_quantity) as ' . $columns),
            CampaignSortByAllowEnum::TOTAL_ORDERS => $query->selectRaw('count(order.id) as ' . $columns),
            CampaignSortByAllowEnum::TOTAL_PROFITS => $query->selectRaw('sum(order.total_seller_profit) as ' . $columns),
            CampaignSortByAllowEnum::TOTAL_SALES => $query->selectRaw('sum(order.total_amount) as ' . $columns),
            default => $query,
        };
    }

    public function scopeAnalyticWithFilterDateRangeBefore($query, $columns = null, $dateRange = null)
    {
        if (!is_string($columns)) {
            $columns ??= CampaignSortByAllowEnum::getArrayForAnalyticOrder();

            foreach ($columns as $each) {
                $query->AnalyticWithFilterDateRangeBefore($each, $dateRange);
            }
            return $query;
        }
        if ($columns === CampaignSortByAllowEnum::TOTAL_SALES_BEFORE_RANGE) {
            return $query->selectRaw('sum(order.total_amount) as ' . $columns);
        }

        $query->filterDateRangeWithSameDiffInPass($dateRange);
        return $query;
    }

    public function scopeShippingLate($query, $shippingLate)
    {
        if ($shippingLate) {
            return $query->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('order_product')
                    ->join('supplier_shipping_late_rule', function ($join) {
                        $join->on('order_product.supplier_id', '=', 'supplier_shipping_late_rule.supplier_id')
                            ->on('order.shipping_method', '=', 'supplier_shipping_late_rule.shipping_method')
                            ->join('system_location', 'order.country', '=', 'system_location.code')
                            ->where(function ($query) {
                                $query->where(function ($query) {
                                    $query->whereRaw("supplier_shipping_late_rule.region REGEXP CONCAT('(^|,)', system_location.region_code, '(,|$)')")
                                        ->orWhereRaw("supplier_shipping_late_rule.include_location REGEXP CONCAT('(^|,)', order.country, '(,|$)')");
                                })
                                    ->whereRaw("NOT (supplier_shipping_late_rule.no_location REGEXP CONCAT('(^|,)', order.country, '(,|$)'))")
                                    ->orWhereRaw("supplier_shipping_late_rule.region REGEXP '[*]'");
                            });
                    })
                    ->whereRaw('order_product.order_id = order.id')
                    ->whereRaw('DATEDIFF(CURRENT_DATE, order_product.fulfilled_at) > supplier_shipping_late_rule.date_late');
            })
                ->orWhereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('order_product')
                        ->whereRaw('order_product.order_id = order.id')
                        ->whereRaw("DATEDIFF(CURRENT_DATE, order_product.fulfilled_at) > " . self::DEFAULT_ORDER_SHIPPING_LATE_DATES);
                });
        }

        return $query;
    }

    public function rememberOldData(): void
    {
        $this->old_total_seller_profit = $this->total_seller_profit;
        $this->old_total_amount = $this->total_amount;
        $this->old_total_discount = $this->total_discount;
        $this->old_processing_fee = $this->processing_fee;
        $this->old_total_fulfill_fee = $this->total_fulfill_fee;

        foreach ($this->products as $product) {
            $product->rememberOldData();
        }
    }

    public function removeOldData(): void
    {
        unset(
            $this->old_total_seller_profit,
            $this->old_total_amount,
            $this->old_total_discount,
            $this->old_processing_fee,
            $this->old_total_fulfill_fee
        );
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'code');
    }

    public function issues(): HasMany
    {
        return $this->hasMany(OrderIssue::class, 'order_id', 'id')->limit(10)->orderByDesc('created_at');
    }

    public function scopeCountActiveSellers($query)
    {
        $query->selectRaw('count(DISTINCT(`seller_id`)) as active_sellers');
        $query->whereIn('type', [
            OrderTypeEnum::CUSTOM,
            OrderTypeEnum::REGULAR,
        ]);

        return $query;
    }

    public function scopeGetLastPaidOrder($q, $startTime, $endTime)
    {
        return $q->select('id', 'seller_id', 'paid_at')
            //most recent orders
            ->where(function ($q) {
                $q->where('paid_at', function ($q) {
                    $q->selectRaw('MAX(t2.paid_at)')
                        ->from('order as t2')
                        ->whereColumn('t2.seller_id', 'order.seller_id');
                });
            })
            ->whereBetween('paid_at', [$endTime, $startTime])
            ->groupBy('seller_id')
            ->orderBy('paid_at', 'desc');
    }

    /**
     * @param callable|null $callback
     * @return Collection
     */
    public function listPaid(callable $callback = null): Collection
    {
        return static::query()
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->tap(function ($q) use ($callback) {
                $callback ??= fn($q) => $q->limit(10);

                $callback($q);
            })
            ->get();
    }

    /**
     * @param string|array $detail
     * @param string $displayLevel
     * @return void
     */
    public function logChangeDesign($detail = '', string $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN): void
    {
        OrderHistory::insertLog(
            $this,
            OrderHistoryActionEnum::CHANGE_DESIGN,
            $detail,
            $displayLevel,
        );
    }

    /**
     * @return bool
     */
    public function fulfillStatusWasChangedToInvalid(): bool
    {
        return $this->wasChanged('fulfill_status')
            && $this->fulfill_status === OrderFulfillStatus::INVALID;
    }

    /**
     * @return bool
     */
    public function isFulfill(): bool
    {
        return $this->type === OrderTypeEnum::FULFILLMENT;
    }

    /**
     * @return bool
     */
    public function skipFulfill(): bool
    {
        return !in_array(
            $this->fulfill_status,
            [OrderFulfillStatus::UNFULFILLED, OrderFulfillStatus::PROCESSING],
            true
        );
    }

    public function getOrderFullFillStatus()
    {
        $isOrderHasInvalidProduct = OrderProduct::query()
            ->where('order_id', $this->id)
            ->where('fulfill_status', OrderProductFulfillStatus::INVALID)
            ->exists();
        if ($isOrderHasInvalidProduct) {
            return OrderFulfillStatus::INVALID;
        }

        $isOrderHasReviewingProduct = OrderProduct::query()
            ->where('order_id', $this->id)
            ->where('fulfill_status', OrderProductFulfillStatus::REVIEWING)
            ->exists();
        if ($isOrderHasReviewingProduct) {
            return OrderFulfillStatus::REVIEWING;
        }

        return OrderFulfillStatus::UNFULFILLED;
    }

    public function templates(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'order_product', 'order_id', 'template_id', 'id', 'id');
    }

    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class, 'order_product', 'order_id', 'supplier_id', 'id', 'id');
    }

    public function sellers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'order_product', 'order_id', 'seller_id', 'id', 'id');
    }

    /**
     * Khi location null thì lấy location của order
     *
     * @param \App\Models\SystemLocation|array|null $location
     *
     * @return array|string[]
     */
    public function prepareLocationCodes($location = null): array
    {
        $location ??= SystemLocation::ofOrderId($this->id);

        if ($location instanceof SystemLocation) {
            $locationCodes = $location->getRegionCodes();
        } else {
            $locationCodes = Arr::wrap($location) ?: ['*'];
        }

        return $locationCodes;
    }

    /**
     * @return float
     */
    public function getPlatformFeeAttribute(): float
    {
        if ($this->type !== OrderTypeEnum::REGULAR) {
            return 0;
        }

        return 0.065 * (($this->total_product_amount ?? .0) - ($this->total_discount ?? .0)) + ($this->tip_amount ?? .0);
    }

    /**
     * @return float
     */
    public function getSenProfitAttribute(): float
    {
        return ($this->total_fulfill_profit ?? .0) + ($this->processing_fee ?? .0);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function assign_supplier_histories(): HasMany
    {
        return $this->hasMany(OrderAssignSupplierHistory::class);
    }

    /**
     * @return \App\Models\Order[]|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function loadHasNotBeenNotifiedCrossShipping(int $take = 50)
    {
        return static::query()
            ->whereHas(
                'assign_supplier_histories',
                fn($q) => $q->whereNull('notified')->where('cross_shipping', 1)
            )
            ->whereIn('type', self::PLATFORM_ORDER_TYPES)
            ->take($take)
            ->get();
    }

    /**
     * @return bool
     */
    public function isPlatformOrder(): bool
    {
        return in_array($this->type, self::PLATFORM_ORDER_TYPES, true);
    }

    /**
     * @param $query
     *
     * @return Builder
     */
    public function scopePlatformOrder($query): Builder
    {
        return $query->whereIn('type', self::PLATFORM_ORDER_TYPES);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(SystemLocation::class, 'country', 'code');
    }

    /**
     * @param array $filters
     * @param               $tz
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function queryNoShipOrders(array $filters, $tz): Builder
    {
        return self::filter([
            'tz' => $tz,
            'type' => self::PLATFORM_ORDER_TYPES,
            'fulfill_status' => OrderFulfillStatus::NO_SHIP,
            'status' => OrderStatus::withoutDraft(),
            ...$filters
        ], OrderFilter::class)
            ->with([
                'order_products:id,order_id,product_name,fulfill_status,quantity,options,supplier_id,supplier_name,fulfill_product_id,template_id,fulfill_sku',
                'order_products.logs' => fn($q) => $q->orderByDesc('created_at'),
                'seller:id,name,email',
                'location:code,name',
            ])
            ->orderByDesc('created_at');
    }

    public static function queryLateProductionOrders(array $filters, $tz): Builder
    {
        foreach ($filters as $key => $filter) {
            if (is_null($filter)) {
                unset($filters[$key]);
            }
        }
        return self::filter($filters, OrderFilter::class)
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->wherehas('order_products', function ($q) use ($tz) {
                $q->where('fulfill_status', OrderFulfillStatus::PROCESSING);
                $q->where('fulfilled_at', '<', now()->clone()->subRealHours($tz)->subDays(7));
                $q->whereIn('sen_fulfill_status', [OrderSenFulfillStatus::YES, OrderSenFulfillStatus::PENDING]);
                $q->whereNotNull('supplier_id');
            })
            ->with([
                'order_products:id,order_id,product_name,fulfill_status,quantity,options,supplier_id,supplier_name,fulfill_product_id,template_id,fulfill_sku,fulfilled_at',
                'order_products.logs' => fn($q) => $q->orderByDesc('created_at'),
                'seller:id,name,email',
                'location:code,name',
            ])
            ->orderByDesc('created_at');
    }

    /**
     * @param $query
     * @return int
     */
    public function scopeCheckMarkWarningOrder($query): int
    {
        if (isEnvTesting()) {
            return 0;
        }

        if ($this->total_amount <= self::ORDER_WARNING_AMOUNT && $this->total_quantity <= self::ORDER_WARNING_QUANTITY) {
            return 0;
        }
        $fulfillStatus = OrderFulfillStatus::ON_HOLD;
        $supportStatus = OrderSupportStatusEnum::WARNING_LARGE_QUANTITY_AMOUNT;
        $note = 'Order is on-hold because of large quantity or amount';
        $alreadyHold = OrderHistory::query()
            ->where('order_id', $this->id)
            ->where('action', OrderHistoryActionEnum::HOLD_ORDER)
            ->where('display_level', OrderHistoryDisplayLevelEnum::ADMIN)
            ->where('support_status', $supportStatus)
            ->where('admin_detail', $note)
            ->exists();
        if ($alreadyHold) {
            return 0;
        }
        $updated = $query->where('id', $this->id)->update([
            'fulfill_status' => $fulfillStatus,
            'admin_note' => $note,
            'support_status' => $supportStatus,
        ]);

        if (is_numeric($updated) && $updated > 0) {
            try {
                $this->fulfill_status = $fulfillStatus;
                $this->support_status = $supportStatus;
                $this->admin_note = $note;
                OrderHistory::insertLog(
                    $this,
                    OrderHistoryActionEnum::HOLD_ORDER,
                    $note
                );
            } catch (\Throwable $e) {
                logException($e, 'checkMarkWarningOrder');
            }
        }
        return $updated;
    }

    public function getEstimateDeliveryDates()
    {
        try {
            $estimatedDelivery = $this->getShippingTime();
            $printingDay = $this->getPrintingTime();
            $printingDate = Carbon::parse($this->paid_at)->addDays($printingDay);
            $printDate = $printingDate;
            $fromDate = $printingDate->copy()->addDays($estimatedDelivery[0]);
            $toDate = $printingDate->copy()->addDays($estimatedDelivery[1]);
            return [
                'print_date' => $printDate,
                'from_date' => $fromDate,
                'to_date' => $toDate
            ];
        } catch (\Throwable $e) {
            logException($e);
        }
        return [
            'print_date' => '',
            'from_date' => '',
            'to_date' => ''
        ];
    }

    /**
     * @param array $data
     * @param $paymentObjectId
     * @param $gatewayId
     * @return mixed
     */
    private function correctPaymentMethod(array &$data, $paymentObjectId, $gatewayId): array
    {
        if (empty($this->payment_method)) {
            if (!empty($paymentObjectId)) {
                if (str_starts_with($paymentObjectId, 'pi_')) {
                    $data['payment_method'] = PaymentMethodEnum::STRIPE;
                } else {
                    $data['payment_method'] = PaymentMethodEnum::PAYPAL;
                }
                $this->payment_method = $data['payment_method'];
            }
        } else if (!is_null($gatewayId)) {
            $data['payment_gateway_id'] = $gatewayId;
            $gateway = PaymentGateway::query()
                ->where('id', $gatewayId)
                ->where('active', 1)
                ->value('gateway');
            if (!empty($gateway) && !str_starts_with(strtolower($this->payment_method), strtolower($gateway))) {
                $data['payment_method'] = $gateway;
                $this->payment_method = $data['payment_method'];
            }
        }
        return $data;
    }

    public function checkDiscountAmountBundleDiscount($orderProduct): bool
    {
        $opPromotionRule = $orderProduct->promotionRule;
        return !(isset($opPromotionRule) && ($opPromotionRule->type === PromotionTypeEnum::BUNDLE_DISCOUNT));
    }

    public function isPaidBySellerPayment()
    {
        return $this->type === OrderTypeEnum::CUSTOM;
    }

    public function belongToCurrentSeller()
    {
        return $this->seller_id === currentUser()->getUserId();
    }

    public function isFraudStatus($status)
    {
        return $this->fraud_status === $status;
    }

    public function setRegionSyncedAt($value)
    {
        $this->region_synced_at = $value;
    }

    public function scopeFilterUnfulfilledSupplierOrder($query)
    {
        return $query
            ->where('fulfill_status', OrderFulfillStatus::UNFULFILLED)
            ->where(function ($q) {
                $q->where(function ($typeQuery) {
                    $typeQuery->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                        ->where('paid_at', '<=', now()->subHours(12));
                })
                    ->orWhere(function ($typeQuery) {
                        $typeQuery->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                            ->where('paid_at', '<=', now()->subHours());
                    });
            });
    }

    /**
     * @return array
     */
    public function getSellerArtistProfits(): array
    {
        $profits = [];
        if ($this->type !== OrderTypeEnum::REGULAR) {
            return $profits;
        }
        $profits[$this->seller_id] = $this->total_seller_profit;
        $this->products->map(function (OrderProduct $orderProduct) use (&$profits) {
            if (!empty($orderProduct->seller_id) && $orderProduct->seller_id !== $this->seller_id && $orderProduct->artist_profit) {
                if (empty($profits[$orderProduct->seller_id])) {
                    $profits[$orderProduct->seller_id] = $orderProduct->artist_profit;
                } else {
                    $profits[$orderProduct->seller_id] += $orderProduct->artist_profit;
                }
            }
        });
        return $profits;
    }

    /**
     * @param bool $save
     * @return bool
     */
    public function calculateAndSaveOrderServiceAndFulfillFee(bool $save = true): bool
    {
        if ($save && $this->total_fulfill_fee > 0 && $this->fulfill_fee_paid === $this->total_fulfill_fee) {
            return false;
        }
        $this->calculateFulfillProcessingFee();
        $this->calculateSellerProfit();
        if ($save) {
            $this->push();
        }
        return true;
    }

    /**
     * @return bool
     */
    public function isNotifyChinaDeliveryLate(): bool
    {
        //only check if date from Jan 20 to Feb 5
        if (now()->between('2025-01-20', '2025-02-06')) {
            try {
                foreach ($this->products as $product) {
                    $template = $product->template;
                    if ($template) {
                        $isMadeInChina = optional($template->category)->slug === 'made-in-china';
                        if ($isMadeInChina || in_array($template->full_printed, [ProductPrintType::AOP, ProductPrintType::PRINT_3D_FULL, ProductPrintType::EMBROIDERY], true)) {
                            return true;
                        }
                    }
                }
            } catch (\Throwable $e) {
                logException($e);
            }
        }
        return false;
    }

    public function isFBA(): bool
    {
        return $this->type === OrderTypeEnum::FBA;
    }
}
