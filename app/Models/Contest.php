<?php

namespace App\Models;

/**
 * App\Models\Contest
 *
 * @property int $id
 * @property string $name
 * @property string $image
 * @property string $rewards
 * @property \Illuminate\Support\Carbon|null $start_time
 * @property \Illuminate\Support\Carbon|null $end_time
 * @property \Illuminate\Support\Carbon|null $registration_date
 * @property int $status 0:inactive, 1: active
 * @property int $need_join_contest 1: yes, 0: no
 * @property string $settings
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Contest isActive()
 * @method static \Illuminate\Database\Eloquent\Builder|Contest isMainContest()
 * @method static \Illuminate\Database\Eloquent\Builder|Contest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contest query()
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereRewards($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contest whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Contest extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'contest';
    protected $fillable = [
        'name',
        'image',
        'rewards',
        'start_time',
        'end_time',
        'status',
        'settings',
        'registration_date',
        'need_join_contest',
    ];

    protected $casts = [
        'registration_date' => 'datetime',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    public function scopeIsActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopeIsMainContest($query)
    {
        return $query->where('status', 2);
    }
}
