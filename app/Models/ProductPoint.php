<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\ProductPoint
 *
 * @property int $id
 * @property int $product_id
 * @property float $points
 * @property string|null $by_locations
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint whereByLocations($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint wherePoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPoint whereProductId($value)
 * @mixin \Eloquent
 */
class ProductPoint extends Model
{
    use HasFactory;

    /**
     * @var string
     *
     * table name
     */
    protected $table = 'product_points';

    protected $fillable = [
        'id',
        'product_id',
        'points',
        'by_locations'
    ];

    public $timestamps = false;
}
