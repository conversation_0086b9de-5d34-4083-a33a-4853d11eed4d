<?php

namespace App\Models;

/**
 * App\Models\DeviceInfo
 *
 * @property int $device_id
 * @property string|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo whereDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceInfo whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DeviceInfo extends Model
{
    protected $table = 'device_info';
    protected $primaryKey = 'device_id';
    protected $fillable = ['device_id', 'status'];
}
