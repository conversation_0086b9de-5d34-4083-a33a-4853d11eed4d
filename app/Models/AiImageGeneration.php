<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiImageGeneration extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'prompt',
        'negative_prompt',
        'aspect_ratio',
        'model',
        'magic_prompt_option',
        'style_type',
        'seed',
        'num_images',
        'result_images',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'result_images' => 'array',
        'seed' => 'integer',
        'num_images' => 'integer',
    ];

    /**
     * Get the user that owns the image generation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
