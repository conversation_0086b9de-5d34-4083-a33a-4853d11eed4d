<?php

namespace App\Actions\Storefront\Order;

use App\Models\Customer;
use App\Models\Order;
use App\Models\SellerCustomer;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Bus\Dispatchable;
use Throwable;

class CreateCustomer
{
    use Dispatchable;

    private string $email;
    private $seller_id;
    private $store_id;
    private $order_id;

    public function __construct(string $email, $seller_id = null, $store_id = null, $order_id = null)
    {
        $this->email = $email;
        $this->seller_id = $seller_id;
        $this->store_id = $store_id;
        $this->order_id = $order_id;
    }

    public function handle(): ?Customer
    {
        $user = null;
        try {
            $this->createIgnoreDuplicateEmail();
            $user = tap($this->getUser(), function ($user) {
                if (!$this->seller_id || !$user) {
                    return;
                }
                $sellerCustomer = $this->attachSellerToCustomer($user->id);
                if (!$this->store_id) {
                    return;
                }
                $sellerCustomer->store_id = $this->store_id;
                $sellerCustomer->save();
            });
        } catch (Throwable $e) {
            // ignore duplicate entry
            if (!$e instanceof QueryException || $e->errorInfo[1] !== 1062) {
                logToDiscord(
                    'Error: ' . $e->getMessage() . PHP_EOL .
                    'File: ' . $e->getFile() . PHP_EOL .
                    'Line: ' . $e->getLine() . PHP_EOL
                );
            }
        }
        if ($user && $this->order_id) {
            $order = Order::query()->find($this->order_id);
            if ($order && (int)$order->customer_id !== (int)$user->id) {
                $order->customer_id = $user->id;
            }
        }
        return $user;
    }

    /**
     * @return void
     * @throws Throwable
     */
    private function createIgnoreDuplicateEmail(): void
    {
        Customer::query()->insertOrIgnore([
            'email' => $this->email,
        ]);
    }

    /**
     * @return Customer|null
     */
    private function getUser(): ?Customer
    {
        return Customer::query()->firstWhere('email', $this->email);
    }

    /**
     * @param int $customerId
     * @return SellerCustomer
     */
    private function attachSellerToCustomer(int $customerId): SellerCustomer
    {
        return SellerCustomer::query()->firstOrNew([
            'seller_id'   => $this->seller_id,
            'customer_id' => $customerId
        ]);
    }
}
