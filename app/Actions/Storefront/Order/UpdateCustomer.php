<?php

namespace App\Actions\Storefront\Order;

use App\Enums\QueueName;
use App\Enums\SellerCustomerStatus;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\Order;
use App\Models\SellerCustomer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class UpdateCustomer implements ShouldQueue
{
    use Dispatchable, SerializesModels, InteractsWithQueue, Queueable;

    private Order $order;
    private array $userInfo;

    public function __construct(Order $order, array $userInfo)
    {
        $this->order = $order;
        $this->userInfo = $userInfo;
        $this->onQueue(QueueName::ORDER_EVENTS);
    }

    public function handle(): ?Customer
    {
        if (!$this->order->customer_email) {
            return null;
        }

        try {
            $user = Customer::query()
                ->where('email', $this->order->customer_email)
                ->first();
            if (!$user) {
                $user = CreateCustomer::dispatchSync(
                    $this->order->customer_email,
                    $this->order->seller_id,
                    $this->order->store_id,
                    $this->order->id
                );
            }
            if (!$user) {
                throw new ModelNotFoundException('Customer not found. Order: ' . $this->order->id);
            }

            $country = strtoupper($this->userInfo['country']);
            $address = CustomerAddress::query()->firstOrCreate([
                'user_id' => $user->id,
            ], [
                'name'      => $this->order->customer_name,
                'phone'     => $this->order->customer_phone,
                'address'   => $this->order->address,
                'address_2' => $this->order->address_2,
                'city'      => $this->order->city,
                'state'     => $this->order->state,
                'postcode'  => $this->order->postcode,
                'country'   => $country,
            ]);
            $this->order->shipping_address = $address->id;
            $this->order->billing_address = $address->id;
            $this->order->save();
            $user->name = $this->order->customer_name;
            $user->phone = $this->order->customer_phone;
            $user->country = $country;
            $user->city = $this->order->city;
            $user->state = $this->order->state;
            $user->save();

            // unsubscribe customer
            $subscribedStatus = $this->userInfo['subscribed'] ? SellerCustomerStatus::ACTIVE : SellerCustomerStatus::INACTIVE;
            SellerCustomer::query()
                ->where([
                    'seller_id'   => $this->order->seller_id,
                    'customer_id' => $user->id
                ])
                ->update([
                    'status' => $subscribedStatus
                ]);
        } catch (Throwable $e) {
            logException($e, __CLASS__);
        }
        return null;
    }
}
