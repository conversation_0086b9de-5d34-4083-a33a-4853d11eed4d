<?php

namespace App\Actions\Admin\Analytic;

use App\Enums\DateRangeEnum;
use App\Enums\EventLogsTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PgsAndMysqlVersionEnum;
use App\Enums\SaleReport\DataIndexEnum;
use App\Enums\SaleReport\DataTypeEnum;
use App\Enums\SaleReport\PeriodEnum;
use App\Enums\SaleReport\TypeEnum;
use App\Enums\UserInfoKeyEnum;
use App\Models\EventLogs;
use App\Models\IndexEventLogs;
use App\Models\Order;
use App\Models\SalesReport;
use App\Models\User;
use Carbon\Carbon;
use Throwable;

class InsertSaleReportAction
{
    private $context = [
        'category' => 'process_sale_report',
    ];
    private $data;
    private $data_product;
    private $key;
    private $unique_key;
    private string $period = PeriodEnum::HOUR;

    private array $dataInsert = [];
    private array $sellers = [];
    private array $eachDataInsert = [];
    private $time;
    private string $type = '';
    private string $sellerId = '';
    private ?string $type_index = '';

    public function handle($request = null): void
    {
        // cache()->clear();
        try {
            $dateRanges                = [];
            $dateRanges['type']        = DateRangeEnum::CUSTOM;
            $dateRanges['is_timezone'] = true;

            $startDate = optional($request)->date('start_time') ?? now()->subHour()->startOfHour();
            $endDate   = optional($request)->date('end_time') ?? now()->subHour()->endOfHour();
            graylogInfo(
                'InsertSaleReportAction 2: '
                    . $startDate->format('Y-m-d H:i:s')
                    . ' - ' . $endDate->format('Y-m-d H:i:s'),
                $this->context
            );

            $methodAdd   = 'add' . ucfirst($this->period);
            $methodStart = 'startOf' . ucfirst($this->period);
            $methodEnd   = 'endOf' . ucfirst($this->period);

            for ($date = $startDate; $date < $endDate; $date->$methodAdd()) {
                $startTime           = $date->clone()->$methodStart();
                $endTime             = $date->clone()->$methodEnd();
                $dateRanges['range'] = [
                    $startTime,
                    $endTime,
                ];
                graylogInfo(
                    'Start setData',
                    $this->context
                );
                $response = $this->setData($dateRanges);
                graylogInfo('End setData', $this->context);

                graylogInfo(
                    'Start insert',
                    $this->context
                );
                $this->insert($response);
                graylogInfo('End Insert', $this->context);
            }
            graylogInfo('End InsertSaleReportAction', $this->context);
        } catch (Throwable $e) {
            graylogError('InsertSaleReportAction Failed. Exception: ' . $e->getMessage(), $this->context);
        }
    }

    private function setData($dateRanges, $dbVersion = PgsAndMysqlVersionEnum::MYSQL): array
    {
        $response = [];

        $orders = Order::query()
            ->select([
                'paid_at',
                'id',
                'type',
                'country',
                'device',
                'device_detail',
                'ad_campaign',
                'ad_source',
                'ad_medium',
                'seller_id',
                'store_id',
                'total_quantity',
                'total_amount',
                'total_seller_profit',
                'insurance_fee',
                'tip_amount',
                'total_product_amount',
                'total_shipping_amount',
                'total_discount',
                'total_refund',
            ])
            ->with([
                'products' => static function ($query) {
                    $query->select([
                        'order_id',
                        'seller_id',
                        'campaign_id',
                        'template_id',
                        'quantity',
                        'total_amount',
                        'discount_amount',
                        'shipping_cost',
                        'seller_profit',
                    ]);
                }
            ])
            ->with([
                'seller' => static function ($query) {
                    $query->select([
                        'id',
                        'utc_offset',
                    ]);
                }
            ])
            ->filterDateRange($dateRanges, null, null, null, -1)
            ->get();
        graylogInfo('Start Orders: ' . count($orders), $this->context);

        foreach ($orders as $order) {
            $timestamp      = Carbon::parse($order->paid_at)->format('Y-m-d H:00:00');
            $each           = &$response[$timestamp];
            $this->data     = $order;
            $dataIndex      = DataIndexEnum::PLATFORM;
            $needUpdateType = false;

            if ($order->type === OrderTypeEnum::FULFILLMENT || $order->type === OrderTypeEnum::FBA) {
                $dataIndex = DataIndexEnum::FULFILL;
            } elseif ($order->type === OrderTypeEnum::CUSTOM) {
                $dataIndex = DataIndexEnum::CUSTOM;
            } elseif ($order->type === OrderTypeEnum::SERVICE) {
                $dataIndex = DataIndexEnum::SERVICE;
            }
            $this->updateTypes($each[TypeEnum::PLATFORM], 'setValueOrder', $dataIndex);

            /** @noinspection InArrayMissUseInspection */
            /** @noinspection PhpInArrayCanBeReplacedWithComparisonInspection */
            if (in_array($order->type, [
                OrderTypeEnum::CUSTOM,
            ])) {
                $needUpdateType = true;
            }

            if ($needUpdateType) {
                $this->updateTypes($each[TypeEnum::PLATFORM]);
            }

            $sellerId = $order->seller_id;
            $passed   = $this->setSellerAndCheck($order);

            if ($passed) {
                $eachSeller = &$each[$sellerId];
                $this->updateTypes($eachSeller[TypeEnum::SELLER][$sellerId], 'setValueOrder', $dataIndex);
                $this->updateTypes($eachSeller[TypeEnum::STORE][$sellerId], 'setValueOrder', $dataIndex);
                if ($needUpdateType) {
                    $this->updateTypes($eachSeller[TypeEnum::SELLER][$sellerId]);
                    $this->updateTypes($eachSeller[TypeEnum::STORE][$sellerId]);
                }
            }

            foreach ($order->products as $product) {
                $this->data_product = $product;
                // update product template
                $this->setValueOrderProduct($each[TypeEnum::PLATFORM][DataTypeEnum::PRODUCT][$product->template_id]);

                if ($passed) {
                    $campaignId = $product->campaign_id;
                    $this->updateTypes(
                        $eachSeller[TypeEnum::CAMPAIGN][$campaignId],
                        'setValueOrderProduct',
                        $dataIndex,
                    );
                    if ($needUpdateType) {
                        $this->updateTypes(
                            $eachSeller[TypeEnum::CAMPAIGN][$campaignId],
                            'setValueOrderProduct',
                        );
                    }

                    $this->setValueOrderProduct($eachSeller[TypeEnum::SELLER][$product->seller_id][DataTypeEnum::PRODUCT][$product->template_id]);
                    $this->setValueOrderProduct($eachSeller[TypeEnum::STORE][$order->store_id][DataTypeEnum::PRODUCT][$product->template_id]);
                    $this->setValueOrderProduct($eachSeller[TypeEnum::CAMPAIGN][$campaignId][DataTypeEnum::PRODUCT][$product->template_id]);
                }
            }
        }
        graylogInfo('End Orders', $this->context);
        if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $events = IndexEventLogs::query()
                ->select([
                    'timestamp',
                    'seller_id',
                    'store_id',
                    'campaign_id',
                    'type',
                    'country',
                    'device',
                    'device_detail',
                    'ad_campaign',
                    'ad_medium',
                    'ad_source',
                    'session_id',
                ])
                ->filterDateRange($dateRanges, null, null, null, -1)
                ->where('seller_id', '!=', 0)
                ->get();
            $users = User::query()
                ->select([
                    'id',
                    'utc_offset'
                ])
                ->with([
                    'infos' => static function ($query) {
                        $query->select([
                            'user_id',
                            'value',
                        ]);
                        $query->where('key', UserInfoKeyEnum::EXCLUDE_COUNTRIES);
                        $query->whereNotNull('value');
                    }
                ])
                ->get();
            $usersEvent = [];
            foreach ($users as $user) {
                $usersEvent[$user->id] = $user;
            }

            foreach ($events as $event) {
                if (array_key_exists($event->seller_id, $usersEvent)) {
                    $event->seller = $usersEvent[$event->seller_id];
                }
            }
        } else if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $events = EventLogs::query()
                ->select([
                    'timestamp',
                    'seller_id',
                    'store_id',
                    'campaign_id',
                    'type',
                    'country',
                    'device',
                    'device_detail',
                    'ad_campaign',
                    'ad_medium',
                    'ad_source',
                    'session_id',
                ])
                ->with([
                    'seller' => static function ($query) {
                        $query->select([
                            'id',
                            'utc_offset',
                        ]);
                        $query->with([
                            'infos' => static function ($query) {
                                $query->select([
                                    'user_id',
                                    'value',
                                ]);
                                $query->where('key', UserInfoKeyEnum::EXCLUDE_COUNTRIES);
                                $query->whereNotNull('value');
                            }
                        ]);
                    }
                ])
                ->filterDateRange($dateRanges, null, null, null, -1)
                ->where('seller_id', '!=', 0)
                ->get();
        }

        graylogInfo('Start Events: ' . count($events), $this->context);

        foreach ($events as $event) {
            $timestamp        = Carbon::parse($event->timestamp)->format('Y-m-d H:00:00');
            $each             = &$response[$timestamp];
            $type             = $this->getCorrectType($event->type);
            $this->data       = $event;
            $this->key        = $type;
            $this->unique_key = $type . '_' . $event->session_id;

            $this->updateTypes($each[TypeEnum::PLATFORM], 'setValueEvent');

            $sellerId = $event->seller_id;
            $passed   = $this->setSellerAndCheck($event);
            if ($passed) {
                $eachSeller = &$each[$sellerId];
                $this->updateTypes($eachSeller[TypeEnum::SELLER][$event->seller_id], 'setValueEvent');
                $this->updateTypes($eachSeller[TypeEnum::STORE][$event->store_id], 'setValueEvent');
                $this->updateTypes($eachSeller[TypeEnum::CAMPAIGN][$event->campaign_id], 'setValueEvent');
            }
        }
        graylogInfo('End Events: ' . count($events), $this->context);
        unset($this->data);

        return $response;
    }

    private function setSellerAndCheck($obj): bool
    {
        if (is_null($obj->seller)) {
            return false;
        }

        $sellerId = $obj->seller->id;
        if (empty($this->sellers[$sellerId])) {
            $this->sellers[$sellerId] = $obj->seller;
        }

        $keyExclude = UserInfoKeyEnum::EXCLUDE_COUNTRIES;
        if ($this->sellers[$sellerId]->infos->isNotEmpty()) {
            $this->sellers[$sellerId]->$keyExclude = explode(',', $this->sellers[$sellerId]->infos);
            if (in_array($obj->country, $this->sellers[$sellerId]->$keyExclude)) {
                return false;
            }
        }

        return true;
    }

    private function setDefault(&$arr): void
    {
        $keys = [
            'visits',
            'add_to_carts',
            'checkouts',
            'orders',
            'items',
            'total_sales',
            'seller_profit',
            'insurance_fee',
            'tip',
            'total_product_amount',
            'total_shipping_amount',
            'total_discount',
            'total_refund',
        ];
        foreach ($keys as $key) {
            $arr[$key] = 0;
        }
    }

    private function updateTypes(&$arr, string $method = 'setValueOrder', $dataIndex = DataIndexEnum::PLATFORM): void
    {
        $data_types = DataTypeEnum::getValues();

        foreach ($data_types as $data_type) {
            // ignore campaign data
            if (
                $this->type === TypeEnum::CAMPAIGN
                &&
                in_array($data_type, DataTypeEnum::getArrForTypeCampaign())
            ) {
                continue;
            }

            if ($data_type !== DataTypeEnum::OVERVIEW) {
                $dataIndex = $this->data->$data_type;
                if (
                    empty($dataIndex)
                    &&
                    in_array($data_type, DataTypeEnum::getArrCanTypeNA())
                ) {
                    $dataIndex = DataIndexEnum::NA;
                }
            }

            $this->$method($arr[$data_type][$dataIndex]);
        }
    }

    private function setValueOrder(&$arr): void
    {
        if (empty($arr)) {
            $this->setDefault($arr);
        }

        $arr['orders']++;
        $arr['items']                 += $this->data->total_quantity;
        $arr['total_sales']           += $this->data->total_amount;
        $arr['seller_profit']         += $this->data->total_seller_profit;
        $arr['insurance_fee']         += $this->data->insurance_fee;
        $arr['tip']                   += $this->data->tip_amount;
        $arr['total_product_amount']  += $this->data->total_product_amount;
        $arr['total_shipping_amount'] += $this->data->total_shipping_amount;
        $arr['total_discount']        += $this->data->total_discount;
        $arr['total_refund']          += $this->data->total_refund;
    }

    private function setValueOrderProduct(&$arr): void
    {
        if (empty($arr)) {
            $this->setDefault($arr);
        }

        $arr['orders']++;
        $arr['items']                 += $this->data_product->quantity;
        $arr['total_sales']           += $this->data_product->total_amount + $this->data_product->shipping_cost + $this->data_product->discount_amount;
        $arr['seller_profit']         += $this->data_product->seller_profit;
        $arr['total_product_amount']  += $this->data_product->total_amount;
        $arr['total_shipping_amount'] += $this->data_product->shipping_cost;
        $arr['total_discount']        += $this->data_product->discount_amount;
    }

    private function setValueEvent(&$arr): void
    {
        if (!isset($arr[$this->key])) {
            $this->setDefault($arr);
        }

        if (!isset($arr['unique_key'][$this->unique_key])) {
            $arr['unique_key'][$this->unique_key] = true;
            $arr[$this->key]++;
        }
    }

    private function getCorrectType(string $type): string
    {
        switch ($type) {
            case EventLogsTypeEnum::VISIT:
                return 'visits';
            case EventLogsTypeEnum::ADD_TO_CART:
                return 'add_to_carts';
            case EventLogsTypeEnum::INIT_CHECKOUT:
                return 'checkouts';
        }

        return DataIndexEnum::NA;
    }

    private function insert($response): void
    {
        foreach ($response as $time => $type) {
            try {
                $this->time = Carbon::parse($time);
                $this->getType($type);
                SalesReport::query()
                    ->where('timestamp', $this->time->clone()->toDateTimeString())
                    ->delete();
                foreach (array_chunk($this->dataInsert, 1000) as $chunk) {
                    SalesReport::query()
                        ->insert($chunk);
                }
                $this->dataInsert = [];
            } catch (Throwable $e) {
                logToDiscord(
                    'Insert Sales Report Error'
                        . PHP_EOL . $e->getMessage()
                );
            }
        }
    }

    private function getType($data): void
    {
        foreach ($data as $key => $each) {
            if ($key === TypeEnum::PLATFORM) {
                $this->type       = $key;
                $this->type_index = null;
                $this->getPlatform($each);
            } else {
                $this->sellerId = $key;
                foreach ($each as $type => $eachData) {
                    $this->type = $type;
                    $this->getSeller($eachData);
                }
            }
        }
    }

    private function getPlatform($data): void
    {
        foreach ($data as $data_type => $each) {
            $this->getDataIndex($each, $data_type);
        }
    }

    private function getSeller($data): void
    {
        foreach ($data as $type_index => $each) {
            if ($type_index === 0) {
                continue;
            }
            foreach ($each as $data_type => $eachData) {
                $this->type_index = $type_index;
                $this->getDataIndex($eachData, $data_type);
            }
        }
    }

    private function setEachDataInsertDefault($data_type): void
    {
        if ($this->type === TypeEnum::PLATFORM) {
            $sellerId  = 0;
            $utcOffset = 7;
        } else {
            $sellerId  = $this->sellerId;
            $utcOffset = data_get($this->sellers, $sellerId . '.utc_offset', 7);
        }

        $this->eachDataInsert = [
            'date_timezone' => $this->time->clone()->addHours($utcOffset)->startOfDay()->toDateString(),
            'timestamp'     => $this->time->clone()->toDateTimeString(),
            'type'          => $this->type,
            'data_type'     => $data_type,
            'period'        => $this->period,
            'seller_id'     => $sellerId,
        ];
    }

    private function getDataIndex($data, $data_type): void
    {
        foreach ($data as $data_index => $each) {
            unset($each['unique_key']);

            if (empty($data_index)) {
                if (in_array($data_type, DataTypeEnum::getArrCanTypeNA())) {
                    $data_index = DataIndexEnum::NA;
                } else {
                    $data_index = null;
                }
            }

            $this->setEachDataInsertDefault($data_type);
            $this->eachDataInsert['data_index'] = $data_index;
            $this->eachDataInsert['type_index'] = $this->type_index;
            $this->getData($each);
        }
    }

    private function getData($each): void
    {
        foreach ($each as $column => $value) {
            $this->eachDataInsert[$column] = $value;
        }

        $this->dataInsert[]   = $this->eachDataInsert;
        $this->eachDataInsert = [];
    }
}
