<?php

namespace App\Actions\Commons;

use App\Models\Order;

class SendWooCommerceTrackingWebhookAction extends AbstractWooCommerceWebhookAction
{
    public function handle(Order $order, array $tracking): void
    {
        graylogInfo('Sending tracking webhook', [
            'category' => 'woocommerce_webhook',
            'order_number' => $order->order_number,
            'tracking_code' => $tracking['tracking_code'],
            'shipping_carrier' => $tracking['shipping_carrier']
        ]);

        $this->sendWebhook($order, [
            'event' => 'order.tracking_updated',
            'order_number' => $order->order_number,
            'tracking_number' => $tracking['tracking_code'],
            'carrier' => $tracking['shipping_carrier']
        ]);
    }
}
