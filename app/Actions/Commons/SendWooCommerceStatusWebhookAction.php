<?php

namespace App\Actions\Commons;

use App\Models\Order;

class SendWooCommerceStatusWebhookAction extends AbstractWooCommerceWebhookAction
{
    public function handle(Order $order, string $newStatus): void
    {
        $this->sendWebhook($order, [
            'event' => 'order.status_updated',
            'order_number' => $order->order_number,
            'status' => $newStatus
        ]);
    }
}
