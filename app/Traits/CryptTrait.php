<?php
namespace App\Traits;

use App\Enums\CacheKeys;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Str;

trait CryptTrait
{
    /**
     * @return Encrypter
     * @throws \Exception
     */
    public function crypt(): Encrypter
    {
        return cacheAlt()->remember(CacheKeys::SYSTEM_CONFIG_ENCRYPT_KEY, CacheKeys::CACHE_30D, function () {
            $key = base64_decode(Str::after(config('database.encryption_key'), 'base64:'));
            return new Encrypter($key, config('app.cipher'));
        });
    }
}
