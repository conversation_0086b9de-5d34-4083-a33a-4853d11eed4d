<?php

namespace App\Traits;

use App\Models\Campaign;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Relationships\CustomBelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

trait HasRelationShipsCustom
{
    use HasRelationships;

    public function belongsTo($related, $foreignKey = null, $ownerKey = null, $relation = null, $callback = null): CustomBelongsTo
    {
        $instance = $this->newRelatedInstance($related);

        $query = $instance->newQuery()->when($this->checkRelatedInstance($instance), function ($query) {
            $query->on($this->connection);
        });

        if (is_null($foreignKey)) {
            $foreignKey = Str::snake($relation) . '_' . $instance->getKeyName();
        }

        $ownerKey = $ownerKey ?: $instance->getKeyName();
        return $this->newBelongsTo($query, $this, $foreignKey, $ownerKey, $relation, $callback);
    }

    public function hasMany($related, $foreignKey = null, $localKey = null)
    {
        $instance = $this->newRelatedInstance($related);

        $query = $instance->newQuery()->when($this->checkRelatedInstance($instance), function ($query) {
            $query->on($this->connection);
        });

        $foreignKey = $foreignKey ?: $this->getForeignKey();

        $localKey = $localKey ?: $this->getKeyName();

        return $this->newHasMany($query, $this, $instance->getOnlyTable() . '.' . $foreignKey, $localKey);
    }

    public function hasOne($related, $foreignKey = null, $localKey = null)
    {
        $instance = $this->newRelatedInstance($related);

        $query = $instance->newQuery()->when($this->checkRelatedInstance($instance), function ($query) {
            $query->on($this->connection);
        });

        $foreignKey = $foreignKey ?: $this->getForeignKey();

        $localKey = $localKey ?: $this->getKeyName();

        return $this->newHasOne($query, $this, $instance->getOnlyTable() . '.' . $foreignKey, $localKey);
    }

    protected function newBelongsTo(Builder $query, Model $child, $foreignKey, $ownerKey, $relation, $callback = null): CustomBelongsTo
    {
        return new CustomBelongsTo($query, $child, $foreignKey, $ownerKey, $relation, $callback);
    }

    public function checkRelatedInstance($instance): bool
    {
        return ($instance instanceof ExpressCampaign || $instance instanceof Product || $instance instanceof Campaign || $instance instanceof ProductVariant || $instance instanceof File);
    }
}
