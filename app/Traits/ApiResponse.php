<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;

trait ApiResponse
{
    /**
     * Return success response
     * (Check Campaign controller for demo)
     *
     * @param mixed $data
     * @param array|string|null $message
     * @param int $code
     * @return JsonResponse
     */
    protected function successResponse($data = null, $message = null, int $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $code);
    }

    /**
     * Return error response
     *
     * @param mixed $message
     * @param int $code
     * @param array $customPayload
     * @param array $customData
     * @return JsonResponse
     */
    protected function errorResponse(mixed $message = null, int $code = 200, array $customPayload = [], array $customData = []): JsonResponse
    {
        $data = [
            'success' => false,
            'message' => $message,
            'data' => null
        ];

        if (!empty($customPayload)) {
            $data['customPayload'] = $customPayload;
        }

        if (!empty($customData)) {
            $data['data'] = $customData;
        }

        return response()->json($data, $code);
    }
}
