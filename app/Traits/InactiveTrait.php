<?php

namespace App\Traits;

trait InactiveTrait

{
    protected string $processId;

    protected string $token;

    protected string $fileType;
    protected string $fileName;

    protected string $type;

    protected array $category;

    protected int $days;

    protected int $limit;

    protected int $count;
    protected string $threeMonthAgoDateTime;
    protected string $sixMonthAgoDateTime;

    protected string $cacheKey;



    public function scopeInactiveCampaignByCondition1(&$query, $sellerId, $sixMonthAgoDateTime)
    {
        $query
        ->where('seller_id', $sellerId)
        ->where('is_deleted', 0)
        ->where('product_type', '=', 'campaign')
        ->where('created_at', '<=', $sixMonthAgoDateTime)
        ->where(function ($q) use ($sixMonthAgoDateTime) {
            $q->where('visited_at', '<', $sixMonthAgoDateTime)
                ->orWhereNull('visited_at');
        });

        return $query;
    }
}
