<?php

namespace App\Traits;

use App\Enums\OrderFulfillStatus;
use App\Models\Order;
use App\Models\OrderProduct;
use Modules\OrderService\Models\RegionOrders;

trait CorrectFulfillStatus
{
    public static function correctFulfillStatus(Order|RegionOrders $order): string
    {
        $fulfillStatus = $order->fulfill_status;

        if ($fulfillStatus === OrderFulfillStatus::ON_HOLD) {
            $fulfillStatus = OrderFulfillStatus::UNFULFILLED;

            $stats = OrderProduct::query()
                ->selectRaw('count(CASE WHEN fulfill_status = "designing" THEN 1 END) as designings')
                ->selectRaw('count(CASE WHEN fulfill_status = "invalid" THEN 1 END) as invalids')
                ->selectRaw('count(CASE WHEN fulfill_status = "reviewing" THEN 1 END) as reviewings')
                ->firstWhere('order_id', $order->id);

            if ($stats) {
                if ($stats->designings > 0) {
                    $fulfillStatus = OrderFulfillStatus::DESIGNING;
                } else if ($stats->invalids > 0) {
                    $fulfillStatus = OrderFulfillStatus::INVALID;
                } else if ($stats->reviewings > 0) {
                    $fulfillStatus = OrderFulfillStatus::REVIEWING;
                }
            }
        }

        return $fulfillStatus;
    }
}
