<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait ScopeOrderByArray
{
    // beware: cause have group by so let this scope latest for correct order query
    /** @noinspection SpellCheckingInspection */
    public function scopeOrderByArray($query, $arrId, $column, $isGroupBy = true, $typeArr = 'int')
    {
        // remove empty value
        $arrId     = array_filter($arrId);
        $stringIds = implode(', ', $arrId);
        $stringIds = escapeSpecialCharacter($stringIds, false);
        return $query->leftJoin(
            DB::raw("unnest('{" . $stringIds . "}'::" . $typeArr . "[])  WITH ORDINALITY t(i, ord)"),
            't.i',
            $column
        )
            // add order column to group by for order by
            ->when($isGroupBy, function ($q) {
                return $q->groupBy('t.ord');
            })
            ->orderBy('t.ord');
    }
}
