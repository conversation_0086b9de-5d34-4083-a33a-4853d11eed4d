<?php

namespace App\Traits;

use Illuminate\Encryption\Encrypter as BaseEncrypter;
use Illuminate\Support\Str;

trait Encrypter
{
    public static function encrypter(): BaseEncrypter
    {
        $key = base64_decode(Str::after(config('database.encryption_key'), 'base64:'));
        return new BaseEncrypter($key, config('app.cipher'));
    }

    public static function safeLoadConfig($model, $configField = 'config', $encryptedField = 'encrypted'): string
    {
        $config = $model->$configField;

        return $model->$encryptedField
            ? self::encrypter()->decrypt($config)
            : $config;
    }
}
