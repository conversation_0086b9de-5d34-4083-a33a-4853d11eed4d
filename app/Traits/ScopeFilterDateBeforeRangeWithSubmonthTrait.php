<?php

namespace App\Traits;

use App\Enums\DateRangeEnum;

trait ScopeFilterDateBeforeRangeWithSubmonthTrait
{
    public function scopeFilterDateBeforeRangeWithSubmonth(
        $query,
        $dateRange,
        $startDate = null,
        $endDate = null,
        $column = null,
        $sellerId = null,
        $isOrderBy = false,
        $submonth = 0
    ) {
        if (is_array($dateRange)) {
            $dateRanges = $dateRange;
        } else {
            $dateRanges         = [];
            $dateRanges['type'] = $dateRange;
            if ($dateRange === DateRangeEnum::CUSTOM) {
                $dateRanges['range'] = [
                    $startDate,
                    $endDate,
                ];
            }
        }

        $column               ??= self::FILTER_COLUMN_DATE;
        $dateRanges['column'] = $column;

        $query = filterQueryDateBeforeRangeWithSubMonth($dateRanges, $query, $sellerId, $submonth);

        if ($isOrderBy) {
            return $query->orderByDesc(self::FILTER_COLUMN_DATE);
        }

        return $query;
    }
}
