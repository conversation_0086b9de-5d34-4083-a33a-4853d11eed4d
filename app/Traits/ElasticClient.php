<?php

namespace App\Traits;

use App\Enums\CacheKeys;
use App\Enums\CollectionModeEnum;
use App\Enums\ElasticProductStatusEnum;
use App\Enums\HomeListingEnum;
use App\Enums\PersonalizedType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\SortListingProductEnum;
use App\Http\Controllers\Storefront\ProductController;
use App\Models\BoughtTogetherLog;
use App\Models\Collection;
use App\Models\Product;
use App\Models\SellerCollection;
use App\Models\Store;
use App\Models\StoreCollection;
use App\Models\StoreProduct;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\StoreService;
use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;
use Exception;
use GuzzleHttp\RequestOptions;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Request;
use Modules\SellerAPI\Data\GetListingCampaignsData;
use Modules\SellerAPI\Data\IndexCampaignData;
use Throwable;

trait ElasticClient
{
    use ApiResponse;

    protected $instance = null;
    protected $instance2 = null;
    protected $response = null;
    protected $debug = false;
    protected $search = null;

    /**
     * @param string|null $function
     * @param array $params
     * @param null $otherHost
     * @param bool $useIndices
     * @param null $timeout
     * @return ClientBuilder|mixed|null
     * @throws Throwable
     */
    public function elastic(string $function = null, array $params = [], $otherHost = null, bool $useIndices = false, $timeout = null): mixed
    {
        if (empty($otherHost)) {
            $host = config('scout_elastic.client.hosts.elasticsearch');
            $host2 = config('scout_elastic.client.hosts.elasticsearch2');
        } else {
            $host = SystemConfig::getElasticLog();
        }
        if (is_null($timeout)) {
            $timeout = config('scout_elastic.timeout');
        }
        $connect_timeout = config('scout_elastic.connect_timeout');

        if (empty($host)) {
            return null;
        }
        $userAgent = sprintf(
            "elasticsearch-php/%s (PHP %s) / %s / %s",
            Client::VERSION,
            PHP_VERSION,
            config('senprints.server_info'),
            gethostname()
        );
        $connectionsParams = [
            'client' => [
                RequestOptions::TIMEOUT => $timeout,
                RequestOptions::CONNECT_TIMEOUT => $connect_timeout,
                RequestOptions::HEADERS => [
                    'User-Agent' => [$userAgent],
                ],
            ]
        ];
        $params = array_merge($params, $connectionsParams);
        if (is_null($this->instance)) {
            $this->instance = ClientBuilder::create()
                ->setConnectionParams($connectionsParams)
                ->setHosts([$host])
                ->build();
        }
        if (is_null($this->instance2) && !empty($host2['host']) && !empty($host2['port'])) {
            $this->instance2 = ClientBuilder::create()
                ->setConnectionParams($connectionsParams)
                ->setHosts([$host2])
                ->build();
        }
        if (!is_null($function)) {
            throw_if(!method_exists($this->instance, $function), Exception::class, "[Elasticsearch Client] Method '{$function}' not found");
            try {
                if ($this->debug) {
                    graylogInfo("[ElasticSearch Client] $function", [
                        'category' => 'elastic_query_log',
                        'user_type' => 'system',
                        'user_id' => currentUser() ? currentUser()->getUserId() : null,
                        'function' => $function,
                        'params' => $params,
                    ]);
                }
                if($useIndices) {
                    if ($function === 'create') {
                        if (!$this->instance->indices()->exists(['index' => $params['index']])) {
                            $result = $this->instance->indices()->$function($params);
                        } else {
                            $result = $this->elastic('count', $params);
                        }
                    }
                    else {
                        $result = $this->instance->indices()->$function($params);
                    }
                } else {
                    $result = $this->instance->{$function}($params);
                }
                if(!is_null($this->instance2) && !empty($host2) && !in_array($function, ['search', 'count'], true)) {
                    $this->instance2->{$function}($params);
                }
                if (empty($result) || !empty($result['errors'])) {
                    if (!empty($result['errors'])) {
                        logToDiscord("Elasticsearch: $function" . "\r\n" . "Result: " . json_encode($result) . "\r\n", 'error_elastic');
                    }
                    graylogInfo("Can not execute $function on elasticsearch, Query: " . json_encode($params) . ", Result: " . json_encode($result), [
                        'category' => 'elastic_sync_log',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action'  => $function,
                    ]);
                }
                return $result;
            } catch (Throwable $e) {
                logToDiscord(
                    "```" . "\r\n" .
                    "🔰 Elasticsearch: " . $function . "\r\n" .
                    "🔰 Exception: " . $e->getMessage() . "\r\n" .
                    "🔰 Error Code: " . $e->getCode() . "\r\n" .
                    "🔰 Host Name: " . gethostname() . "\r\n" .
                    "🔰 Host Detail: " . json_encode(Arr::get($host, 'host')) . "\r\n" .
                    '🔰 Server Info: ' . config('senprints.server_info') . PHP_EOL .
                    "```" . "\r\n",
                    'error_elastic',
                    true
                );
                graylogError("Elasticsearch: $function : " . $e->getMessage(), [
                    'category' => 'elastic_query_log',
                    'user_id' => currentUser() ? currentUser()->getUserId() : null,
                    'function' => $function,
                    'params' => $params,
                    'trace' => $e->getTraceAsString(),
                ]);
                return null;
            }
        }

        return $this->instance;
    }

    /**
     * @param $productType
     * @param int $limitResults
     * @return array|null
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function elasticPromotionSearchBySeller($productType, int $limitResults = 15, $index = null): ?array
    {
        $user                = currentUser();
        $sellerId            = $user->getUserId();
        $id                  = request()->get('id');
        $queryString         = [];
        $queryString['bool'] = [];
        $seller = currentUser()->getInfoAccess();
        $elasticSearchIndex = $index ?? $seller?->getElasticSearchIndex() ?? get_env('ELATICSEARCH_INDEX', 'products');

        if (empty($id)) {
            $query = request()->get('q');
            if(!empty($query)) {
                $queryCondition = &$queryString['bool'];

                $queryCondition['should'][]['match']    = [
                    'name' => [
                        'query'         => $query,
                        'fuzziness'     => 2,
                        'prefix_length' => 1,
                    ]
                ];
                $queryCondition['should'][]['match']    = [
                    'slug' => [
                        'query'         => $query,
                        'fuzziness'     => 2,
                        'prefix_length' => 1,
                    ]
                ];
                $queryCondition['minimum_should_match'] = 1;
            }
        } else {
            $queryString['bool']['filter'][]['term'] = [
                'id' => $id,
            ];
        }

        if ($productType !== ProductType::TEMPLATE && !$user->isAdmin()) {
            $queryString['bool']['filter'][]['term'] = [
                'seller_id' => $sellerId,
            ];
        }

        if ($productType === ProductType::TEMPLATE) {
            $queryString['bool']['filter'][]['bool']['must_not']['terms'] = [
                'system_type' => ['fulfill', 'fulfill_fba']
            ];
        }

        $queryString['bool']['filter'][]['term'] = [
            'product_type' => $productType,
        ];

        $queryString['bool']['filter'][]['term'] = [
            'status' => ProductStatus::ACTIVE
        ];

        $arr =  [
            'index' => $elasticSearchIndex,
            'body' => [
                'query' => $queryString,
                'size' => $limitResults,
                '_source' => [
                    'includes' => [
                        'id',
                        'name',
                        'thumb_url',
                    ]
                ],
            ],
            'scroll' => '1m'
        ];
        try {
            $this->response = $this->elastic('search', $arr);

            return $this->elasticResponse();
        } catch (Throwable $e) {
            logException($e);
            return null;
        }
    }

    /**
     * @param array $arrFilter
     * @param string $type
     * @param null $store
     * @return array
     */
    private function getQuerySearch(array $arrFilter = [], string $type = ProductType::CAMPAIGN, $store = null, $isGetFilter = false): array
    {
        /* @see docs/elasticsearch/filter_product.json */
        if (is_null($store)) {
            $store = StoreService::getCurrentStoreInfo();
        }

        if ($isGetFilter) {
            $type = ProductType::PRODUCT;
        }

        if (isset($arrFilter['s'])) {
            $this->search = $arrFilter['s'];
        }

        if (!$store->enable_search) {
            $arrFilter = Arr::except($arrFilter, ['s', 'sort']);
        }

        $query = $this->addFilterToQueryByStore($store);
        $isSearched = $store->market_place_listing;

        if (Arr::has($arrFilter, 'collection_slug')) {
            $query['bool']['filter'][]['term'] = [
                'collection_slugs.keyword' => Arr::get($arrFilter, 'collection_slug')
            ];
        }

        if (Arr::has($arrFilter, 'collection_id')) {
            $query['bool']['filter'][]['term'] = [
                'collection_ids' => Arr::get($arrFilter, 'collection_id')
            ];
        }

        if (Arr::has($arrFilter, 'exclude_personalized')) {
            $query['bool']['filter'][]['term'] = [
                'personalized' => PersonalizedType::NONE
            ];
        }

        if (Arr::has($arrFilter, 's')) {
            $search = Arr::get($arrFilter, 's');
            $searchText = cleanSpecialCharacters($search);

            $queryCondition = &$query['bool']['should']['function_score']['query']['bool'];
            $queryCondition['should'][]['match_phrase'] = [
                'campaign_name' => [
                    'query' => $search,
                    'boost' => 3,
                ]
            ];
            $queryCondition['should'][]['match_phrase'] = [
                'name' => [
                    'query' => $search,
                    'boost' => 2,
                ]
            ];
            $queryCondition['should'][]['match'] = [
                'campaign_name' => [
                    'query' => $search,
                ]
            ];

            // match exactly if search have number
            if (preg_match('/\d/', $search)) {
                // Filter with all words
                $queryCondition['should'][]['match']    = [
                    'search_text' => [
                        'query' => $searchText,
                    ]
                ];
            } else {
                // Filter with contains some few words
                $queryCondition['should'][]['match']    = [
                    'search_text' => [
                        'query'     => $searchText,
                        'fuzziness' => 1, // độ sai
                    ]
                ];
            }
            // if default product => boost
            $query['bool']['should']['function_score']['functions'][] = [
                'filter' => [
                    'range' => [
                        'default_product_id' => [
                            'gt' => 0,
                        ]
                    ]
                ],
                'weight' => 1.05,
            ];
            $query['bool']['should']['function_score']['score_mode'] = 'sum';
            $query['bool']['should']['function_score']['boost_mode'] = 'multiply';
            $isSearched = true;
            $type = ProductType::PRODUCT;
        }

        if (Arr::has($arrFilter, 'category_slug')) {
            $query['bool']['filter'][]['term'] = [
                'category_slugs.keyword' => Arr::get($arrFilter, 'category_slug')
            ];
            $type = ProductType::PRODUCT;
        }

        if (Arr::has($arrFilter, 'category_id')) {
            $query['bool']['filter'][]['term'] = [
                'category_ids.keyword' => Arr::get($arrFilter, 'category_id')
            ];
            $type = ProductType::PRODUCT;
        }

        if (Arr::has($arrFilter, 'color')) {
            $query['bool']['filter'][]['term'] = [
                'options.color.keyword' => Arr::get($arrFilter, 'color')
            ];
            $type = ProductType::PRODUCT;
        }

        if (Arr::has($arrFilter, 'template_ids')) {
            $query['bool']['filter'][]['terms'] = [
                'template_id' => Arr::get($arrFilter, 'template_ids')
            ];
            $type = ProductType::PRODUCT;
        }

        if (Arr::has($arrFilter, 'product')) {
            $stringProducts = Arr::get($arrFilter, 'product');
            preg_match_all('/\d+/',escapeSpecialCharacter($stringProducts, false), $arrProducts);

            $query['bool']['filter'][]['terms'] = [
                'template_id' => Arr::flatten($arrProducts)
            ];
            $type = ProductType::PRODUCT;
        }

        // correct example: 10-20
        if (Arr::has($arrFilter, 'filter_price')) {
            $rangePrice = explode('-', Arr::get($arrFilter, 'filter_price'));

            if (count($rangePrice) === 2) {
                $minPrice = (float)$rangePrice[0];
                $maxPrice = (float)$rangePrice[1];
                $query['bool']['filter'][]['range']['price'] = [
                    'gte' => $minPrice,
                    'lte' => $maxPrice
                ];
                $type = ProductType::PRODUCT;
            }
        }
        if (Arr::has($arrFilter, 'min_price')) {
            $minPrice = (float)Arr::get($arrFilter, 'min_price');
            $query['bool']['filter'][]['range']['price'] = [
                'gte' => $minPrice,
            ];
            $type = ProductType::PRODUCT;
        }
        if (Arr::has($arrFilter, 'max_price')) {
            $maxPrice = (float)Arr::get($arrFilter, 'max_price');
            $query['bool']['filter'][]['range']['price'] = [
                'lte' => $maxPrice,
            ];
            $type = ProductType::PRODUCT;
        }

        $sellerId = $store->seller_id;
        $nickname = $arrFilter['nickname'] ?? null;
        if (!empty($nickname)) {
            $seller = User::getUserBySlug($nickname);
            if (!empty($seller)) {
                $sellerId = $seller->id;
                $query['bool']['filter'][]['term'] = [
                    'seller_id' => $sellerId
                ];
            }
        }

        $query['bool']['filter'][]['term'] = [
            'status' => ProductStatus::ACTIVE
        ];

        // default newest
        $sort = ['created_at' => 'desc'];
        if ($store->random_popular) {
            $sort = ['created_at' => 'asc'];
        }

        $typeSort = Arr::get($arrFilter, 'sort');

        if (!in_array($typeSort, SortListingProductEnum::asArray())) {
            // default relevant when searching
            if (Arr::has($arrFilter, 's')) {
                $typeSort = SortListingProductEnum::RELEVANT;
            } else {
                // default featured when not found sort
                $typeSort = SortListingProductEnum::FEATURED;
            }
        }
        $arrayProductId = [];
        switch ($typeSort) {
            case SortListingProductEnum::RELEVANT:
                $sort = ['_score' => 'desc'];
                break;
            case SortListingProductEnum::OLDEST:
                $sort = ['created_at' => 'asc'];
                break;
            case SortListingProductEnum::HIGHEST_PRICE:
                $sort = ['price' => 'desc'];
                break;
            case SortListingProductEnum::LOWEST_PRICE:
                $sort = ['price' => 'asc'];
                break;
            case SortListingProductEnum::FEATURED:
                if (!Arr::has($arrFilter, 'collection_slug')) {
                    goto popular;
                }
                $collectionSlug = Arr::get($arrFilter, 'collection_slug');
                $collectionId = Collection::where('slug', $collectionSlug)->value('id');

                $typeListing = $this->setProductIdsAndGetTypeListing($collectionId, $arrayProductId);
                goto listing;
            case SortListingProductEnum::POPULAR:
                popular:
                $type = ProductType::PRODUCT;
                if ($this->checkShuffle()) {
                    $arrayProductId = topSellerTemplateIds($sellerId);
                    $condition      = 'not';
                    $column         = 'template_id';
                    goto listing;
                }
                $sort = ['score' => 'desc'] + $sort;
                break;
            case SortListingProductEnum::A_TO_Z:
                $sort = ['name.keyword' => 'asc'];
                break;
            case SortListingProductEnum::Z_TO_A:
                $sort = ['name.keyword' => 'desc'];
                break;
            case 'listing':
                listing:
                if (empty($condition)) {
                    $condition = 'should';
                }
                if (empty($column)) {
                    $column = 'id';
                }
                if (!empty($arrayProductId)) {
                    $typeListing ??= ProductType::PRODUCT;
                    [$query, $sort] = $this->getQuerySearchAndSortFilterByArrProductId(
                        $query,
                        $arrayProductId,
                        $typeListing,
                        $sort,
                        $condition,
                        $type,
                        $column,
                    );
                    // for not filter by product_type anymore
                    unset($type);
                }
                break;
        }

        if (isset($type)) {
            if ($isGetFilter) {
                $query['bool']['filter'][]['terms'] = [
                    'product_type' => [
                        ProductType::PRODUCT,
                        ProductType::CAMPAIGN
                    ]
                ];
            } else {
                $productType = $store->isExpressListing() ? ProductType::CAMPAIGN : $type;
                $query['bool']['filter'][]['term'] = [
                    'product_type' => $productType
                ];
            }
        }

        if ($this->checkShuffle($typeSort)) {
            $tempQuery                               = $query;
            $query                                   = [];
            $query['function_score']['boost_mode']   = 'replace';
            $query['function_score']['query']        = $tempQuery;
            $query['function_score']['random_score'] = [
                'seed'  => 10,
                'field' => '_seq_no',
            ];
        }

        if ($typeSort === SortListingProductEnum::POPULAR) {
            $tempQuery = $query;
            $query = [];
            $query['function_score']['boost_mode'] = 'replace';
            $query['function_score']['query'] = $tempQuery;
            $query['function_score']['functions'] = [
                [
                    'filter' => [
                        'term' => [
                            'store_ids' => $store->id
                        ]
                    ],
                    'weight' => 3,
                ],
                [
                    'filter' => [
                        'term' => [
                            'seller_id' => $sellerId
                        ]
                    ],
                    'weight' => 2,
                ],
                [
                    'filter' => [
                        'term' => [
                            'public_status' => ElasticProductStatusEnum::PUBLIC
                        ]
                    ],
                    'weight' => 1,
                ],
            ];
        }

        if ($isSearched) {
            // add sort by default
            $sort += ['default_product_id' => 'desc'];
        }
        return [$query, $sort];
    }

    /**
     * @param $query
     * @param $arrayProductId
     * @param $typeListing
     * @param array $oldSort
     * @param string $condition
     * @param string $productTypeToAppend
     * @param string $column
     * @return array
     */
    private function getQuerySearchAndSortFilterByArrProductId(
        $query,
        $arrayProductId,
        $typeListing,
        array $oldSort = [],
        string $condition = 'must',
        string $productTypeToAppend = ProductType::PRODUCT,
        string $column = 'id'
    ): array {
        $typeListing ??= ProductType::PRODUCT;
        // convert to integer for compare
        $arrayProductId = array_map('intval', $arrayProductId);

        if ($condition === 'should') {
            $groupCondition = &$query['bool']['filter'][]['bool'];
            $condition1                           = &$groupCondition['should'][]['bool']['must'];
            $condition1[]['terms']['id']          = $arrayProductId;
            $condition1[]['term']['product_type'] = $typeListing;

            $condition2 = &$groupCondition['should'][]['bool']['must'];
            $condition2[]['bool']['must_not']['terms']['default_product_id'] = $arrayProductId;
            $condition2[]['term']['product_type']                            = $productTypeToAppend;

            $groupCondition['minimum_should_match']                          = 1;
        } elseif($condition === 'must') {
            $query['bool']['filter'][]['terms']['id'] = $arrayProductId;
            $query['bool']['filter'][]['term']['product_type'] = $typeListing;
        }

        if (!empty($arrayProductId) && $arrayProductId !== [0]) {
            $arrIdSortBy        = array_flip($arrayProductId);
            $arrSort['_script'] = [
                'type'   => 'number',
                'script' => [
                    // get by id script
                    'id'     => "sort-by-data",
                    'params' => [
                        'field' => $column,
                        'data'  => $arrIdSortBy,
                    ],
                ],
                'order'  => 'asc',
            ];

            $newSort = array_merge($arrSort, ['_score' => 'desc'], $oldSort);
        } else {
            $newSort = array_merge(['_score' => 'desc'], $oldSort);
        }

        return [$query, $newSort];
    }

    /**
     * @param array $arrFilter
     * @param bool $isGetQuery
     * @param null $store
     * @return array|JsonResponse|LengthAwarePaginator
     */
    public function elasticListingProduct(array $arrFilter = [], $isGetQuery = false, $store = null)
    {
        /* @see docs/elasticsearch/search_campaign.txt */
        if (is_null($store)) {
            $store = StoreService::getCurrentStoreInfo();
        }
        $seller = $store->seller;
        try {
            [$query, $sort] = $this->getQuerySearch($arrFilter, store: $store);
        } catch (Throwable $e) {
            return [];
        }
        if (!$seller) {
            return [];
        }
        $hasKeyword = Arr::has($arrFilter, 's');
        $indexes = StoreService::getElasticSearchIndexes($seller, $store, $hasKeyword);
        $numberPerPage = Arr::get($arrFilter, 'limit', ProductController::NUMBER_PER_PAGE);
        $currentPage   = Arr::get($arrFilter, 'page');
        $currentPage   = max($currentPage, 1);
        $maxPage = $store->random_popular ? 3 : 10;
        $currentPage   = fmod($currentPage - 1, $maxPage) + 1; //return first 10 page to prevent crawl
        $querySearch = [
            'index' => $indexes,
            'body' => [
                'query' => $query,
                'size' => $numberPerPage,
                'from' => $numberPerPage * ($currentPage - 1),
                '_source' => [
                    'includes' => Product::getArrListingElastic()
                ],
                'sort' => $sort,
//                group by design_id
                'collapse' => [
                    'field' => 'design_id'
                ],
//                 this for count unique design cause total not correct anymore
                'aggs' => [
                    'unique_design' => [
                        'cardinality' => [
                            'field' => 'design_id'
                        ]
                    ]
                ]
            ]
        ];
        if($isGetQuery) {
            return $querySearch;
        }

        try {
            $this->response = $this->elastic('search', $querySearch);
            return $this->response;
        } catch (Throwable $e) {
            $storeInfo = StoreService::getCurrentStoreInfo();
            logToDiscord(
                'Listing Product Storefront failed'
                . "\r\nCurrent store: " . json_encode($storeInfo)
                . "\r\nQuery search: " . json_encode($querySearch)
                . "\r\nError: " . $e->getMessage(),
                'error_elastic',
            );
            return [];
        }
    }

    /**
     * @param string $sort
     * @return bool
     */
    private function checkShuffle(string $sort = HomeListingEnum::BEST_SELLER): bool
    {
        if (!in_array($sort, [
            SortListingProductEnum::POPULAR,
            HomeListingEnum::BEST_SELLER,
            HomeListingEnum::NEW_ARRIVALS
        ], true)) {
            return false;
        }
        $storeInfo = StoreService::getCurrentStoreInfo();
        if (!$storeInfo) {
            return false;
        }

        return $storeInfo->random_popular;
    }

    /**
     * @param $numberPerPage
     * @param $currentPage
     * @return \Illuminate\Http\JsonResponse|LengthAwarePaginator
     */
    private function elasticResponseListingProduct($numberPerPage, $currentPage)
    {
        if (is_null($this->response)) {
            return $this->errorResponse();
        }

        $elasticResponse = $this->response;

        if (empty($elasticResponse['hits']['hits'])) {
            return $this->errorResponse();
        }

        $arr = Arr::pluck($elasticResponse['hits']['hits'], '_source');

        $uri = preg_replace("/page=\d+/", '', Request::getRequestUri());

        // this total is not for count group by
//        $total = $elasticResponse["hits"]["total"]["value"];
        $total = $elasticResponse['aggregations']['unique_design']['value'];

        $products = new LengthAwarePaginator(
            $arr,
            $total,
            $numberPerPage,
            $currentPage,
            ['path' => $uri]
        );
        unset($elasticProducts, $elasticResponse);
        return $products;
    }

    /**
     * @param array $arrFilter
     * @param bool $isGetQuery
     * @param null $store
     * @return array|JsonResponse
     * @throws Throwable
     */
    public function elasticGetFilterProduct(array $arrFilter = [], bool $isGetQuery = false, $store = null)
    {
        if (is_null($store)) {
            $store = StoreService::getCurrentStoreInfo();
        }
        try {
            [$query, $sort] = $this->getQuerySearch($arrFilter, 'product', store: $store, isGetFilter: true);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
        $seller = $store->seller;
        if (!$seller) {
            return $this->errorResponse();
        }
        $hasKeyword = Arr::has($arrFilter, 's');
        $indexes = StoreService::getElasticSearchIndexes($seller, $store, $hasKeyword);
        // get list product match
        $numberProduct = 500;
        $querySearch   = [
            'index' => $indexes,
            'body'  => [
                'track_total_hits' => false,
                'query'            => $query,
                'size'             => $numberProduct,
                'sort'             => $sort,
                '_source'          => [
                    'includes' => '_id'
                ],
            ]
        ];
        $response = $this->elastic('search', $querySearch);
        if (is_null($response)) {
            return $this->errorResponse();
        }
        $ids = Arr::pluck($response['hits']['hits'], '_id');
        if (empty($ids)) {
            return $this->errorResponse();
        }
        $query                              = [];
        $query['bool']['filter'][]['terms'] = [
            '_id' => $ids
        ];

        // 0: don't include response product
        $numberSizeGroup = 0;

        // get sort collection
        $orderCollection = [];
        // design must unique to count
        $groupCollection['design_unique'] = [
            'terms' => [
                'field' => 'design_id'
            ]
        ];
        foreach ($sort as $key => $value) {
            if ($key === '_script') {
                continue;
            }
            $sortKey = 'sortBy' . $key;
            // get max value then order by that
            $field = ($key === '_score') ? 'script' : 'field';

            $groupCollection[$sortKey] = [
                'max' => [
                    $field => $key
                ]
            ];

            $orderCollection[] = [
                $sortKey => $value
            ];
        }
        // sort by other sort then count product in collection
        $orderCollection[] = ['_count' => 'desc'];

        $querySearch = [
            'index' => $indexes,
            'body'  => [
                'query' => $query,
                'aggs'  => [
                    'category_id_group'   => [
                        'terms' => [
                            'field' => 'category_ids',
                            'size'  => 1000
                        ]
                    ],
                    'color_group'         => [
                        'terms' => [
                            'field' => 'options.color.keyword',
                            'size'  => 70
                        ]
                    ],
                    'max_price'           => [
                        'max' => [
                            'field' => 'price'
                        ],
                    ],
                    'collection_id_group' => [
                        'terms' => [
                            'field' => 'collection_ids',
                            'size'  => 100,
                            'order' => $orderCollection
                        ],
                        'aggs'  => $groupCollection,
                    ],
                    'template_id_group'   => [
                        'terms' => [
                            'field' => 'template_id',
                            'size'  => 1000
                        ],
                    ],
                ],
                'size'  => $numberSizeGroup,
            ]
        ];
        if ($isGetQuery) {
            return $querySearch;
        }

        try {
            $this->response = $this->elastic('search', $querySearch);
            return $this->elasticResponseFilterProduct();
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    /**
     * @return array|\Illuminate\Http\JsonResponse
     */
    private function elasticResponseFilterProduct()
    {
        if (is_null($this->response)) {
            return $this->errorResponse();
        }

        $elasticResponse = $this->response;
        $array = [];

        $array['category_id_group'] = Arr::pluck(
            $elasticResponse['aggregations']['category_id_group']['buckets'],
            'key'
        );

        $arrCollectionId = [];
        foreach ($elasticResponse['aggregations']['collection_id_group']['buckets'] as $each) {
//             remove collection have one product or all product belong to one campaign
            if ($each['doc_count'] > 1 && count($each['design_unique']['buckets']) > 1) {
                $arrCollectionId[] = $each['key'];
            }
        }
        $array['collection_group'] = [];
        if (!empty($arrCollectionId)) {
            $collections = Collection::query()
                ->select('id', 'name', 'slug')
                ->whereIn('id', $arrCollectionId)
                ->orderByRaw('FIELD(id, ' . implode(',', $arrCollectionId) . ') desc')
                ->get()
                ->keyBy('id');

            foreach ($arrCollectionId as $id) {
                if (!isset($collections[$id])) {
                    continue;
                }

                $collection = $collections[$id];
                $array['collection_group'][] = [
                    'id'   => $id,
                    'name' => $collection->name,
                    'slug' => $collection->slug,
                ];

                $keyword = $this->search;
                if (!empty($keyword)) {
                    usort($array['collection_group'], static function ($a, $b) use ($keyword) {
                        $search = strtolower($keyword);
                        $aName  = strtolower($a['name']);
                        $bName  = strtolower($b['name']);

                        similar_text($search, $aName, $percA);
                        similar_text($search, $bName, $percB);

                        return $percB <=> $percA;
                    });
                }

                $array['collection_group'] = array_slice($array['collection_group'], 0, 10);
            }
        }

        $array['max_price'] = $elasticResponse['aggregations']['max_price']['value'];
        $array['min_price'] = 0;
        $array['color_group'] = Arr::pluck($elasticResponse['aggregations']['color_group']['buckets'], 'key');
        $array['template_id_group'] = Arr::pluck($elasticResponse['aggregations']['template_id_group']['buckets'], 'key');

        unset($elasticResponse);
        return $array;
    }

    /**
     * @param string $type
     * @param $store
     * @param $collectionId
     * @return array
     * @throws Exception
     */
    public function elasticHomeListingProductsRaw(string $type, $store, $collectionId = null): array
    {
        $cache = cacheAlt();
        if (empty($store)) {
            return [];
        }
        if ($store->id) {
            $tag = CacheKeys::getStoreId($store->id);
            $cache->tags([$tag]);
        }

        return $cache
            ->remember(
            CacheKeys::getHomeListingType($type, $store->id, $collectionId),
            CacheKeys::CACHE_24H,
            function () use ($type, $store, $collectionId) {
                try {
                    $sort  = ['created_at' => 'desc'];
                    $limit = 8;
                    $queryFilter['bool']['filter'][]['term'] = [
                        'status' => ProductStatus::ACTIVE
                    ];
                    $arrayProductId = [];
                    $seller = $store->seller;
                    if (!$seller) {
                        return [];
                    }
                    $indexes = StoreService::getElasticSearchIndexes($seller, $store);
                    switch ($type) {
                        case HomeListingEnum::FEATURED:
                            $collectionId ??= $store->feature_collection_id;
                            if (empty($collectionId)) {
                                return [];
                            }
                            $queryFilter = $this->addFilterToQueryByStore(
                                $store,
                                $queryFilter,
                                true
                            );

                            $queryFilter['bool']['filter'][]['term'] = [
                                'collection_ids' => $collectionId
                            ];
                            $typeListing = $this->setProductIdsAndGetTypeListing(
                                $collectionId,
                                $arrayProductId
                            );

                            [$query, $sort] = $this->getQuerySearchAndSortFilterByArrProductId(
                                $queryFilter,
                                $arrayProductId,
                                $typeListing,
                                $sort,
                                'should',
                                ProductType::CAMPAIGN
                            );
                            break;
                        case HomeListingEnum::NEW_ARRIVALS:
                            if ($store->new_arrival_collection_id === CollectionModeEnum::DISABLED) {
                                return [];
                            }
                            if ($store->new_arrival_collection_id === CollectionModeEnum::AUTO) {
                                $queryFilter = $this->addFilterToQueryByStoreV2($store, $queryFilter);
                                $queryFilter['bool']['filter'][]['term'] = [
                                    'product_type' => ProductType::CAMPAIGN
                                ];
                            } else {
                                [$query, $sort] = $this->listingByCollectionId(
                                    $store->new_arrival_collection_id,
                                    $queryFilter,
                                    $sort
                                );
                            }
                            break;
                        case HomeListingEnum::BEST_SELLER:
                            if ($store->best_seller_collection_id === CollectionModeEnum::DISABLED) {
                                return [];
                            }
                            $sort = ['_score' => 'desc'];
                            $productType = $store->isExpressListing() ? ProductType::CAMPAIGN : ProductType::PRODUCT;
                            if ($store->best_seller_collection_id === CollectionModeEnum::AUTO) {
                                if ($this->checkShuffle() && !empty($store->id)) {
                                    $query['function_score']['boost_mode']   = 'replace';
                                    $query['function_score']['functions'][]['random_score'] = [
                                        'seed'  => 10,
                                        'field' => '_seq_no',
                                    ];
                                    if ($store->id !== Store::SENPRINTS_STORE_ID) {
                                        $ids = StoreProduct::query()
                                            ->select('product_id')
                                            ->where('store_id', $store->id)
                                            ->filterAddedCampaign()
                                            ->pluck('product_id')
                                            ->toArray();
                                        $queryFilter['bool']['filter'][]['terms'] = [
                                            'campaign_id' => $ids
                                        ];
                                    }
                                } else {
                                    $queryFilter = $this->addFilterToQueryByStoreV2($store, $queryFilter);
                                }
                                $queryFilter['bool']['filter'][]['term'] = [
                                    'product_type' => $productType
                                ];
                            } else {
                                [$query, $sort] = $this->listingByCollectionId(
                                    $store->best_seller_collection_id,
                                    $queryFilter,
                                    $sort,
                                    $productType
                                );
                            }
                            break;
                    }

                    if (($store->new_arrival_collection_id === CollectionModeEnum::AUTO && $type === HomeListingEnum::NEW_ARRIVALS) || ($store->best_seller_collection_id === CollectionModeEnum::AUTO && $type === HomeListingEnum::BEST_SELLER)) {
                        $query['function_score']['query'] = $queryFilter;
                        $query['function_score']['functions'] = array_merge([
                            [
                                'filter' => [
                                    'term' => [
                                        'store_ids' => $store->id
                                    ]
                                ],
                                'weight' => 3,
                            ],
                            [
                                'filter' => [
                                    'term' => [
                                        'seller_id' => $store->seller_id
                                    ]
                                ],
                                'weight' => 2,
                            ],
                            [
                                'filter' => [
                                    'term' => [
                                        'public_status' => ElasticProductStatusEnum::PUBLIC
                                    ]
                                ],
                                'weight' => 1,
                            ],
                        ], $query['function_score']['functions'] ?? []);
                    }

                    $querySearch    = [
                        'index' => $indexes,
                        'body'  => [
                            'track_total_hits' => false,
                            'query'            => $query ?? $queryFilter,
                            'size'             => $limit,
                            '_source'          => [
                                'includes' => Product::getArrListingElastic()
                            ],
                            'sort'             => $sort,
                            'collapse'         => [
                                'field' => 'design_id'
                            ],
                        ]
                    ];

                    $this->response = $this->elastic('search', $querySearch);

                    $arr = $this->elasticResponse();
                    if (is_null($arr)) {
                        return [];
                    }

                    // if not enough featured will append new_arrivals
                    if ($type === HomeListingEnum::FEATURED && count($arr) < $limit) {
                        $excludeIds  = array_column($arr, 'id');
                        $newArrivals = $this->elasticHomeListingProductsRaw(
                            HomeListingEnum::NEW_ARRIVALS,
                            $store
                        );
                        $numberNeedMore = $limit - count($arr);
                        foreach ($newArrivals as $each) {
                            if (!in_array($each['id'], $excludeIds)) {
                                $arr[] = $each;
                                $numberNeedMore--;
                            }
                            if ($numberNeedMore === 0) {
                                break;
                            }
                        }
                    }

                    if ($this->checkShuffle()) {
                        shuffle($arr);
                    }

                    return $arr;
                } catch (Throwable $e) {
                    logToDiscord(
                        'Listing Home Product Storefront failed'
                        . "\r\nException: ". $e->getMessage()
                        . "\r\nType: " . $type
                        . "\r\nCurrent store Id: " . json_encode($store->id),
                        'error_elastic',
                    );
                    return [];
                }
            });
    }

    /**
     * @param $collectionId
     * @param $productIds
     * @return string|null
     */
    private function setProductIdsAndGetTypeListing($collectionId, &$productIds): ?string
    {
        if (empty($collectionId)) {
            return null;
        }

        $storeInfo = StoreService::getCurrentStoreInfo();
        if (!$storeInfo) {
            return null;
        }
        $featureIds = SellerCollection::where('seller_id', $storeInfo->seller_id)
            ->where('collection_id', $collectionId)
            ->value('feature_ids');
        if (!empty($featureIds)) {
            $productIds  = explode(',', $featureIds);

            return ProductType::CAMPAIGN;
        }

        return null;
    }

    /**
     * @return array|null
     */
    private function elasticResponse(): ?array
    {
        if (is_null($this->response)) {
            return null;
        }

        $elasticResponse = $this->response;
        if (empty($elasticResponse['hits']['hits'])) {
            return [];
        }

        return Arr::pluck($elasticResponse['hits']['hits'], '_source');
    }

    /**
     * @param $store
     * @param array $query
     * @param bool $isFeatured
     * @return array
     */
    private function addFilterToQueryByStore($store, array $query = [], bool $isFeatured = false): array
    {
        if (!is_null($store)) {
            if ($store->id !== Store::SENPRINTS_STORE_ID) {
                $storeId = $store->id;
                $sellerId = $store->seller_id;
                if (!$isFeatured && !$store->list_all_my_campaigns) {
                    $queryCondition                         = &$query['bool']['filter'][]['bool'];
                    $queryCondition['should'][]['term']     = [
                        'store_ids' => $storeId
                    ];
                    $queryCondition['should'][]['terms']    = [
                        'collection_ids' => Arr::pluck($store['collections'], 'collection_id')
                    ];
                    $queryCondition['minimum_should_match'] = 1;
                }

                if (!$store->market_place_listing) {
                    $query['bool']['filter'][]['term'] = [
                        'seller_id' => $sellerId
                    ];
                } else {
                    $queryCondition = &$query['bool']['filter'][]['bool'];
                    $queryCondition['should'][]['term'] = [
                        'public_status' => ElasticProductStatusEnum::PUBLIC
                    ];
                    $queryCondition['should'][]['term'] = [
                        'seller_id' => $sellerId
                    ];
                    $queryCondition['minimum_should_match'] = 1;
                }
            } else { // if is SenPrints
                $query['bool']['filter'][]['term'] = [
                    'public_status' => ElasticProductStatusEnum::PUBLIC
                ];
            }
        }

        return $query;
    }

    private function addFilterToQueryByStoreV2(Store $store, array $query = []): array
    {
        $storeId = $store->id;
        if ($storeId !== Store::SENPRINTS_STORE_ID) {
            $sellerId = $store->seller_id;
            $queryCondition = &$query['bool']['filter'][]['bool'];
            $queryCondition['should'][]['term'] = [
                'store_ids' => $storeId
            ];

            if ($store->market_place_listing) {
                $queryCondition['should'][]['term'] = [
                    'public_status' => ElasticProductStatusEnum::PUBLIC
                ];
            }

            if ($store->list_all_my_campaigns) {
                $queryCondition['should'][]['term'] = [
                    'seller_id' => $sellerId
                ];
            }
        } else {
            $query['bool']['filter'][]['term'] = [
                'public_status' => ElasticProductStatusEnum::PUBLIC
            ];
        }
        return $query;
    }

    /**
     * Delete products by productIds or campaignIds
     *
     * @param null $productIds
     * @param null $index
     * @param bool $delete
     * @return bool
     * @throws Throwable
     */
    public function elasticDeleteProductsByProductIds($productIds = null, $index = null, $delete = true): bool
    {
        if (empty($productIds)) {
            return false;
        }
        // Convert all kinds type to array
        if (!is_array($productIds)) {
            $productIds = (array) $productIds;
        }
        $productIds = array_filter($productIds, function($id) {
            return $id > 0;
        });
        if (empty($productIds)) {
            return false;
        }

        if (!empty($index) && !str_starts_with($index, 'products')) {
            return false;
        }
        if (empty($index)) {
            $index = 'products*'; // delete all index has prefix is "products"
        }

        try {
            $query                                  = [];
            $queryCondition                         = &$query['bool'];
            $queryCondition['should'][]['terms']    = [
                'id' => $productIds
            ];
            $queryCondition['should'][]['terms']    = [
                'campaign_id' => $productIds
            ];
            $queryCondition['minimum_should_match'] = 1;
            graylogInfo("Deleted products on elasticsearch.", [
                'category' => 'elastic_sync_log',
                'user_type' => 'system',
                'action'  => "deleteByQuery",
                'index_es'  => $index,
                "product_ids" => json_encode($productIds),
                "total_product" => count($productIds),
                "query_es" => json_encode($query),
                "response_es" => json_encode($this->response),
            ]);
            if ($delete) {
                $this->response = $this->elastic('deleteByQuery', [
                    'index'     => $index,
                    'refresh'   => true,
                    'conflicts' => 'proceed',
                    'body'      => [
                        'query' => $query
                    ]
                ]);
                return $this->response && isset($this->response['deleted']);
            }
            return true;
        } catch (\Exception $e) {
            logToDiscord(
                __FUNCTION__
                . "\r\nException: " . $e->getMessage()
                . "\r\nProductIds: " . json_encode($productIds)
                . "\r\nQuery: " . json_encode($queryCondition),
                'error_elastic',
            );
            return false;
        }
    }

    /**
     * @return bool|null
     * @throws Throwable
     */
    public function elasticPing(): ?bool
    {
        return $this->elastic('ping');
    }

    /**
     * @param array $filters
     * @param array $dateRange
     * @return int
     * @throws Throwable
     */
    public function elasticCount(array $filters = [], array $dateRange = [], ?string $index = 'products*'): int
    {
        try {
            $arrFilter = [];
            $query = [];
            if (empty($filters)) {
                $query = ['match_all' => (object)[]];
            } else {
                foreach ($filters as $field => $value) {
                    $fields = explode('.', $field);

                    // if columns is from database, skip
                    // ex: sales_reports.type
                    if (count($fields) > 1) {
                        continue;
                    }

                    if ($field === 'store_id') {
                        $field = 'store_ids';
                    }
                    $arrFilter[] = [
                        'term' => [
                            $field => $value
                        ]
                    ];
                }
            }
            if (!empty($dateRange)) {
                $dateRanges = filterQueryByDateRange($dateRange);
                if (is_array($dateRanges)) {
                    $arrFilter[]['range']['created_at'] = [
                        'gte' => $dateRanges[0]->format('Y-m-d\TH:i:s'),
                        'lte' => $dateRanges[1]->format('Y-m-d\TH:i:s')
                    ];
                } elseif (!is_null($dateRanges)) {
                    $arrFilter[]['range']['created_at'] = [
                        'gte' => $dateRanges->format('Y-m-d\TH:i:s'),
                    ];
                }
            }
            if (!empty($arrFilter)) {
                $query = [
                    'bool' => [
                        'filter' => $arrFilter,
                    ]
                ];
            }
            // count all product
            $query = [
                'index' => $index,
                'body'  => [
                    'query' => $query
                ]
            ];
            $this->response = $this->elastic('count', $query);
            return (int) $this->response['count'];
        } catch (Exception $ex) {
            return 0;
        }
    }

    /**
     * @param array $campaignIds
     * @param array $arrFilter
     * @param $typeSort
     * @param $limit
     * @param bool $disableCollapse
     * @param bool $isSameCollection
     * @return array
     * @throws Throwable
     */
    public function elasticGetProductByBundleDiscount(array $campaignIds, array $arrFilter, $typeSort, $limit, bool $disableCollapse = false, bool $isSameCollection = false): array
    {
        $queryString = [];
        $store = StoreService::getCurrentStoreInfo();
        $queryString['bool']['filter'][]['term'] = [
            'status' => 'active'
        ];

        $productType = $store->isExpressListing() ? ProductType::CAMPAIGN : ProductType::PRODUCT;
        if (!empty($arrFilter['product_type'])) {
            $productType = $arrFilter['product_type'];
        }
        $queryString['bool']['filter'][]['term'] = [
            'product_type' => $productType
        ];
        if ($store->id !== Store::SENPRINTS_STORE_ID && !$store->market_place_upsell) {
            $queryString['bool']['filter'][]['term'] = [
                'seller_id' => $store->seller_id
            ];
        } else {
            $queryCondition = &$queryString['bool']['filter'][]['bool'];
            $queryCondition['should'][]['term'] = [
                'seller_id' => $store->seller_id
            ];
            $queryCondition['should'][]['term'] = [
                'public_status' => ElasticProductStatusEnum::PUBLIC
            ];
            $queryCondition['minimum_should_match'] = 1;
        }

        if (!empty($arrFilter['exclude_campaign_ids'])) {
            $queryString['bool']['must_not'][]['terms'] = [
                'campaign_id' => array_values($arrFilter['exclude_campaign_ids']),
            ];
        }

        if (!empty($arrFilter['exclude_ids'])) {
            $queryString['bool']['must_not'][]['terms'] = [
                'id' => array_values($arrFilter['exclude_ids']),
            ];
        }

        if (!empty($arrFilter['exclude_template_ids'])) {
            $queryString['bool']['must_not'][]['terms'] = [
                'template_id' => array_values($arrFilter['exclude_template_ids']),
            ];
        }
        foreach ($arrFilter as $key => $value) {
            switch ($key) {
                case 'campaign_ids':
                    $queryString['bool']['filter'][] = [
                        'terms' => ['campaign_id' => $value]
                    ];
                    break;
                case 'campaign_id':
                    $queryString['bool']['filter'][] = [
                        'term' => ['campaign_id' => $value]
                    ];
                    break;
                case 'template_ids':
                    $queryString['bool']['filter'][] = [
                        'terms' => ['template_id' => $value]
                    ];
                    break;
                case 'collection_ids':
                    $queryString['bool']['must'][] = [
                        'terms' => ['collection_ids' => $value]
                    ];
                    break;
                case 'collection_id':
                    $queryString['bool']['filter'][] = [
                        'term' => ['collection_ids' => $value]
                    ];
                    break;
                case 'product_ids':
                    $queryString['bool']['must'][] = [
                        'terms' => ['id' => $value]
                    ];
                    break;
                case 'product_id':
                    $queryString['bool']['filter'][] = [
                        'term' => ['id' => $value]
                    ];
                    break;
            }
        }
        $sort = [];
        switch ($typeSort) {
            case 'auto':
                // get product bought together
                $arrayProductId = BoughtTogetherLog::query()
                    ->whereIn('campaign_id1', $campaignIds)
                    ->where('timestamp', '>=', now()->subDays(30))
                    ->groupBy('product_id2')
                    ->orderByRaw('count(product_id2) desc')
                    ->get('product_id2')
                    ->pluck('product_id2')
                    ->toArray();
                if (!empty($arrayProductId)) {
                    [$queryString, $sort] = $this->getQuerySearchAndSortFilterByArrProductId(
                        $queryString,
                        $arrayProductId,
                        ProductType::PRODUCT,
                        [],
                        'should'
                    );
                    $sort += ['score' => 'desc'];
                    break;
                }
                break;
            case 'best_seller':
                $sort = ['score' => 'desc'];
                break;
            case 'newest':
                $sort = ['created_at' => 'desc'];
                break;
        }
        $query = [
            'index' => StoreService::getElasticSearchIndexes($store->seller, $store),
            'body'  => [
                'track_total_hits' => false,
                'query'            => $queryString,
                'size'             => $limit,
                '_source'          => [
                    'includes' => Product::getArrListingElastic()
                ],
                'sort'             => $sort,
            ]
        ];
        if(!$disableCollapse) {
            $query['body']['collapse'] = [
                'field' => 'design_id'
            ];
        }

        if ($isSameCollection && !empty($query['body']['query']) && !empty($arrFilter['collection_ids'])) {
            $collectionIds = $arrFilter['collection_ids'];
            $queryMain = $query['body']['query'];
            unset($query['body']['query']);
            $functionScore = [];
            $countCollection = count($collectionIds);
            $mainScoreCondition = [];
            $subScoreConditions = [];
            foreach ($collectionIds as $collectionId) {
                $mainScoreCondition [] = [
                    'term' => [
                        'collection_ids' => $collectionId
                    ]
                ];

                $subScoreConditions [] = [
                    'filter' => [
                        'terms' => [
                            'collection_ids' => [$collectionId]
                        ]
                    ],
                    'weight' => 1
                ];
            }
            $functionScore [] = [
                'filter' => [
                    'bool' => [
                        'must' => $mainScoreCondition
                    ]
                ],
                'weight' => $countCollection
            ];
            foreach ($subScoreConditions as $condition) {
                $functionScore [] = $condition;
            }
            $query['body']['query']['function_score'] = [
                'query' => $queryMain,
                'functions' => $functionScore,
                'score_mode' => 'sum',
                'boost_mode' => 'multiply'
            ];
            unset($query['body']['sort']);
            $query['body']['sort'] = [
                '_score' => ['order' => 'desc']
            ];
        }
        $result = $this->elastic('search', $query);
        $arr    = Arr::get($result, 'hits.hits', []);
        return Arr::pluck($arr, '_source');
    }

    /**
     * @param array $filters
     * @param int $size
     * @return array
     */
    public function elasticGetProductUpsell(array $filters, int $size): array
    {
        $indexes = StoreService::getElasticSearchIndexes($this->currentStore->seller, $this->currentStore ?? StoreService::getCurrentStoreInfo());
        if (empty($indexes)) {
            $indexes = get_env('ELATICSEARCH_INDEX', 'products');
        }
        $query = [
            'index' => $indexes,
            'body'  => [
                'track_total_hits' => false,
                'query' => $filters,
                '_source' => [
                    'includes' => Product::getArrListingElastic()
                ],
                'size' => $size,
                'collapse' => [
                    'field' => 'design_id'
                ],
                // get default product first
                'sort' => [
                    '_score' => 'desc',
                    'score' => 'desc',
                ]
            ]
        ];
        try {
            $response = $this->elastic('search', $query);
        } catch (Throwable $e) {
            logToDiscord(
                "```" . "\r\n" .
                "🔰 Function: elasticGetProductUpsell\r\n" .
                "🔰 Exception: {$e->getMessage()}" .
                "```" . "\r\n",
                'error_elastic',
            );
            graylogError("Elasticsearch: " . $e->getMessage(), [
                'category' => 'elastic_query_log',
                'user_id' => currentUser() ? currentUser()->getUserId() : null,
                'function' => __FUNCTION__,
                'params' => json_encode($filters),
                'trace' => $e->getTraceAsString(),
            ]);
            return [];
        }

        if (is_null($response)) {
            return [];
        }

        $data = $response['hits']['hits'];
        if (empty($data)) {
            return [];
        }

        return Arr::pluck($data, '_source');
    }

    /**
     * @param array $filters
     * @param array $options
     * @param $limit
     * @return array
     * @throws Throwable
     */
    public function elasticGetCountCampaignBySeller(array $filters, array $options, $limit): array
    {
        $query = [
            'index' => get_env('ELATICSEARCH_INDEX', 'products') . '*',
            'body'  => [
                'track_total_hits' => false,
                'query' => $filters,
                'aggs' => [
                    'count_campaign_by_seller' => [
                        'terms' => [
                            'field' => 'seller_id',
                        ]
                    ]
                ],
                'size' => 0,
            ]
        ];
        if(!empty($options['is_sort_by_campaigns'])){
            $query['body']['aggs']['count_campaign_by_seller']['aggs'] = [
                'bucket_sort' => [
                    'bucket_sort' => [
                        'sort' => [
                            '_count' => [
                                'order' => $options['direction']
                            ]
                        ],
//                        'from' => $options['offset'],
                        'size' => $options['page'] * $limit,
                    ]
                ]
            ];
        } else {
            $query['body']['aggs']['count_campaign_by_seller']['terms']['size'] = $limit;
        }

        $response = $this->elastic('search', $query);

        if (is_null($response)) {
            return [];
        }

        $data = Arr::get($response, 'aggregations.count_campaign_by_seller.buckets');
        $arr = [];
        foreach ($data as $each){
            $arr[] = [
                'seller_id' => $each['key'],
                'campaigns' => $each['doc_count'],
            ];
        }

        return $arr;
    }

    /**
     * @param array $query
     * @param array $body
     * @param bool $includeTotal
     * @param bool $searchAllIndex
     * @param string|null $index
     * @return array|null
     * @throws Throwable
     */
    public function elasticGetProductAndTotal(array $query, array $body, bool $includeTotal = true, bool $searchAllIndex = false, ?string $index = null): ?array
    {
        $searchQuery = [
            'index' => $index ?? get_env('ELATICSEARCH_INDEX', 'products') . ($searchAllIndex ? '*' : null),
            'body'  => $body
        ];

        $this->response = $this->elastic('search', $searchQuery);

        $response = $this->elasticResponse();

        if (empty($response) && $includeTotal) {
            return [[], 0];
        }

        if ($includeTotal) {
            $total = $this->elasticGetTotal($query, $searchAllIndex, index: $index);

            return [$response, $total];
        }

        return $response ?? [];
    }

    /**
     * @param array $query
     * @param bool $searchAllIndex
     * @param string|null $index
     * @return int
     * @throws Throwable
     */
    public function elasticGetTotal(array $query, bool $searchAllIndex = false, ?string $index = null): int
    {
        $searchQuery = [
            'index' => $index ?? get_env('ELATICSEARCH_INDEX', 'products') . ($searchAllIndex ? '*' : null),
            'body'  => [
                'query' => $query
            ]
        ];
        $response = $this->elastic('count', $searchQuery);

        return Arr::get($response, 'count', 0);
    }

    /**
     * @param array $body
     * @return array
     * @throws Throwable
     */
    public function elasticGetProductFeed(array $body, bool $searchAllIndex = false, ?string $index = null): array
    {
        $searchQuery = [
            'index' => $index ?? get_env('ELATICSEARCH_INDEX', 'products') . ($searchAllIndex ? '*' : null),
            'body'  => $body
        ];

        $this->response = $this->elastic('search', $searchQuery) ?? [];

        return $this->elasticResponse();
    }

    /**
     * @param $products
     * @return array
     */
    private function getCampaignIds($products): array
    {
        $arr = [];
        foreach ($products as $product) {
            if ($product['product_type'] === ProductType::PRODUCT) {
                $arr[] = $product['campaign_id'];
            } elseif ($product['product_type'] === ProductType::CAMPAIGN) {
                $arr[] = $product['id'];
            }
        }
        return $arr;
    }

    /**
     * @param array $body
     * @param int $timeout
     * @return array
     * @throws Throwable
     */
    public function elasticGetOptionsSizesAndColors(array $body, $timeout = 120): array
    {
        $searchQuery = [
            'index' => get_env('ELATICSEARCH_INDEX', 'products'),
            'body'  => $body
        ];

        $response = $this->elastic('search', $searchQuery, timeout: $timeout);

        $arr = [];
        $arr['colors'] = Arr::pluck(Arr::get($response, 'aggregations.color_group.buckets', []), 'key');

        $arr['sizes'] = Arr::pluck(Arr::get($response, 'aggregations.size_group.buckets', []), 'key');

        return $arr;
    }

    /**
     * @param array $body
     * @param bool $searchAllIndex
     * @return array|null
     * @throws Throwable
     */
    public function elasticGetCampaignById(array $body, bool $searchAllIndex = false): ?array
    {
        // get total
        $searchQuery = [
            'index' => env('ELATICSEARCH_INDEX', 'products') . ($searchAllIndex ? '*,-products_archived' : null),
            'body'  => $body,
        ];
        $this->response = $this->elastic('search', $searchQuery);
        $response = $this->elasticResponse();
        return $response[0]??[];
    }

    /**
     * @param array $query
     * @param array $body
     * @return array|null
     * @throws Throwable
     */
    public function elasticGetProductsByCustomConditions(array $body): ?array
    {
        // get total
        $searchQuery = [
            'index' => env('ELATICSEARCH_INDEX', 'products'),
            'body'  => $body,
        ];
        $this->response = $this->elastic('search', $searchQuery);
        $response = $this->elasticResponse();
        return $response??[];
    }

    /**
     * @param $sellerId
     * @param string $type
     * @param $store
     * @param $collectionId
     * @param $categoryId
     * @param string $keywords
     * @return array
     * @throws Exception
     */
    public function elasticAffiliateListingProductsRaw($sellerId, $store, $collectionId = null, $categoryId = null, $keywords = ''): array
    {
        return cache()
            ->remember(
            CacheKeys::getAffiliateListingType($sellerId, $keywords, $store->id, $collectionId, $categoryId),
            CacheKeys::CACHE_1H,
            function () use ($sellerId, $keywords, $store, $collectionId, $categoryId) {
                try {
                    $limit = 8;
                    $queryFilter['bool']['filter'][]['term'] = [
                        'status' => ProductStatus::ACTIVE
                    ];

                    $queryFilter['bool']['must'][]['term'] = [
                        'store_ids' => $store->id
                    ];

                    $queryFilter['bool']['filter'][]['range'] = [
                        'id' => [
                            'gte' => 1
                        ]
                    ];

                    $queryFilter['bool']['should'][]['exists'] = [
                        'field' => 'campaign_name'
                    ];

                    if ($store->isExpressListing()) {
                        $queryFilter['bool']['filter'][]['term'] = [
                            'product_type' => ProductType::CAMPAIGN
                        ];
                    } else {
                        $queryFilter['bool']['filter'][]['term'] = [
                            'product_type' => ProductType::PRODUCT
                        ];
                    }

                    if (!empty ($keywords)) {
                        $keywords = explode(',', $keywords);
                        foreach ($keywords as $keyword) {
                            $queryFilter['bool']['should'][]['wildcard'] = [
                                'campaign_name' => "*$keyword*"
                            ];
                        }
                    }
                    $bestSellerQueryAllow = false;
                    if (isset($collectionId)) {
                        $queryFilter['bool']['filter'][]['term'] = [
                            'collection_ids' => $collectionId
                        ];
                    }else{
                        $bestSellerQueryAllow = true;
                    }

                    if (isset($categoryId)) {
                        $queryFilter['bool']['filter'][]['term'] = [
                            'category_ids' => $categoryId
                        ];
                    } else {
                        $bestSellerQueryAllow = true;
                    }

                    if ($bestSellerQueryAllow) {
                        if ($store->best_seller_collection_id === CollectionModeEnum::AUTO) {
                            if ($store->id !== Store::SENPRINTS_STORE_ID) {
                                $ids = StoreProduct::query()
                                    ->select('product_id')
                                    ->where('store_id', $store->id)
                                    ->filterAddedCampaign()
                                    ->pluck('product_id')
                                    ->toArray();
                                if (!empty($ids)) {
                                    $queryFilter['bool']['filter'][]['terms'] = [
                                        'campaign_id' => $ids
                                    ];
                                } else {
                                    self::loadConditionForTopPopularityCollection($store->id, $queryFilter);
                                }
                            } else {
                                self::loadConditionForTopPopularityCollection($store->id, $queryFilter);
                            }
                        } else if ($store->best_seller_collection_id === CollectionModeEnum::DISABLED) {
                            self::loadConditionForTopPopularityCollection($store->id, $queryFilter);
                        } else {
                            $queryFilter['bool']['should'][]['term'] = [
                                'collection_ids' => $store->best_seller_collection_id
                            ];

                        }
                    }

                    if ($store->id === Store::SENPRINTS_STORE_ID) {
                        $queryFilter['bool']['should'][]['term'] = [
                            'seller_id' => $sellerId
                        ];
                    }  else {
                        $queryFilter['bool']['filter'][]['term'] = [
                            'seller_id' => $sellerId
                        ];
                    }
                    $sort = ['_score' => 'desc'];

                    $querySearch    = [
                        'body'  => [
                            'track_total_hits' => false,
                            'query'            => $query ?? $queryFilter,
                            "collapse" => [
                                "field" => "campaign_id"
                            ],
                            'size'             => $limit,
                            '_source'          => [
                                'includes' => [
                                    'slug',
                                    'campaign_name',
                                    'thumb_url',
                                    'collection_ids',
                                    'category_ids',
                                    'currency_code',
                                    'price',
                                    'product',
                                    'personalized',
                                    'popularity',
                                    'id',
                                    'name',
                                    'campaign_id',
                                ],
                                // 'includes' => Product::getArrListingElastic()
                            ],
                            'sort' => [
                                $sort,
                                [
                                    "_script" => [
                                        "type" => "number",
                                        "script" => [
                                            "source" => "doc['campaign_name.keyword'].size() > 0 ? 0 : 1"
                                        ],
                                        "order" => 'asc',
                                    ]
                                ]
                            ]

                        ]
                    ];
                    $this->response = $this->elastic('search', $querySearch);

                    $arr = $this->elasticResponse();
                    if (is_null($arr)) {
                        return [];
                    }

                    return $arr;
                } catch (Throwable $e) {
                    logToDiscord(
                        'Listing Home Product Storefront failed'
                        . "\r\nException: ". $e->getMessage()
                        . "\r\nCurrent store Id: " . json_encode($store->id),
                        'error_elastic',
                    );
                    throw new \RuntimeException('Listing Product Storefront failed');
                }
            });
    }

    /**
     * @param string $index
     * @return bool
     */
    public function createIndex(string $index): bool
    {
        try {
            $settings = data_get($this->elastic('get', ['index' => 'products'], useIndices: true), 'products.settings.index', []);
            $params = [
                'index' => $index,
                'body' => [
                    'settings' => [
                        'number_of_shards' => data_get($settings, 'number_of_shards', 1),
                        'number_of_replicas' => data_get($settings, 'number_of_replicas', 1)
                    ],
                ]
            ];
            $this->response = $this->elastic('create', $params, useIndices: true);
            return $this->response !== null;
        } catch (Throwable $e) {
            logToDiscord(
                __FUNCTION__
                . "\r\nException: " . $e->getMessage()
                . "\r\nIndex: " . $index,
                'error_elastic',
            );
            return false;
        }
    }

    private static function loadConditionForTopPopularityCollection ($storeId, &$queryFilter) {
        $collectionIds = StoreCollection::query()->select('collection_id')->where('store_id', $storeId)->orderBy('popularity', 'asc')->pluck('collection_id')->toArray();
        if (!empty($collectionIds)) {
            $arrFilter = &$queryFilter['bool']['should'];
            foreach ($collectionIds as $id) {
                $arrFilter[]['term'] = [
                    'collection_ids' => $id
                ];
            }
        }
    }

    public function deleteStockStatusByProductIds(array $productIds = []): bool
    {
        try {
            $index = 'stock_status';
            if (empty($productIds)) {
                return false;
            }
            $query = [];
            $query['bool']['filter'][]['terms'] = [
                'product_id' => $productIds
            ];
            $queryCondition['minimum_should_match'] = 1;
            graylogInfo("Deleted products on stock status on elasticsearch.", [
                'category' => 'elastic_sync_log',
                'user_type' => 'system',
                'action' => 'deleteByQuery',
                'index_es' => $index,
                "product_ids" => json_encode($productIds),
                "total_product" => count($productIds),
                "query_es" => json_encode($query),
                "response_es" => json_encode($this->response),
            ]);
            $this->response = $this->elastic('deleteByQuery', [
                'index' => $index,
                'refresh' => true,
                'conflicts' => 'proceed',
                'body' => [
                    'query' => $query
                ]
            ]);
            return $this->response && isset($this->response['deleted']);
        } catch (\Exception $e) {
            logToDiscord(
                __FUNCTION__
                . "\r\nException: " . $e->getMessage()
                . "\r\nProductIds: " . json_encode($productIds)
                . "\r\nQuery: " . json_encode($queryCondition),
                'error_elastic',
            );
            return false;
        }
    }

    private function listingByCollectionId($collectionId, $queryFilter, $sort, $defaultProductType = ProductType::CAMPAIGN): array
    {
        $queryFilter['bool']['filter'][]['term'] = [
            'collection_ids' => $collectionId
        ];
        $store = StoreService::getCurrentStoreInfo();
        if (!$store->market_place_listing) {
            $queryFilter['bool']['filter'][]['term'] = [
                'seller_id' => $store->seller_id
            ];
        }
        $featureIds = SellerCollection::query()
            ->where('seller_id', $store->seller_id)
            ->where('collection_id', $collectionId)
            ->value('feature_ids');
        if (empty($featureIds)) {
            $queryFilter['bool']['filter'][]['term'] = [
                'product_type' => $defaultProductType
            ];
            return [$queryFilter, $sort];
        }

        $productIds  = explode(',', $featureIds);
        return $this->getQuerySearchAndSortFilterByArrProductId(
            $queryFilter,
            $productIds,
            ProductType::CAMPAIGN,
            $sort,
            'should',
            ProductType::CAMPAIGN
        );
    }

    private function getListingCampaigns(IndexCampaignData $data, Store $store): GetListingCampaignsData
    {
        try {
            $filter = array_filter($data->toArray(), function ($value) {
                return $value !== null;
            });
            [$query, $sort] = $this->getQuerySearch(
                arrFilter: $filter,
                store: $store,
            );
        } catch (Throwable $e) {
            return GetListingCampaignsData::from();
        }
        // dump($query);

        $hasKeyword = (bool) $data->s;
        $indexes = StoreService::getElasticSearchIndexes(
            store: $store,
            hasKeyword: $hasKeyword
        );
        $numberPerPage = $data->per_page;
        $currentPage   = $data->page;
        $querySearch = [
            'index' => $indexes,
            'body' => [
                'query' => $query,
                'size' => $numberPerPage,
                'from' => $numberPerPage * ($currentPage - 1),
                '_source' => [
                    'includes' => [
                        'id',
                        'slug',
                        'campaign_id',
                        'name',
                        'product_type',
                        'thumb_url',
                        'seller_id',
                        'price',
                    ]
                ],
                'sort' => $sort,
                // group by design_id
                'collapse' => [
                    'field' => 'design_id'
                ],
            ]
        ];

        try {
            $response = [];
            $this->response = $this->elastic('search', $querySearch);
            $response['data'] = $this->elasticResponse();
            $response['total'] = $this->response['hits']['total']['value'] ?? 0;

            return GetListingCampaignsData::from($response);
        } catch (Throwable $e) {
            return GetListingCampaignsData::from();
        }
    }
}
