<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\AvatarApi;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CrawlEmailAvatar implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $email;
    protected bool $dispatchFromCronJob;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $email, bool $dispatchFromCronJob = true)
    {
        $this->email = $email;
        $this->dispatchFromCronJob = $dispatchFromCronJob;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $emailProfile = AvatarApi::getProfile($this->email);
        $user = User::where('email', $this->email);

        if ($emailProfile) {
            $update = ['crawled_email_avatar_at' => now()];

            if (!empty($emailProfile['Image'])) {
                $update['avatar'] = $emailProfile['Image'];
            }

            $user->update($update);
        } elseif ($this->dispatchFromCronJob) {
            $user->update(['crawled_email_avatar_at' => null]);
        }
    }
}
