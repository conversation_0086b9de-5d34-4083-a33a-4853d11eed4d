<?php

namespace App\Jobs;

use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderProduct;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CheckOrderAtRisk implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?Order $order;

    public function __construct(int $orderId)
    {
        $this->order = Order::query()
            ->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED])
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->withWhereHas('order_products', function ($query) use ($orderId) {
                $query->whereHas('template', function ($query) use ($orderId) {
                    $query->where('created_at', '<', Carbon::now()->subMonth());
                    $query->whereDoesntHave('order', function ($query) use ($orderId) {
                        $query->where('order_id', '<>', $orderId);
                        $query->where('created_at', '>=', Carbon::now()->subMonth());
                        $query->whereNull('order_product.deleted_at');
                    });
                });
            })
            ->find($orderId);
        $this->onQueue('order');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if(isEnvTesting()) {
            return;
        }
        try {
            if ($this->order) {
                $orderProducts = $this->order->order_products;
                $arrTemplate = [];
                $orderProducts->groupBy('template_id')->each(function ($itemGroupBy) use (&$arrTemplate) {
                    $arrTemplate[] = "{$itemGroupBy->first()->product_name}({$itemGroupBy->first()->sku})";
                });
                $templates = implode(', ', $arrTemplate);
                OrderProduct::query()->whereIn('id', $orderProducts->pluck('id'))->update(['at_risk' => true]);
                if (app()->isProduction()) {
                    $message =  mentionDiscord(DiscordUserIdEnum::BIZDEV) . mentionDiscord(DiscordUserIdEnum::SS);
                } else {
                    $message =  mentionDiscord(DiscordUserIdEnum::VINH);
                }
                $message .= "\nThis order is marked as at risk.";
                $embedDesc = [
                    [
                        'description' => "Order ID: #{$this->order->id}\nTemplate: {$templates}",
                        'color' => 15548997
                    ]
                ];
                logToDiscord($message, DiscordChannel::ORDER_AT_RISK, embeds: $embedDesc);
            }
        } catch (\Exception $exception) {
            logException($exception, 'CheckOrderAtRisk@handle');
        }
    }
}
