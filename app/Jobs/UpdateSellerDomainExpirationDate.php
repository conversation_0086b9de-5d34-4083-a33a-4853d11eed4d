<?php

namespace App\Jobs;

use App\Models\SellerDomain;
use App\Services\WhoisDomainService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateSellerDomainExpirationDate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $domainId;
    public $domain;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($domainId, $domain)
    {
        $this->domainId = $domainId;
        $this->domain = $domain;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->domain) {
            return;
        }

        $expirationDate = WhoisDomainService::getExpirationDate($this->domain);

        if ($expirationDate === null) {
            return;
        }

        SellerDomain::where([
            'id' => $this->domainId,
            'domain' => $this->domain,
        ])->update([
            'domain_expired_at' => $expirationDate
        ]);
    }
}
