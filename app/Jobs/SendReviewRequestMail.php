<?php

namespace App\Jobs;

use App\Enums\OrderTypeEnum;
use App\Enums\ProductReviewRequestStatusEnum;
use App\Enums\SmartRemarketingEnum;
use App\Enums\UpsellTypeEnum;
use App\Http\Controllers\Storefront\UpsellController;
use App\Models\Order;
use App\Models\Store;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendReviewRequestMail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $orderId;

    /**
     * Create a new job instance.
     *
     * @param int $orderId
     * @return void
     */
    public function __construct(int $orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        $order = Order::query()->select(['id', 'customer_name', 'customer_email', 'access_token', 'store_id', 'seller_id', 'type'])
            ->with('store:id,name,sub_domain,domain,logo_url,phone,address,social_accounts,product_review_coupon,domain_status,smart_remarketing')
            ->with('order_products:id,order_id,product_name,product_url,thumb_url,campaign_title,quantity,options')
            ->with('seller')
            ->find($this->orderId);

        if (!$order) {
            logToDiscord('buyer.product_review_request failed: Order ID #' . $this->orderId . ' not found', 'email');
            return;
        }

        $hasReviewCoupon = !empty($order->store->product_review_coupon);
        $store = $order->store;
        if (empty($store)) {
            logToDiscord('buyer.product_review_request failed: Store of Order ID #' . $this->orderId . ' not found', 'email');
            return;
        }
        $reviewUrl = $order->store->base_url;
        graylogInfo('Mail request review log', [
            'category' => 'mail_request_review_log',
            'order_id' => $this->orderId ?? '',
            'store_id' => $order->store->id ?? '',
            'base_url' => $reviewUrl ?? '',
            'action'  => "schedule"
        ]);
        if (!$hasReviewCoupon && $order->type === OrderTypeEnum::REGULAR) {
            $reviewUrl = getMarketPlaceDomain();
            $marketPlace = Store::query()->select('product_review_coupon')->find(Store::SENPRINTS_STORE_ID);
            if ($marketPlace && !empty($marketPlace->product_review_coupon)) {
                $hasReviewCoupon = true;
            }
        }

        $customerName = $order->customer_name;
        $subject = 'Your reviews are meaningful with us';

        if (!empty($customerName)) {
            $customerName = explode(' ', $customerName, 2)[0];
            $subject = $customerName . ', your reviews are meaningful with us';
        }

        if ($order->seller && $order->seller->smart_remarketing) {
            if ($order->seller->smart_remarketing === SmartRemarketingEnum::ENABLED_WITH_UPSELL_MP) {
                $relatedProducts = (new UpsellController($order->store_id))->getSmartUpsell($order->id, 'review_email');
            }
            else {
                $relatedProducts = (new UpsellController($order->store_id))->getUpsellProductsForEmail($order->order_products->pluck('product_id')->toArray(), UpsellTypeEnum::POST_SALE);
            }
            $relatedProducts = array_slice($relatedProducts, 0, 4);
        } else {
            $upsellController = new UpsellController($order->store_id, 4);
            $relatedProducts = $upsellController->getUpsellProductsForEmail($order->order_products->pluck('product_id')->toArray()) ?? [];
        }

        foreach ($relatedProducts as &$product) {
            $product['slug'] = 'https://' . $reviewUrl . "/" . $product['slug'];
            $product['campaign_name'] ??= $product['name'];
        }
        unset($product);

        $dataSendMailLog = [
            'sellerId' => $order->seller_id ?? null,
            'storeId' => $store->id ?? null,
            'orderId' => $this->orderId ?? null
        ];

        $content = [
            'to' => $order->customer_email,
            'template' => 'buyer.product_review_request',
            'data' => [
                'order_id' => $this->orderId,
                'subject' => $subject,
                'customer_name' => $customerName,
                'access_token' => $order->access_token,
                'store_info' => $order->store,
                'products' => $order->order_products,
                'review_url' => $reviewUrl,
                'has_review_coupon' => $hasReviewCoupon,
                'related_products' => $relatedProducts
            ],
            'sendMailLog' => $dataSendMailLog
        ];

        $sendMail = sendEmail($content);

        if (!$sendMail) {
            Order::query()->where('id', $this->orderId)->update(['review_request_status' => ProductReviewRequestStatusEnum::PENDING]);
            logToDiscord('buyer.product_review_request failed - Order : ' . $this->orderId . ' - Customer Name: ' . $customerName . ' - Email: ' . $order->customer_email, 'email');
            return;
        }

        Order::query()->where('id', $this->orderId)->update(['review_request_status' => ProductReviewRequestStatusEnum::COMPLETED]);
    }
}
