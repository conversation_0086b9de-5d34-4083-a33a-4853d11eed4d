<?php

namespace App\Jobs;

use App\Enums\DiscordChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Actions\Commons\RejectOrdersAction;
class CancelOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderIds;
    protected $orderProductIds;
    /**
     * Create a new job instance.
     */
    public function __construct($orderIds, $orderProductIds)
    {
        $this->orderIds = $orderIds;
        $this->orderProductIds = $orderProductIds;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $action = app(RejectOrdersAction::class);
            $action->handle(
                $this->orderIds,
                $this->orderProductIds
            );
        } catch (\Exception $e) {
            logToDiscord(
                'Error in cancel order job: '
                . "\r\nFulfill order product id: " . $this->orderProduct->id
                . "\r\nError: " . $e->getMessage()
                , DiscordChannel::FULFILL_ORDER
                , true
            );
        }
    }
}
