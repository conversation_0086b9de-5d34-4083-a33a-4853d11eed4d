<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\SystemConfigTypeEnum;
use App\Models\IndexOrder;
use App\Models\IndexOrderProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\SystemConfig;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncOrdersToSingleStoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $queueName = 'sync-order-product';
    protected int $limit;

    /**
     * @param int $limit
     */
    public function __construct($limit = 1000)
    {
        $this->limit = $limit;
        $this->onQueue($this->queueName);
    }

    /**
     * @throws \Throwable
     */
    public function handle()
    {
        try {
            $currentDatetime = now()->clone()->toDateTimeString();
            SystemConfig::setConfig(CacheKeys::SYNC_ORDERS_TO_SINGLESTORE, array(
                'value' => $currentDatetime,
                'status' => 1,
                'type' => SystemConfigTypeEnum::BACKEND
            ));
            $this->startProcess();
        } catch (\Throwable $e) {
            logToDiscord("Sync orders from Mysql to SingleStore error: {$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}");
            $this->fail($e);
        }
    }

    /**
     * @return void
     */
    private function startProcess()
    {
        $total_orders = $this->syncOrders();
        $total_order_products = $this->syncOrderProducts();
        // make sure all orders and order products are re-synced because the data on singlestore is not correct
        $this->resyncOrders();
        $this->resyncOrderProducts();
        if($total_orders < $this->limit && $total_order_products < $this->limit) {
            graylogInfo('End sync orders to SingleStore: Orders: ' . $total_orders . ', Order Products: ' . $total_order_products . ' / ' . $this->limit, [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "sync"
            ]);
            SystemConfig::setConfig(CacheKeys::SYNC_ORDERS_TO_SINGLESTORE, array(
                'value' => null,
                'status' => 1,
                'type' => SystemConfigTypeEnum::BACKEND
            ));
            return;
        }
        dispatch(new self($this->limit));
    }

    /**
     * @param array $ids
     * @return int
     */
    private function syncOrders($ids = array())
    {
        try {
            $startTime = round(microtime(true) * 1000);
            $orders = Order::query()
                ->when(!empty($ids), function ($query) use ($ids) {
                    $query->whereIn('id', $ids);
                })
                ->when(empty($ids), function ($query) {
                    $query->whereRaw('(sync_at IS NULL OR sync_at < updated_at OR sync_at <= deleted_at)');
                })
                ->withTrashed()
                ->limit($this->limit)
                ->get();
            if ($orders->isEmpty()) {
                if (!empty($ids)) {
                    IndexOrder::query()->withTrashed()->whereIn('id', $ids)->forceDelete();
                }
                return 0;
            }
            $total = $orders->count();
            $sync_at = now();
            graylogInfo('Start sync orders to SingleStore: Total: ' . $total . ' / ' . $this->limit, [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "sync"
            ]);
            $orders = $orders->map(function (Order $order) use ($sync_at) {
                $order->makeHidden(['shipping_address', 'billing_address']);
                $order->sync_at = $sync_at;
                $order->updated_at = $sync_at;
                return $order;
            });
            $allIds = [];
            if (!empty($ids)) {
                // Delete orders that are not in the list of ids
                $deleted_ids = array_diff($ids, $orders->pluck('id')->toArray());
                if (!empty($deleted_ids)) {
                    IndexOrder::query()->withTrashed()->whereIn('id', $deleted_ids)->forceDelete();
                    $allIds = array_merge($allIds, $deleted_ids);
                }
            }
            foreach ($orders->chunk(round($this->limit / 10)) as $order_chunk) {
                $chunk_ids = $order_chunk->pluck('id')->toArray();
                IndexOrder::query()->withTrashed()->whereIn('id', $chunk_ids)->forceDelete();
                $inserted = IndexOrder::query()->insert($order_chunk->toArray());
                if ($inserted) {
                    $updated = Order::query()->withTrashed()->whereIn('id', $chunk_ids)->update(['sync_at' => $sync_at, 'updated_at' => $sync_at]);
                    if ($updated) {
                        $allIds = array_merge($allIds, $chunk_ids);
                    }
                }
            }
            $allIds = array_unique($allIds);
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            $count = count($allIds);
            graylogInfo("Sync-ed orders $total / $count products to Singlestore in $time ms", [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'synced_ids' => json_encode($allIds),
                'action'  => "sync"
            ]);
            return $total;
        } catch (\Throwable $e) {
            logException($e);
            graylogError("Sync orders to SingleStore error: {$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}", [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "sync"
            ]);
            return 0;
        }
    }

    /**
     * @param array $ids
     * @return int
     */
    private function syncOrderProducts($ids = array())
    {
        try {
            $startTime = round(microtime(true) * 1000);
            $orderProducts = OrderProduct::query()
                ->when(!empty($ids), function ($query) use ($ids) {
                    $query->whereIn('id', $ids);
                })
                ->when(empty($ids), function ($query) {
                    $query->whereRaw('(sync_at IS NULL OR sync_at < updated_at OR sync_at <= deleted_at)');
                })
                ->withTrashed()
                ->limit($this->limit)
                ->get();
            if ($orderProducts->isEmpty()) {
                if (!empty($ids)) {
                    IndexOrderProduct::query()->withTrashed()->whereIn('id', $ids)->forceDelete();
                }
                return 0;
            }
            $total = $orderProducts->count();
            $sync_at = now();
            graylogInfo('Start sync order products to SingleStore: Total: ' . $total . ' / ' . $this->limit, [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "sync"
            ]);
            $orderProducts = $orderProducts->map(function (OrderProduct $orderProduct) use ($sync_at){
                $orderProduct->sync_at = $sync_at;
                $orderProduct->updated_at = $sync_at;
                return $orderProduct;
            });
            $allIds = [];
            if (!empty($ids)) {
                // Delete order products that are not in the list of ids
                $deleted_ids = array_diff($ids, $orderProducts->pluck('id')->toArray());
                if (!empty($deleted_ids)) {
                    IndexOrderProduct::query()->withTrashed()->whereIn('id', $deleted_ids)->forceDelete();
                    $allIds = array_merge($allIds, $deleted_ids);
                }
            }
            foreach ($orderProducts->chunk(round($this->limit / 10)) as $orderProductChunk) {
                $chunk_ids = $orderProductChunk->pluck('id')->toArray();
                IndexOrderProduct::query()->withTrashed()->whereIn('id', $chunk_ids)->forceDelete();
                $inserted = IndexOrderProduct::query()->insert($orderProductChunk->toArray());
                if ($inserted) {
                    $updated = OrderProduct::query()->withTrashed()->whereIn('id', $chunk_ids)->update(['sync_at' => $sync_at, 'updated_at' => $sync_at]);
                    if ($updated) {
                        $allIds = array_merge($allIds, $chunk_ids);
                    }
                }
            }
            $allIds = array_unique($allIds);
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            $count = count($allIds);
            graylogInfo("Sync-ed order products $total / $count products to Singlestore in $time ms", [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'synced_ids' => json_encode($allIds),
                'action'  => "sync"
            ]);
            return $total;
        } catch (\Throwable $e) {
            logException($e);
            graylogError("Sync order products to SingleStore error: {$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}", [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "sync"
            ]);
            return 0;
        }
    }

    /**
     * @return int
     */
    private function resyncOrders()
    {
        try {
            $order_ids = IndexOrder::query()->select('id')->whereRaw('(sync_at IS NULL OR sync_at < updated_at OR sync_at <= deleted_at)')->withTrashed()->limit($this->limit)->get()->pluck('id')->toArray();
            if (empty($order_ids)) {
                return 0;
            }
            graylogInfo('Start re-sync orders to SingleStore: Total: ' . count($order_ids) . ' / ' . $this->limit, [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "resync"
            ]);
            return $this->syncOrders($order_ids);
        } catch (\Throwable $e) {
            logException($e);
            $this->fail($e);
            return 0;
        }
    }

    /**
     * @return int
     */
    private function resyncOrderProducts()
    {
        try {
            $order_product_ids = IndexOrderProduct::query()->select('id')->whereRaw('(sync_at IS NULL OR sync_at < updated_at OR sync_at <= deleted_at)')->withTrashed()->limit($this->limit)->get()->pluck('id')->toArray();
            if (empty($order_product_ids)) {
                return 0;
            }
            graylogInfo('Start re-sync order products to SingleStore: Total: ' . count($order_product_ids) . ' / ' . $this->limit, [
                'category' => 'orders_sync_log',
                'user_type' => 'system',
                'action'  => "resync"
            ]);
            return $this->syncOrderProducts($order_product_ids);
        } catch (\Throwable $e) {
            logException($e);
            $this->fail($e);
            return 0;
        }
    }
}
