<?php

namespace App\Jobs;

use App\Actions\Commons\SendEmailConfirmToBuyer;
use App\Enums\SendMail\LogStatus as SendMailLogStatus;
use App\Enums\SendMail\Template;
use App\Models\Order;
use App\Models\SendMailLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Marketing\Supports\EmailBuilder;


class ResendMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private SendMailLog $mailLog;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(SendMailLog $mailLog)
    {
        $this->mailLog = $mailLog;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if ($this->mailLog->template === Template::BUYER_ORDER_CONFIRMATION && empty($this->mailLog->content)) {
                $order = Order::query()->whereKey($this->mailLog->order_id)->first();
                if ($order) {
                    (new SendEmailConfirmToBuyer())->handle($order);
                }
            } else {
                app()->make('mailer')->to($this->mailLog->email_to)->send(new EmailBuilder($this->mailLog->content, $this->mailLog->subject));
            }
            $this->mailLog->status = SendMailLogStatus::SENT;
            $this->mailLog->sent_at = now();

            graylogInfo("ResendMailJob: ResendMailJob", [
                'category' => 'job_resend_mail',
                'mail_log_template' => $this->mailLog->template,
                'user_type' => 'system',
                'action' => 'queue',
                'mail_log_id' => $this->mailLog->id,
                'status' => 'success'
            ]);
        } catch (\Exception $e) {
            $this->mailLog->status = SendMailLogStatus::FAILED;
            $this->mailLog->logs = 'ResendMailJob: ' . $e->getMessage();
            graylogError("Exception! ResendMailJob: ResendMailJob} " . $e->getMessage(), [
                'category' => 'job_resend_mail',
                'mail_log_template' => $this->mailLog->template,
                'mail_log_id' => $this->mailLog->id,
                'user_type' => 'system',
                'action' => 'queue',
                'status' => 'error exception',
            ]);
        }
        $this->mailLog->save();
    }
}
