<?php

namespace App\Jobs;

use App\Enums\PaymentMethodEnum;
use App\Enums\QueueName;
use App\Models\PaymentGateway;
use App\Models\StripeCustomer;
use App\Services\OrderService;
use App\Traits\Encrypter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Throwable;

class UpdatePaymentIntentForOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Encrypter;

    private PaymentGateway $gateway;
    private bool $receiptEmail = false;

    /**
     * Create a new job instance.
     */
    public function __construct(public $transactionId, public $order, public $paymentGatewayId)
    {
        $this->onQueue(QueueName::ORDER_EVENTS);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $gateway = PaymentGateway::query()->where('gateway', PaymentMethodEnum::STRIPE)->whereKey($this->paymentGatewayId)->first();
            $order = $this->order;
            if (!$gateway || !$order) {
                logToDiscord('Cannot find Stripe Gateway ' . $this->paymentGatewayId . ' - Or order not found ' . $order->id, 'error_checkout');
                return;
            }
            $this->gateway = $gateway;
            $config = self::safeLoadConfig($gateway);
            $configObj = json_decode($config, false, 512, JSON_THROW_ON_ERROR);
            $this->receiptEmail = data_get($configObj, 'receipt_email', false);
            $secretKey = data_get($configObj, 'secret_key');
            if (!$secretKey) {
                $message = 'Cannot find Stripe Secret Key ' . $this->paymentGatewayId;
                $message .= ' - Info ' . $order->id;
                logToDiscord($message, 'error_checkout');
                return;
            }
            Stripe::setApiKey($secretKey);
            $data = [];
            $this->setStripeCustomer($data, $order, $this->paymentGatewayId);
            $response = PaymentIntent::retrieve($this->transactionId);
            if (!empty($response['customer'])) {
                unset($data['customer']);
            }
            PaymentIntent::update($this->transactionId, $data);
            return;
        } catch (Throwable $e) {
            logException($e, "UpdatePaymentIntentForOrderJob::handle()\nGateway Id: " . $this->paymentGatewayId, 'error_checkout');
            return;
        }
    }

    /**
     * @param $data
     * @param $order
     * @param $gatewayId
     * @return void
     * @throws ApiErrorException
     * @throws Throwable
     */
    private function setStripeCustomer(&$data, $order, $gatewayId): void
    {
        if (empty($order->customer_email) || empty($order->customer_id)) {
            return;
        }
        try {
            $dataCustomer = [
                'name' => $order->customer_name ?? $order->customer_email,
                'phone' => $order->customer_phone,
                'email' => $order->customer_email,
                'address' => [
                    'line1' => $order->address ?? 'N/A',
                    'city' => $order->city,
                    'country' => $order->country,
                    'line2' => $order->address_2,
                    'postal_code' => $order->postcode,
                    'state' => $order->state,
                ],
            ];

            // if customer had filled name and address, other fields can be null
            if (!is_null($order->customer_name) && !is_null($order->address)) {
                $data['shipping'] = OrderService::getShipping($order);
                $dataCustomer['shipping'] = $data['shipping'];
            }
            // check if exist stripe customer
            $stripeId = StripeCustomer::query()
                ->where('customer_id', $order->customer_id)
                ->where('gateway_id', $gatewayId)
                ->value('stripe_id');

            if (empty($stripeId)) {
                $customer = Customer::create($dataCustomer);
                $stripeId = $customer->id;
                StripeCustomer::query()->upsert([
                    'gateway_id' => $gatewayId,
                    'customer_id' => $order->customer_id,
                    'stripe_id' => $stripeId
                ], ['gateway_id', 'customer_id']);
            } else {
                Customer::update($stripeId, $dataCustomer);
            }
            $data['customer'] = $stripeId;
            if ($this->receiptEmail) {
                $data['receipt_email'] = $order->customer_email;
            }
        } catch (ApiErrorException $e) {
            logToDiscord(
                'Create API Customer Stripe failed.'
                . 'UpdatePaymentIntentForOrderJob'
                . "\nOrder Id: " . $order->id
                . "\nGateway Id: " . $gatewayId
                . "\nException: " . $e->getMessage()
                . "\nRequest Id: " . $e->getRequestId(),
                'error_checkout'
            );
            throw $e;
        } catch (Throwable $e) {
            logToDiscord(
                'Create Customer Stripe failed.'
                . 'UpdatePaymentIntentForOrderJob'
                . "\nOrder Id: " . $order->id
                . "\nGateway Id: " . $gatewayId
                . "\nException: " . $e->getMessage(),
                'error_checkout'
            );
            throw $e;
        }
    }
}
