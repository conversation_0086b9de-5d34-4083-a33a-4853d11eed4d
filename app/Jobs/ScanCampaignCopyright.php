<?php

namespace App\Jobs;

use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\DiscordUserIdEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Models\Campaign;
use App\Models\File;
use App\Models\Product;
use App\Models\SellerHistory;
use App\Models\SystemConfig;
use App\Models\TrademarkList;
use App\Models\TrademarkResult;
use App\Models\User;
use App\Services\Gemini;
use Google\ApiCore\ApiException;
use Google\ApiCore\ValidationException;
use Google\Cloud\Vision\V1\AnnotateImageResponse as AnnotateImageResponseAlias;
use Google\Cloud\Vision\V1\Feature\Type;
use Google\Cloud\Vision\V1\ImageAnnotatorClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class ScanCampaignCopyright implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $campaign;
    protected $seller;

    protected $file;
    protected $imageUri;
    protected $imageContent;
    protected $imageAnnotator;

    // TM flag
    protected $flaggedByLogo = false;
    protected $flaggedByText = false;
    protected $flaggedByTitle = false;
    protected $blockCampaign = 0;
    protected $flaggedLogs = [];
    protected $textLogs = [];

    protected $channelLog = 'scan_trademark';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($campaignId, $sellerId = null)
    {
        $this->seller = User::query()->find($sellerId);
        $this->blockCampaign = (int)SystemConfig::getConfig('block_campaign_after_scan_trademark', 0);
        $this->campaign = Campaign::query()
            ->onSellerConnection($this->seller)
            ->with(['seller'])->whereKey($campaignId)->first();
        $this->file = File::query()
            ->onSellerConnection($this->seller)
            ->firstWhere([
                'campaign_id' => $campaignId,
                'type' => FileTypeEnum::DESIGN,
                'option' => FileRenderType::PRINT,
                'status' => FileStatusEnum::ACTIVE
            ]);
        $this->onQueue('check_copyright');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        if (!$this->campaign || !$this->file || !$this->file->file_url) {
            return;
        }

        if (!$this->seller) {
            $this->seller = $this->campaign->seller;
        }

        // skip if already checked
        if ($this->isScanned($this->file)) {
            return;
        }

        if ($this->seller?->status === UserStatusEnum::TRUSTED || $this->seller?->role === UserRoleEnum::DESIGNER || $this->seller?->custom_payment) {
            return;
        }

        $filePath = $this->file->file_url;
        $imgUrl = null;
        if (app()->isProduction()) {
            $imgUrl = 'https://img-internal.cloudimgs.net';
        }
        $this->imageUri = imgUrl($filePath, 'validate', null, $imgUrl);
        $this->imageContent = '';

        try {
            $data = [
                'file_id' => $this->file->id,
                'seller_id' => $this->campaign->seller_id,
                'campaign_id' => $this->campaign->id,
                'created_at' => currentTime(),
            ];
            $trademark_id = TrademarkResult::query()->insertGetId($data);
            $result = $this->scanDesign();
            if ((!$this->flaggedByLogo && !$this->flaggedByText && !$this->flaggedByTitle) || empty($result)) {
                TrademarkResult::query()->whereKey($trademark_id)->update([
                    'updated_at' => currentTime(),
                    'status' => 'good'
                ]);
                return;
            }
            $flaggedBy = '';
            $status = 'flagged';
            if ($this->flaggedByText) {
                $flaggedBy = 'text';
                if (!empty($this->flaggedLogs['text'])) {
                    $status = 'violated';
                } else {
                    // Reset flag text state in case text in flagged not violated
                    $this->flaggedByText = false;
                }
            }
            if ($this->flaggedByLogo) {
                $flaggedBy = 'logo';
                if (!empty($this->flaggedLogs['logo'])) {
                    $status = 'violated';
                } else {
                    // Reset flag logo state in case logo in flagged not violated
                    $this->flaggedByLogo = false;
                }
            }
            if ($this->flaggedByTitle) {
                $flaggedBy = 'text';
                $status = 'violated';
            }
            if (!empty($flaggedBy)) {
                $this->flaggedLogs['imageUri'] = $this->imageUri;
            }
            if ($this->flaggedByText && $this->flaggedByLogo) {
                $flaggedBy = 'both';
            }
            if (!empty($this->textLogs)) {
                $this->textLogs = array_values($this->textLogs);
                if (!empty($this->flaggedLogs['text'])) {
                    $this->flaggedLogs['text'] = array_merge($this->flaggedLogs['text'], $this->textLogs);
                } else {
                    $this->flaggedLogs['text'] = $this->textLogs;
                }
                $this->flaggedLogs['text'] = array_unique($this->flaggedLogs['text']);
            }
            $logo = (!empty($result['logo']) && is_array($result['logo'])) ? implode(',', $result['logo']) : null;
            $text = (!empty($result['text']) && is_array($result['text'])) ? implode(',', $result['text']) : null;
            $data = [
                'logos' => $logo,
                'tags' => $text,
                'flagged' => $flaggedBy,
                'status' => $status,
                'flagged_logs' => json_encode($this->flaggedLogs),
                'updated_at' => currentTime(),
            ];
            TrademarkResult::query()->whereKey($trademark_id)->update($data);
            if ($this->blockCampaign && $status === 'violated') {
                $this->campaign->tm_status = CampaignTrademarkStatusEnum::TM;
                $this->campaign->sync_status = Product::SYNC_DATA_STATS_ENABLED;
                $this->campaign->public_status = CampaignPublicStatusEnum::NO;
                if ($this->campaign->status === CampaignStatusEnum::ACTIVE || $this->campaign->status === CampaignStatusEnum::INACTIVE) {
                    SendTradeMarkNotificationToSeller::dispatch($this->campaign->seller_id, $this->campaign->id);
                }
                if ($this->campaign->status !== CampaignStatusEnum::DRAFT) {
                    $this->campaign->status = CampaignStatusEnum::BLOCKED;
                }
                $this->campaign->save();
                User::query()->whereKey($this->campaign->seller_id)->update(['status' => UserStatusEnum::FLAGGED, 'flag_log' => 'Campaign #' . $this->campaign->id . ' violates TM policy.']);
                SellerHistory::query()->insert(array(
                    'seller_id' => $this->campaign->seller_id,
                    'action' => SellerHistoryActionEnum::BLOCK_CAMPAIGN,
                    'seller_status' => UserStatusEnum::FLAGGED,
                    'campaign_id' => $this->campaign->id,
                    'details' => 'Campaign #' . $this->campaign->id . ' violates TM policy: ' . json_encode($this->flaggedLogs),
                ));
                // Sync campaign to elasticsearch
                (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($this->campaign->id, sellerId: $this->seller?->id);
            }
            $debugMsg = 'File URL: ' . $this->imageUri . "\n Data: ";
            $debugMsg .= json_encode($data);
            $this->logToDiscord($debugMsg);
            // Release variables
            unset($result, $this->flaggedLogs, $this->flaggedByLogo, $this->blockCampaign, $this->flaggedByText, $this->flaggedByTitle, $this->textLogs);
        } catch (\Throwable $e) {
            logException($e, 'ScanCampaignCopyright@handle', $this->channelLog, true);
            $this->fail($e);
        }
    }

    /**
     * @param $file
     * @return bool
     */
    private function isScanned($file): bool
    {
        return TrademarkResult::query()
            ->where([
                'file_id' => $file->id,
                'seller_id' => $file->seller_id,
                'campaign_id' => $file->campaign_id
            ])
            ->exists();
    }

    /**
     * @return void
     * @throws ValidationException
     */
    private function initImageAnnotator(): void
    {
        $this->imageAnnotator = new ImageAnnotatorClient([
            'credentials' => storage_path('google-vision-credentials.json')
        ]);
    }

    /**
     * @return AnnotateImageResponseAlias|null
     */
    private function detectTextAndLogo(): ?AnnotateImageResponseAlias
    {
        try {
            return $this->imageAnnotator->annotateImage(!empty($this->imageContent) ? $this->imageContent : $this->imageUri, [
                Type::TEXT_DETECTION,
                Type::LOGO_DETECTION
            ]);
        } catch (ApiException $e) {
            logException($e, 'ScanCampaignCopyright', $this->channelLog, true);
            $this->imageAnnotator->close();
            $this->fail($e);
            return null;
        }
    }

    /**
     * Ref: https://cloud.google.com/vision/docs/samples/vision-logo-detection-gcs
     * @throws ValidationException
     */
    private function scanDesign(): ?array
    {
        $this->initImageAnnotator();
        $this->searchCampaignTitle();

        $response = $this->detectTextAndLogo();
        if (!$response) {
            return null;
        }
        if ($response->hasError()) {
            $response = $this->retryScanDesign();
            if (!$response) {
                return null;
            }
        }
        $logo = $this->getLogoAnnotations($response);
        $text = $this->getTextAnnotations($response);
        if ($this->flaggedByLogo) {
            $totalResult = $this->checkTineye($this->imageUri);
            if ($totalResult > 0) {
                $this->flaggedLogs['artwork popular'] = $totalResult;
            }
        }
        $this->imageAnnotator->close();
        if (empty($logo) && empty($text)) {
            return null;
        }
        return [
            'logo' => $logo,
            'text' => $text
        ];
    }

    private function scanWithGemini(): void
    {
        $ttl = 86400; // 1 day
        $cacheKey = 'gemini_scan_design_count';
        $count = Cache::remember($cacheKey, $ttl, function () {
            return 0;
        });

        $limit = 2300; // 10% of created campaigns in a day

        if ($count >= $limit) {
            return;
        }

        $res = (new Gemini())->scanImage($this->imageUri);

        if (is_null($res)) {
            return;
        }
        graylogInfo('Gemini Scan result', [
            'image' => $this->imageUri,
            'result' => $res
        ]);

        Cache::increment($cacheKey);
    }

    /**
     * @param $retry
     * @param $maxRetry
     * @return AnnotateImageResponseAlias|null
     */
    private function retryScanDesign($retry = 0, $maxRetry = 4): ?AnnotateImageResponseAlias
    {
        try {
            $this->imageContent = file_get_contents($this->imageUri);
            $response = $this->detectTextAndLogo();
            if (!$response || $response->hasError()) {
                while (true) {
                    $retry++;
                    if ($retry === $maxRetry) {
                        $time = now()->timezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s');
                        $errorMessage = 'Time Asia/HCM: ' . $time . "\n";
                        $errorMessage .= 'Retry Scan: ' . ($retry + 1) . "\n";
                        $errorMessage .= 'Error Message: ' . $response->getError()->getMessage() . "\n";
                        $errorMessage .= 'Error Code: ' . $response->getError()->getCode() . "\n";
                        $errorMessage .= 'Scan Image: ' . $this->imageUri;
                        $this->logToDiscord($errorMessage);
                        return null;
                    }

                    $response = $this->detectTextAndLogo();

                    if (!$response->hasError()) {
                        return $response;
                    }
                    sleep(10);
                }
            }

            return $response;
        } catch (\Throwable $e) {
            logException($e, 'ScanCampaignCopyright', $this->channelLog, true);
            $this->imageAnnotator->close();
            $this->fail($e);
            return null;
        }
    }

    /**
     * @param $response
     * @return array|null
     */
    private function getLogoAnnotations($response): ?array
    {
        $logos = $response->getLogoAnnotations();
        if (count($logos) === 0) {
            return null;
        }
        $result = [];
        $keywords = [];
        foreach ($logos as $logo) {
            $score = $logo->getScore();
            if ($score >= 0.9) {
                $keyword = $logo->getDescription();
                $result[] = $keyword . ' (' . round($score, 2) . ')';
                if (!$this->isAcceptTradeMarkLogo($keyword)) {
                    $this->flaggedByLogo = true;
                    if ($this->isTradeMarkLogo($keyword)) {
                        $keywords[] = $keyword;
                    }
                }
            }
        }
        if (count($result) === 0) {
            return null;
        }
        if (!empty($keywords)) {
            $this->flaggedLogs['logo'] = implode(', ', array_unique($keywords));
        }
        return $result;
    }

    private function getTextAnnotations($response): array
    {
        $texts = $response->getTextAnnotations();
        $result = [];
        $string = '';
        if (count($texts) > 0) {
            foreach ($texts as $text) {
                $txt = trim($text->getDescription());
                if (strpos($txt, "\n") !== false) {
                    continue;
                }
                $txt = preg_replace(
                    [
                        '_[<>]+_',
                        '_[-+=!(){}[\]^"~*?:\\/\\\\]|&(?=&)|\|(?=\|)_',
                    ],
                    '',
                    $txt
                );
                if (empty($txt)) {
                    continue;
                }
                $result[] = trim($txt);
            }
            $string = implode(' ', $result);
        }
        $result = [];
        if (empty($string)) {
            return $result;
        }
        $result[] = Str::lower($string);
        $trademarkKeywords = $this->searchTrademark($result);
        if (!empty($trademarkKeywords)) {
            $this->flaggedLogs['text'] = array_unique($trademarkKeywords);
        }
        return $result;
    }

    private function searchCampaignTitle(): array
    {
        $result = [];
        if (!empty($this->campaign->name) && $this->campaign->status !== CampaignStatusEnum::DRAFT) {
            $result[] = Str::lower($this->campaign->name);
        }
        if (empty($result)) {
            return $result;
        }
        $trademarkKeywords = $this->searchTrademark($result);
        if (!empty($trademarkKeywords)) {
            $this->flaggedByTitle = true;
            $this->flaggedLogs['title'] = implode(', ', array_unique($trademarkKeywords));
        }
        return $result;
    }

    /**
     * @param array $keywords
     * @return array|null
     */
    private function searchTrademark(array $keywords): ?array
    {
        if (empty($keywords)) {
            return null;
        }
        $keywords = array_map('strtolower', $keywords);
        $trademarkKeywords = [];
        foreach ($keywords as $keyword) {
            $acceptTradeMarkLogo = $this->isAcceptTradeMarkLogo($keyword);
            $tradeMarkText = $this->isTradeMarkText($keyword);
            $doubtTradeMarkText = $this->isDoubtTradeMarkText($keyword);
            if (empty($acceptTradeMarkLogo) && !empty($tradeMarkText)) {
                $trademarkKeywords[] = $tradeMarkText;
            } else if (!empty($doubtTradeMarkText)) {
                $this->flaggedByText = true;
                $this->textLogs[$doubtTradeMarkText] = $doubtTradeMarkText;
            }
        }
        if (count($trademarkKeywords) === 0) {
            return null;
        }
        $this->flaggedByText = true;
        return array_unique($trademarkKeywords);
    }

    /**
     * @param $trademark
     * @param $string
     * @return false|int
     */
    private function isMatchKeyword($trademark, $string)
    {
        try {
            $trademark = str_replace('/', '\\/', $trademark);
            return preg_match("/\b{$trademark}\b/", $string);
        } catch (\Throwable $e) {
            $debugMsg = 'Match keyword function error: ' . $e->getMessage() . "\n";
            $debugMsg .= 'String: ' . $string . "\n";
            $debugMsg .= 'Trademark: ' . $trademark . "\n";
            $debugMsg .= 'Pattern: ' . "/\b{$trademark}\b/" . "\n";
            $this->logToDiscord($debugMsg);
            return false;
        }
    }

    /**
     * @param $keyword
     * @return bool|string
     */
    private function isTradeMarkText($keyword)
    {
        $trademarks = $this->getBlockTextTradeMarkList();
        if (empty($trademarks)) {
            return false;
        }
        $trademarks = array_map('strtolower', $trademarks);
        foreach ($trademarks as $trademark) {
            if ($this->isMatchKeyword($trademark, $keyword)) {
                return $trademark;
            }
        }
        return false;
    }

    /**
     * @param $keyword
     * @return bool|string
     */
    private function isTradeMarkLogo($keyword)
    {
        $trademarks = $this->getBlockLogoTradeMarkList();
        if (empty($trademarks)) {
            return false;
        }
        $trademarks = array_map('strtolower', $trademarks);
        foreach ($trademarks as $trademark) {
            if ($this->isMatchKeyword($trademark, $keyword)) {
                return $trademark;
            }
        }
        return false;
    }

    /**
     * @param $keyword
     * @return bool|string
     */
    private function isAcceptTradeMarkLogo($keyword)
    {
        $trademarks = $this->getAcceptLogoTradeMarkList();
        if (empty($trademarks)) {
            return false;
        }
        $trademarks = array_map('strtolower', $trademarks);
        foreach ($trademarks as $trademark) {
            if ($this->isMatchKeyword($trademark, $keyword)) {
                return $trademark;
            }
        }
        return false;
    }

    /**
     * @param $keyword
     * @return bool|string
     */
    private function isDoubtTradeMarkText($keyword)
    {
        $texts = $this->getDoubtTextList();
        if (empty($texts)) {
            return false;
        }
        $texts = array_map('strtolower', $texts);
        foreach ($texts as $text) {
            if ($this->isMatchKeyword($text, $keyword)) {
                return $text;
            }
        }
        return false;
    }

    private function getTradeMarkList()
    {
        $ttl = 86400 * 30; // 30 days
        return Cache::remember('trademarks', $ttl, function () {
            return TrademarkList::query()->get()->toArray();
        });
    }

    /**
     * @return array
     */
    private function getDoubtTextList(): array
    {
        $trademarks = $this->getTradeMarkList();
        if (!empty($trademarks)) {
            return collect($trademarks)->filter(fn($item) => empty($item['block_text']) && empty($item['block_logo']) && empty($item['accept_logo']))->pluck('text')->toArray();
        }
        return [];
    }

    /**
     * @return array
     */
    private function getBlockTextTradeMarkList(): array
    {
        $trademarks = $this->getTradeMarkList();
        if (!empty($trademarks)) {
            return collect($trademarks)->filter(fn($item) => !empty($item['block_text']))->pluck('text')->toArray();
        }
        return [];
    }

    /**
     * @return array
     */
    private function getBlockLogoTradeMarkList(): array
    {
        $trademarks = $this->getTradeMarkList();
        if (!empty($trademarks)) {
            return collect($trademarks)->filter(fn($item) => !empty($item['block_logo']))->pluck('text')->toArray();
        }
        return [];
    }

    /**
     * @return array
     */
    private function getAcceptLogoTradeMarkList(): array
    {
        $trademarks = $this->getTradeMarkList();
        if (!empty($trademarks)) {
            return collect($trademarks)->filter(fn($item) => !empty($item['accept_logo']))->pluck('text')->toArray();
        }
        return [];
    }

    /**
     * @param string $imageUrl
     * @return int
     */
    private function checkTineye(string $imageUrl): int
    {
        $apiKey = config('senprints.tineye_api_key');
        if (!$apiKey) {
            return 0;
        }
        try {
            $result = Http::withoutVerifying()
                ->withHeaders(['x-api-key' => $apiKey])
                ->get('https://api.tineye.com/rest/search/', [
                    'image_url' => $imageUrl,
                    'limit' => 0 // we don't need any result
                ]);

            if ($result->failed()) {
                return 0;
            }
            $json = $result->json();
            return (int)$json['stats']['total_results'];
        } catch (\Throwable $e) {
            logException($e, 'checkTineye -> Url: ' . $imageUrl, $this->channelLog, true);
            return 0;
        }
    }

    /**
     * @param $log
     * @param bool $mention
     * @return void
     */
    private function logToDiscord($log, bool $mention = false): void
    {
        $message = '';

        if ($mention && app()->isProduction()) {
            $message .= mentionDiscord(DiscordUserIdEnum::THANGNM) . "\n";
        }

        $message .= "Seller ID: # {$this->campaign->seller_id}\nCampaign ID: #" . $this->campaign->id . "\nFile ID: #" . $this->file->id . "\n";
        logToDiscord($message . $log, $this->channelLog);
    }
}
