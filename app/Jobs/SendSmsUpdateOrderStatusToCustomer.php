<?php

namespace App\Jobs;

use App\Enums\TelegramUserIdEnum;
use App\Http\Controllers\SmsController;
use App\Models\Order;
use App\Services\SMSService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Str;

class SendSmsUpdateOrderStatusToCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $order = Order::findOrFail($this->orderId);
        $phoneNumber = $order->formatted_phone_number;

        if (is_string($phoneNumber)) {
            $url = 'https://' . $order->store_domain . '/order/status/' . $order->access_token;
            $makeShortUrl = SmsController::generateShortUrl($url);

            if ($makeShortUrl->isError()) {
                logToTelegram(TelegramUserIdEnum::JAMES, "Gen short url failed: \r\n{$url}");
                return;
            }

            $order->load(['store']);

            $storeName = optional($order->store)->name;
            $customerName = explode('-', Str::slug($order->customer_name));
            $customerName = ucfirst($customerName[0]);

            $template = '[{{ $store_name }}] {{ $customer_name }}, your order has been printed. Pls track its deliver status here {{ $short_url }}. Reply STOP to opt-out';

            $message = Blade::render($template, [
                'store_name' => $storeName,
                'customer_name' => $customerName,
                'short_url' => $makeShortUrl->shortUrl
            ]);

            $smsId = Str::uuid();
            $sendBy = SMSService::getDriver($phoneNumber);
            $sms = new SMSService($smsId, $message, $phoneNumber, $sendBy, null, null, $order->id, $order->seller_id);
            [$status, $twilioMsg] = $sms->sendNow();

            if ($status === 'duplicate' || $status === 'error') {
                graylogInfo("Duplicate hash!", [
                    'category' => 'tracking_status_logs',
                    'text' => json_encode($twilioMsg),
                    'status' => $status,
                    'action' => 'resend_sms',
                ]);
            } else {
                graylogInfo('[17Track-SmartRemarketing] The order #' . $order->id . ' has been printed, push sms to: ' . $phoneNumber . '. Content: ' . $message, [
                    'category' => 'tracking_status_logs',
                ]);
            }
        }
    }
}
