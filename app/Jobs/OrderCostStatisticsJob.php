<?php

namespace App\Jobs;

use App\Enums\OrderStatus;
use App\Enums\QueueName;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OrderCostStatisticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;

        $this->onQueue(QueueName::ORDER_STATISTICS);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        /** @var Order $order */
        $order = Order::with('products')->findOrFail($this->orderId);

        if ($this->shouldCalculate($order)) {
            $order->calcTotalShippingCostAndProfit()->push();
        }
    }


    /**
     * Chỉ tính toán khi đơn hàng đã thanh toán và chưa hoàn trả
     *
     * @param     \App\Models\Order     $order
     *
     * @return bool
     */
    private function shouldCalculate(Order $order): bool
    {
        return $order->paid_at && !in_array($order->fulfill_status, [OrderStatus::REFUNDED, OrderStatus::CANCELLED], true);
    }

    /**
     * @param $exception
     *
     * @return void
     */
    public function fail($exception = null): void
    {
        logException($exception, __CLASS__);
    }
}
