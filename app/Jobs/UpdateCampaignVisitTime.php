<?php
namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\CampaignArchivedStatus;
use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class UpdateCampaignVisitTime implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $queueName = 'log';
    protected $id;

    public function __construct($id)
    {
        $this->id = $id;
        $this->onQueue($this->queueName);
    }

    public function handle()
    {
        try {
            $campaignId = $this->id;
            $visitTime = Carbon::now()->startOfHour();
            cache()->remember($campaignId . '-' . $visitTime->timestamp, CacheKeys::CACHE_1H, function () use ($campaignId, $visitTime) {
                $campaign =  Product::query()->withTrashed()->where('id', $campaignId)->first();
                if (!$campaign) {
                    return null;
                }
                Product::query()
                    ->withTrashed()
                    ->where('id', $campaignId)
                    ->orWhere('campaign_id', $campaignId)
                    ->update([
                        'visited_at' => $visitTime,
                        'sync_status' => Product::SYNC_DATA_STATS_ENABLED,
                        'archived' => CampaignArchivedStatus::NOT_ARCHIVED,
                    ]);
                return $visitTime;
            });
        } catch (\Throwable $e) {
            logException($e);
        }
    }
}
