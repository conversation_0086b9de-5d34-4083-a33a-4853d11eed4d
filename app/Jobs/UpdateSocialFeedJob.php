<?php

namespace App\Jobs;

use App\Enums\SocialFeedTypeEnum;
use App\Models\SocialFeed;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateSocialFeedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        SocialFeed::query()
            ->where('type', SocialFeedTypeEnum::DAILY)
            ->get()->each(function (SocialFeed $socialFeed) {
                CreateExportFileSocialFeed::dispatch($socialFeed->id);
        });
    }
}
