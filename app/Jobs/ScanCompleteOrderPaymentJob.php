<?php

namespace App\Jobs;

use App\Enums\CurrencyEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\QueueName;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Traits\Encrypter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Stripe\StripeClient;

class ScanCompleteOrderPaymentJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Encrypter;

    protected Order $order;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
        $this->onQueue(QueueName::ORDER_EVENTS);
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return 'scan-order:' . ($this->order->id ?? 0);
    }

    /**
     * Get the cache driver for the unique job lock.
     */
    public function uniqueVia(): Repository
    {
        return Cache::driver('database');
    }

    /**
     * @return int
     */
    public function handle(): int
    {
        try {
            if (empty($this->order) || empty($this->order->payment_gateway_id) || $this->order->payment_status === OrderPaymentStatus::PAID) {
                return 0;
            }
            $order_id = $this->order->id;
            $order_number = $this->order->order_number;
            $seller_id = $this->order->seller_id;
            $paymentGatewayId = $this->order->payment_gateway_id;
            $search_order_id = Str::of($order_number)->explode('-')->last();
            graylogInfo('Start scan Order ID: ' . $order_id, [
                'category' => 'pending_payment_order',
                'action' => 'job',
                'completed' => 0,
                'order_id' => $order_id,
                'payment_gateway_id' => $paymentGatewayId,
            ]);
            $gateways = PaymentGateway::query()
                ->where('gateway', 'stripe')
                ->where(function ($q) use ($seller_id, $paymentGatewayId) {
                    $q->where('seller_id', $seller_id);
                    $q->orWhere('id', $paymentGatewayId);
                })
                ->where(function ($q) {
                    $q->where('active', 1);
                    $q->orWhere(function ($qr) {
                        $qr->where('active', 0)
                            ->where('updated_at', '>=', now()->clone()->subDays(2));
                    });
                })
                ->get();
            foreach ($gateways as $gateway) {
                $config = self::safeLoadConfig($gateway);
                $configObj = json_decode($config);
                $stripe = new StripeClient(['api_key' => $configObj->secret_key, 'stripe_version' => '2020-08-27']);
                try {
                    $result = $stripe->charges->search(['query' => 'metadata[\'order_id\']:\'' . $search_order_id .'\'']);
                } catch (\Throwable $e) {
                    graylogInfo('Order not paid. Order ID: ' . $order_id, [
                        'category' => 'pending_payment_order',
                        'action' => 'job',
                        'completed' => 0,
                        'order_id' => $order_id,
                        'payment_gateway_id' => $paymentGatewayId,
                        'gateway_id' => $gateway->id,
                        'error' => $e->getMessage()
                    ]);
                    continue;
                }
                if (empty($result->data)) {
                    try {
                        $result = $stripe->charges->search(['query' => 'metadata[\'order_id\']:\'' . $order_id .'\'']);
                    } catch (\Throwable $e) {
                        graylogInfo('Order not paid. Order ID: ' . $order_id, [
                            'category' => 'pending_payment_order',
                            'action' => 'job',
                            'completed' => 0,
                            'order_id' => $order_id,
                            'payment_gateway_id' => $paymentGatewayId,
                            'gateway_id' => $gateway->id,
                            'error' => $e->getMessage()
                        ]);
                        continue;
                    }
                    if (empty($result->data)) {
                        graylogInfo('Order not paid. Order ID: ' . $order_id, [
                            'category' => 'pending_payment_order',
                            'action' => 'job',
                            'completed' => 0,
                            'order_id' => $order_id,
                            'payment_gateway_id' => $paymentGatewayId,
                            'gateway_id' => $gateway->id,
                            'error' => "No payment intent data"
                        ]);
                        continue;
                    }
                }
                $charge = $result->data[0];
                $paymentObjectId = $charge->payment_intent;
                if (!$charge->paid || $charge->status !== 'succeeded') {
                    graylogInfo('Order not paid. Order ID: ' . $order_id, [
                        'category' => 'pending_payment_order',
                        'action' => 'job',
                        'completed' => 0,
                        'order_id' => $order_id,
                        'payment_gateway_id' => $paymentGatewayId,
                        'gateway_id' => $gateway->id,
                        'error' => "Order not paid",
                        'charge' => $charge
                    ]);
                    continue;
                }
                $totalPaid = $charge->amount_captured / 100;
                $currencyCode = strtoupper($charge->currency);
                if ($currencyCode === CurrencyEnum::EUR) {
                    $currencyRate = $totalPaid / $this->order->total_amount;
                    $totalPaid = $this->order->total_amount;
                } else {
                    $currencyRate = null;
                    $currencyCode = null;
                }
                $this->order->paymentCompleted($totalPaid, $paymentObjectId, false, $currencyCode, $currencyRate, $gateway->id);
                graylogInfo('Completed Order ID: ' . $order_id, [
                    'category' => 'pending_payment_order',
                    'action' => 'job',
                    'completed' => 1,
                    'order_id' => $order_id,
                    'payment_gateway_id' => $paymentGatewayId,
                    'gateway_id' => $gateway->id,
                    'transaction_id' => $paymentObjectId,
                    'charge' => $charge
                ]);
                break;
            }
            return 1;
        } catch (\Throwable $e) {
            $this->fail($e);
            return 0;
        }
    }
}
