<?php

namespace App\Jobs;

use App\Models\ContactFormLogs;
use App\Traits\ContactForm;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendContactFormToCrisp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use ContactForm;

    public string $contactFormLogId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $contactFormLogId)
    {
        $this->contactFormLogId = $contactFormLogId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $log = ContactFormLogs::query()->find($this->contactFormLogId);

        if (!$log) {
            graylogError('Contact form log not found', [
                'category' => 'contact_form_crisp',
                'log_id' => $this->contactFormLogId
            ]);
            return;
        }

        // we already checked this in the command:
        // \App\Console\Commands\ResendContactFormToCrisp::handle()
        // but just in case
        if ($log->sent_to_crisp) {
            return;
        }

        $data = $log->toArray();

        $store = $log->store;

        if (!is_object($store)) {
            return;
        }

        $seller = $store->seller;

        $orderNumber = $log->order_number;

        // add missing data
        try {
            $data['order_status_url'] = self::getOrderStatusUrl($orderNumber, $log->customer_email);
            $data['seller_id'] = $store->seller_id;
            $data['is_custom_store'] = $seller->custom_payment ? 1 : 0;
            $data['cc'] = $store->cc_email;
            $data['store_name'] = $store->name;
            $data['seller_email'] = $seller->email;
            $data['segments'] = ['resend'];
        } catch (\Exception $e) {
            $this->fail($e);
        }

        try {
            self::sendToCrisp($data);

            // mark as sent
            $log->sent_to_crisp = true;
            $log->save();
        } catch (\Exception $e) {
            $this->fail($e);
        }
    }
}
