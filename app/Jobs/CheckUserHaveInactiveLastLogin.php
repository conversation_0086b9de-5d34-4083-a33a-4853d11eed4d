<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Models\User;
use App\Models\UserLog;
use App\Services\InactiveService;
use App\Traits\InactiveTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CheckUserHaveInactiveLastLogin implements ShouldQueue
{
    use InactiveTrait;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $sellers;
    private bool $shouldSend1StMail;

    private ?string $appointMail;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $sellers, $processId, $token, $type, $category, $limit, $count, $shouldSend1StMail, $appointMail = null)
    {
        $this->sellers = $sellers;
        $this->processId = $processId;
        $this->token = $token;
        $this->fileType = 'Job';
        $this->fileName = $this->fileType . ':' . basename(__FILE__);
        $this->type = $type;
        $this->category = $category;
        $this->days = isEnvLocalOrDev() ? 8 : 30;
        $this->limit = $limit;
        $this->count = $count;
        $this->sixMonthAgoDateTime = now()->clone()->subMonths(6)->toDateTimeString();
        $this->shouldSend1StMail = $shouldSend1StMail;
        $this->appointMail = $appointMail;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            InactiveService::logToGraylog($this->processId, $this->fileName, 'Start...', $this->category);
            $startEsTime = round(microtime(true) * 1000);
            // *** get inactive users last login
            $sellerInactiveLastLogin = $this->getUsersInactiveLastLogin($this->sellers);
            $endEsTime = round(microtime(true) * 1000);
            $esTime = InactiveService::getEstimatedTime($startEsTime, $endEsTime);
            InactiveService::logToGraylog($this->processId, $this->fileName, 'Total Time to check [Inactive Last login] of sellers in this scan: ' . $esTime . 'ms', $this->category);

            if (!empty($sellerInactiveLastLogin)) {
                $userIds = array_keys($sellerInactiveLastLogin);
                $newUserIds = InactiveService::newUserIdsHaveTypeInactive($userIds, $this->type);

                if (!empty($newUserIds)) {
                    $newSellerInactiveLastLogin = array_intersect_key($sellerInactiveLastLogin, array_flip($newUserIds));
                    $startEsTime = round(microtime(true) * 1000);
                    // *** insert new users to clean settings
                    $result = InactiveService::insertNewUsersCleanSettings($newSellerInactiveLastLogin, $this->type, $this->token);
                    $endEsTime = round(microtime(true) * 1000);
                    $esTime = InactiveService::getEstimatedTime($startEsTime, $endEsTime);
                    InactiveService::logToGraylog($this->processId, $this->fileName, 'Total Time to insert new users to clean settings: ' . $esTime . 'ms', $this->category);

                    if ($result) {
                        Cache::put('last_inactive_last_login_token', $this->token, CacheKeys::CACHE_24H);
                        //*** Mail: Only local or dev
                        if ($this->shouldSend1StMail) {
                            if (!is_null($this->appointMail)) {
                                $config = InactiveService::appointConfigMail($this->appointMail, $this->type, $this->token);
                                sendEmail($config);
                                InactiveService::logToGraylog($this->processId, $this->fileName, 'Config appointMail: ' . $this->appointMail, $this->category);
                            } else {
                                InactiveService::send1stWarningMail($newSellerInactiveLastLogin, $this->type, $this->token);
                            }
                        } else {
                            InactiveService::logToGraylog($this->processId, $this->fileName, 'Config send_1st_mail: False' , $this->category);
                        }
                        $this->logReportAfterScan($newSellerInactiveLastLogin);
                    } else {
                        InactiveService::logToGraylog($this->processId, $this->fileName, 'Insert NewUsersCleanSettings return false ', $this->category, true);
                    }
                } else {
                    InactiveService::logToGraylog($this->processId, $this->fileName, 'Cannot find any new users who have ' . $this->type , $this->category, true);
                }
            } else {
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Cannot find any users who have ' . $this->type, $this->category, true);
            }
        } catch (\Exception $exception) {
            InactiveService::logExceptionToGraylog($this->processId, $this->fileName, $this->type, $exception);
            return;
        }
    }

    private function logReportAfterScan(array $newUsersChecked): void
    {
        $newUserIdsChecked = implode(", ", array_keys($newUsersChecked));

        $context = [
            'category' => $this->type,
            'newIds' => $newUserIdsChecked,
        ];

        InactiveService::logToGraylog($this->processId, $this->fileName, 'Numbers new users id who have ' . $this->type . ' : ' . count($newUsersChecked), $context, true);
    }

    private function getUsersInactiveLastLogin(array $sellers): array
    {
        //6 tháng ko login và ko có sales -> inactive_last_login

        $sellerIds = array_keys($sellers);
        $excludeSellers = User::query()
            ->whereIn('id', $sellerIds)
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('order')
                    ->where('payment_status', 'paid')
                    ->whereNotNull('paid_at')
                    ->where('status', '!=', 'cancelled')
                    ->where('order.paid_at', '>', $this->sixMonthAgoDateTime)
                    ->whereRaw('order.seller_id = user.id');
            })
            ->get()
            ->pluck('id')
            ->toArray();

        //get sellerIds that have no orders in the last 6 months -> go to checking inactive_last_login
        $sellerIds = array_values(array_diff($sellerIds, $excludeSellers));

        $userIdsWithLogs = UserLog::query()
            ->whereIn('user_id', $sellerIds)
            ->distinct()
            ->pluck('user_id')
            ->all();

        // Users without any logs -> inactive_last_login
        $userIdsWithoutLogs = array_diff($sellerIds, $userIdsWithLogs);

        // Users with logs -> checking and get inactive_last_login
        $inactiveUsersWithLogs = UserLog::query()
            ->whereIn('user_id', $userIdsWithLogs)
            ->select('user_id', DB::raw('MAX(created_at) as last_login'))
            ->groupBy('user_id')
            ->havingRaw('MAX(created_at) < ?', [$this->sixMonthAgoDateTime])
            ->pluck('user_id')
            ->all();

        //result
        $allInactiveUsers = array_merge($userIdsWithoutLogs, $inactiveUsersWithLogs);
        return array_intersect_key($sellers, array_flip($allInactiveUsers));
    }
}
