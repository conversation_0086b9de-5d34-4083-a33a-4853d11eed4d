<?php

namespace App\Jobs;

use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderHistoryActionEnum;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Services\AddressService;
use App\Services\MailService;
use App\Services\OrderService;
use App\Traits\CorrectFulfillStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Models\RegionOrders;

class VerifyCustomerAddress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, CorrectFulfillStatus;

    protected $order;
    protected $force = false; // ignore the address_verified field
    protected $reconfirm = false;
    protected $sendMail = false;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order|RegionOrders $order, bool $force = false, bool $reconfirm = false, $sendMail = false)
    {
        $this->order = $order;
        $this->force = $force;
        $this->reconfirm = $reconfirm;
        $this->sendMail = $sendMail;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $order = $this->order;
        if ($order->isCustomServiceOrder() || $order->isServiceOrder()) {
            return;
        }
        if ($order->address_verified === OrderAddressVerifiedEnum::INVALID && $this->sendMail) {
            MailService::sendMailInvalidAddressNotification($order);
            return;
        }
        if ($order->address_verified !== OrderAddressVerifiedEnum::UNVERIFIED && !$this->force) {
            return;
        }

        $isValidAddress = $this->reconfirm ? AddressService::easyPostVerify($order) : AddressService::verify($order);

        if ($isValidAddress) {
            $order->address_verified = OrderAddressVerifiedEnum::VERIFIED;

            if ($this->reconfirm && empty($order->admin_note)) { // if not hold by CS
                $order->fulfill_status = self::correctFulfillStatus($order);
            }
        } else {
            $order->address_verified = OrderAddressVerifiedEnum::INVALID;

            if ($this->reconfirm) {
                $order->admin_note = OrderHistoryActionEnum::NEED_VERIFY_ADDRESS_MANUALLY;
            }
        }

        $order->save();
        $order = $order->refresh();
        $verifyStatus = $isValidAddress ? 'valid' : 'invalid';
        $reVerify = $this->reconfirm ? 'true' : 'false';
        if (!$isValidAddress && $this->sendMail) {
            MailService::sendMailInvalidAddressNotification($order);
        }
        $orderRegion = $order->region;
        $orderHistoryInsertData = [
            $order,
            OrderHistoryActionEnum::REVALIDATE_CUSTOMER_ADDRESS,
            'Customer address is ' . $verifyStatus,
        ];
        if ($orderRegion) {
            RegionOrderHistory::onRegion($orderRegion)->getModel()->insertLog(
                ...$orderHistoryInsertData
            );
        } else {
            OrderHistory::insertLog(
                ...$orderHistoryInsertData
            );
        }
        OrderService::processCustomOrder($order);
        graylogInfo("Verify address order #$order->id: $verifyStatus" . ($this->reconfirm ? ' - rechecked' : ''), [
            'category' => 'verify_order_address',
            'order_id' => $order->id,
            'status' => $verifyStatus,
            'is_re_verify' => $reVerify
        ]);
    }
}
