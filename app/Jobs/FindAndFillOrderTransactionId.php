<?php

namespace App\Jobs;

use App\Enums\OrderPaymentStatus;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Traits\Encrypter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Stripe\StripeClient;

class FindAndFillOrderTransactionId implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Encrypter;

    protected Order $order;

    protected bool $isFillTransactionId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order $order, bool $isFillTransactionId = false)
    {
        $this->order = $order;
        $this->isFillTransactionId = $isFillTransactionId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if (empty($this->order) || $this->order->payment_status !== OrderPaymentStatus::PAID) {
                graylogInfo('Order not paid. - Order ID: ' . $this->order->id, [
                    'category' => 'scan_order_transaction_id',
                    'action' => 'job',
                    'completed' => 0,
                    'order_id' => $this->order->id,
                    'payment_gateway_id' => $this->order->payment_gateway_id,
                ]);
            } else {
                $order_id = $this->order->id;
                $order_number = $this->order->order_number;
                $seller_id = $this->order->seller_id;
                $paymentGatewayId = $this->order->payment_gateway_id;
                if (!preg_match('/\d-\w+$/', $order_number)) {
                    return;
                }
                $search_order_id = Str::of($order_number)->explode('-')->last();
                graylogInfo('Start scan Order ID: ' . $order_id, [
                    'category' => 'scan_order_transaction_id',
                    'action' => 'job',
                    'completed' => 0,
                    'order_id' => $order_id,
                    'payment_gateway_id' => $paymentGatewayId,
                ]);
                $gateways = PaymentGateway::query()
                    ->where('gateway', 'LIKE', 'stripe%')
                    ->where(function ($q) use ($seller_id, $paymentGatewayId) {
                        $q->where('seller_id', $seller_id);
                        $q->orWhere('id', $paymentGatewayId);
                    })
                    ->get();

                foreach ($gateways as $gateway) {
                    $config = self::safeLoadConfig($gateway);
                    $configObj = json_decode($config);
                    $stripe = new StripeClient(['api_key' => $configObj->secret_key, 'stripe_version' => '2020-08-27']);
                    try {
                        $result = $stripe->charges->search(['query' => 'metadata[\'order_id\']:\'' . $search_order_id . '\'']);
                    } catch (\Throwable $e) {
                        graylogInfo('Exception error: ' . $e->getMessage(), $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway));
                        continue;
                    }
                    if (empty($result->data)) {
                        try {
                            $result = $stripe->charges->search(['query' => 'metadata[\'order_id\']:\'' . $order_id .'\'']);
                        } catch (\Throwable $e) {
                            graylogInfo('Exception error: ' . $e->getMessage(), $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway));
                            continue;
                        }
                        if (empty($result->data)) {
                            graylogInfo('Order not paid. - Order ID: ' . $order_id . ' on Gateway ID: ' . $gateway->id, $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway));
                            continue;
                        }
                    }
                    $charge = $result->data[0];
                    $paymentObjectId = $charge->payment_intent;
                    if (!$charge->paid || $charge->status !== 'succeeded') {
                        graylogInfo('Charge not paid. Order ID: ' . $order_id, $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway, $paymentObjectId));
                    } else {
                        graylogInfo('Found transaction_id:' . $paymentObjectId . 'For Order ID: ' . $order_id, $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway, $paymentObjectId));

                        $this->order->transaction_id = $paymentObjectId;
                        $this->order->payment_gateway_id = $gateway->id;

                        if ($this->isFillTransactionId) {
                            if ($this->order->save()) {
                                graylogInfo('Update transaction id for order success. Order ID: ' . $order_id, $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway, $paymentObjectId));
                            } else {
                                graylogInfo('Update transaction id for order failed. Order ID: ' . $order_id, $this->setGrayLogContext($order_id, $paymentGatewayId, $gateway, $paymentObjectId));
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            graylogInfo('Exception error: ' . $e->getMessage(), [
                'category' => 'scan_order_transaction_id',
                'action' => 'job',
                'completed' => 0,
            ]);
        }
    }

    private function setGrayLogContext($order_id, $paymentGatewayId, $gateway, $transactionId = null): array
    {
        $context = [
            'category' => 'scan_order_transaction_id',
            'action' => 'job',
            'order_id' => $order_id,
            'payment_gateway_id' => $paymentGatewayId,
            'gateway_id' => $gateway->id,
        ];

        $transactionId ?? $context['transaction_id'] = $transactionId;

        return $context;
    }
}
