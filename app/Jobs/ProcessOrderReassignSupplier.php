<?php
namespace App\Jobs;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderSupportStatusEnum;
use App\Enums\ShippingMethodEnum;
use App\Http\Controllers\Admin\FulfillController;
use App\Models\Order;
use App\Models\OrderAssignSupplierHistory;
use App\Models\OrderHistory;
use App\Models\Supplier;
use App\Models\SystemLocation;
use App\Services\ClassifyDesignService;
use App\Services\FulfillmentService;
use App\Services\TradeMarkService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class ProcessOrderReassignSupplier implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        $order = Order::query()->whereKey($this->orderId)->first();
        if (!$order) {
            throw new \RuntimeException("Order not found. Order Id: " . $this->orderId);
        }
        $order->load('products');
        $orderLocation = SystemLocation::findByCountryCodeThenSetForAssign($order);
        if (!$orderLocation) {
            throw new \RuntimeException("Order location not found. Order Id: " . $this->orderId);
        }
        try {
            $onHoldScriptMessage = '';
            $logMessage = [];
            $logs = [];
            $excludeSuppliers = [];
            $violatedKeywords = [];
            $isHoldOrderFulfill = false;
            $isViolatedTM = false;
            $designTypeNotSupport = '';
            $isNotSupportExpressShipping = false;
            $isOrderHasDesignLeftChest = false;
            $continueCheckShippingMethod = isset($order->shipping_method) && $order->shipping_method === ShippingMethodEnum::EXPRESS;
            $trademark_suppliers = Supplier::query()->select('id')->with(['trademark_list' => function ($q) {
                $q->select('text', 'supplier_id');
            }])->where('status', 1)->whereHas('trademark_list')->get();
            if ($continueCheckShippingMethod) {
                $excludeSuppliers = Supplier::query()->select('id')->where('status', 1)->where('no_express_line_ship', 1)->get()->pluck('id')->toArray();
            }
            foreach ($order->products as $product) {
                if ($product->skipAssignSupplier()) {
                    continue;
                }
                $product->load('supplier');
                $supplier = $product->supplier;
                if (!$supplier) {
                    continue;
                }
                $oldSupplierId = (int) $product->supplier_id;
                $oldSupplierName = $product->supplier_name;
                $cacheKey = 'reassign_supplier_' . $product->id . '_' . $oldSupplierId . '_' . $this->orderId;
                if (cache()->has($cacheKey)) {
                    continue;
                }
                cache()->put($cacheKey, md5($cacheKey), 30);
                if (!isset($logMessage[$product->id])) {
                    $logMessage[$product->id] = '';
                }
                /*
                 * Handle no express ship.
                 */
                if ($continueCheckShippingMethod && $supplier->no_express_line_ship) {
                    $logMessage[$product->id] .= 'Re-assigned because supplier is not ship express ';
                    $onHoldScriptMessage .= ' supplier no ship express,';
                    $isNotSupportExpressShipping = true;
                }

                /*
                 * Handle corner placement.
                 */
                if ($product->is_corner_placement && $order->getIsCornerPlacement()) {
                    $cfg = suppliers()->where('supplier_id', $oldSupplierId)->first();
                    if ($cfg && data_get($cfg, 'skip_corner_placement', false)) {
                        $excludeSuppliers[] = $oldSupplierId;
                        $logMessage[$product->id] .= 'Re-assigned because order has design corner placement ';
                        $onHoldScriptMessage .= ' supplier dont support corner placement,';
                        $isOrderHasDesignLeftChest = true;
                    }
                }

                /*
                 * Handle trademark.
                 */
                if (!empty($supplier->trademark_keywords) || $trademark_suppliers->isNotEmpty()) {
                    $campaignId = $product->campaign_id;
                    $keywords = TradeMarkService::getTradeMarkKeywordByCampaignId($product->seller_id, $campaignId);
                    if (!empty($keywords)) {
                        $trademarkKeywords = collect($supplier->trademark_keywords)->filter()->unique()->values()->map(fn($keyword) => trim($keyword))->map(fn($keyword) => strtolower($keyword))->toArray();
                        $isViolated = false;
                        foreach ($keywords as $keyword) {
                            if (TradeMarkService::isTradeMarkKeyword($trademarkKeywords, $keyword)) {
                                $isViolated = true;
                                $violatedKeywords[] = $keyword;
                                $isViolatedTM = true;
                                break;
                            }
                        }
                        if ($isViolated) {
                            $excludeSuppliers[] = $oldSupplierId;
                            $logMessage[$product->id] .= 'Re-assigned because of TM ';
                            $onHoldScriptMessage .= ' violated supplier TM,';
                            // Check other suppliers
                            if ($trademark_suppliers->isNotEmpty()) {
                                foreach ($trademark_suppliers as $tm_supplier) {
                                    $trademarkKeywords = $tm_supplier->trademark_list->pluck('text')->filter()->unique()->values()->map(fn($keyword) => trim($keyword))->map(fn($keyword) => strtolower($keyword))->toArray();
                                    $isViolated = false;
                                    foreach ($keywords as $keyword) {
                                        if (TradeMarkService::isTradeMarkKeyword($trademarkKeywords, $keyword)) {
                                            $isViolated = true;
                                            $isViolatedTM = true;
                                            $violatedKeywords[] = $keyword;
                                            break;
                                        }
                                    }
                                    if ($isViolated) {
                                        $excludeSuppliers[] = $tm_supplier->id;
                                    }
                                }
                            }
                        }
                    }
                }
                $files = collect();
                if (Str::isJson($product->options)) {
                    $options = json_decode($product->options, false, 512, JSON_THROW_ON_ERROR);
                    $color = correctOptionValue(optional($options)->color);
                    $size = null;
                    if ($product->isFullPrintedType()) {
                        $size = correctOptionValue(optional($options)->size);
                    }
                    [$files,] = FulfillmentService::getOrderProductFiles($product->id, $order->id, $product->product_id, $size, $color, false, false, $product);
                }
                $suppliersHasSupportType = suppliers()->whereNotNull('support_type')->select('supplier_id', 'support_type')->values();
                $supplierSupportType = [];
                $supplierIdSupportType = [];
                if ($suppliersHasSupportType->isNotEmpty()) {
                    $supplierConfig = $suppliersHasSupportType->where('supplier_id', $oldSupplierId)->first();
                    $supplierSupportType = $supplierConfig ? data_get($supplierConfig, 'support_type', []) : [];
                    $supplierIdSupportType = $suppliersHasSupportType->pluck('supplier_id')->toArray();
                }

                if (!empty($supplierSupportType) && !in_array($oldSupplierId, $excludeSuppliers, true) && $files->isNotEmpty()) {
                    foreach ($files as $file) {
                        if (!$file->file_url) {
                            continue;
                        }
                        $designType = ClassifyDesignService::instance()->handle(imgUrl($file->file_url, 'full_hd'), $order->id);
                        if ($designType !== '' && !in_array($designType, $supplierSupportType, true)) {
                            $designTypeNotSupport = $designType;
                            $excludeSuppliers[] = $oldSupplierId;
                            $logMessage[$product->id] .= 'Re-assigned because of supplier not support (' . $designType . ')';
                            $onHoldScriptMessage .= ' supplier not support (' . $designType . '),';
                            break;
                        }
                    }
                }

                if (!empty($excludeSuppliers)) {
                    $excludeSuppliers = array_map('intval', array_values(array_unique($excludeSuppliers)));
                    $supplierName = FulfillController::reAssignSupplierByLocationWithExcludeSupplier($product, $orderLocation, $excludeSuppliers);
                    if ($product->supplier_id && $product->wasReAssignSupplier()) {
                        // Re-assign supplier after check trademark and design type
                        if ($suppliersHasSupportType->isNotEmpty() && in_array((int) $product->supplier_id, $supplierIdSupportType, true) && $files->isNotEmpty()) {
                            $supplierConfig = $suppliersHasSupportType->where('supplier_id', $product->supplier_id)->first();
                            $supplierSupportType = $supplierConfig ? data_get($supplierConfig, 'support_type', []) : [];
                            graylogInfo('Start detect design type - Order ID: ' . $this->orderId, [
                                'category' => 'detect_design_type',
                                'order_id' => $this->orderId,
                                'supplier_id' => $product->supplier_id,
                                'file_ids' => $files->pluck('id')->toArray(),
                            ]);
                            foreach ($files as $file) {
                                if (!$file->file_url) {
                                    continue;
                                }
                                $designType = ClassifyDesignService::instance()->handle(imgUrl($file->file_url, 'full_hd'), $order->id);
                                if ($designType !== '') {
                                    $designTypeNotSupport = $designType;
                                    $suppliersHasSupportType->each(function ($supplier) use ($designType, &$excludeSuppliers) {
                                        if (in_array((int) $supplier['supplier_id'], $excludeSuppliers, true)) {
                                            return;
                                        }
                                        if (!in_array($designType, $supplier['support_type'], true)) {
                                            $excludeSuppliers[] = $supplier['supplier_id'];
                                        }
                                    });
                                    if (!in_array($designType, $supplierSupportType, true)) {
                                        $excludeSuppliers[] = $product->supplier_id;
                                        $logMessage[$product->id] .= 'Re-assigned because of supplier not support (' . $designType . ')';
                                        $onHoldScriptMessage .= ' supplier not support (' . $designType . '),';
                                        break;
                                    }
                                }
                            }
                            $excludeSuppliers = array_map('intval', array_values(array_unique($excludeSuppliers)));
                            $supplierName = FulfillController::reAssignSupplierByLocationWithExcludeSupplier($product, $orderLocation, $excludeSuppliers);
                            if (!$product->supplier_id || !$product->wasReAssignSupplier()) {
                                $isHoldOrderFulfill = true;
                            }
                        }
                    } else {
                        $isHoldOrderFulfill = true;
                    }
                    if (!$isHoldOrderFulfill) {
                        OrderAssignSupplierHistory::createFromOrderProduct($product);
                        if ($product->cross_shipping) {
                            $order->markCrossShipping(true);
                        }
                        $textSupplier = $supplierName ? "From $oldSupplierName To $supplierName" : null;
                        $logs[] = [$logMessage[$product->id], $product->id, $product->product_name, $textSupplier];
                        $product->save();
                        graylogInfo('Re-assigned to other supplier - Order ID: ' . $this->orderId, [
                            'category' => 'reassign_supplier',
                            'order_id' => $this->orderId,
                            'exclude_suppliers' => $excludeSuppliers,
                        ]);
                    }
                }
            }
            $message = "";
            if ($isHoldOrderFulfill) {
                $violatedKeywords = array_unique($violatedKeywords);
                $message .= 'Can not re-assigned to other supplier.';
                if ($isViolatedTM) {
                    $message .= ' Violated TM: ' . implode(', ', $violatedKeywords);
                }
                if ($isNotSupportExpressShipping) {
                    $message .= ' No supplier support ship express';
                }
                if ($isOrderHasDesignLeftChest) {
                    $message .= ' No supplier support design left chest';
                }
                if ($designTypeNotSupport !== '') {
                    $message .= ' Supplier is not support: ' . $designTypeNotSupport;
                }
                $order->update([
                    'support_status' => OrderSupportStatusEnum::NEED_REASSIGN_SUPPLIER_MANUALLY,
                ]);
                if ($order->isPaid() && in_array($order->getFulfillStatus(), [OrderFulfillStatus::UNFULFILLED, OrderFulfillStatus::DESIGNING, OrderFulfillStatus::REVIEWING, OrderFulfillStatus::INVALID, OrderFulfillStatus::NO_SHIP], true)) {
                    $note = "On hold because of $onHoldScriptMessage but product(s) can not re-assigned to other supplier.";
                    $order->update([
                        'fulfill_status' => OrderFulfillStatus::ON_HOLD,
                        'admin_note' => $note
                    ]);
                    OrderHistory::insertLog(
                        $order->refresh(),
                        OrderHistoryActionEnum::HOLD_ORDER,
                        $note
                    );
                }
                TradeMarkService::logToDiscord($this->orderId, $message, '15548997');
                graylogError('Can not re-assigned to other supplier - Order ID: ' . $this->orderId, [
                    'category' => 'reassign_supplier',
                    'order_id' => $this->orderId,
                    'exclude_suppliers' => $excludeSuppliers,
                    'violated_keywords' => $violatedKeywords,
                ]);
            } else if (!empty($logs)) {
                $detail = collect($logs)->map(fn($log) => implode(' - ', $log))->implode(PHP_EOL);
                $message .= $detail;
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::ASSIGNED_SUPPLIER,
                    $detail,
                );
                TradeMarkService::logToDiscord($this->orderId, $message);
            }
        } catch (\Throwable $e) {
            TradeMarkService::logToDiscord($this->orderId, $e->getMessage(), '15548997');
        }
    }
}
