<?php

namespace App\Helpers;

class ProductAssetsHelper
{
    /**
     * Replace product id in file path
     *
     * @param string $path
     * @param mixed $productId
     * @return string|null
     */
    public static function replaceIdFilePath(string $path, $oldProductId, $productId)
    {
        return preg_replace('/p\/' . $oldProductId . '\//', 'p/' . $productId . '/', $path);
    }

    /**
     * Replace product id in file url
     *
     * @param string $url
     * @param mixed $productId
     * @return string|null
     */
    public static function replaceIdFileUrl(string $url, $oldProductId, $productId)
    {
        return preg_replace('/s2\/p\/' . $oldProductId . '\//', 's2/p/' . $productId . '/', $url);
    }

    /**
     * Check if path is url
     *
     * @param string $path
     * @return int|false
     */
    public static function isAssetUrl(string $path)
    {
        return preg_match('/^(http|https)/i', $path);
    }

    /**
     * Check if path is product asset path
     *
     * @param string $path
     * @return int|false
     */
    public static function isAssetPath(string $path)
    {
        return preg_match('/^p\/\d+\//i', $path);
    }

    /**
     * Process file url
     *
     * @param string $fileUrl
     * @param mixed $productId
     * @param mixed $newProductId
     * @return string|null
     */
    public static function replaceIdFile(string $fileUrl, $productId, $newProductId)
    {
        if (self::isAssetPath($fileUrl)) {
            return self::replaceIdFilePath($fileUrl, $productId, $newProductId);
        }
        if (self::isAssetUrl($fileUrl)) {
            return self::replaceIdFileUrl($fileUrl, $productId, $newProductId);
        }
        return $fileUrl;
    }

    /**
     * Check if value is json
     *
     * @param string $value
     * @return bool
     */
    public static function isJson(string $value)
    {
        if (!is_string($value)) {
            return false;
        }
        if (empty($value)) {
            return false;
        }
        json_decode($value);
        return json_last_error() === JSON_ERROR_NONE;
    }
}
