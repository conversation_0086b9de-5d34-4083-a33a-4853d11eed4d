<?php

namespace App\Helpers;

class DesignJsonHelper
{
    /**
     * Replace urls
     *
     * @param array $json
     * @param mixed $productId
     * @param mixed $newProductId
     * @return array
     */
    public static function replaceId(array $json, $productId, $newProductId)
    {
        foreach ($json as $key => $value) {
            if (empty($value)) {
                continue;
            }
            if (is_array($value)) {
                $json[$key] = self::replaceId($value, $productId, $newProductId);
            } else {
                if (ProductAssetsHelper::isAssetPath($value)) {
                    $json[$key] = ProductAssetsHelper::replaceIdFilePath($value, $productId, $newProductId);
                }
                if (ProductAssetsHelper::isAssetUrl($value)) {
                    $json[$key] = ProductAssetsHelper::replaceIdFileUrl($value, $productId, $newProductId);
                }
            }
        }
        return $json;
    }

    /**
     * Extract urls
     *
     * @param array $json
     * @return array
     */
    public static function extractFilePaths(array $json)
    {
        $filePaths = [];
        foreach ($json as $key => $value) {
            if (empty($value)) {
                continue;
            }
            if (is_array($value)) {
                $filePaths = array_merge($filePaths, self::extractFilePaths($value));
            } else {
                if (ProductAssetsHelper::isAssetPath($value)) {
                    $filePaths[] = $value;
                }
                if (ProductAssetsHelper::isAssetUrl($value)) {
                    $filePath = self::extractFilePath($value);
                    if ($filePath) {
                        $filePaths[] = $filePath;
                    }
                }
            }
        }
        return $filePaths;
    }

    /**
     * Extract file path
     * @param string $url
     * @return string|null
     */
    public static function extractFilePath(string $url)
    {
        if (preg_match('/s2\/(p\/\d+\/[^\s\n\r]+\.\w{3,5})$/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }
}
