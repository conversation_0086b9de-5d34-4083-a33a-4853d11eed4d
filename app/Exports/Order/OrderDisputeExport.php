<?php

namespace App\Exports\Order;

use App\Enums\PaymentMethodEnum;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;


class OrderDisputeExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize, WithEvents
{
    protected collection $orderDisputes;
    protected array $fieldOptions = [];
    protected array $fieldPrintSpaces = [];
    public function __construct($orderDisputes)
    {
        $this->orderDisputes = $orderDisputes;
    }

    public function array(): array
    {
        $arr = [];
        foreach ($this->orderDisputes as $orderDispute) {
            if (!isset($orderDispute->order)) continue;
            try {
                $each = [];
                $flagLogs = '';
                if (isset($orderDispute->order->flag_logs) && count($orderDispute->order->flag_logs) > 0) {
                    $flagLogs .= 'Flag log:';

                    foreach ($orderDispute->order->flag_logs as $flagLog) {
                        $flagLogs .=  $flagLog . '. ';
                    }
                }
                $each[] = $flagLogs;
                // if ($orderDispute->is_fraud) {
                //     $flagLogs = '';
                //     if (count($orderDispute->order->flag_logs) > 0) {
                //         $flagLogs .= 'Flag log:';

                //         foreach ($orderDispute->order->flag_logs as $flagLog) {
                //             $flagLogs .=  $flagLog . '. ';
                //         }
                //     }
                //     $each[] = $flagLogs;
                // } else {
                //     $each[] = '';
                // }
                $orderDetail = $orderDispute->order;
                $payment = $orderDetail->payment_gateway;
                $each[] = isset($payment->account_id) ? $payment->account_id : '' . (isset($orderDetail->payment_method) ? ' (' . $orderDetail->payment_method . ')' : '');

                $each[] =  Carbon::parse($orderDispute->dispute_created_at)->format('Y-m-d');
                $each[] = $orderDispute->id;
                if (isset($orderDispute->order->payment_method) && $orderDispute->order->payment_method == PaymentMethodEnum::PAYPAL) {
                    $each[] = '';
                } else {
                    if ($orderDispute->klarna) {
                        $each[] = 'yes';
                    } else {
                        $each[] = 'no';
                    }
                }
                $each[] = strval($orderDispute->order->order_number ?? $orderDispute->order_id);
                $each[] = $orderDetail->created_at;
                $each[] = $orderDetail->total_amount;
                if (isset($orderDispute->staff) && isset($orderDispute->staff->name)) {
                    $each[] = $orderDispute->staff->name;
                } else {
                    $each[] = '';
                }

                if (isset($orderDetail->seller)) {
                    $each[] = $orderDetail->seller->name;
                } else {
                    $each[] = '';
                }

                $each[] = $orderDispute->dispute_opened_reason;
                $each[] = $orderDispute->supplier_names;
                $each[] = $orderDispute->dispute_tracking_status;
                $each[] = $orderDispute->dispute_type;
                $each[] = $orderDispute->dispute_status;
                $each[] = isset($orderDispute->dispute_due_date) ? Carbon::parse($orderDispute->dispute_due_date)->format('Y-m-d') : '';
                $each[] = $orderDispute->dispute_solution;
                $submitCasesDispute = $orderDispute->submit_cases;
                foreach (range(0, 2) as $submiteCaseStep) {
                    if (isset($submitCasesDispute[$submiteCaseStep])) {
                        $each[] = isset($submitCasesDispute[$submiteCaseStep]->is_submitted) && $submitCasesDispute[$submiteCaseStep]->is_submitted ? 'yes' : 'no';
                        $each[] = isset($submitCasesDispute[$submiteCaseStep]->datestamps) ?  Carbon::parse($submitCasesDispute[$submiteCaseStep]->datestamps)->format('Y-m-d') : '';
                    } else {
                        $each[] = '';
                        $each[] = '';
                    }
                }
                $reachoutDispute = $orderDispute->reachouts;
                foreach (range(0, 2) as $reachoutStep) {
                    if (isset($reachoutDispute[$reachoutStep])) {
                        $each[] = $reachoutDispute[$reachoutStep]->email ? 'yes' : 'no';
                        $each[] = $reachoutDispute[$reachoutStep]->sms ? 'yes' : 'no';
                        // $each [] = $reachoutDispute[$reachoutStep]->datestamps ?? '';
                        $each[] = isset($reachoutDispute[$reachoutStep]->datestamps) ?  Carbon::parse($reachoutDispute[$reachoutStep]->datestamps)->format('Y-m-d') : '';
                    } else {
                        $each[] = '';
                        $each[] = '';
                        $each[] = '';
                    }
                }

                $arr[] = $each;
            } catch (\Exception $e) {
                continue;
            }
        }
        return $arr;
    }

    public function title(): string
    {
        return 'export dispute';
    }

    public function headings(): array
    {

        $arr = [
            [
                'Flagged',
                'Payment Account',
                'Date',
                'Dispute ID',
                'Klarna',
                'Order ID',
                'Order date',
                'Value Order',
                'Person in charge',
                'Seller account',
                'Dispute opened reason',
                'Suppliers',
                'Tracking status',
                'Type',
                'Status dispute case',
                'Due date',
                'Solutions',
                'Submit case 1','',
                'Submit case 2','',
                'Submit case 3','',
                'Reach out 1', '', '',
                'Reach out 2 after 2 biz days', '', '',
                'Reach out 3 after 3 biz days', '', '',

            ],
            [
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                'Check',
                'Date',
                'Check',
                'Date',
                'Check',
                'Date',
                'Email',
                'SMS',
                'Date',
                'Email',
                'SMS',
                'Date',
                'Email',
                'SMS',
                'Date',
            ]
        ];
        return $arr;
    }


    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('R1:S1');
        $sheet->mergeCells('T1:U1');
        $sheet->mergeCells('V1:W1');

        $sheet->mergeCells('X1:Z1');
        $sheet->mergeCells('AA1:AC1');
        $sheet->mergeCells('AD1:AF1');

        $sheet->mergeCells('A1:A2');
        $sheet->mergeCells('B1:B2');
        $sheet->mergeCells('C1:C2');
        $sheet->mergeCells('D1:D2');
        $sheet->mergeCells('E1:E2');
        $sheet->mergeCells('F1:F2');
        $sheet->mergeCells('G1:G2');
        $sheet->mergeCells('H1:H2');
        $sheet->mergeCells('I1:I2');
        $sheet->mergeCells('J1:J2');
        $sheet->mergeCells('K1:K2');
        $sheet->mergeCells('L1:L2');
        $sheet->mergeCells('M1:M2');
        $sheet->mergeCells('N1:N2');
        $sheet->mergeCells('O1:O2');
        $sheet->mergeCells('P1:P2');
        $sheet->mergeCells('Q1:Q2');
        $sheet->getColumnDimension('A')->setAutoSize(false);
        $sheet->getStyle('A1:AF2')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        return [
            'A1:AF1' => ['font' => ['bold' => true]],
            'A2:AF2' => ['font' => ['bold' => true]],
            'A' => ['alignment' => ['wrapText' => true]],
            'F' => [
                'numberFormat' => [
                    'formatCode' => NumberFormat::FORMAT_TEXT
                ]
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function (AfterSheet $event) {
                $event->sheet->getDelegate()->getColumnDimension('A')->setWidth(30);
            },

        ];
    }
}
