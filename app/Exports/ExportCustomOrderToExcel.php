<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportCustomOrderToExcel implements FromCollection, WithHeadings
{
    protected $orderProducts;
    protected $headings;

    public function __construct($orderProducts, $headings)
    {
        $this->orderProducts = $orderProducts;
        $this->headings = $headings;
    }

    public function collection(): Collection
    {
        return new Collection($this->orderProducts);
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
