<?php

namespace App\Exports\Fulfill\Sheets;

use App\Enums\ProductOptionEnum;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class ProductVariantSheet implements FromArray, WithTitle, WithHeadings
{
    private string $name;
    private array $data;

    public function __construct(string $name, array $data)
    {
        $this->name = $name;
        $this->data = $data;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function title(): string
    {
        return $this->name;
    }

    public function headings(): array
    {
        $arr = [
            'Product SKU',
            'SKU',
            'Base Cost',
            'Out Of Stock',
            'Weight',
        ];

        foreach (ProductOptionEnum::getValues() as $option) {
            $arr[] = 'Options:' . ucwords($option);
        }

        return $arr;
    }
}
