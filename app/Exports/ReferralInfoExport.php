<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ReferralInfoExport implements FromArray, WithHeadings
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $arr = [];
        foreach ($this->data as $each) {
            $arr[] = [
                $each->name,
                $each->email,
                $each->sold_items ?? 0,
                $each->fulfill_items ?? 0,
                $each->commission ?? 0,
                $each->created_at->format('Y-m-d'),
            ];
        }

        return $arr;
    }

    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Sold Items',
            'Fulfill Items',
            'Commission',
            'Created At',
        ];
    }
}
