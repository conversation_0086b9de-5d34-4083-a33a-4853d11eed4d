<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ExportProcessingPayout implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected array $payouts;
    protected array $headings = [
        'payee id',
        'amount',
        'currency',
        'internal payment id',
        'description',
        'payment date',
        'group id',
    ];

    public function __construct($payouts)
    {
        $this->payouts = $payouts;
    }

    public function collection(): Collection
    {
        $dataExport = [];
        foreach ($this->payouts as $payout) {
            $dataExport[] = [
                'email_po' => $payout['email_po'],
                'amount' => abs($payout['amount']),
                'currency' => 'USD',
                'payment_id' => $payout['payment_id'],
                'description' => 'Senprints Payout ' . $payout['payment_id'] . ': ' . $payout['description'],
                'payment_date' => Carbon::parse($payout['created_at'])->format('m/d/Y'),
                'group_id' => 'Senprints Payout'
            ];
        }

        return new Collection($dataExport);
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
