<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportFulfillOrderSummaryToExcel implements FromCollection, WithHeadings
{
    protected array $headings;
    protected $orders;

    public function __construct($orders)
    {
        $this->orders = $orders;
        $this->headings = [
            'Sen Order Number',
            'Order Number',
            'Customer Name',
            'Email',
            'Phone',
            'Address',
            'Address 2',
            'City',
            'State',
            'Zipcode',
            'Country',
            'Total items',
            'Shipping method',
            'Total products ($)',
            'Total shipping ($)',
            'Processing fee ($)',
            'Total amount ($)',
            'Created',
        ];
    }

    public function collection(): Collection
    {
        return new Collection($this->orders);
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
