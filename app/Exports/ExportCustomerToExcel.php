<?php

namespace App\Exports;

use App\Enums\SellerCustomerStatus;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportCustomerToExcel implements FromCollection, WithHeadings
{
    protected $customers;

    public function __construct($customers)
    {
        $this->customers = $customers;
    }

    public function collection(): Collection
    {
        return $this->customers->map(function ($customer) {
            $customer->store_id = $customer->recent_store ? $customer->recent_store->name : '';
            $customer->status = currentUser()->isSeller() ? SellerCustomerStatus::getLabel($customer->status) : $customer->status;
            return $customer;
        });
    }

    public function headings(): array
    {
        return [
            'CUSTOMER ID',
            'NAME',
            'EMAIL',
            'PHONE',
            'COUNTRY',
            'CITY',
            'STATE',
            'RECENT STORE',
            'TOTAL ORDERS',
            'TOTAL PURCHASES',
            'CREATED AT',
            'STATUS',
        ];
    }
}
