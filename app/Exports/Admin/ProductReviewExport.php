<?php
namespace App\Exports\Admin;

use App\Models\ProductReview;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ProductReviewExport implements FromCollection, WithHeadings, WithMapping
{
    protected Collection $reviews;

    public function __construct(Collection $reviews)
    {
        $this->reviews = $reviews;
    }

    public function collection(): Collection
    {
        return $this->reviews;
    }

    public function map($row): array
    {
        /** @var ProductReview $row */
        return [
            $row->order->order_number,
            $row->order->paid_at->format('M d, Y H:i'),
            $row->order->customer_email,
            $row->order->customer_name,
            $row->order->country,
            optional($row->seller)->email ?? 'Unknown',
            optional($row->seller)->name ?? 'Unknown',
            $row->comment,
            $row->average_rating,
            $row->created_at->setTimezone('Asia/Ho_Chi_Minh')->format('M d, Y H:i'),
            optional($row->staff)->name ?? 'Unassigned',
            $row->finished_at?->setTimezone('Asia/Ho_Chi_Minh')->format('M d, Y H:i') ?? '',
            optional($row->staffSupport)->name ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'Order ID',
            'Order at',
            'Customer Email',
            'Customer Name',
            'Country',
            'Email Seller',
            'Seller Name',
            'Review',
            'Rating',
            'Review at',
            'Assigned to',
            'Updated at',
            'Support',
        ];
    }
}
