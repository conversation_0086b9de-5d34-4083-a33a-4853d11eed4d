<?php

namespace App\Exports\Admin\Orders;

use App\Enums\OrderSupportStatusEnum;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderResumed implements FromCollection, WithMapping, WithHeadings
{
    /** @var Collection<int, Order> */
    protected Collection $orders;

    /**
     * OrdersExport constructor.
     * @param Collection<int, Order> $orders
     */
    public function __construct(Collection $orders)
    {
        $this->orders = $orders;
    }

    /**
     * @return Collection<int, Order>
     */
    public function collection(): Collection
    {
        return $this->orders;
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->order_number,
            $row->customer['email'] ?? null,
            $row->customer['name'] ?? null,
            $row->address_verified ? 'Yes' : 'Invalid Address',
            $row->seller->email,
            $row->seller->name,
            $row->total_amount,
            $row->total_quantity,
            $row->type,
            $row->status,
            $row->fulfill_status,
            $row->order_note,
            $row->admin_note,
            $row->staff->name ?? null,
            OrderSupportStatusEnum::getDescription($row->support_status),
            $row->resumed_by,
            Carbon::parse($row->resumed_at)->timezone('Asia/Ho_Chi_Minh')->format('d/m/y H:i'),
            Carbon::parse($row->updated_at)->timezone('Asia/Ho_Chi_Minh')->format('d/m/y H:i')
        ];
    }

    public function headings(): array
    {
        // Define the headings for your CSV file.
        return [
            'ID',
            'Order Number',
            'Customer Email',
            'Customer Name',
            'Address Verified',
            'Seller Email',
            'Seller Name',
            'Total Amount',
            'Total Quantity',
            'Type',
            'Status',
            'Fulfill Status',
            'Order Note',
            'Admin Note',
            'Staff Name',
            'Support Status',
            'Resumed By',
            'Resumed At',
            'Updated At'
        ];
    }
}
