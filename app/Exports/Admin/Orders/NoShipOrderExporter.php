<?php

namespace App\Exports\Admin\Orders;

use App\Models\Order;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Throwable;

class NoShipOrderExporter implements FromCollection, WithHeadings
{
    /**
     * OrdersExport constructor.
     * @param Collection<int, Order> $orders
     */
    public function __construct(private readonly Collection $orders) {}

    /**
     * @return Collection<int, Order>
     * @throws Throwable
     */
    public function collection(): Collection
    {
        return $this->orders->reduce(function($result, $order) {
            $order->order_products->each(function($op) use($order, &$result) {
                $result->push([
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'seller_email' => $order->seller->email,
                    'seller_name' => $order->seller->name,
                    'address' => $order->address,
                    'address2' => $order->address2,
                    'city' => $order->city,
                    'state' => $order->state,
                    'post_code' => $order->postcode,
                    'country_code' => $order->country,
                    'country_name' => $order->location?->name,
                    'created_at' => $order->created_at,
                    'paid_at' => $order->paid_at,
                    'order_status' => $order->status,
                    'template_id' => $op->template_id,
                    'fulfill_product_id' => $op->fulfill_product_id,
                    'product_name' => $op->product_name,
                    'options' => opts_to_variants($op->options ?? ''),
                    'quantity' => $op->quantity,
                    'fulfill_status' => $op->fulfill_status,
                    'supplier' => $op->supplier_name ?? null,
                ]);
            });

            return $result;
        }, new Collection());
    }

    public function headings(): array
    {
        return [
            'ID',
            'Order Number',
            'Seller Email',
            'Seller Name',
            'Address',
            'Address2',
            'City',
            'State',
            'Post Code',
            'Country Code',
            'Country Name',
            'Created At',
            'Paid At',
            'Order Status',
            'Template Id',
            'Fulfill Product Id',
            'Product Name',
            'Options',
            'Quantity',
            'Fulfill Status',
            'Supplier'
        ];
    }
}
