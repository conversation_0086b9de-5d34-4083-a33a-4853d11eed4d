<?php

namespace App\Exports\Admin\Analytics;

use App\Http\Controllers\Analytic3\SellerController;
use App\Models\Store;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class DashboardSheet implements FromArray, WithHeadings, WithTitle
{
    protected SellerController $controller;
    protected Store $store;
    private array $data;

    public function __construct($controller, $store)
    {
        $this->controller = $controller;
        $this->store = $store;
    }

    /**
     * @throws \Throwable
     */
    public function headings(): array
    {
        $this->controller->arrFilter['store_id'] = $this->store->id;

        $this->data = $this->controller->getOverview()->toArray();

        return array_keys($this->data);
    }

    public function array(): array
    {
        return [$this->data];
    }

    public function title(): string
    {
        return $this->store->name;
    }
}
