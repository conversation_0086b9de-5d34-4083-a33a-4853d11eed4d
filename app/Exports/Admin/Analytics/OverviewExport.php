<?php

namespace App\Exports\Admin\Analytics;

use App\Http\Controllers\Analytic3\SellerController;
use App\Models\Store;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\Exportable;

class OverviewExport implements WithMultipleSheets
{
    use Exportable;

    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function sheets(): array
    {
        $controller = new SellerController();
        $controller->setCommonFilter($this->request);

        $user = currentUser();
        $sellerId = $user->getUserId();
        $authId = $user->getAuthorizedAccountId();
        $store_ids = get_team_seller_stores($sellerId, $authId);
        $stores = Store::query()
            ->select([
                'id',
                'name',
            ])
            ->where('seller_id', $sellerId)
            ->when($store_ids, function ($query, $store_ids) {
                return $query->whereIn('id', $store_ids);
            })
            ->get();

        $sheets = [];
        foreach ($stores as $store) {
            $sheets[] = new DashboardSheet($controller, $store);
        }

        return $sheets;
    }
}
