<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TrackingExcelExport implements FromCollection, WithHeadings
{
    protected $orders;

    public function __construct($orders)
    {
        $this->orders = $orders;
    }

    public function collection(): Collection
    {
        return $this->orders;
    }

    public function headings(): array
    {
        return [
            'SITE_URL',
            'ORDER_NUMBER',
            'ORDER_ID',
            'ORDER_ITEM_SKU',
            'ORDER_ITEM_ID',
            'TRACKING_NUMBER',
            'TRACKING_CARRIER',
            'TRACKING_URL'
        ];
    }
}
