<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SellerPayoutsExport implements FromArray, WithHeadings
{
    protected array $payouts;
    protected array $headings;

    public function __construct($payouts, $headings)
    {
        $this->payouts = $payouts;
        $this->headings = $headings;
    }

    public function array(): array
    {
        return $this->payouts;
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
