<?php

namespace App\Events;

use App\Models\Order;
use Modules\OrderService\Models\RegionOrders;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderPaymentCompleted
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public Order|RegionOrders $order;

    /**
     * Create a new event instance.
     *
     * @param $order
     */
    public function __construct($order)
    {
        $this->order = $order;
    }
}
