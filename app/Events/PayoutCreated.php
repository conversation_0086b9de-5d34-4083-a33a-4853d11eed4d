<?php

namespace App\Events;

use App\Models\SellerBilling;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PayoutCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public SellerBilling $payout;
    public User $user;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $user, SellerBilling $payout)
    {
        $this->user = $user;
        $this->payout = $payout;
    }
}
