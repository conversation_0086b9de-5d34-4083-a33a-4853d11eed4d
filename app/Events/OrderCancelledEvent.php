<?php
namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderCancelledEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var Order
     */
    public Order $order;

    /**
     *OrderCancelledEvent  constructor.
     * @param $orderId
     */
    public function __construct($orderId)
    {
        $this->order = Order::query()->whereKey($orderId)->first();
    }
}
