<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderProductFulfilled
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Order $order;
    public array $tracking;

    public function __construct(string $orderId, array $tracking)
    {
        $order = Order::firstWhere('id', $orderId);

        if ($order) {
            $this->order = $order;
            $this->tracking = $tracking;
        }
    }
}
