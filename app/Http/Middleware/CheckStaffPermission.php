<?php

namespace App\Http\Middleware;

use App\Enums\SystemRole;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CheckStaffPermission
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure $next
     * @param string|array $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        $auth = currentUser();

        abort_if(!$auth, 403);

        // skip if is admin
        if ($auth->hasRole(SystemRole::ADMIN)) {
            return $next($request);
        }

        try {
            $role = $auth->getInfo()->roles->first();

            if ( ! $role || $role->guard_name !== 'admin') {
                abort(403);
            }

            if (strpos($permission, '|') !== false) {
                $permissions = explode('|', $permission);
            } else {
                $permissions = Arr::wrap($permission);
            }

            $hasPermission = $auth->getInfo()->hasAnyPermission($permissions)
                            || $role->hasAnyPermission($permissions);

            abort_unless($hasPermission, 403);
        } catch (\Exception $e) {
            abort(403);
        }

        return $next($request);
    }
}
