<?php

namespace App\Http\Middleware;

use App\Enums\UserStatusEnum;
use App\Services\SenPrintsAuth;
use App\Services\SessionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;

class CheckAuthAccess
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $token = JWTAuth::parseToken();
            $payload = $token->getPayload();
            if (!isset($payload['service_name'])) {
                return \response()->json([
                    'success' => false
                ], 200);
            }

            if (!check_access_app_service($payload['service_name'])) {
                return \response()->json([
                    'success' => false
                ], 200);
            }

            if (!check_access_auth_guard($payload['guard_name'])) {
                return \response()->json([
                    'success' => false
                ], 200);
            }

            SenPrintsAuth::setRequestData($payload, $request);

            Auth::shouldUse($payload['guard_name']);
            if (!is_null($request->access_account_id) && $payload['service_name'] === 'admin') {
                Auth::shouldUse('admin');
            }

            SessionService::log(json_encode($request->all()));
            if (!$request->session_token ||
                (Auth::check() && !app(SessionService::class)->checkSession(Auth::user(), $request->session_token))) {
                return \response()->json([
                    'success' => false,
                    'message' => 'Session expired.'
                ], 200);
            }

            // check current status
            if (Auth::check() && Auth::user()->status === UserStatusEnum::HARD_BLOCKED) {
                return \response()->json([
                    'success' => false,
                    'message' => 'Access denied.'
                ], 200);
            }

            if (Auth::check() && Auth::user()->tfa_enable && !$request->tfa_confirm && !in_array($request->path(), ["me", "system-configs"])) {
                return \response()->json([
                    'success' => false,
                    'message' => 'Two factor authentication code required.'
                ], 200);
            }

            return $next($request);
        } catch (TokenExpiredException $e) {
            return \response()->json([
                'success' => false,
                'error' => 'token_expired'
            ], 401);
        } catch (TokenInvalidException $e) {
            return \response()->json([
                'success' => false,
                'error' => 'token_invalid'
            ], 401);
        } catch (JWTException $e) {
            return \response()->json([
                'success' => false,
                'error' => 'token_absent'
            ], 406);
        }
    }
}
