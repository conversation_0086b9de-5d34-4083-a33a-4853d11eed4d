<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class DiscordBotAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->header('x-api-key') === config('discord.bot_api_key')) {
            return $next($request);
        }

        abort(403, 'Unauthorized');
    }
}
