<?php

namespace App\Http\Middleware;

use App\Models\ApiKey;
use App\Services\SenPrintsApi;
use App\Traits\ParseRequest;
use Closure;
use Illuminate\Http\Request;

class AccessExternalApi
{
    use ParseRequest;

    public function handle(Request $request, Closure $next)
    {
        $token = $this->getAuthorizationToken() ?? $request->route('token');

        if (is_null($token)) {
            return response()->json([
                'success' => false,
                'error'   => 'api_key_absent'
            ], 406);
        }
        // Decrypt api key & get info
        $key = SenPrintsApi::parseKey($token);
        if (is_null($key)) {
            return response()->json([
                'success' => false,
                'error'   => 'api_key_invalid'
            ], 401);
        }

        $apiKey = ApiKey::query()
            ->where('access_token', $token)
            ->where('status', 1)
            ->where(function ($q) {
                $q
                    ->orWhere('expired', '>', now())
                    ->orWhereNull('expired');
            })
            ->first();
        if (is_null($apiKey)) {
            return response()->json([
                'success' => false,
                'error'   => 'api_key_expired'
            ], 401);
        }

        // Extend api key info for request
        $request->merge(['api_key' => $apiKey]);
        // set auth current user
        currentUser($apiKey->reference_id, [
            'service_name' => $apiKey->type,
            // 'guard_name'   => 'user', // dont need this
        ]);

        $response = $next($request);

        logHttp(
            [
                'from'          => $token,
                'request_body'  => $request->getContent(),
                'response_body' => $response->content(),
            ]
        );

        return $response;
    }
}
