<?php

namespace App\Http\Middleware;

use App\Services\UserLog;
use Closure;
use Illuminate\Http\Request;

class SellerLog
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // only log for seller
        if (currentUser()->isSeller()) {
            try {
                UserLog::logSellerActivities();
            } catch (\Throwable $e) {
                // ignore
            }
        }

        return $next($request);
    }
}
