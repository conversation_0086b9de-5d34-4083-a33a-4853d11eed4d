<?php

namespace App\Http\Middleware;

use App\Enums\SystemRole;
use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;

class EnsureUserIsAdmin
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (currentUser()->hasRole(SystemRole::ADMIN)) {
            return $next($request);
        }

        return $this->errorResponse('Access denied.', 403);
    }
}
