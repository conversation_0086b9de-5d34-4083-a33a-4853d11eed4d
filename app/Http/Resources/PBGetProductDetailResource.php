<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PBGetProductDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $variants = $this->variants->map(function ($item, $key) {
            $variantInfo = explode('-', $item->variant_key);
            if (count($variantInfo) === 2) {
                $color = ucfirst($variantInfo[0]);
                $size = strtoupper($variantInfo[1]);
            } else {
                $color = '';
                $size = '';
            }
            return [
                'sku' => $item->sku,
                'color' => $color,
                'size' => $size,
                'key' => $item->variant_key,
                'suggest_price' => $item->price,
                'base_cost' => $item->base_cost,
                'location_code' => $item->location_code,
                'out_of_stock' => $item->out_of_stock,
            ];
        });
        $this->mockups->map(function ($item) {
            return s3Url($item->file_url);
        });
        $category = $this->category ? [
                'id' => $this->category->category_id,
                'name' => $this->category->name,
                'full_name' => $this->category->full_name,
            ] : null;
        return [
            'id' => $this->id,
            'name' => $this->name,
            'thumb_url' => $this->thumbnail_img ? imgUrl($this->thumbnail_img->file_url, 'original') : '',
            'category' => $category,
            'sku' => $this->sku,
            'price' => $this->price,
            'options' => json_decode($this->options, true),
            'variants' => $variants,
            'print_spaces' => json_decode($this->print_spaces, true),
        ];
    }
}
