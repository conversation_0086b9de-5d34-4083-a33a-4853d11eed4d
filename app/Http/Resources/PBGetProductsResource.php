<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PBGetProductsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'sku' => $this->sku,
            'thumb_url' => s3Url($this->thumb_url)
        ];
    }
}
