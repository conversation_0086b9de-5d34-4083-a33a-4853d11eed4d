<?php

namespace App\Http\Requests\Auth;

use App\Rules\Auth\ValidTokenResetPasswordRule;
use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'token' => [
                'bail',
                'required',
                'string',
                new ValidTokenResetPasswordRule
            ],
            'password' => 'bail | required | string | min:8',
            're_password' => 'bail | required | string | min:8 | same:password',
        ];
    }

    public function messages()
    {
        return [
            'min' => 'The :attribute must be at least :min characters.',
            'same' => 'The confirm password is incorrect.',
        ];
    }
}
