<?php

namespace App\Http\Requests;

use App\Enums\SMSLogStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SMSAdminGetRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'start_date' => ['nullable', 'integer'],
            'end_date' => ['nullable', 'integer'],
            'status' => ['nullable', Rule::in(SMSLogStatus::getValues())],
            'order_id' => ['nullable', 'string'],
            'send_by' => ['nullable', Rule::in(['whatsapp', 'sms'])],
            'per_page' => ['nullable', 'integer', 'max:500']
        ];
    }
}
