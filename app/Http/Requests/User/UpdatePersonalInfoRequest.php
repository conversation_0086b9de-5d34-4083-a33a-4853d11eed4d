<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdatePersonalInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'facebook' => 'nullable|url',
            'phone' => 'nullable|phone:AUTO,US,VN',
            'address' => 'nullable|string',
            'sale_volume' => 'nullable|string',
            'birthday' => 'nullable|date',
            'city' => 'nullable|string',
            'country' => 'nullable|string',
            'postcode' => 'nullable|string',
            'state' => 'nullable|string',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        // https://github.com/Propaganistas/Laravel-Phone
        return [
            'phone.phone' => 'The :attribute field contains an invalid number.',
            'facebook.url' => 'Please enter a valid Facebook URL.'
        ];
    }
}
