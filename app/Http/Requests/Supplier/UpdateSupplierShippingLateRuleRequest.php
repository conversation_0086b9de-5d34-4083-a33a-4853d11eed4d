<?php

namespace App\Http\Requests\Supplier;

use App\Rules\Suppliers\ShippingLateRegionLocationRule;
use App\Rules\Suppliers\ShippingLateRule;
use App\Rules\Suppliers\ShippingLateSupplierRule;
use App\Services\SupplierService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class UpdateSupplierShippingLateRuleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    public function validationData()
    {
        return array_merge($this->all(), [
            'supplierId' => $this->route('supplierId'),
            'shippingLateRuleId' => $this->route('shippingLateRuleId'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'supplierId' => [
                'required',
                'integer',
                new ShippingLateSupplierRule(),
            ],
            'shippingLateRuleId' => [
                'required',
                'integer',
                new ShippingLateRule(),
            ],
            'shipping_method' => 'required|string|max:255',
            'location' => [
                'required',
                'array',
                new ShippingLateRegionLocationRule(),
            ],
            'no_location' => 'array',
            'date_late' => 'required|integer',
        ];
    }


    public function withValidator ($validator) {
        $validator->after(function ($validator) {
            $noLocation = $this->no_location;
            $locations = $this->location;
            $response = SupplierService::validateLocation($locations, $noLocation);
            if (!$response['accept']) {
                $validator->errors()->add('shipping_late_location_err', $response['message']);
            }
        });
    }

    public function failedValidation($validator)
    {
        $response = response()->json([
            'message' => $validator->errors()->toArray()['shipping_late_location_err'][0],
            'success' => false,
        ], 200);
        throw new HttpResponseException($response);
    }
}
