<?php

namespace App\Http\Requests\Supplier;

use App\Rules\LocationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateSupplierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'integer',
                'unique:supplier,id',
            ],
            'name' => 'required|string|max:255',
            'email' => 'required|max:255|unique:supplier',
            'location' => ['nullable','string','max:255',new LocationRule()],
            'tm_support' => 'nullable|integer|in:0,1',
            'max_items' => [
                'required',
                'integer',
                'min:0',
                'max:' . 1_000_000,
            ],
        ];
    }
}
