<?php

namespace App\Http\Requests;

use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddProductFulfillOrderRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'order_id' => ['required', 'integer', Rule::exists('order', 'id')],
            'products' => ['required', 'array'],
            'products.*.options' => ['required', 'json'],
            'products.*.template_id' => ['required', 'integer', new CheckExistsIdRule('product')],
            'products.*.mockups' => ['required', 'array'],
            'products.*.mockups.*.print_space' => ['required', 'string'],
            'products.*.mockups.*.file_url' => ['required', 'string'],
            'products.*.designs' => ['required', 'array'],
            'products.*.designs.*.file_url' => ['required', 'string'],
            'products.*.designs.*.print_space' => ['required', 'string'],
        ];
    }
}
