<?php

namespace App\Http\Requests\Campaign;

use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SaveCampaignDesign extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $seller = currentUser()->getInfoAccess();
        $connection = $seller->getPrivateConnection();

        return [
            'data' => ['required', 'array'],
            'data.*.file_url' => ['nullable', 'string'],
            'data.*.mockup_id' => ['required', 'exists:file,id'],
            'data.*.product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product', connection: $connection),
            ],
            'data.*.campaign_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product', connection: $connection),
            ],
            'data.*.is_default_mockup' => ['nullable', 'string']
        ];
    }
}
