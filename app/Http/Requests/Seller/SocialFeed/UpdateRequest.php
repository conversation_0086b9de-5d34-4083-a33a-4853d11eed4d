<?php

namespace App\Http\Requests\Seller\SocialFeed;

use App\Enums\FeedPlatformEnum;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'id' => [
                'nullable',
            ],
            'name' => [
                'required',
                'string',
            ],
            'store_id' => [
                'required',
                'numeric',
            ],
            'type' => [
                'nullable',
                'boolean',
            ],
            'collection_ids' => [
                'nullable',
                'array',
            ],
            'product_ids' => [
                'nullable',
                'array',
            ],
            'default_product_only' => [
                'nullable',
                'boolean',
            ],
            'colors' => [
                'nullable',
                'array',
            ],
            'default_color_only' => [
                'nullable',
                'boolean',
            ],
            'other_colors_only' => [
                'nullable',
                'boolean',
            ],
            'sizes' => [
                'nullable',
                'array',
            ],
            'include_keywords' => [
                'nullable',
                'array',
            ],
            'include_price_currency' => [
                'nullable',
                'boolean',
            ],
            'platform' => [
                'nullable',
                'string',
                'in:' . implode(',', FeedPlatformEnum::getValues()),
            ],
            'start_date' => [
                'nullable',
                'date',
            ],
            'end_date' => [
                'nullable',
                'date',
            ],
            'keywords' => [
                'nullable',
                'array',
            ],
            'exclude_social_feed_ids' => [
                'nullable',
                'array',
            ],
            'custom_label_with_collection' => [
                'nullable',
                'boolean',
            ],
            'custom_label_0' => [
                'nullable',
                'string',
            ],
            'custom_label_1' => [
                'nullable',
                'string',
            ],
            'custom_label_2' => [
                'nullable',
                'string',
            ],
            'custom_label_3' => [
                'nullable',
                'string',
            ],
            'custom_label_4' => [
                'nullable',
                'string',
            ],
            'shipping_label' => [
                'nullable',
                'string',
            ],
            'default_gender' => [
                'nullable',
                'numeric',
            ],
        ];
    }
}
