<?php

namespace App\Http\Requests\Seller\Team;

use App\Enums\SellerTeamRoleEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateRoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'role' => [
                'required',
                'string',
                Rule::in(SellerTeamRoleEnum::getValues())
            ],
            'storefronts' => [
                'nullable',
                'array'
            ]
        ];
    }
}
