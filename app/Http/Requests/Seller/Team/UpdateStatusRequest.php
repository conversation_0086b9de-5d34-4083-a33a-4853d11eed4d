<?php

namespace App\Http\Requests\Seller\Team;

use App\Enums\SellerTeamStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => [
                'bail',
                'required',
                'string',
                Rule::in([
                    SellerTeamStatusEnum::ACCEPTED,
                    SellerTeamStatusEnum::DISABLED
                ])
            ]
        ];
    }
}
