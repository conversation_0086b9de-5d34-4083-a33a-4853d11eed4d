<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CrawlVariantStockRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'variant_sku' => ['required'],
            'product_id' => ['required'],
            'supplier_id' => ['required', function($attribute, $value, $fail) {
                if (! $cfg = suppliers()->firstWhere('supplier_id', $value)) {
                    $fail('Supplier Not Found !');
                }

                if (empty($cfg['sync_oss_variant'])) {
                    $fail('Supplier Not Support Sync Variant OOS !');
                }
            }],
        ];
    }
}
