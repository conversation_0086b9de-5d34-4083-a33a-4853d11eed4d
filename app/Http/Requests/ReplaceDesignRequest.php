<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReplaceDesignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'file' => ['nullable', 'required_without:file_base64', 'file', 'image'],
            'file_base64' => ['nullable', 'string'],
            'old_file_id' => ['required'],
            'old_table' => ['required', 'string', Rule::in(['file', 'design'])],
        ];
    }
}
