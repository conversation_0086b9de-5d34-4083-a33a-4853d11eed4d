<?php

namespace App\Http\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;

class ExportCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize():bool
    {
        // team member can't export customer
        return (currentUser()->isSeller() && !currentUser()->isAuthorizedAccount()) || currentUser()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'q' => [
                'string',
            ],
            'country' => [
                'string'
            ],
            'store_id' => [
                'integer'
            ],
            'min_order' => [
                'integer',
            ]
        ];
    }
}
