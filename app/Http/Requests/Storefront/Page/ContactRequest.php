<?php

namespace App\Http\Requests\Storefront\Page;

use App\Rules\RecaptchaV2;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class ContactRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => [
                'bail',
                'required',
                'email'
            ],
            'name' => [
                'nullable',
                'string',
                'min:1',
            ],
            'message' => [
                'required',
                'string',
                'min:5',
            ],
            'subject' => [
                'nullable',
                'string',
            ],
            'order_number' => [
                'nullable',
                'string',
            ],
            'attached_files' => [
                'nullable',
                'array',
            ],
            'token' => [
                'required',
                'string',
                new RecaptchaV2('storefront')
            ],
        ];
    }
}
