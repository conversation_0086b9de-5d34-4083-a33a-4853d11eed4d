<?php

namespace App\Http\Requests\Storefront\Promotion;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class IndexBundleDiscountRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'campaign_ids'   => [
                'required',
                'array',
                'min:1',
            ],
            'campaign_ids.*' => [
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        // ['123,456'] => [123, 456]
        if (
            $this->has('campaign_ids')
            && is_array($this->input('campaign_ids'))
            && count($this->input('campaign_ids')) === 1
        ) {
            $campaignIds = $this->input('campaign_ids')[0];
            if (!is_array($campaignIds)) {
                $arr  = explode(',', $campaignIds);
                $data = [];
                foreach ($arr as $value) {
                    if (!in_array($value, $data)) {
                        $data[] = (int)$value;
                    }
                }
            } else {
                $data = $campaignIds;
            }
            $this->merge(
                [
                    'campaign_ids' => $data,
                ]
            );
        }
    }
}
