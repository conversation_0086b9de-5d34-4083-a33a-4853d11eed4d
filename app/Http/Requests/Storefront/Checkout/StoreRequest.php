<?php

namespace App\Http\Requests\Storefront\Checkout;

use App\Rules\IsValidCountryRule;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;
    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'products' => [
                'bail',
                'required',
                'array',
                'filled',
            ],
            'products.*.product_id' => [
                'bail',
                'required',
                'integer',
            ],
            'products.*.campaign_id' => [
                'bail',
                'required',
                'integer',
            ],
            'products.*.full_printed' => [
                'nullable',
                'integer',
            ],
            'products.*.quantity' => [
                'bail',
                'required',
                'integer',
            ],
            'products.*.price' => [
                'nullable',
                'numeric',
            ],
            'products.*.seller_id' => [
                'nullable',
                'numeric',
            ],
            'products.*.variantPrice' => [
                'nullable',
                'numeric',
            ],
            'products.*.extra_custom_fee' => [
                'nullable',
                'integer',
            ],
            'products.*.productBundleId' => [
                'nullable',
                'integer',
            ],
            'products.*.options.color' => [
                'nullable',
                'string',
                'regex:/^[^><\\\\\/;?:=\'"]*$/',
            ],
            'products.*.options.size' => [
                'nullable',
                'string',
                'regex:/^[^><\\\\\/;?:=\'"]*$/',
            ],
            'access_token' => [
                'nullable',
                'string',
                'regex:/^[^><\\\\\/;?:=\'"]*$/',
            ],
            'country' => [
                'nullable',
                'string',
                new IsValidCountryRule(),
            ],
            'visit_info' => [
                'nullable',
                'array',
            ],
            'discount_code' => [
                'nullable',
                'string',
            ],
            'currency_code' => [
                'nullable',
                'string',
            ],
            'customer_custom_options' => [
                'nullable',
                'array'
            ]
        ];
    }
}
