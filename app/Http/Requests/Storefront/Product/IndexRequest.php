<?php

namespace App\Http\Requests\Storefront\Product;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class IndexRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;
    protected $stopOnFirstFailure = true;

    protected bool $hideError = true;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            's'               => [
                'bail',
                'string',
                'nullable',
            ],
            'color'           => [
                'bail',
                'string',
                'nullable',
            ],
            'category_slugs'  => [
                'bail',
                'string',
                'nullable',
            ],
            'collection_slug' => [
                'bail',
                'string',
                'nullable',
            ],
            'filter_price'    => [
                'bail',
                'string',
                'nullable',
            ],
            'page'            => 'bail|integer',
            // 'sort' => 'bail|string|in:' . implode(',', SortListingProductEnum::asArray()),
            'limit'           => 'bail|integer|min:1|max:24',
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->has('page')) {
            $page = (int)filter_var($this->input('page'), FILTER_SANITIZE_NUMBER_INT);
            $page = max(1, $page);
            $this->merge(
                [
                    'page' => $page,
                ]
            );
        }
    }
}
