<?php

namespace App\Http\Requests\Storefront\Product;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class SimilarRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;
    protected $stopOnFirstFailure = true;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'filled',
            ]
        ];
    }
}
