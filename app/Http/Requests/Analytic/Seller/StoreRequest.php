<?php

namespace App\Http\Requests\Analytic\Seller;

use App\Rules\Seller\StoreRule;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules['store_id'] = [
            'nullable',
            new StoreRule(),
        ];

        return $rules;
    }
}
