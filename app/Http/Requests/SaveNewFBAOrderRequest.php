<?php

namespace App\Http\Requests;

use App\Rules\ValidCountry;
use App\Traits\AddressValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SaveNewFBAOrderRequest extends FormRequest
{
    use AddressValidation;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'order_number' => ['required', 'string', Rule::unique('order', 'order_number')],
            'store_name' => ['nullable', 'string'],
            'store_domain' => ['nullable', 'string'],
            'customer_name' => ['nullable', 'string'],
            'address' => ['nullable', 'string'],
            'address_2' => ['nullable', 'string'],
            'city' => ['nullable', 'string'],
            'state' => ['nullable', 'string'],
            'postcode' => ['nullable', 'string'],
            'country' => ['nullable', 'string', new ValidCountry($this->input('country'), true)],
            'order_note' => ['nullable', 'string'],
            'shipping_label' => ['required', 'string'],
        ];
    }
}
