<?php

namespace App\Http\Requests;

use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SellerSaveProductVariantsRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $seller = currentUser()->getInfoAccess();
        $connection = $seller->getPrivateConnection();
        return [
            'product_id' => ['required', 'integer', new CheckExistsIdRule('product', connection: $connection)],
            'location_code' => ['required', 'string', Rule::exists('system_location', 'code')],
            'variants' => ['required', 'array'],
            'variants.*.product_id' => ['required', 'integer', new CheckExistsIdRule('product',  connection: $connection)],
            'variants.*.campaign_id' => ['required', 'integer', new CheckExistsIdRule('product',  connection: $connection)],
            'variants.*.variant_key' => ['required', 'string'],
            'variants.*.price' => ['required', 'numeric'],
            'variants.*.old_price' => ['nullable', 'numeric'],
            'variants.*.base_cost' => ['required', 'numeric'],
            'variants.*.out_of_stock' => ['nullable', 'integer'],
            'variants.*.sku' => ['nullable', 'string'],
        ];
    }
}
