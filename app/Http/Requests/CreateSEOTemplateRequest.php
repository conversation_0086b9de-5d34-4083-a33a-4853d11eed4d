<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateSEOTemplateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'string', Rule::unique('seo_content_template', 'type')],
            'title' => ['required', 'string'],
            'description' => ['string', 'nullable'],
            'content' => ['string', 'nullable'],
            'keywords' => ['array', 'nullable'],
            'data_json' => ['json', 'nullable'],
            'link_json' => ['json', 'nullable'],
        ];
    }
}
