<?php

namespace App\Http\Requests\ApiKey;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class GenerateRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'referenceId' => 'integer|nullable'
        ];
    }
}
