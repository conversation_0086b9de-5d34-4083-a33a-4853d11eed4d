<?php

namespace App\Http\Requests;

use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class BulkCreateCampaignV2Request extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $seller = currentUser()->getInfoAccess();
        $connection = $seller->getPrivateConnection() ?? config('database.default');
        return [
            'campaign_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product', connection: $connection),
            ],
            'design_info.name' => 'required|string',
            'design_info.slug' => 'required|string',
            'design_info.file_url' => 'required|string',
            'design_info.collection' => 'nullable|string',
            'uploaded_at' => 'nullable|date',
            'ip_address' => 'nullable|string'
        ];
    }
}
