<?php

namespace App\Http\Requests\Email\Tracking;

use Illuminate\Foundation\Http\FormRequest;

class ClickedRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'redirectUrl' => 'nullable|url',
            'hashId' => 'nullable|uuid'
        ];
    }
}
