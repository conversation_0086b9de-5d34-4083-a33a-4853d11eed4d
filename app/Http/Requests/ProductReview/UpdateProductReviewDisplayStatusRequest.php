<?php

namespace App\Http\Requests\ProductReview;

use App\Enums\ProductReviewDisplayEnum;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProductReviewDisplayStatusRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => [
                'required',
                Rule::in(ProductReviewDisplayEnum::getValues())
            ]
        ];
    }
}
