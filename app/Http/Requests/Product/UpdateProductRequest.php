<?php

namespace App\Http\Requests\Product;

use App\Enums\PricingModeEnum;
use App\Rules\CheckExistsIdRule;
use App\Rules\LocationRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProductRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    public function authorize(): bool
    {
        $user = currentUser();

        return $user->isAdmin() && $user->can('update_product');
    }

    public function rules(): array
    {
        return [
            'product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:32',
            'category_id' => 'nullable|exists:category,id',
            'base_cost' => 'required|numeric',
            'price' => 'required|numeric',
            'shipping_cost' => 'required|numeric',
            'extra_print_cost' => 'nullable|numeric',
            'print_spaces' => 'nullable|json',
            'files_mockup' => 'nullable|array',
            'files_mockup.*' => 'nullable|image|max:20000',
            'thumbnail_img' => 'nullable|image|max:20000',
            'options' => 'nullable|json',
            'attributes' => 'nullable|json',
            'pricing_mode' => ['required', 'string', Rule::in(PricingModeEnum::getValues())],
            'market_location' => [
                'nullable',
                'string',
                new LocationRule()
            ],
            'quantity' => 'nullable|integer|min:1',
        ];
    }

    public function messages()
    {
        return [
            'files_mockup.*.image' => 'File must be image',
            'files_mockup.*.max' => 'File size must be smaller than 20MB',
            'base_cost:regex' => 'Base cost must be number',
            'price:regex' => 'Price must be number',
            'shipping_cost:regex' => 'Shipping cost must be number',
        ];
    }
}
