<?php

namespace App\Http\Requests;

use App\Rules\ValidCountry;
use App\Traits\AddressValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SaveNewFulfillOrderRequest extends FormRequest
{
    use AddressValidation;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'order_number' => ['required', 'string', Rule::unique('order', 'order_number')],
            'store_name' => ['nullable', 'string'],
            'store_domain' => ['nullable', 'string'],
            'customer_name' => ['required', 'string'],
            'address' => ['required', 'string'],
            'address_2' => ['nullable', 'string'],
            'ioss_number' => ['nullable', 'string'],
            'city' => ['required', 'string'],
            'state' => self::getStateRule($this->input('country')),
            'postcode' => ['required', 'string'],
            'country' => ['required', 'string', new ValidCountry($this->input('country'))],
            'order_note' => ['nullable', 'string'],
        ];
    }
}
