<?php

namespace App\Http\Requests;

use App\Enums\TradeMarkStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkUpdateTrademarkStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_ids' => 'required|array',
            'tm_status' => [
                'required',
                Rule::in([TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::VIOLATED, TradeMarkStatusEnum::FLAGGED])
            ]
        ];
    }
}
