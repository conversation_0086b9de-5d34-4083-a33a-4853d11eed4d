<?php

namespace App\Http\Requests;

use App\Models\Campaign;
use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveProductThumbnailRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        if (!Auth::check()) {
            return false;
        }
        $seller = currentUser()->getInfoAccess();
        return Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $this->route('campaign_id'),
                'seller_id' => $seller->id,
            ])
            ->exists();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $seller = currentUser()->getInfoAccess();
        $connection = $seller->getPrivateConnection();

        return [
            'data' => ['required', 'array'],
            'data.*.product_id' => ['required', 'integer', new CheckExistsIdRule('product', connection: $connection)],
            'data.*.campaign_id' => ['required', 'integer', new CheckExistsIdRule('product', connection: $connection)],
            'data.*.thumb_url' => ['nullable', 'string'],
            'data.*.default_option' => ['nullable', 'string', Rule::exists('system_colors', 'name')],
        ];
    }
}
