<?php

namespace App\Http\Requests\Admin\Order;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class UpdateNoteRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'column' => [
                'required',
                'string',
                'in:admin_note,order_note',
            ],
            'note'   => [
                'nullable',
                'string',
            ],
        ];
    }
}
