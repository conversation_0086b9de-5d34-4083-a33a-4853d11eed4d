<?php

namespace App\Http\Requests\Admin\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateOrderProductOptionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'order_id' => ['required', 'integer', Rule::exists('order', 'id')],
            'order_product_id' => ['required', 'integer', Rule::exists('order_product', 'id')],
            'quantity' => ['required', 'integer'],
            'template_id' => ['required', 'integer', Rule::exists('product', 'id')],
            'options' => ['required', 'array'],
            'country' => ['required', 'string'],
        ];
    }
}
