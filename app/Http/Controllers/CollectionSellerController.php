<?php

namespace App\Http\Controllers;

use App\Enums\StorageDisksEnum;
use App\Http\Requests\Seller\Collection\UpdateNameRequest;
use App\Models\Campaign;
use App\Models\Collection;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\SellerCollection;
use App\Models\StoreCollection;
use App\Traits\ApiResponse;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Log;
use Throwable;

class CollectionSellerController extends Controller
{
    use ApiResponse;

    /**
     * @param Request $request
     * @return SellerCollection[]|LengthAwarePaginator|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public function index(Request $request)
    {
        $collections = SellerCollection::query()
            ->select([
                'collection.id',
                'collection.name',
                'collection.slug',
                'seller_collection.created_at'
            ])
            ->leftJoin('collection', 'collection.id', '=', 'seller_collection.collection_id')
            ->where('seller_id', currentUser()->getUserId());

        // if is searching
        $store = $request->get('store');
        $query = $request->get('q');
        $sortBy = $request->get('sortBy', 'created_at');
        $direction = $request->get('direction', 'desc');
        $perPage = $request->get('per_page', 15);
        $sortableFields = ['name', 'created_at'];
        $limit = $request->get('limit');

        if ($query) {
            $collections = $collections->where('collection.name', 'like', '%' . $query . '%');
        }

        if ($store) {
            $collections->addSelect('store_collection.store_id');
            $collections->leftJoin('store_collection', 'store_collection.collection_id', '=', 'collection.id');
            $collections->where('store_collection.store_id', $store);
        }

        if (!$sortBy || !in_array($sortBy, $sortableFields)) {
            $sortBy = 'created_at';

        }
        if (!$direction || !in_array($direction, ['asc', 'desc'])) {
            $direction = 'desc';
        }
        $collections->orderBy($sortBy, $direction);

        if (!empty($limit) && $limit === 'none') {
            $result = $collections->latest()->get();
        } else {
            $result = $collections->latest()->paginate($perPage);
            $result->appends('q', $query);
        }
        return $result;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getCollectionForSelect(Request $request): JsonResponse
    {
        $search = $request->get('q');
        $extra = $request->get('extra', []);
        $query = SellerCollection::query()
            ->with('collection')
            ->where('seller_id', currentUser()->getUserId());
        $cloneQuery = clone $query;

        $collections = $query
            ->when($search, function ($query) use ($search) {
                $query->whereHas('collection', function ($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%');
                });
            })
            ->whereNotIn('collection_id', $extra)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $collectionsExtra = $cloneQuery
            ->whereIn('collection_id', $extra)
            ->orderBy('created_at', 'desc')
            ->get();

        $results = [];

        foreach ($collections as $collection) {
            $results[] = [
                'id' => $collection->collection_id,
                'name' => $collection->collection->name,
            ];
        }

        foreach ($collectionsExtra as $collection) {
            $results[] = [
                'id' => $collection->collection_id,
                'name' => $collection->collection->name,
            ];
        }

        return $this->successResponse($results);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        // Get user
        $userId = currentUser()->getUserId();

        // Validate
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'max:255'],
        ]);

        // If error return error response
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 500);
        }

        // Get post data
        $name = $request->post('name');
        if ($this->isCollectionExists($name)) {
            return $this->errorResponse('Collection name is already exists.');
        }

        $collection = self::createCollection($name, $userId);

        if ($collection) {
            return $this->successResponse([
                'collection_id' => $collection->collection_id,
                'created_at' => $collection->created_at
            ], 'Collection created.'); // Use ApiResponse traits
        }

        return $this->errorResponse('Have an error when create collection.');
    }

    private function isCollectionExists($name)
    {
        $parsedName = preg_replace('/[^a-zA-Zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ0-9 ]/iu', '', $name);
        $parsedName = trim($parsedName);
        $parsedName = ucwords(strtolower($parsedName));

        return SellerCollection::query()
            ->where('seller_id', currentUser()->getUserId())
            ->whereHas('collection', function ($query) use ($parsedName) {
                $query->where('name', $parsedName);
            })
            ->exists();
    }

    /**
     * @param $name
     * @param $userId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|SellerCollection
     */
    public static function createCollection($name, $userId)
    {
        // Create new collection
        $collection = Collection::addCollection($name);

        // if ($collection->parent_id !== $parentId) {
        //     $collection->parent_id = $parentId;
        //     $collection->save();
        // }

        // Add to 'seller_collection' table
        return SellerCollection::query()->firstOrCreate([
            'seller_id' => $userId,
            'collection_id' => $collection->id,
        ]);
    }


    /**
     * @param $collectionId
     * @return JsonResponse
     * @throws Throwable
     */
    public function destroy($collectionId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $products = ProductCollection::query()
            ->select('product_id')
            ->where([
                'seller_id' => $seller->id,
                'collection_id' => $collectionId
            ])
            ->get();

        $deletedProducts = 0;
        $deletedCollections = 0;

        try {
            DB::beginTransaction();

            if ($products->count() > 0) {
                $ids = $products->pluck('product_id')->unique()->toArray();

                // update sync status
                if (count($ids) > 1000) {
                    foreach (array_chunk($ids, 1000) as $chunk_ids) {
                        Product::query()
                            ->onSellerConnection($seller)
                            ->whereIn('id', $chunk_ids)
                            ->orWhereIn('campaign_id', $chunk_ids)
                            ->update(['sync_status' => Product::SYNC_DATA_STATS_ENABLED]);
                    }
                } else {
                    Product::query()
                        ->onSellerConnection($seller)
                        ->whereIn('id', $ids)
                        ->orWhereIn('campaign_id', $ids)
                        ->update(['sync_status' => Product::SYNC_DATA_STATS_ENABLED]);
                }

                // delete products from product_collection table
                $deletedProducts = ProductCollection::query()
                    ->whereIn('product_id', $ids)
                    ->delete();
            }

            $deletedCollections = SellerCollection::query()
                ->where([
                    'seller_id' => $seller->id,
                    'collection_id' => $collectionId
                ])
                ->delete();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
        }

        return $deletedCollections
            ? $this->successResponse([
                'deleted_collections' => $deletedCollections,
                'deleted_products' => $deletedProducts
            ])
            : $this->errorResponse();
    }

    /**
     * @param int $collectionId
     * @return JsonResponse
     */
    public function getInfo(int $collectionId): JsonResponse
    {
        $collection = SellerCollection::query()
            ->select(['collection.id', 'collection.name', 'seller_collection.created_at'])
            ->leftJoin('collection', 'collection.id', '=', 'seller_collection.collection_id')
            ->firstWhere([
                'seller_id' => currentUser()->getUserId(),
                'collection.id' => $collectionId
            ]);

        return is_null($collection)
            ? $this->errorResponse()
            : $this->successResponse($collection);
    }

    public function updateFeaturedCampaigns(Request $request, $collectionId): JsonResponse
    {
        $campaignIds = $request->post('campaign_ids');

        $updated = SellerCollection::query()
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'collection_id' => $collectionId
            ])
            ->update(['feature_ids' => $campaignIds]);

        return $updated ? $this->successResponse() : $this->errorResponse();
    }

    public function getFeaturedCampaigns($collectionId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $item = SellerCollection::query()
            ->select('feature_ids')
            ->firstWhere([
                'seller_id' => currentUser()->getUserId(),
                'collection_id' => $collectionId
            ]);

        if (!$item || !$item->feature_ids) {
            return $this->errorResponse();
        }
        // reverse to get list by array first
        $featureIds = array_reverse(explode(',', $item->feature_ids));
        $featureIdsString = "'" . implode("','", $featureIds) . "'";

        $campaigns = Campaign::query()
            ->onSellerConnection($seller)
            ->select('id', 'name', 'thumb_url')
            ->whereIn('id', $featureIds)
            ->orderByRaw('FIELD(id,' . $featureIdsString . ') desc')
            ->get();

        return $this->successResponse($campaigns);
    }

    public function updateName(UpdateNameRequest $request, $id): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $name = $request->input('name');

        try {
            $count = ProductCollection::query()
                ->where([
                    'seller_id' => $seller->id,
                    'collection_id' => $id,
                ])
                ->count();

            if ($count > 10000) {
                return $this->errorResponse('Collection is too large. Please remove/add new collection manually.');
            }

            $collection = Collection::updateCollection($name);
            if ((int)$collection->id === (int)$id) {
                return $this->errorResponse('Collection name is already exists.');
            }

            $checkExists = SellerCollection::query()
                ->where([
                    'seller_id' => $seller->id,
                    'collection_id' => $collection->id
                ])
                ->exists();

            if ($checkExists) {
                SellerCollection::query()
                    ->where([
                        'seller_id' => $seller->id,
                        'collection_id' => $id,
                    ])
                    ->delete();
            } else {
                SellerCollection::query()
                    ->where([
                        'seller_id' => $seller->id,
                        'collection_id' => $id,
                    ])
                    ->update(['collection_id' => $collection->id]);
            }

            ProductCollection::query()->where([
                'seller_id' => $seller->id,
                'collection_id' => $id,
            ])->update(['collection_id' => $collection->id]);

            $productIds = ProductCollection::query()
                ->where([
                    'seller_id' => $seller->id,
                    'collection_id' => $collection->id,
                ])
                ->pluck('product_id')
                ->toArray();

            foreach (array_chunk($productIds, 500) as $chunk) {
                Product::query()
                    ->onSellerConnection($seller)
                    ->whereIn('id', $chunk)
                    ->orWhereIn('campaign_id', $chunk)
                    ->update(['sync_status' => Product::SYNC_DATA_STATS_ENABLED]);
            }

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e);
            return $this->errorResponse();
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteCollectionBanner(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|integer',
            'collection_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            $this->errorResponse($validator->errors()->first(), 403);
        }

        $objectId = $request->input('collection_id');
        $storeId = $request->input('store_id');
        $query = StoreCollection::query()
            ->where('store_id', $storeId)
            ->where('collection_id', $objectId);
        $storeCollection = $query->first();

        if ($storeCollection === null) {
            return $this->errorResponse('Collection not found', 403);
        }

        if ($query->update(['banner_url' => null]) !== false) {
            $pathDir = 'collections/' . $storeId . '/' . $objectId;

            if (Storage::disk(StorageDisksEnum::DEFAULT)->deleteDirectory($pathDir) === false) {
                return $this->errorResponse('Failed to delete collection banner (43278647)', 403);
            }

            return $this->successResponse();
        }

        return $this->errorResponse('Failed to delete collection banner (76452734)', 403);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateCollectionBanner(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|integer',
            'collection_id' => 'required|integer',
            'temp_path' => 'required',
        ]);

        if ($validator->fails()) {
            $this->errorResponse($validator->errors()->first(), 403);
        }

        $objectId = $request->input('collection_id');
        $storeId = $request->input('store_id');
        $query = StoreCollection::query()
            ->where('store_id', $storeId)
            ->where('collection_id', $objectId);

        $storeCollection = $query->first();

        if ($storeCollection === null) {
            Log::error('Collection not found', [
                'action' => 'updateCollectionBanner',
                'store_id' => $storeId,
                'collection_id' => $objectId
            ]);
            return $this->errorResponse('Collection not found', 403);
        }

        $whitelistExt = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $tempPath = $request->input('temp_path');
        $fileExists = Storage::disk(StorageDisksEnum::DEFAULT)->exists($tempPath);

        if ($fileExists === false) {
            return $this->errorResponse('Files not exists', 403);
        }

        $arrayPath = explode('/', $tempPath);
        $fileName = end($arrayPath);
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $newPath = "collections/{$storeId}/{$objectId}/banner.{$extension}";

        if (!in_array($extension, $whitelistExt)) {
            return $this->errorResponse('File type not allowed', 403);
        }

        try {
            $newPath = saveTempFileAws($tempPath, $newPath);
            if (empty($newPath)) {
                return $this->errorResponse('Upload failed (34826347)', 403);
            }

            $query->update(['banner_url' => $newPath]);

            return $this->successResponse([
                'banner_url' => $newPath,
            ], 'Upload success');
        } catch (Exception $exception) {
            Log::error($exception->getMessage(), [
                'collection_id' => $objectId,
                'store_id' => $storeId,
                'temp_path' => $tempPath,
                'new_path' => $newPath,
            ]);
            return $this->errorResponse('Upload failed (347628374)');
        }
    }
}
