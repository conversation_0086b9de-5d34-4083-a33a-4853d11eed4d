<?php

namespace App\Http\Controllers;

use App\Actions\Admin\Analytic\GetSaleReportDetailAction;
use App\Actions\Admin\Analytic\InsertSaleReportAction;
use App\Console\Commands\SyncStatsOrder;
use App\Enums\AccessExternalApiType;
use App\Enums\CacheKeys;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentGatewayRefundStatusEnums;
use App\Enums\PaymentMethodEnum;
use App\Enums\PersonalizedType;
use App\Enums\QueueName;
use App\Http\Controllers\Admin\FulfillController;
use App\Http\Controllers\Analytic\AdminController;
use App\Http\Controllers\Storefront\PaypalController;
use App\Http\Controllers\Storefront\StripeController;
use App\Jobs\CreateExportFileSocialFeed;
use App\Jobs\SendBuyerOrderConfirmationJob;
use App\Jobs\SendOrderToPBJob;
use App\Mail\TestEmail;
use App\Models\ApiLog;
use App\Models\Elastic;
use App\Models\FulfillProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\PaymentGateway;
use App\Models\PaymentGatewayRefund;
use App\Models\SaleReport;
use App\Models\SendMailLog;
use App\Models\SocialFeed;
use App\Providers\FulfillAPI\ObjectFulfill;
use App\Repositories\PrintGeekWebhookRepository;
use App\Services\SMSService;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\Encrypter;
use Carbon\Carbon;
use Faker\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Redis;
use Stripe\Stripe;
use Stripe\WebhookEndpoint;
use Throwable;
use App\Jobs\MoveUserToCustomer;

class TestController extends Controller {

    use ApiResponse;
    use ElasticClient;
    use Encrypter;

    public function test(Request $request): JsonResponse
    {
        return $this->successResponse();
    }

    public function crawlProductsAndProductVariants(): void {
        try {
            $object = new ObjectFulfill();
            $arrSupplierIdsHaveCrawlVariant = $object::getSupplierIdsHaveAPI('crawl_variant');
            $arrSupplierIdsHaveOnlyCrawlProduct = array_diff(
                    $object::getSupplierIdsHaveAPI('crawl_product'),
                    $arrSupplierIdsHaveCrawlVariant
            );
            DB::statement('SET SESSION group_concat_max_len = 1000000');
            $arrProductWithSku = FulfillProduct::query()
                    ->select('supplier_id')
                    ->selectRaw('GROUP_CONCAT(sku) as skus')
                    ->has('fulfill_mappings')
                    ->whereIn('supplier_id', $arrSupplierIdsHaveCrawlVariant)
                    ->groupBy('supplier_id')
                    ->get();
            foreach ($arrProductWithSku as $each) {
                $supplierId = $each->supplier_id;
                $arrSkus = explode(',', $each->skus);

                // todo @long: update for crawl variant
                // if(!empty($arrSkus)) {
                //     $objectProvider = $object->getProviderBySupplierId($supplierId);
                //     $objectProvider->crawlProductVariants($arrSkus);
                // }
            }

            foreach ($arrSupplierIdsHaveOnlyCrawlProduct as $supplierId) {
                $objectProvider = $object->getProviderBySupplierId($supplierId);
                $objectProvider->crawlProductsAndProductVariants();
            }
        } catch (Throwable $e) {
            logException($e);
        }
    }

    public function cacheStatus(Request $request): JsonResponse
    {
        $cacheKey = $request->get('cache_key');
        if ($cacheKey) {
            $result = syncScanCache($cacheKey);

            if (!empty($result)) {
                return $this->successResponse($result);
            }
            return $this->errorResponse('Result is empty');
        }
        return $this->errorResponse('cache_key is empty');
    }

    public function clearCache(Request $request): JsonResponse {
        $cacheKey = $request->get('cache_key');
        if ($cacheKey) {
            syncClearCache($cacheKey);
            return $this->successResponse($cacheKey);
        }
        return $this->errorResponse($cacheKey);
    }

    public function renderPb(Request $request) {
        $orderId = $request->get('order_id');
        $order = Order::find($orderId);
        if ($order) {
            SendOrderToPBJob::dispatch($order);
        }
    }

    public function updateDomainGatewayStripe(Request $request): void {
        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $oldEndpoint = 'api-us.senprints.net';
        $gateways = PaymentGateway::query()
                ->where('gateway', PaymentMethodEnum::STRIPE)
                ->get();

        foreach ($gateways as $gateway) {
            cache()->remember('update_payment_gateway' . $gateway->id, CacheKeys::CACHE_1H, function () use ($gateway, $oldEndpoint) {
                $config = json_decode($gateway->config, true);
                echo 'Gateway ID ' . $gateway->id . '<br>';
                if (!empty($config['endpoint_secret'])) {
                    try {
                        PaymentGatewayController::updateWebhookEndpoint($config, $oldEndpoint, true, $gateway->id);
                    } catch (Throwable $e) {
                        logToDiscord(
                                __FUNCTION__
                                . '|Detail: '
                                . $e->getMessage()
                                , 'error_checkout'
                        );
                    }
                }

                return 1;
            });
        }
    }

    public function deleteGatewayStripe(Request $request): void {
        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $gateways = PaymentGateway::query()
                ->where('gateway', PaymentMethodEnum::STRIPE)
                ->get();

        foreach ($gateways as $gateway) {
            $config = json_decode($gateway->config, true);
            Stripe::setApiKey($config['secret_key']);
            $webhooks = WebhookEndpoint::all([
                        'limit' => 100,
            ]);

            foreach ($webhooks as $webhook) {
                if (Str::contains($webhook['url'], 'api-us.senprints.net')) {
                    WebhookEndpoint::retrieve($webhook['id'])->delete();
                }
            }
        }
    }

    /**
     * @param Request $request
     * @param OrderProduct $product
     * @return JsonResponse
     */
    public function updateCustomOptions(Request $request, OrderProduct $product) {
        $validator = \Validator::make($request->all(), [
                    'custom_options' => 'required'
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        $customOptions = $request->post('custom_options');
        if ($product->personalized === PersonalizedType::PB) {
            $product->custom_options = $customOptions;
            if ($product->save()) {
                return $this->successResponse('Success');
            }
        }
        return $this->errorResponse('Unknown');
    }

    public function kienTest() {
        $startTime = floor(microtime(true) * 1000);
        Log::channel('gelf')->debug("graylog", ['foo' => 'bar']);
        $endTime = floor(microtime(true) * 1000);
        $processTime = $endTime - $startTime;
        echo "Graylog Process time: {$processTime}";

        $startTime = floor(microtime(true) * 1000);
        Log::channel('discord')->debug("discord", ['foo' => 'bar']);
        $endTime = floor(microtime(true) * 1000);
        $processTime = $endTime - $startTime;
        echo " --- Discord log Process time: {$processTime}";
    }

    public function testJobRefund() {
        $limit = 50;
        $refunds = PaymentGatewayRefund::query()
            ->with(['order'])
            ->where('status', PaymentGatewayRefundStatusEnums::PROCESSING)
            ->limit($limit)
            ->get();

        if($refunds->count() === 0) {
            return $this->errorResponse('No refund available');
        }
        DB::beginTransaction();
        try {
            $total = 0;
            $countSuccess = 0;
            foreach($refunds as $refund) {
                if(empty($refund->order)) {
                    continue;
                }
                $order = $refund->order;
                $refundedAmountOnGateway = PaymentGatewayRefund::query()
                    ->select('refund_amount')
                    ->where([
                        'status' => PaymentGatewayRefundStatusEnums::COMPLETED,
                        'order_id' => $order->id
                    ])
                    ->get()
                    ->sum('refund_amount');


                $refundAvailable = $order->total_amount - $refundedAmountOnGateway;
                $diffRefundable = $refundAvailable / 1000;
                if($refund->refund_amount > $refundAvailable && $refund->refund_amount <= $diffRefundable + $refundAvailable) {
                    $refund->refund_amount = $refundAvailable;
                } else if($refund->refund_amount > $diffRefundable + $refundAvailable) {
                    $refund->status = PaymentGatewayRefundStatusEnums::ERROR;
                    $refund->log = 'Refund refused. The partial refund amount must be less than or equal to the remaining amount';
                }
                if($order->payment_method === PaymentMethodEnum::PAYPAL) {
                    try {
                        $refundResponse = (new PaypalController())->refundOrderToGateway($refund->order, $refund->refund_amount, $refund->is_full_refund);
                        if($refundResponse['success']) {
                            $refund->status = PaymentGatewayRefundStatusEnums::COMPLETED;
                            $total += $refund->refund_amount;
                            $countSuccess ++;
                        } else {
                            $refund->status = PaymentGatewayRefundStatusEnums::ERROR;
                            $refund->log = $refundResponse['message'];
                        }
                    } catch (Throwable $exception) {
                        $refund->status = PaymentGatewayRefundStatusEnums::ERROR;
                        $refund->log = $exception->getMessage();
                    }
                    $refund->save();
                } else if ($refund->order->payment_method === PaymentMethodEnum::STRIPE) {
                    try {
                        $refundResponse = (new StripeController())->refundOrderToGateway($refund->order, $refund->refund_amount, $refund->is_full_refund);
                        if(empty($refundResponse) || $refundResponse['status'] !== 'succeeded') {
                            $refund->status = PaymentGatewayRefundStatusEnums::ERROR;
                            $refund->log = $refundResponse['reason'];
                        } else {
                            $refund->status = PaymentGatewayRefundStatusEnums::COMPLETED;
                            $total += $refund->refund_amount;
                            $countSuccess ++;
                        }
                    } catch (Throwable $exception) {
                        $refund->status = PaymentGatewayRefundStatusEnums::ERROR;
                        $refund->log = $exception->getMessage();
                    }

                    $refund->save();
                }
            }
            DB::rollBack();
            if($total > 0) {
                Log::info("Refund $countSuccess requests. Total $$total");
            }
            return $this->successResponse();
        } catch (Throwable $throwable) {
            dd($throwable);
            DB::rollBack();
            logToDiscord($throwable->getMessage());
            Log::info('===========Error refund to payment gateway===========');
            Log::error("Message: {$throwable->getMessage()} - " . "Line: {$throwable->getLine()} - " . "File: {$throwable->getFile()}");
            $this->errorResponse($throwable->getMessage());
        }
    }

    /**
     * Update default template for about pages
     * Keep the method for future use
     * Add new route to test.php to run on production
     *
     * @return void
     */
    public function updateAboutPages(): void
    {
        $template = <<<EOL
Welcome to {storeName}, your one-stop shop for high-quality printing products. We are dedicated to providing our customers with the best possible experience when it comes to purchasing printed merchandise. Our products are produced using the latest technology and the highest quality materials.

We take pride in the quality of our products, and this is evident in the attention to detail and craftsmanship that goes into every item we produce. Our team of experienced professionals is committed to ensuring that every product meets the highest standards of quality, so you can trust that when you purchase from us, you're getting a product that is built to last.

We understand that customers have a lot of choices when it comes to purchasing printed products, which is why we strive to go above and beyond to provide excellent customer service. Our knowledgeable team is always available to answer any questions you may have and help you find the perfect product for your needs.

We are grateful for your interest in our company and the products we offer. We hope you will give us the opportunity to serve you and to become your go-to source for high-quality printed products. Thank you for choosing {storeName}.
EOL;

        $updated = \App\Models\Page::select('content')
            ->where(function ($query) {
                $query->where('content', 'like', '%Alice%')
                    ->orWhere('content', 'like', '%Hatter%')
                    ->orWhere('content', 'like', '%White Rabbit%');
            })
            ->where('slug', 'about')
            ->update(['content' => $template]);

        echo "Updated $updated records";
    }

    public function updateSaleReport(Request $request)
    {
        $year  = $request->input('year', date('Y'));
        $data  = [];
        $start = Carbon::createFromDate($year, 1, 1);

        for ($i = 0; $i <= 11; $i++) {
            $endDate = $start->copy()->addMonthsNoOverflow($i);
            $year    = $endDate->year;
            $month   = $endDate->month;
            $compare = AdminController::compareTimeToCurrent($year, $month);
            if ($compare > 0) {
                break;
            }
            // $data[] = $month . '/' . $year;
            $dateRanges = GetSaleReportDetailAction::getDateRanges($year, $month);

            $value = Order::query()
                ->selectRaw('COUNT(DISTINCT `seller_id`) as fulfill_sellers')
                ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->whereNotIn('status', [
                    OrderStatus::CANCELLED,
                    OrderStatus::REFUNDED,
                ])
                ->where('type', OrderTypeEnum::FULFILLMENT)
                ->addFilterAnalytic([], $dateRanges)
                ->value('fulfill_sellers');
            $time  = GetSaleReportDetailAction::getTime($year, $month);

            $data[] = [
                'time'  => $time,
                'type'  => 'fulfill_sellers',
                'value' => $value,
            ];
        }

        SaleReport::insert($data);
        // return $data;
    }

    public function insertSaleReport(Request $request)
    {
        if (!$request->has('senprint_key')) {
            abort(404);
        }
        (new InsertSaleReportAction())->handle($request);
    }

    public function updateFulfillOrderIdByLog(Request $request)
    {
        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $supplierId = $request->input('supplier_id');
        $presentFulfillStatus = [
            OrderProductFulfillStatus::REJECTED,
        ];

        $orderIds = OrderProduct::query()
            ->where('supplier_id', $supplierId)
            ->whereIn('fulfill_status', $presentFulfillStatus)
            // ->whereNull('fulfill_order_id')
            // ->where(function ($q) {
            //     $q->orWhere('fulfill_exception_log', 'Order not found');
            //     $q->orWhere('fulfill_exception_log', 'ok');
            // })
            ->pluck('order_id');

        $data = ApiLog::query()
            ->select([
                'order_id',
                'fulfill_order_id',
                'created_at',
            ])
            ->where('reference_id', $supplierId)
            ->where('reference_type', AccessExternalApiType::SUPPLIER)
            ->where('name', 'CREATE_ORDER')
            ->whereIn('order_id', $orderIds)
            ->groupBy('order_id')
            ->orderByDesc('created_at')
            ->get();


        DB::beginTransaction();
        foreach ($data as $each) {
            try {
                OrderProduct::query()
                    ->where('order_id', $each->order_id)
                    ->where('supplier_id', $supplierId)
                    ->where('fulfill_status', $presentFulfillStatus)
                    ->update([
                        'fulfill_order_id' => $each->fulfill_order_id,
                    ]);
            } catch (\Throwable $e) {
            }
        }
        DB::commit();
    }

    public function updateFulfillOrderIdByLogWebhook(Request $request): void
    {
        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $supplierId = 14;
        $orderProducts = OrderProduct::query()
            ->where('supplier_id', $supplierId)
            ->where('fulfill_status', OrderProductFulfillStatus::FULFILLED)
            ->whereRaw("tracking_code = ''")
            ->pluck('fulfill_order_id');

        $data = ApiLog::query()
            ->select([
                'request_body',
            ])
            ->where('reference_id', $supplierId)
            ->where('reference_type', 'supplier')
            ->where('type', 'webhook')
            ->where('created_at', '>=', '2023-03-15 00:00:00')
            ->whereIn('fulfill_order_id', $orderProducts)
            // ->limit(1)
            ->get();

        foreach ($data as $each) {
            $body     = $each->request_body;
            $response = json_decode($body, true);
            if ($response['status'] === 'shipped') {
                $result = (new PrintGeekWebhookRepository($response, $supplierId))->get();

                FulfillController::updateByResponse($result, true);
            }
        }
    }

    public function syncOrder(Request $request)
    {
        if (!$request->has('senprint_key')) {
            abort(404);
        }
        $hour = $request->get('hour', 24);


        (new SyncStatsOrder())->handle($hour);
        return 'done';
    }

    public function sendTestWhatsapp(Request $request) {
        $to = $request->get('phone', '+84985411191');
        $name = $request->get('name', 'Thang NM');
        $body = $request->get('body', "Hi $name, how is about your T-shirt? Here are some great designs cool.me/love for you.");
        $phone_number = Str::startsWith($to, '+') ? $to : '+' . $to;
        $smsId = Str::uuid();
        $sms = new SMSService($smsId, $body, $phone_number,'whatsapp');
        [$status, $twilioMsg] = $sms->sendNow();
        return response()->json([
            'success' => true,
            'message' => $status,
            'result' => $twilioMsg
        ]);
    }

    public function sendEmailConfirm(Request $request)
    {
        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $status = $request->get('status');
        $template = $request->get('template');
        $emailTo = $request->get('email_to');
        $orderId = $request->get('order_id');
        $startDate = $request->date('start_date');
        $endDate = $request->date('end_date');

        if (empty($startDate)) {
            $startDate = now()->subRealWeek();
        }
        if (empty($endDate)) {
            $endDate = now();
        }

        $orderIds = SendMailLog::query()
            ->when($status, function ($q) use ($status) {
                $q->where('status', $status);
            })
            ->when($template, function ($q) use ($template) {
                $q->where('template', $template);
            })
            ->when($emailTo, function ($q) use ($emailTo) {
                $q->where('email_to', $emailTo);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->when($orderId, function ($q) use ($orderId) {
                $q->where('order_id', $orderId);
            })
            ->pluck('order_id');

        $template = $request->input('template');

        foreach ($orderIds as $orderId) {
            cache()->remember(
                'have_sent_email_' . $template . $orderId,
                CacheKeys::CACHE_1W,
                function () use ($orderId) {
                    $order = Order::find($orderId);
                    SendBuyerOrderConfirmationJob::dispatchAfterResponse($order);
                });
        }
    }

    public function sendTestEmail(Request $request)
    {
        $order_id = $request->get('order_id');
        $customerEmail = $request->get('email', '<EMAIL>');
        if (empty($order_id)) {
            return $this->errorResponse('Order ID is required');
        }
        $order = Order::query()->whereKey($order_id)->first();
        if (empty($order)) {
            return $this->errorResponse('Order is required');
        }
        $customerName = $order->customer_name;
        $config = [
            'to'          => $customerEmail,
            'template'    => 'buyer.order_ship_late_notification',
            'data'        => [
                'subject'          => 'Shipping Notification',
                'name'             => $customerName,
                'email'            => $customerEmail,
                'order'            => [
                    'id'                    => $order->id,
                    'status_url'            => $order->status_url,
                    'order_number'          => $order->order_number
                ],
                'store_info' => StoreService::getStoreInfo($order->store_id)
            ],
            'sendMailLog' => [
                'sellerId' => $order->seller_id ?? null,
                'storeId' => $order->store_id ?? null,
                'orderId' => $order->id ?? null
            ],
            'store_log' => 0
        ];
        $sent = sendEmail($config);
        var_dump($sent);
    }

    public function refreshFeed(Request $request, $id)
    {
        if (!$request->has('senprint_key')) {
            abort(404);
        }

        $feed = SocialFeed::query()
            ->where([
                'id'        => $id,
            ])
            ->firstOrFail();

        $total                = (new Elastic())->getProductWithDefaultFeed($feed->filters, true);
        $feed->total_products = $total;
        $feed->path           = null;
        $feed->total_exported = 0;
        $feed->save();
        $queueName = QueueName::SOCIAL_FEED;
        if ($total > SocialFeedController::LIMIT_PRODUCTS) {
            $queueName = QueueName::LARGE_SOCIAL_FEED;
        }
        CreateExportFileSocialFeed::dispatch($id, $queueName);
    }

    public function testSendMail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'token' => 'required|string|in:tester',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $faker = Factory::create();
        $emailTo = $request->get('email');
        $subject = $request->get('subject', $faker->text(100));
        $content = $request->get('content', $faker->paragraphs(5, true));
        $senderName = $request->get('sender_name', $faker->text(50));
        $data = [
            'subject' => $subject,
            'content' => $content,
        ];

        $email = new TestEmail($data);
        $email->from(getSenderEmail(), $senderName);
        Mail::to($emailTo)->send($email);

        return $this->successResponse();
    }

    public function checkingScanCache(Request $request)
    {
        $cursor = $request->get('cursor', '0');
        $pattern = $request->get('pattern', '*');
        $cacheKey = $request->get('cache_key', '');
        $count = $request->get('count', 100);
        $db = $request->get('db', null);
        $usePrefix = $request->get('use_prefix', false);
        $fullPattern = $request->get('full_pattern', null);

        if ($usePrefix && $cacheKey && is_null($fullPattern)) {
            $fullPattern = $this->preparePattern($cacheKey);
        } elseif (is_null($fullPattern)) {
            $fullPattern = $pattern . $cacheKey;
        }

        $redis = new Redis();
        $redis->connect('redis', 6379);

        if (!is_null($db)) {
            $redis->select((int)$db);
        }

        $scanResult = $redis->scan($cursor, $fullPattern, $count);

        return response()->json([
            'db' => $db,
            'use_prefix' => $usePrefix,
            'cache_key' => $cacheKey,
            'pattern' => $pattern,
            'full_pattern' => $fullPattern,
            'count' => $count,
            'cursor' => $cursor,
            'result' => $scanResult,
        ]);
    }

    public function putAlternativeCache(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string',
            'value' => 'required|string',
            'ttl' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $key = $request->get('key');
        $value = $request->get('value');
        $ttl = $request->get('ttl', 60);
        $store = $request->get('store', CacheKeys::CACHE_TYPE_ALTERNATIVE);

        if ($store === 'default') {
            $store = null;
        }

        $result = cache()->store($store)->put($key, $value, $ttl);

        return response()->json([
            'request key' => $key,
            'request value' => $value,
            'request ttl' => $ttl,
            'result' => $result ? 'success' : 'failed',
        ]);
    }

    public function getAlternativeCache(Request $request): JsonResponse
    {
        $key = $request->get('key');
        $store = $request->get('store', CacheKeys::CACHE_TYPE_ALTERNATIVE);
        $tags = [];

        if ($store === 'default') {
            $store = null;
        }

        $cache = cache()->store($store);

        if (!empty($tags)) {
            $cache = $cache->tags($tags);
        }

        $value = $cache->get($key);
        $isExist = $cache->has($key);

        return response()->json([
            'request key' => $key,
            'exist' => $isExist ? 'yes' : 'no',
            'value' => $value,
        ]);
    }

    public function forgetAlternativeCache(Request $request): JsonResponse
    {
        $key = $request->get('key');
        $store = $request->get('store', CacheKeys::CACHE_TYPE_ALTERNATIVE);
        $tags = [];

        if ($store === 'default') {
            $store = null;
        }

        $cache = cache()->store($store);

        if (!empty($tags)) {
            $cache = $cache->tags($tags);
        }

        $result = $cache->forget($key);
        return response()->json([
            'request key' => $key,
            'result' => $result ? 'success' : 'failed',
        ]);
    }

    /**
     * Get total ES campaigns
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function getTotalEsCampaigns()
    {
        $dateRanges = [
            'type' => 'last_7_days',
        ];
        $total = $this->elasticCount([], $dateRanges);
        if ($total > 0) {
            return response()->json([
                'result' => $total,
            ]);
        }
         return response()->json([
            'result' => 0,
        ], 500);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function moveUserToCustomer(Request $request)
    {
        $userIds = $request->post('user_ids');
        if (empty($userIds)) {
            return $this->errorResponse('User IDs are required');
        }
        MoveUserToCustomer::dispatch($userIds)->onQueue('default');
        return $this->successResponse('Users moved successfully');
    }

    public function testAssignPrioritySupplier (Request $request) {
        $orderId = $request->get('order_id');
        if (!$orderId) {
            return $this->errorResponse('Order ID is required');
        }

        $order = Order::query()->with('products')->find($orderId);
        if (!$order) {
            return $this->errorResponse('Order not found');
        }

        try {
            $order->assignPrioritySupplier();
            $order->save();
            foreach ($order->products as $product) {
                $product->save();
            }
            return $this->successResponse('Priority supplier assigned successfully');
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to assign priority supplier');
        }
    }
}
