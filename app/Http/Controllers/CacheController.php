<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Product;
use App\Models\Slug;
use App\Traits\ApiResponse;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\Rule;

class CacheController extends Controller
{
    use ApiResponse;

    //Get list caches
    public function listCacheKeys(): JsonResponse
    {
        $keys = CacheKeys::getKeys();
        $keys = array_values(array_filter($keys, function ($key) {
            return !str_contains($key, 'PREFIX') && !str_contains($key, 'CACHE_') && $key !== CacheKeys::ALL_CACHE;
        }));

        return $this->successResponse($keys);
    }

    public function clearCache(Request $request): JsonResponse
    {
        $user = currentUser();
        $key = $request->post('key');

        if (!$user->isAdmin()) {
            return $this->errorResponse('Access denied', 403);
        }
        try {
            $value = CacheKeys::fromKey($key)->value;

            $cacheKeys = [];
            if ($value === CacheKeys::ALL_CACHE) {
                $cacheKeys['all'] = true;
                syncClearCache($cacheKeys);

                return $this->successResponse();
            }

            // auto add tag for templates to clear cache
            switch ($value) {
                case CacheKeys::SYSTEM_PRODUCT_TEMPLATES:
                case CacheKeys::LIST_PRODUCT_TEMPLATES:
                case CacheKeys::LIST_VARIANT_BY_TEMPLATE:
                case CacheKeys::SYSTEM_EXPRESS_PRODUCT_TEMPLATE:
                case CacheKeys::TEMPLATES:
                    $cacheKeys['tags'] = [CacheKeys::SYSTEM_PRODUCT_TEMPLATES];
                    break;
            }

            $cacheKeys[] = $value;
            syncClearCache($cacheKeys);
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse('Invalid key');
        }
    }

    public function clearStoreCache($storeId): JsonResponse
    {
        try {
            clearStoreCache($storeId);
            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function clearCampaignCache(Request $request): JsonResponse
    {
        $slug = $request->post('slug');
        $cacheKey = CacheKeys::getProductCacheKey($slug);

        try {
            syncClearCache([$cacheKey]);
            syncClearCache([$cacheKey], CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function sellerClearCampaignCache(Request $request): JsonResponse
    {
        $user = currentUser();
        $data = $request->validate([
            'slugs' => [
                Rule::requiredIf(function () use ($request){
                    return empty($request->get('ids'));
                }),
                'array'
            ],
            'ids' => [
                Rule::requiredIf(function () use ($request){
                    return empty($request->get('slugs'));
                }),
                'array'
            ],
        ]);
        $slugs = data_get($data, 'slugs', []);
        $ids = data_get($data, 'ids', []);
        if (empty($slugs) && empty($ids)) {
            return $this->errorResponse('Invalid request', 400);
        }
        if (count($slugs) > 500 || count($ids) > 500) {
            return $this->errorResponse('Maximum 500 slugs', 400);
        }
        try {
            $filterCampaignIds = [];
            $filterSlugs = [];
            if (!empty($slugs)) {
                $campaigns = Slug::query()
                    ->where('seller_id', $user->getUserId())
                    ->whereIn('slug', $slugs)
                    ->limit(500)
                    ->get();
                if ($campaigns->isNotEmpty()) {
                    $filterCampaignIds = $campaigns->pluck('campaign_id')->toArray();
                    $filterSlugs = $campaigns->pluck('slug')->toArray();
                }
            }
            if (!empty($ids)) {
                $campaigns = Campaign::query()
                    ->onSellerConnection($user)
                    ->select(['id', 'slug'])
                    ->whereIn('id', $ids)
                    ->where('seller_id', $user->getUserId())
                    ->whereNotNull('slug')
                    ->limit(500)
                    ->get();
                if ($campaigns->isNotEmpty()) {
                    $filterCampaignIds = array_merge($campaigns->pluck('id')->toArray(), $filterCampaignIds);
                    $filterSlugs = array_merge($campaigns->pluck('slug')->toArray(), $filterSlugs);
                }
            }
            $campaignIds = array_unique($filterCampaignIds);
            $slugs = array_unique($filterSlugs);
            if (empty($slugs) && empty($campaignIds)) {
                return $this->errorResponse('No campaign found', 400);
            }
            try {
                (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignIds, sellerId: $user->getUserId());
            } catch (\Throwable $e) {
                Product::query()
                    ->onSellerConnection($user)
                    ->filterByProductOrCampaignIds($campaignIds)
                    ->update(['sync_status' => 0]);
            }
            foreach ($slugs as $slug) {
                $cacheKey = CacheKeys::getProductCacheKey($slug);
                syncClearCache([$cacheKey]);
                syncClearCache([$cacheKey], CacheKeys::CACHE_TYPE_ALTERNATIVE);
            }
            return $this->successResponse([
                'total' => count($slugs),
                'slugs' => $slugs,
                'campaign_ids' => $campaignIds
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @return JsonResponse
     */
    public function clearCacheNode(): JsonResponse
    {
        try {
            $url = config('senprints.clear_cache_node_url');

            Http::withoutVerifying()->timeout(15)->get($url);

            return $this->successResponse();
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
