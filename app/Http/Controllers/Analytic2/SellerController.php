<?php

/** @noinspection PhpUndefinedMethodInspection */

/** @noinspection PhpMethodParametersCountMismatchInspection */

namespace App\Http\Controllers\Analytic2;

use App\Enums\CacheKeys;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\DateRangeEnum;
use App\Enums\PgsAndMysqlVersionEnum;
use App\Enums\SaleReport\DataIndexEnum;
use App\Enums\SaleReport\DataTypeEnum;
use App\Enums\SaleReport\TypeEnum;
use App\Http\Controllers\Analytic\SellerController as SellerControllerAlias;
use App\Http\Requests\Analytic\Seller\CampaignRequest;
use App\Http\Requests\Analytic\Seller\StoreRequest;
use App\Models\Campaign;
use App\Models\Order;
use App\Models\SalesReport;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SellerController extends SellerControllerAlias
{
    public ?string $dataType = DataTypeEnum::OVERVIEW;
    public ?string $dataIndex = null;
    public ?string $type = null;
    public ?string $id = null;

    /** @noinspection MagicMethodsValidityInspection
     * @noinspection PhpMissingParentConstructorInspection
     */
    public function __construct()
    {
        if (request('date_range') !== DateRangeEnum::TODAY) {
            $this->controller = $this;
            $this->model      = new SalesReport();
        } else {
            $this->controller = new SellerControllerAlias();
        }
    }

    public function dashboard(Request $request): JsonResponse
    {
        $this->controller->setCommonFilter($request);
        $data = $this->controller->getOverview('dashboard', true, true);

        return $this->successResponse($data);
    }

    public function getCommonQuery(
        $getActiveSeller = false,
        $isDashboard = false,
        $justIncludeDataIndexPlatform = false
    ) {
        return $this->controller->getAnalyticQuery()
            ->getAnalyticOverview($isDashboard, $justIncludeDataIndexPlatform);
    }

    protected function getAnalyticOverview($typeAnalytic, $allReportsWithoutTime, $dbVersion = PgsAndMysqlVersionEnum::POSTGRES): object
    {
        $query = $this->controller->getCommonQuery(false, true, true);

        if ($typeAnalytic !== 'dashboard') {
            $overview = $query->first();
        } else {
            $user = currentUser();

            if ($user->isAdmin()) {
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::FULFILL . "' THEN orders END) AS fulfillment_order_count");
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::FULFILL . "' THEN items END) AS fulfillment_items");
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::FULFILL . "' THEN total_sales END) AS fulfillment_order_sales");
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::CUSTOM . "' THEN orders END) AS custom_order_count");
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::CUSTOM . "' THEN items END) AS custom_order_items");
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::CUSTOM . "' THEN total_sales END) AS custom_order_sales");
                $query->selectRaw("SUM(CASE WHEN data_index = '" . DataIndexEnum::PLATFORM . "' THEN total_sales END) AS total_sales");
            }
            $overview = $query->first();
            if (is_null($overview)) {
                return (object)[];
            }

            if ($user->isAdmin()) {
                $overview->insurance_fee  *= 0.8;
                $overview->active_sellers = Order::query()
                    ->countActiveSellers()
                    ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                    ->value('active_sellers');
            } else {
                $overview->insurance_fee *= 0.2;
            }
            $this->controller->addAttributeToOverview($overview, $allReportsWithoutTime, $user);
        }

        return model_map($overview, 'floatval');
    }

    public function getOverViewAndViews(
        string $field,
        ?array $filterArrId = [],
        ?string $orderBy = '',
        int $limit = 20,
        $dbVersion = PgsAndMysqlVersionEnum::POSTGRES
    ): array {
        $alias = $field;
        switch ($field) {
            case 'ad_campaign':
            case 'ad_medium':
            case 'ad_source':
                $alias = 'name';
                break;
            case 'template_id':
                $field = 'template_product';
                $alias = 'template_id';
                break;
        }

        $query = $this->controller->getAnalyticQuery(null, [
            'sales_reports.data_type',
        ]);
        if ($field === 'store_id') {
            $query->selectRaw('type_index as ' . $alias);
            $query->where('data_type', DataTypeEnum::OVERVIEW);
            $query->groupBy('type_index');
        } else {
            $query->selectRaw('data_index as ' . $alias);
            $query->where('data_type', $field);
            $query->groupBy('data_index');
        }

        if ($orderBy) {
            $query->selectRaw("sum($orderBy) as $orderBy");
        } else {
            $query->getAnalyticOverview();
        }

        if (!empty($filterArrId)) {
            $query->whereIn('type_index', $filterArrId);
        }


        switch ($alias) {
            case 'country':
                $query->with('system_location:code,name');
                break;
            case 'template_id':
                $query->with('template_product:id,name');
                break;
            case 'store_id':
                $query->addSelect('seller_id');
                $query->groupBy('seller_id');
                $query->with('seller:id,email');
                $query->with('store:id,domain,sub_domain');
                break;
        }

        if ($orderBy === CampaignSortByAllowEnum::CONVERSION_RATE || !$orderBy) {
            $query->orderByDesc(CampaignSortByAllowEnum::TOTAL_ORDERS);
        } else {
            $query->orderByDesc($orderBy);
        }

        $data = $query
            ->limit($limit)
            // ->dd()
            ->get()
            ->toArray();

        foreach ($data as &$each) {
            $each['conversion_rate'] = (!empty($each['orders']) && !empty($each['visit'])) ? $each['orders'] / $each['visit'] : 0;
            if (isset($each['system_location'])) {
                $each['name'] = $each['system_location']['name'];
                unset($each['system_location']);
            }
        }

        return $data;
    }

    public function setCommonFilter(Request $request, $setDateRangeByRequest = true): void
    {
        $this->controller->type ??= currentUser()->isAdmin() ? TypeEnum::PLATFORM : TypeEnum::SELLER;

        $this->arrFilter[$this->getTableColumn('type')] = $this->controller->type;

        if (!empty($this->controller->dataType)) {
            $this->arrFilter[$this->getTableColumn('data_type')] = $this->controller->dataType;
        }

        if (!empty($this->controller->dataIndex)) {
            $this->arrFilter[$this->getTableColumn('data_index')] = $this->controller->dataIndex;
        }

        if (!empty($this->controller->id)) {
            $this->arrFilter[$this->getTableColumn('type_index')] = $this->controller->id;
        }

        parent::setCommonFilter($request, $setDateRangeByRequest);
    }

    protected function getTableColumn(string $type): string
    {
        return $this->model->getTable() . '.' . $type;
    }

    public function campaign(CampaignRequest $request, $campaignId): JsonResponse
    {
        $campaignName = Campaign::query()
            ->where([
                'id' => $campaignId,
                // 'seller_id' => currentUser()->getUserId()
            ])
            ->value('name');

        if (!$campaignName) {
            return $this->errorResponse('Campaign not found');
        }

        $this->controller->type = TypeEnum::CAMPAIGN;
        $this->controller->id   = $campaignId;

        $arr = $request->except([
            'campaign_id',
        ]);
        /** @noinspection CallableParameterUseCaseInTypeContextInspection */
        $request = new Request($arr);
        $this->controller->setCommonFilter($request);

        $overview  = $this->controller->getOverview('campaign');
        $countries = $this->controller->getOverViewAndViews('country');

        $this->controller->dataType = DataTypeEnum::PRODUCT;
        $this->controller->setCommonFilter($request);
        $products = $this->controller->getAnalyticQuery()
            ->getAnalyticOverview(true)
            ->addSelect('data_index')
            ->with('product:id,name')
            ->groupBy('data_index')
            ->get();

        return $this->successResponse(
            [
                'campaign_name' => $campaignName,
                'analytic'      => [
                    'overview'  => $overview,
                    'products'  => $products,
                    'countries' => $countries,
                ]
            ]
        );
    }

    public function getStores(Request $request): JsonResponse
    {
        $this->controller->type = TypeEnum::STORE;

        return parent::{__FUNCTION__}($request);
    }

    public function getChart(StoreRequest $request): array
    {
        $dateType = 'day';
        $user     = currentUser();
        $isAdmin  = $user->isAdmin();
        $timeout  = $isAdmin ? CacheKeys::CACHE_1H : CacheKeys::CACHE_24H;
        $storeId  = $request->get('store_id');

        $tags = [
            'seller_id_' . currentUser()->getUserId()
        ];

        if ($storeId) {
            $tags[] = CacheKeys::getStoreId($storeId);
        }

        return cache()
            ->tags($tags)
            ->remember(
                CacheKeys::getStats('chart', $dateType, DateRangeEnum::LAST_30_DAYS, $storeId),
                $timeout,
                function () use ($request, $isAdmin) {
                    $request->merge([
                        'date_type'  => DateRangeEnum::CUSTOM,
                        'start_date' => now()->subDays(15),
                        'end_date'   => now()->subDays(),
                        'datetime'   => true,
                    ]);

                    $this->controller->dataIndex = DataIndexEnum::PLATFORM;
                    $this->controller->setCommonFilter($request);
                    $days = 15;
                    $now  = getTodayTimeZone(null, false)->subDays($days);

                    $dates = [];
                    for ($i = 1; $i < $days; $i++) {
                        $dates[] = $now->copy()->addDays($i)->format('Y-m-d');
                    }

                    $data = $this->controller->getAnalyticQuery()
                        ->selectRaw('date_timezone')
                        ->getChart()
                        ->groupByRaw('date_timezone')
                        ->get();

                    $arr           = [];
                    $activeSellers = [];
                    $columns       = [
                        'visits',
                        'add_to_carts',
                        'checkouts',
                        'orders',
                        'items',
                    ];
                    if ($isAdmin) {
                        $columns[]     = 'active_sellers';
                        $activeSellers = Order::query()
                            ->selectRaw('date(paid_at) as paid_at')
                            ->countActiveSellers()
                            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                            ->groupByRaw('date(paid_at)')
                            ->get();
                    }

                    foreach ($dates as $index => $date) {
                        foreach ($data as $key => $each) {
                            if ($each->date_timezone === $date) {
                                foreach ($columns as $column) {
                                    $arr[$column][$index] = (int)$each->$column;
                                }
                                unset($data[$key]);
                                break;
                            }
                        }
                        foreach ($activeSellers as $key => $each) {
                            $paidAt = $each->paid_at->format('Y-m-d');
                            if ($paidAt === $date) {
                                $arr['active_sellers'][$index] = (int)$each->active_sellers;
                                unset($activeSellers[$key]);
                                break;
                            }
                        }

                        // set default
                        foreach ($columns as $column) {
                            if (!isset($arr[$column][$index])) {
                                $arr[$column][$index] = 0;
                            }
                        }

                        $arr['dates'][$index] = Str::after($date, '-');
                    }

                    return $arr;
                }
            );
    }
}
