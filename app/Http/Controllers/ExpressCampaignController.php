<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignRenderModeEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\CloudMockupApis;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\StorageDisksEnum;
use App\Enums\UserRoleEnum;
use App\Jobs\ScanCampaignCopyright;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Jobs\UpdateExpressCampaignJob;
use App\Models\BulkUploadLog;
use App\Models\Campaign;
use App\Models\Category;
use App\Models\Collection;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductCollection;
use App\Models\ProductVariant;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\TemplateCampaign;
use App\Models\User;
use App\Services\CampaignService;
use App\Services\SenPrintsAuth;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Campaign\Jobs\SyncSlugJob;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Throwable;

class ExpressCampaignController extends BaseCampaignController
{
    use ApiResponse, ElasticClient;

    private static function addCampaignToStore($stores, $campaignId, $userId): void
    {
        if (!empty($stores)) {
            $stores = array_column($stores, 'id');
            $totalIds = count($stores);
            // Get total found by list id
            $totalRows = Store::query()
                ->whereIn('id', $stores)
                ->where('seller_id', $userId)
                ->count();

            if ($totalIds === $totalRows) {
                $data = [];
                foreach ($stores as $storeId) {
                    $data[$storeId] = [
                        'store_id' => $storeId,
                        'product_id' => $campaignId
                    ];
                }
                StoreProduct::query()->insertOrIgnore($data);
            }
        }
    }

    private static function addCampaignToCollection($collections, $campaignId, $userId): void
    {
        if (!empty($collections)) {
            $collectionIds = [];

            foreach ($collections as $collection) {
                if (isset($collection['id'])) {
                    if (Collection::query()->where('id', $collection['id'])->exists()) {
                        $collectionIds[] = $collection['id'];
                    }
                } elseif (isset($collection['name'])) {
                    $exists = Collection::query()
                        ->select(['id', 'name'])
                        ->firstWhere('name', $collection['name']);

                    if ($exists) {
                        $collectionIds[] = $exists['id'];
                    } else {
                        $newCollection = Collection::addCollection($collection['name']);

                        if ($newCollection) {
                            $collectionIds[] = $newCollection->id;
                        }
                    }
                }
            }

            foreach ($collectionIds as $collectionId) {
                CampaignController::addCampaignToCollection($campaignId, $userId, $collectionId);
            }
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function createNewTemplate(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $seller = User::query()->find($userId);
        $authId = $user->getAuthorizedAccountId();

        $campaignTitle = $request->input('title');
        $campaignDescription = $request->input('description');
        $campaignThumbnail = $request->input('thumb_url');
        $oldPrice = $request->input('old_price');
        $collections = $request->input('collections');
        $stores = $request->input('storefronts');
        $endTime = $request->input('endTime');
        $setEndTime = $request->boolean('setEndTime');
        $showCountdown = $request->input('show_countdown', 0);
        $publicStatus = $request->input('public_status');
        $currencyCode = $request->input('currency_code');
        $marketLocation = $request->input('market_location');
        $pricingMode = $request->input('pricing_mode');
        $renderMode = $request->input('render_mode');
        $mockupType = $request->input('mockup_type');
        $defaultProductId = $request->input('default_product_id');
        $trackingCode = $request->input('tracking_code');
        $renderMode = !empty($renderMode) ? $renderMode : CampaignRenderModeEnum::NATURE;
        $products = $request->input('products');
        try {
            User::query()
                ->where('id', $userId)
                ->where('role', '!=', UserRoleEnum::CUSTOMER)
                ->update([
                    'pricing_mode' => $pricingMode,
                    'market_location' => $marketLocation,
                    'currency' => $currencyCode,
                ]);

            $newTemplateCamp = [
                'seller_id' => $userId,
                'auth_id' => $authId,
                'name' => $campaignTitle,
                'description' => $campaignDescription,
                'thumb_url' => $campaignThumbnail,
                'mockup_type' => $mockupType,
                'currency_code' => $currencyCode,
                'market_location' => $marketLocation,
                'old_price' => $oldPrice,
                'status' => CampaignStatusEnum::ACTIVE,
                'product_type' => ProductType::CAMPAIGN_TEMPLATE,
                'show_countdown' => $showCountdown,
                'end_time' => $setEndTime ? $endTime : null,
                'default_product_id' => $defaultProductId,
                'public_status' => CampaignPublicStatusEnum::getPublicStatus($publicStatus, $seller),
                'render_mode' => $renderMode,
                'tracking_code' => json_encode($trackingCode),
                'system_type' => ProductSystemTypeEnum::EXPRESS_2,
            ];

            $newTemplateCamp = Campaign::query()
                ->onSellerConnection($seller)
                ->create($newTemplateCamp);
            $newTemplateCampId = $newTemplateCamp->id;

            self::addCampaignToCollection($collections, $newTemplateCampId, $userId);
            self::addCampaignToStore($stores, $newTemplateCampId, $userId);

            foreach ($products as $product) {
                $templateProduct = Product::query()
                    ->firstWhere('id', $product['id']);

                if ($templateProduct === null) {
                    continue;
                }

                $thumbUrl = !empty($product['thumb_url']) ? $product['thumb_url'] : $templateProduct->thumb_url;

                $newProduct = [
                    'seller_id' => $userId,
                    'auth_id' => $authId,
                    'template_id' => $product['id'],
                    'campaign_id' => $newTemplateCampId,
                    'name' => $templateProduct->name,
                    'description' => $templateProduct->description,
                    'thumb_url' => $thumbUrl,
                    'options' => $product['options'],
                    'mockup_type' => $product['mockup_type'],
                    'default_option' => $product['default_option'],
                    'print_spaces' => $templateProduct->print_spaces,
                    'currency_code' => $currencyCode,
                    'market_location' => $marketLocation,
                    'base_cost' => $templateProduct->base_cost,
                    'price' => $product['price'],
                    'old_price' => $product['old_price'],
                    'status' => ProductStatus::ACTIVE,
                    'product_type' => ProductType::PRODUCT_TEMPLATE,
                    'sku' => $templateProduct->sku,
                    'priority' => (int) $product['priority'],
                    'pricing_mode' => $templateProduct->pricing_mode,
                    'public_status' => CampaignPublicStatusEnum::getPublicStatus($publicStatus, $seller),
                    'render_mode' => $renderMode,
                    'personalized' => $templateProduct->personalized,
                ];

                $newProduct = Product::query()
                    ->onSellerConnection($seller)
                    ->create($newProduct);

                if ($defaultProductId === $newProduct->template_id) {
                    $newTemplateCamp->default_product_id = $newProduct->id;
                    $newTemplateCamp->price = $newProduct->price;
                    $newTemplateCamp->save();
                }


                if (!empty($product['variants'])) {
                    $variants = $product['variants'];
                    $variants = array_map(function ($variant) use ($newProduct) {
                        $variant['product_id'] = $newProduct->id;
                        return $variant;
                    }, $variants);

                    ProductVariant::query()
                        ->onSellerConnection($seller)
                        ->insert($variants);
                }
            }
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($newTemplateCampId, sellerId: $seller->id);
            return $this->successResponse([
                'campaign_id' => $newTemplateCampId
            ]);
        } catch (Throwable $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateTemplateCampaign(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $seller = User::query()->find($userId);
        $authId = $user->getAuthorizedAccountId();

        $templateCampaignId = $request->input('template_campaign_id');
        $campaignTitle = $request->input('title');
        $campaignDescription = $request->input('description');
        $campaignThumbnail = $request->input('thumb_url');
        $oldPrice = $request->input('old_price');
        $collections = $request->input('collections');
        $stores = $request->input('storefronts');
        $endTime = $request->input('endTime');
        $setEndTime = $request->input('setEndTime');
        $showCountdown = $request->input('show_countdown');
        $publicStatus = $request->input('public_status');
        $currencyCode = $request->input('currency_code');
        $marketLocation = $request->input('market_location');
        $pricingMode = $request->input('pricing_mode');
        $renderMode = $request->input('render_mode');
        $mockupType = $request->input('mockup_type');
        $defaultProductId = $request->input('default_product_id');
        $trackingCode = $request->input('tracking_code');
        $deletedProducts = $request->input('deleted_products');
        $products = $request->input('products');
        $renderMode = !empty($renderMode) ? $renderMode : CampaignRenderModeEnum::NATURE;
        $query = ExpressCampaign::query()
            ->onSellerConnection($seller)
            ->where('id', $templateCampaignId);
        if (!$query->exists()) {
            return $this->errorResponse('Not found', 404);
        }
        try {
            User::query()
                ->where('id', $userId)
                ->where('role', '!=', UserRoleEnum::CUSTOMER)
                ->update([
                    'pricing_mode' => $pricingMode,
                    'market_location' => $marketLocation,
                    'currency' => $currencyCode,
                ]);

            $newTemplateCamp = [
                'seller_id' => $userId,
                'auth_id' => $authId,
                'name' => $campaignTitle,
                'description' => $campaignDescription,
                'thumb_url' => $campaignThumbnail,
                'mockup_type' => $mockupType,
                'currency_code' => $currencyCode,
                'market_location' => $marketLocation,
                'price' => '',
                'old_price' => $oldPrice,
                'product_type' => ProductType::CAMPAIGN_TEMPLATE,
                'show_countdown' => $showCountdown,
                'end_time' => $setEndTime ? $endTime : null,
                'default_product_id' => $defaultProductId,
                'public_status' => CampaignPublicStatusEnum::getPublicStatus($publicStatus, $seller),
                'render_mode' => $renderMode,
                'tracking_code' => json_encode($trackingCode),
                'temp_status' => TempStatusEnum::DEFAULT,
            ];

            $query->update($newTemplateCamp);
            $newTemplateCamp = $query->first();

            ProductCollection::query()
                ->where([
                    'product_id' => $templateCampaignId,
                    'seller_id' => $userId
                ])
                ->forceDelete();

            self::addCampaignToCollection($collections, $templateCampaignId, $userId);

            StoreProduct::query()
                ->where('product_id', $templateCampaignId)
                ->delete();

            self::addCampaignToStore($stores, $templateCampaignId, $userId);

            if (!empty($deletedProducts) && !empty($templateCampaignId)) {
                ExpressCampaign::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'product_type' => ProductType::PRODUCT_TEMPLATE,
                        'campaign_id' => $templateCampaignId,
                    ])
                    ->whereIn('template_id', $deletedProducts)
                    ->delete();
            }

            foreach ($products as $product) {
                $templateProduct = Product::query()
                    ->firstWhere('id', $product['id']);

                if ($templateProduct === null) {
                    continue;
                }

                $thumbUrl = !empty($product['thumb_url']) ? $product['thumb_url'] : $templateProduct->thumb_url;

                $newProduct = [
                    'seller_id' => $userId,
                    'auth_id' => $authId,
                    'template_id' => $product['id'],
                    'campaign_id' => $templateCampaignId,
                    'name' => $templateProduct->name,
                    'description' => $templateProduct->description,
                    'thumb_url' => $thumbUrl,
                    'options' => $product['options'],
                    'mockup_type' => $product['mockup_type'],
                    'default_option' => $product['default_option'],
                    'print_spaces' => $templateProduct->print_spaces,
                    'currency_code' => $currencyCode,
                    'market_location' => $marketLocation,
                    'base_cost' => $templateProduct->base_cost,
                    'price' => $product['price'],
                    'old_price' => $product['old_price'],
                    'status' => ProductStatus::ACTIVE,
                    'product_type' => ProductType::PRODUCT_TEMPLATE,
                    'sku' => $templateProduct->sku,
                    'priority' => (int) $product['priority'],
                    'pricing_mode' => $templateProduct->pricing_mode,
                    'public_status' => CampaignPublicStatusEnum::getPublicStatus($publicStatus, $seller),
                    'render_mode' => $renderMode,
                    'personalized' => $templateProduct->personalized,
                ];

                if (!empty($product['inserted_id'])) {
                    $productQuery = Product::query()
                        ->onSellerConnection($seller)
                        ->where('id', $product['inserted_id']);
                    if ($productQuery->exists()) {
                        $productQuery->update($newProduct);
                        $newProduct = $productQuery->first();
                    }
                    unset($productQuery);
                } else {
                    $newProduct = Product::query()
                        ->onSellerConnection($seller)
                        ->create($newProduct);
                }

                if ($defaultProductId === $newProduct->template_id) {
                    $newTemplateCamp->update([
                        'default_product_id' => $newProduct->id,
                        'price' => $newProduct->price,
                    ]);
//                    update sell price for all express campaign belong to template
//                    ExpressCampaign::query()
//                        ->where('template_id', $templateCampaignId)
//                        ->update([
//                            'price' => $newProduct->price
//                        ]);
                }

                if (!empty($product['variants'])) {
                    $variants = $product['variants'];
                    $variants = array_map(function ($variant) use ($newProduct) {
                        $variant['product_id'] = $newProduct->id;
                        return $variant;
                    }, $variants);

                    ProductVariant::query()
                        ->onSellerConnection($seller)
                        ->upsert($variants);
                }
            }
            UpdateExpressCampaignJob::dispatch($templateCampaignId, seller: $seller);

            return $this->successResponse([
                'campaign_id' => $templateCampaignId
            ]);
        } catch (Throwable $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateExpressCampaign(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $seller = User::query()->find($userId);
        $authId = $user->getAuthorizedAccountId();

        $expressCampId = $request->input('express_campaign_id');
        $campaignTitle = $request->input('name');
        $campaignDescription = $request->input('description');
        $collections = $request->input('collections');
        $stores = $request->input('storefronts');
        $endTime = $request->input('end_time');
        $publicStatus = $request->input('public_status', 'no');
        $showCountdown = $request->input('show_countdown');
        $listMarketplace = $request->input('list_marketplace');
        $trackingCode = $request->input('tracking_code');
        $designUrl = $request->input('design_url');

        $query = ExpressCampaign::query()
            ->onSellerConnection($seller)
            ->where('id', $expressCampId);
        if (!$query->exists()) {
            return $this->errorResponse('Not found', 404);
        }

        try {
            $newExpressCamp = [
                'seller_id' => $userId,
                'auth_id' => $authId,
                'name' => $campaignTitle,
                'description' => $campaignDescription,
                'price' => '',
                'show_countdown' => $showCountdown,
                'end_time' => $endTime,
                'public_status' => CampaignPublicStatusEnum::getPublicStatus($publicStatus, $seller),
                'tracking_code' => json_encode($trackingCode),
            ];

            $query->update($newExpressCamp);
            $newExpressCamp = $query->with('design')->first();

            // remove old collection
            ProductCollection::query()
                ->where([
                    'product_id' => $expressCampId,
                    'seller_id' => $userId
                ])
                ->forceDelete();

            self::addCampaignToCollection($collections, $expressCampId, $userId);

            StoreProduct::query()
                ->where('product_id', $expressCampId)
                ->delete();

            self::addCampaignToStore($stores, $expressCampId, $userId);

            if (!empty($designUrl) && $designUrl !== $newExpressCamp->design->file_url) {
                $fileName = pathinfo($designUrl, PATHINFO_BASENAME);
                $newArtWorkPath = 'p/' . $expressCampId . '/' . $fileName;
                $oldFile = File::query()
                    ->onSellerConnection($seller)
                    ->firstWhere('id', $newExpressCamp->design->id);
                $validateSize = validateFileUploadedSize($designUrl);
                if (!$validateSize['accept']) {
                    return $this->errorResponse($validateSize['message']);
                }
                if ($oldFile) {
                    $this->saveFileDesign($designUrl, $newArtWorkPath, $expressCampId, $oldFile->print_space, []);
                }

                $newExpressCamp->thumb_url = $newArtWorkPath;
            }

            $newExpressCamp->sync_status = 0;
            $newExpressCamp->save();

            return $this->successResponse([
                'campaign_id' => $expressCampId
            ]);
        } catch (Throwable $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }


    /**
     * Load info when bulk upload
     * @param $id
     * @return JsonResponse
     */
    public function templateInfo($id): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        $userId = $user->getUserId();
        $seller = User::query()->find($userId);
        $query = TemplateCampaign::query()
            ->onSellerConnection($seller)
            ->with(['products' => function ($query) use ($seller) {
                $query->onSellerConnection($seller);
                $query->where('product_type', ProductType::PRODUCT_TEMPLATE);
            }])
            ->with(['designs' => function ($query) use ($seller) {
                $selectFields = [
                    'id',
                    'product_id',
                    'campaign_id',
                    'print_space',
                ];
                $query->onSellerConnection($seller);
                $query->select($selectFields)
                    ->where([
                        'type' => FileTypeEnum::DESIGN,
                    ])->whereIn('option', [
                        FileRenderType::CUSTOM,
                        FileRenderType::PRINT
                    ]);
            }]);

        if ($user->isAdmin()) {
            $query->with(['seller']);
        } else {
            $query->where('seller_id', $userId);
        }

        $result = $query->firstWhere('id', $id);

        if ($result === null) {
            return $this->errorResponse();
        }

        $templateIds = $result->products->pluck('template_id')->toArray();
        $mockups = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'product_id',
                'file_url',
                'design_json',
                'option',
                'type_detail',
                'color_fillable',
                'print_space',
                'position'
            ])
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::EXPRESS,
                'status' => FileStatusEnum::ACTIVE,
            ])
            ->whereIn('product_id', $templateIds)
            ->orderBy('position')
            ->get();

        $result->processing_fee = getProcessingFee();
        $result->mockups = $mockups;

        return $this->successResponse($result);
    }

    /**
     * @param Request $request
     * @param mixed $resizedFileUrl Resized file url
     * @param mixed $campaignId Campaign ID
     * @return false|string File path
     */
    private function saveResizedFile($resizedFileUrl, $campaignId) {
        // prepare variable for s3 file storage
        $fileName = pathinfo($resizedFileUrl, PATHINFO_BASENAME);
        // save resized artwork
        $secretArtworkPath = 'p/' . $campaignId . '/' . $fileName;
        $fileSize = validateFileUploadedSize($resizedFileUrl);
        if ($fileSize && $fileSize['accept']) {
            Storage::disk(StorageDisksEnum::DEFAULT)->move($resizedFileUrl, $secretArtworkPath);
        } else {
            Log::error('File size is too large', [
                'type' => 'express_campaign',
                'file' => $resizedFileUrl,
                'campaign_id' => $campaignId,
            ]);
            return false;
        }
        return $secretArtworkPath;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        $user = currentUser();
        if(!$this->checkCanCreate($user)) {
            return $this->errorResponse($this->getErrorMessageLimitCreating($user));
        }
        $userId = $user->getUserId();
        $seller = User::query()->find($userId);
        $isSellName = !$user->isAdmin() && $user->getInfo()->isSellName();

        $campaignId = $request->post('campaign_id');
        // if campaign id is not set, return error
        if (!isset($campaignId)) {
            return $this->errorResponse('Campaign ID is required');
        }
        $designInfo = $request->post('design_info');
        $defaultPrintSpace = $request->post('default_print_space');

        $existCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select(['id', 'name', 'slug', 'thumb_url', 'status'])
            ->where(['slug' => $designInfo['slug'], 'seller_id' => $userId])
            ->first();

        // delete draft camp to avoid duplicate slug
        if ($existCampaign && $isSellName) {
            // don't create duplicate camp if seller sell name
            return $this->successResponse([
                'new_campaign' => $existCampaign,
                'exist' => true
            ]);
        }

        $name = $designInfo['name'];
        $slug = CampaignService::processBulkUploadSlug($designInfo['slug']);
        $fileUrl = $designInfo['file_url'];
        $resizedFileUrl = $designInfo['resized_file_url'];
        $collectionName = $designInfo['collection'];
        if ($isSellName && empty($collectionName)) {
            $collectionName = $name;
        }

        try {
            $newCampaign = $this->createExpressCampaign((int)$campaignId, $fileUrl, $name, $slug, $seller, $defaultPrintSpace, $resizedFileUrl);
            if (!is_array($newCampaign)) {
                throw new \RuntimeException($newCampaign);
            }

            if (!is_null($collectionName)) {
                // success
                $collection = CollectionSellerController::createCollection($collectionName, $userId);

                if ($collection
                    && CampaignController::addCampaignToCollection($newCampaign['id'], $userId, $collection['collection_id'])) {
                    $newCampaign['collection'] = $collection;
                }
            }

            // save log
            $ipAddress = $request->post('ip_address');
            $uploadedAt = $request->post('uploaded_at');
            try {
                if ($ipAddress && $uploadedAt) {
                    BulkUploadLog::create([
                        'seller_id' => $userId,
                        'parent_campaign_id' => $campaignId,
                        'campaign_id' => $newCampaign['id'],
                        'file_name' => $name,
                        'ip_address' => $ipAddress,
                        'uploaded_at' => $uploadedAt
                    ]);
                }
            } catch (Throwable $exception) {
                logException($exception);
            }
            ScanCampaignCopyright::dispatch($newCampaign['id'], sellerId: $userId);
            return $this->successResponse([
                'new_campaign' => $newCampaign
            ]);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }


    /**
     * Detail to edit
     * @param int $campaignId
     * @return Campaign|Builder|Model
     */
    public function templateDetail(int $campaignId)
    {
        $seller = currentUser()->getInfoAccess();
        $queryFields = [
            'id',
            'name as title',
            'description',
            'show_countdown',
            'end_time',
            'tracking_code',
            'public_status',
            'market_location',
            'currency_code',
            'pricing_mode',
            'pre_discounted_price',
            'old_price',
            'default_product_id'
        ];

        return ExpressCampaign::query()
            ->onSellerConnection($seller)
            ->select($queryFields)
            ->where('id', $campaignId)
            ->with([
                'collections' => fn ($query) => $query->where('product_collection.seller_id', $seller->id),
                'stores',
                'template_products'
            ])
            ->firstOrFail();
    }

    public function expressCampDetail(int $campaignId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $expressCampaign = ExpressCampaign::query()
            ->onSellerConnection($seller)
            ->where('id', $campaignId)
            ->with([
                'design',
                'collections' => fn ($q) => $q->where('product_collection.seller_id', $seller->id),
                'stores'])
            ->firstOrFail();
        $queryFields = [
            'id',
            'market_location',
            'currency_code',
            'pricing_mode',
            'pre_discounted_price',
            'old_price',
        ];

        $templateCampaign = ExpressCampaign::query()
            ->onSellerConnection($seller)
            ->select($queryFields)
            ->where('id', $expressCampaign->template_id)
            ->with(['template_products'])
            ->first();

        return $this->successResponse([
            'express_campaign' => $expressCampaign,
            'template' => $templateCampaign
        ]);
    }

    private function checkCanCreate($user): bool
    {
        return $user->getNumberCampaignCanCreate() > 0;
    }

    private function getErrorMessageLimitCreating($user): string
    {
        return 'You can only create '
            . $user->getInfo()->campaign_limit
            . ' campaigns in 24 hours. Contact support to increase the limit.';
    }

    /**
     * @param int $campaignId
     * @param string $artwork
     * @param string $name
     * @param string $slug
     * @param SenPrintsAuth $user
     * @param string $defaultPrintSpace
     * @return array|false
     */
    private function createExpressCampaign(int $campaignId, string $artwork, string $name, string $slug, User $seller, string $defaultPrintSpace = 'front', $resizedFileUrl = '')
    {
        // Get all data from campaign ID
        $query = ExpressCampaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ]);
        $targetCampaign = $query->with('defaultProduct')->firstOrFail();

        // Create object data for new campaign
        // https://laravel.com/docs/8.x/eloquent#replicating-models
        $data = $targetCampaign->replicate([
            'slug',
            'tm_status',
            'sync_status',
            'elastic_document_id',
            'created_at',
            'updated_at'
        ])->fill([
            'name' => $name,
            'slug' => $slug,
            'product_type' => ProductType::CAMPAIGN_EXPRESS,
            'status' => ProductStatus::ACTIVE,
            'template_id' => $campaignId,
            'default_option' => $defaultPrintSpace
        ])->toArray();
        $data['tracking_code'] = json_encode($data['tracking_code']);
        $newCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->create($data);

        $publicStatus = CampaignPublicStatusEnum::NO;

        if (in_array($targetCampaign->public_status, [
            CampaignPublicStatusEnum::YES,
            CampaignPublicStatusEnum::APPROVED
        ], true)) {
            $publicStatus = CampaignPublicStatusEnum::YES;
        }

        // auto approve if current user is trusted seller
        // temporarily disabled, see issue:
        // https://discord.com/channels/874562708392005672/1006415246530969653/1006415249420857354
//        if ($userInfo && $userInfo->status === UserStatusEnum::TRUSTED) {
//            $newCampaign->public_status = CampaignPublicStatusEnum::APPROVED;
//        } else {
//            $newCampaign->public_status = $publicStatus;
//        }
        $newCampaign->public_status = CampaignPublicStatusEnum::getPublicStatus($publicStatus, $seller);
        try {
            if (!$newCampaign->save()) {
                return false;
            }

            $newCampaignId = $newCampaign->id;

            CampaignService::cloneCollections($campaignId, $newCampaignId);
            CampaignService::cloneStores($campaignId, $newCampaignId);
            CampaignService::cloneUpsell($campaignId, $newCampaignId, $seller->id);

            // prepare variable for s3 file storage
            $fileName = pathinfo($artwork, PATHINFO_BASENAME);

            // Save resized file
            $resizedFilePath = $this->saveResizedFile($resizedFileUrl, $newCampaignId);
            if ($resizedFilePath === false) {
                Log::error('Failed to save resized file', [
                    'type' => 'express_campaign',
                    'file' => $resizedFileUrl,
                    'campaign_id' => $newCampaignId,
                ]);
                return false;
            }

            // save original artwork
            $newArtWorkPath = 'p/' . $newCampaignId . '/' . $fileName;
            $validateSize = validateFileUploadedSize($artwork);
            if (!$validateSize['accept']) {
                return false;
            }
            $saveFileData = $this->saveFileDesign($artwork, $newArtWorkPath, $newCampaignId, $defaultPrintSpace, [], $resizedFilePath);
            $newCampaign->thumb_url = $newArtWorkPath;

            if (!$newCampaign->save()) {
                CampaignService::removeFileByCampaignId($newCampaignId);
                CampaignService::removeUpsellByCampaignId($newCampaignId, $seller->id);
                return false;
            }
            SyncSlugJob::dispatchSync([$newCampaignId], $seller);

            $columns = ['id', 'name', 'slug', 'thumb_url', 'template_id'];
            exportColumns($newCampaign, $columns);
            $arrayCampaign = $newCampaign->toArray();
            $newArrayCampaign = [];

            foreach ($columns as $column) {
                $newArrayCampaign[$column] = $arrayCampaign[$column];
            }

            return array_merge($newArrayCampaign, $saveFileData);
        } catch (Throwable $e) {
            logToDiscord('Bulk Duplicate with File:' . $e->getMessage());
            $newCampaign->slug = null;
            $newCampaign->sync_status = 0;
            $newCampaign->is_deleted = 1;
            $newCampaign->deleted_at = now();
            $newCampaign->save();
            return $e->getMessage();
        }
    }

    public static function registerIdCloudMockup(string $fullPath, string $templateIds = '')
    {
        if (!str_starts_with($fullPath, 'http')) {
            $fullPath = imgUrl($fullPath, 'original');
        }

        $bearerToken = 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpcy5jbG91ZG1vY2t1cHMuY29tL2F1dGgvY3VzdG9tZXIvbG9naW4iLCJpYXQiOjE2NTA1MTEzNzIsIm5iZiI6MTY1MDUxMTM3MiwianRpIjoiUXQwZDd2VTVnUHFEOXBzVCIsInN1YiI6IjMxOCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjciLCJzZXJ2aWNlX25hbWUiOiJjdXN0b21lciIsImd1YXJkX25hbWUiOiJ1c2VyIn0.QPGZaKTeGbsKO3W9zZ1jyjiK7QSINDsFHrDFKtCDkXc';

        $response = Http::withoutVerifying()
            ->withToken($bearerToken)
            ->withHeaders([
                'Content-Type' => 'application/json'
            ])
            ->post(CloudMockupApis::REGISTER_FILE, [
                'remoteFileUrl' => $fullPath,
                'assignTemplates' => $templateIds
            ])
            ->object();

        if ((int)$response->error === 0) {
            return $response->data->code;
        }

        return null;
    }

    /**
     * @param string $tmpPath
     * @param string $path
     * @param int $campaignId
     * @param string $printSpace
     * @param array $mockupIds
     * @param string $resizedFilePath
     * @return array
     */
    private function saveFileDesign(string $tmpPath, string $path, int $campaignId, string $printSpace = 'front', array $mockupIds = [], $resizedFilePath = ''): array
    {
        saveTempFileAws($tmpPath, $path);
        $fullPath = s3Url($path);
        $designJson = [
            'resized_file_path' => $resizedFilePath,
        ];

        $seller = currentUser()->getInfoAccess();
        $data = [
            'seller_id' => $seller->id,
            'campaign_id' => $campaignId,
            'file_url' => $path,
            'file_url_2' => $fullPath,
            'type' => FileTypeEnum::DESIGN,
            'option' => 'print',
            'print_space' => $printSpace,
            'design_json' => json_encode($designJson),
        ];


        File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'type' => FileTypeEnum::DESIGN
            ])
            ->delete();

        $designId = File::query()
            ->onSellerConnection($seller)
            ->insertGetId($data);

        return [
            // 'cloud_mockup_id' => $cloudMockupId,
            'design_id' => $designId
        ];
    }

    /**
     * @throws \Exception
     */
    public function listTemplateProducts(): JsonResponse
    {
        $tag = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
        return cacheAlt()->tags([$tag])->remember(
            CacheKeys::SYSTEM_EXPRESS_PRODUCT_TEMPLATE,
            CacheKeys::CACHE_24H,
            function () {
                $categories = Category::query()
                    ->select('id', 'name', 'full_name', 'slug')
                    ->where('show_dashboard', 1) // only load category to show on dashboard
                    ->get();

                if (count($categories) === 0) {
                    return $this->errorResponse();
                }

                $topTemplateIds = topTemplateIds();
                $templatesCount = count($topTemplateIds);

                $categories = $categories->map(function ($category) use ($topTemplateIds, &$templatesCount) {
                    $product_ids = ProductCategory::query()->where('category_id', $category->id)->get()->pluck('product_id')->toArray();
                    $category->products = collect([]);
                    if (!empty($product_ids)) {
                        $category->products = Product::query()->select([
                            'id',
                            'name',
                            'sku',
                            'thumb_url',
                            'base_cost',
                            'base_costs',
                            'price as suggested_price',
                            'options',
                            'print_spaces',
                            'extra_print_cost',
                            'priority',
                            'mockup_type',
                            'attributes',
                            'pricing_mode',
                            'full_printed',
                            'status',
                            'market_location',
                        ])
                            ->where([
                                'product_type' => ProductType::TEMPLATE,
                                'full_printed' => ProductPrintType::PRINT_2D
                            ])
                            ->where('status', ProductStatus::ACTIVE)
                            ->whereNotIn('system_type', [ProductSystemTypeEnum::FULFILL, ProductSystemTypeEnum::FULFILL_FBA])
                            ->whereIn('id', $product_ids)
                            ->orderBy('priority')
                            ->orderBy('name')
                            ->get();
                    }
                    $sortedProducts = $category->products->sortBy(function ($template) use ($topTemplateIds, $templatesCount) {
                        $key = array_search($template->id, $topTemplateIds);

                        if ($key === false) {
                            return $templatesCount++;
                        }

                        return $key;
                    });

                    unset($category->products);
                    $sortedProducts->map(function ($product) {
                        $options = json_decode($product->options);
                        $colors = [];

                        // check if we have color option
                        if (isset($options->color) && count($options->color) > 0) {
                            foreach ($options->color as $color) {
                                $colors[] = [
                                    'name' => $color,
                                    'hex_code' => color2hex($color)
                                ];
                            }
                        }

                        $product->colors = $colors;
                        $product->mockups = [];
                        $product->selected = false;

                        return $product;
                    });


                    $category['products'] = $sortedProducts->values()->all();

                    return $category;
                });

                return $this->successResponse($categories);
            });
    }
}
