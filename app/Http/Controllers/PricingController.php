<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CurrencyEnum;
use App\Enums\LocationTypeEnum;
use App\Enums\MarketLocationEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PricingModeEnum;
use App\Enums\ProductStatus;
use App\Models\OrderProduct;
use App\Models\Pricing;
use App\Models\Template;
use App\Models\User;
use App\Models\Product;
use App\Services\UserService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PricingController extends Controller
{
    use ApiResponse;

    /* Seller Apis Start */
    public function index(): JsonResponse
    {
        $sellerId = currentUser()->getUserId();
        try {
            $templates = $this->templatesOfSeller($sellerId);
            if ($templates->isEmpty()) {
                return $this->successResponse([
                    'currency_code' => CurrencyEnum::USD,
                    'location_code' => LocationTypeEnum::WORLDWIDE,
                    'products' => []
                ]);
            }
            $pricing = UserService::getPricingBySellerId($sellerId, false, false);
            $sortedTemplatesStatic = self::sortedTemplatesStatic();
            $currencies = SystemConfigController::systemCurrencies();
            $topTemplateIds = topSellerTemplateIds($sellerId);
            $templatesCount = $templates->count();

            $templates = $templates->sortBy(function ($template) use ($topTemplateIds, $templatesCount) {
                $key = array_search($template->id, $topTemplateIds);

                if ($key === false) {
                    return $templatesCount++;
                }

                return $key;
            })->map(function ($template) use ($sortedTemplatesStatic, $pricing, $currencies) {
                $static = $sortedTemplatesStatic->firstWhere('template_id', $template->id);
                $popularPrice = !empty($static) ? $static['popular_price'] : 0;
                $lowestPrice = !empty($static) ? $static['lowest_price'] : 0;
                $highestPrice = !empty($static) ? $static['highest_price'] : 0;
                $savedPricing = $pricing->where('product_id', $template->id)->sortBy('price');
                $variants = [];

                if (is_string($template->options)) {
                    $template->options = json_decode($template->options, true, 512, JSON_THROW_ON_ERROR) ?? [];
                }

                if ($template->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                    if ($template->variants->isEmpty()) {
                        return null;
                    }

                    $colorPosition = getPositionOfKeyInOptions('color', $template->options);
                    $template->options = Arr::except($template->options, 'color');

                    $variants = $template->variants
                        ->map(function ($variant) use ($savedPricing, $colorPosition) {
                            $variantSavedPricing = $savedPricing->firstWhere('variant_key', $variant->variant_key);
                            $parsedVariant = explode('-', $variant->variant_key);

                            if ($colorPosition !== false) {
                                unset($parsedVariant[$colorPosition]);
                            }

                            return [
                                'variant_key' => $variant->variant_key,
                                'parsed_variant' => array_values($parsedVariant),
                                'base_cost' => $variant->base_cost,
                                'suggest_price' => $variant->suggest_price,
                                'price' => $variantSavedPricing->price ?? $variant->suggest_price
                            ];
                        })
                        ->values();
                }

                $firstSavedPricing = $savedPricing->first();
                $currencyCode = $firstSavedPricing->currency_code ?? ($pricing->first()->currency_code ?? CurrencyEnum::USD);
                $currency = $currencies?->firstWhere('code', $currencyCode);
                $rate = $currency->rate ?? 1;
                $locationCode = $firstSavedPricing->location_code ?? '*';
                $status = $firstSavedPricing->status ?? false;

                return [
                    'id' => $template->id,
                    'name' => $template->name,
                    'thumb_url' => $template->thumb_url,
                    'pricing_mode' => $template->pricing_mode,
                    'options' => array_keys($template->options),
                    'variants' => $variants,
                    'price' => $firstSavedPricing->price ?? ($template->suggest_price * $rate),
                    'compare_price' => !empty($firstSavedPricing) && !empty($firstSavedPricing->compare_price) ? $firstSavedPricing->compare_price : 0,
                    'test_price_percent' => !empty($firstSavedPricing) && !empty($firstSavedPricing->test_price_percent) ? $firstSavedPricing->test_price_percent : 0,
                    'popular_price' => $popularPrice,
                    'lowest_price' => $lowestPrice,
                    'highest_price' => $highestPrice,
                    'suggest_price' => $template->suggest_price,
                    'base_cost' => $template->base_cost,
                    'base_costs' => json_decode($template->base_costs),
                    'extra_print_cost' => $template->extra_print_cost,
                    'currency_code' => $currencyCode,
                    'location_code' => $locationCode,
                    'status' => $status
                ];
            })->filter()->values();

            $firstTemplate = $templates->first();
            $currencyCode = !empty($firstTemplate) ? $firstTemplate['currency_code'] : CurrencyEnum::USD;
            $currency = $currencies?->firstWhere('code', $currencyCode);
            $locationCode = !empty($firstTemplate) ? $firstTemplate['location_code'] : '*';
            $showComparePrice = !empty($firstTemplate) && !empty($firstTemplate['compare_price']);

            return $this->successResponse([
                'currency_code' => $currencyCode,
                'location_code' => $locationCode,
                'rate' => $currency->rate ?? 1,
                'show_compare_price' => $showComparePrice,
                'products' => $templates
            ]);
        } catch (\Throwable $e) {
            logException($e, 'PriceController@index');
            return $this->errorResponse();
        }
    }

    public function save(Request $request)
    {
        $sellerId = currentUser()->getUserId();
        $this->dataValidation($request, $sellerId);
        $showComparePrice = $request->input('show_compare_price', false);
        $currencyCode = $request->input('currency_code');
        $products = $request->input('products');

        DB::beginTransaction();
        try {
            $insertRecords = [];

            foreach ($products as $product) {
                $general = [
                    'seller_id' => $sellerId,
                    'product_id' => $product['id'],
                    'currency_code' => $currencyCode,
                    'status' => $product['status']
                ];

                $testPricePercent = !empty($product['test_price_percent']) ? $product['test_price_percent'] : 0;

                if (!empty($product['variants'])) {
                    foreach ($product['variants'] as $variant) {
                        $insertRecords[] = array_merge([
                            'variant_key' => $variant['variant_key'],
                            'price' => (double)$variant['price'],
                            'compare_price' => $showComparePrice ? calculateCompareAtPrice($variant['price']) : null,
                            'test_price_percent' => $testPricePercent,
                            'test_price' => $this->calcTestPrice((double)$variant['price'], $testPricePercent)
                        ], $general);
                    }
                } else {
                    $insertRecords[] = array_merge([
                        'variant_key' => null,
                        'price' => (double)$product['price'],
                        'compare_price' => $showComparePrice ? calculateCompareAtPrice($product['price']) : null,
                        'test_price_percent' => $testPricePercent,
                        'test_price' => $this->calcTestPrice((double)$product['price'], $testPricePercent)
                    ], $general);
                }
            }

            if (!empty($insertRecords)) {
                $now = now();
                $pricing = UserService::getPricingBySellerId($sellerId, false);
                $insertRecords = array_map(function ($record) use ($pricing, $now) {
                    $record['test_price_applied_at'] = null;

                    if ($record['test_price_percent'] != 0) {
                        $savedPricing = $pricing->filter(function ($sP) use ($record) {
                            if (!empty($record['variant_key'])) {
                                return $sP->variant_key == $record['variant_key'] && $sP->product_id == $record['product_id'];
                            }

                            return $sP->product_id == $record['product_id'];
                        })->first();

                        $testPriceApply = !$savedPricing || ($savedPricing->price != $record['price'] || $savedPricing->test_price_percent != $record['test_price_percent']);
                        $record['test_price_applied_at'] = $testPriceApply ? $now : ($savedPricing->test_price_applied_at ?? $now);
                    }

                    return $record;
                }, $insertRecords);

                Pricing::where('seller_id', $sellerId)->delete();
                Pricing::insert($insertRecords);
                DB::commit();
            }

            // synclear cache on storefront
            syncClearCache([CacheKeys::SELLER_PRICING_PREFIX . $sellerId . '_active'], CacheKeys::CACHE_TYPE_ALTERNATIVE);
            cacheAlt()->forget(CacheKeys::SELLER_PRICING_PREFIX . $sellerId . '_all');
            cacheAlt()->forget(CacheKeys::TEST_PRICE_ORDER_PRODUCT . $sellerId);
            clearStoreCache();
            return $this->successResponse();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse();
        }
    }

    /**
     * @param $price
     * @param $percent
     * @return false|float|int
     */
    protected function calcTestPrice($price, $percent)
    {
        if (!empty($percent)) {
            $testPriceBeforeRound = $price / 100 * $percent;

            if ($percent > 0) {
                return ceil($testPriceBeforeRound);
            }

            if ($percent < 0) {
                return floor($testPriceBeforeRound);
            }
        }

        return 0;
    }

    protected function templatesOfSeller($sellerId, $topTemplateOnly = true)
    {
        $topTemplateIds = [];
        if ($topTemplateOnly) {
            $topTemplateIds = topSellerTemplateIds($sellerId, false);
        }
        $templateFieldSelected = [
            'product.id',
            'product.name',
            'product.thumb_url',
            'product.options',
            'product.price as suggest_price',
            'product.base_cost',
            'product.base_costs',
            'product.extra_print_cost',
            'product.pricing_mode'
        ];
        $templates = Template::query()
            ->select($templateFieldSelected)
            ->when(!empty($topTemplateIds), function ($query) use ($topTemplateIds) {
                $query->whereIn('id', $topTemplateIds);
            })
            ->where('status', ProductStatus::ACTIVE)
            ->get();
        if ($templates->isEmpty() || $templates->count() < 5) {
            $seller = User::query()->find($sellerId);
            $templateIds = Product::query()
                ->onSellerConnection($seller)
                ->selectRaw('DISTINCT template_id')
                ->where('seller_id', $sellerId)
                ->where('status', ProductStatus::ACTIVE)
                ->get()
                ->pluck('template_id')
                ->toArray();
            $templatesDiff = array_diff(array_unique($templateIds), $topTemplateIds);
            $moreTemplates = Template::query()
                ->select($templateFieldSelected)
                ->when(!empty($templatesDiff), function ($query) use ($templatesDiff) {
                    $query->whereIn('id', $templatesDiff);
                })
                ->where('status', ProductStatus::ACTIVE)
                ->get();
            $templates = $templates->merge($moreTemplates);
        }

        if ($templates->isNotEmpty()) {
            $customPriceTemplates = $templates->where('pricing_mode', PricingModeEnum::CUSTOM_PRICE);

            if ($customPriceTemplates->isNotEmpty()) {
                $totalOption = [];
                $customPriceTemplateIds = [];

                $customPriceTemplates->each(function ($template) use (&$totalOption, &$customPriceTemplateIds) {
                    $template->options = json_decode($template->options, true) ?? [];
                    $totalOption = array_merge($totalOption, generateVariantKeysByProductOptions($template->options));
                    $customPriceTemplateIds[] = $template->id;
                });

                $totalOption = array_unique($totalOption);

                $templates->load(['variants' => fn($variants) => $variants->select([
                    'product_id', 'variant_key', 'base_cost', 'price as suggest_price', 'out_of_stock'
                ])
                    ->whereIn('variant_key', $totalOption)
                    ->whereIn('product_id', $customPriceTemplateIds)
                    ->where('location_code', '*')
                ]);

                $customPriceTemplates->map(function ($template) {
                    $colorPosition = getPositionOfKeyInOptions('color', $template->options);

                    if ($colorPosition !== false) {
                        $variants = $template->variants->unique(function ($variant) use ($colorPosition) {
                            $explode = explode('-', $variant->variant_key);

                            if (!empty($explode[$colorPosition]) && $explode[$colorPosition] != 'white') {
                                $explode[$colorPosition] = 'white';
                                $variant->variant_key = implode('-', $explode);
                            }

                            return $variant->product_id . '_' . $variant->variant_key;
                        })->values();

                        $template->setRelation('variants', $variants);
                    }

                    return $template;
                });
            }
        }

        return $templates;
    }

    protected function dataValidation(Request $request, $sellerId): void
    {
        $marketLocation = $request->input('market_location');
        $currency = SystemConfigController::systemCurrencies()->firstWhere('code', $request->input('currency_code'));
        $rate = !empty($currency) ? $currency->rate : 1;

        $templates = $this->templatesOfSeller($sellerId, false)
            ->map(function ($template) use ($marketLocation, $rate) {
                $baseCosts = json_decode($template->base_costs, true);
                $baseCost = !empty($baseCosts[$marketLocation]) ? $baseCosts[$marketLocation] : $template->base_cost;
                $template->min_price = (int)floor(min($baseCost * 1.5, $template->suggest_price) * $rate);
                $template->max_price = (int)floor(max($baseCost * 1.5 + $template->extra_print_cost, $template->suggest_price + $template->extra_print_cost) * $rate) * 3;

                if ($template->variants->isNotEmpty()) {
                    $template->variants->map(function ($variant) use ($template, $rate) {
                        $variant->min_price = (int)floor(min($variant->base_cost * 1.5, $variant->suggest_price) * $rate);
                        $variant->max_price = (int)floor(max($variant->base_cost * 1.5 + $template->extra_print_cost, $variant->suggest_price + $template->extra_print_cost) * $rate) * 3;

                        return $variant;
                    });
                }

                return $template;
            });

        $templateIds = $templates->pluck('id')->toArray();
        $customPriceTemplateIds = $templates->where('pricing_mode', PricingModeEnum::CUSTOM_PRICE)->pluck('id')->toArray();
        $request->json()->set('templates', $templateIds);

        Validator::validate($request->all(), [
            'templates' => 'required',
            'market_location' => Rule::in(array_merge(MarketLocationEnum::getValues(), ['*'])),
            'currency_code' => [
                'required',
                Rule::in(CurrencyEnum::getValues())
            ],
            'show_compare_price' => 'required|boolean',
            'products' => 'required|array',
            'products.*.id' => [
                'required',
                Rule::in($templateIds)
            ],
            'products.*.price' => [
                'required',
                'numeric',
                function ($attribute, $value, $fail) use ($request, $templates) {
                    $templateId = $request->input(substr($attribute, 0, strpos($attribute, 'price')) . 'id');
                    $template = $templates->firstWhere('id', $templateId);

                    $testPricePercent = $request->input(substr($attribute, 0, strpos($attribute, 'price')) . 'test_price_percent', 0);
                    $price = $value + $this->calcTestPrice($value, $testPricePercent);

                    if ($template && ($price < $template->min_price || $price > $template->max_price)) {
                        $fail('The :attribute must be between ' . $template->min_price . ' and ' . $template->max_price . '.');
                    }
                }
            ],
            'products.*.status' => 'required|boolean',
            'products.*.test_price_percent' => [
                'integer',
                Rule::in(Pricing::testPricePercentList()->pluck('percent')->toArray())
            ],
            'products.*.variants' => function ($attribute, $value, $fail) use ($request, $customPriceTemplateIds) {
                $templateId = $request->input(substr($attribute, 0, strpos($attribute, 'variants')) . 'id');

                if (!empty($value) && !in_array($templateId, $customPriceTemplateIds)) {
                    $fail('The :attribute was invalid.');
                }

                if (empty($value) && in_array($templateId, $customPriceTemplateIds)) {
                    $fail('The :attribute field is required.');
                }
            },
            'products.*.variants.*.price' => [
                'required',
                'numeric',
                function ($attribute, $value, $fail) use ($request, $templates) {
                    $templateId = $request->input(substr($attribute, 0, strpos($attribute, 'variants')) . 'id');
                    $template = $templates->firstWhere('id', $templateId);

                    if ($template) {
                        $variantKey = $request->input(substr($attribute, 0, strpos($attribute, 'price')) . 'variant_key');
                        $variant = $template->variants->firstWhere('variant_key', $variantKey);

                        $testPricePercent = $request->input(substr($attribute, 0, strpos($attribute, 'variants')) . 'test_price_percent', 0);
                        $price = $value + $this->calcTestPrice($value, $testPricePercent);

                        if ($variant && ($price < $variant->min_price || $price > $variant->max_price)) {
                            $fail('The :attribute must be between ' . $variant->min_price . ' and ' . $variant->max_price . '.');
                        }
                    }
                }
            ],
            'products.*.variants.*.variant_key' => [
                'required',
                function ($attribute, $value, $fail) use ($request, $templates, $customPriceTemplateIds) {
                    $templateId = $request->input(substr($attribute, 0, strpos($attribute, 'variants')) . 'id');

                    if (in_array($templateId, $customPriceTemplateIds) && $template = $templates->firstWhere('id', $templateId)) {
                        $variantKeys = $template->variants->pluck('variant_key')->toArray();

                        if (!in_array($value, $variantKeys)) {
                            $fail('The :attribute was invalid.');
                        }
                    }
                }
            ]
        ]);
    }


    /* Seller Apis End */

    public static function getPricingBySellerId($sellerId, $status = true)
    {
        $cacheKey = CacheKeys::SELLER_PRICING_PREFIX . $sellerId . '_';
        $cacheKey .= $status ? 'active' : 'all';

        return cacheAlt()->remember($cacheKey, CacheKeys::CACHE_24H, function () use ($sellerId, $status) {
            $pricings = Pricing::query()
                ->where('seller_id', $sellerId)
                ->when($status, function ($query, $status) {
                    $query->where('status', $status);
                })
                ->get();
            if ($pricings->isNotEmpty()) {
                foreach ($pricings as $idx => $pricing) {
                    $template = Template::findAndCacheByKey($pricing->product_id);
                    if (empty($template)) {
                        $pricings->forget($idx);
                    }
                }
            }
            return $pricings;
        });
    }

    public static function getPricingListBySellerIds(array $sellerIds): Collection
    {
        $pricing = collect();

        if (!empty($sellerIds)) {
            foreach ($sellerIds as $sellerId) {
                $pricing = $pricing->merge(self::getPricingBySellerId($sellerId));
            }
        }

        return $pricing;
    }

    public function testPricePercentList(): JsonResponse
    {
        return $this->successResponse(Pricing::testPricePercentList());
    }

    public function analytics(): JsonResponse
    {
        $sellerId = currentUser()->getUserId();
        $templates = Template::query()
            ->select([
                'product.id',
                'product.name',
                'product.thumb_url',
                'product.options',
                'product.pricing_mode'
            ])
            ->whereHas('pricings', function ($query) use ($sellerId) {
                $query->where('seller_id', $sellerId)
                    ->where('test_price_percent', '!=', 0);
            })
            ->where('status', ProductStatus::ACTIVE)
            ->get();

        if ($templates->isNotEmpty()) {
            $pricing = self::getPricingBySellerId($sellerId, false);
            cacheAlt()->forget(CacheKeys::TEST_PRICE_ORDER_PRODUCT . $sellerId);
            $orderProducts = cacheAlt()->remember(CacheKeys::TEST_PRICE_ORDER_PRODUCT . $sellerId, CacheKeys::CACHE_4H, function () use ($sellerId) {
                return OrderProduct::query()
                    ->select([
                        'order_product.id',
                        'order_product.order_id',
                        'order_product.seller_id',
                        'order_product.template_id',
                        'order_product.price',
                        'order_product.quantity',
                        'order_product.adjust_test_price',
                        'order_product.options',
                        'order_product.seller_profit',
                        'order.paid_at',
                        'order.type'
                    ])
                    ->leftJoin('order', 'order_product.order_id', 'order.id')
                    ->whereNotNull('order.paid_at')
                    ->whereNotNull('order_product.adjust_test_price')
                    ->whereHas('pricing', function ($query) use ($sellerId) {
                        $query->where('seller_id', $sellerId)
                            ->whereRaw('`order`.`paid_at` >= `pricing`.`test_price_applied_at`');
                    })
                    ->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                    ->where('order.seller_id', $sellerId)
                    ->where('order_product.seller_id', $sellerId)
                    ->orderByDesc('order.paid_at')
                    ->get();
            });

            $firstPricing = $pricing->first();
            $currencyCode = !empty($firstPricing) ? $firstPricing->currency_code : CurrencyEnum::USD;
            $currency = SystemConfigController::findOrDefaultCurrency($currencyCode);

            $templates = $templates->map(function ($template) use ($orderProducts, $pricing, $currency) {
                $options = json_decode($template->options, true) ?? [];
                $colorPosition = getPositionOfKeyInOptions('color', $options);

                $template->setRelation('pricings', $pricing->filter(function ($sP) use ($template) {
                    return $sP->product_id == $template->id && $sP->test_price_percent != 0;
                })->values()
                    ->map(function ($pricing) use ($orderProducts, $template, $colorPosition, $currency) {
                        $pricing->sell_price_order_item_count = 0;
                        $pricing->test_price_order_item_count = 0;

                        $pricing->sell_price_profit = 0;
                        $pricing->test_price_profit = 0;

                        $variantParse = explode('-', str_replace('_', ' ', $pricing->variant_key));
                        unset($variantParse[$colorPosition]);
                        $pricing->varaint_parse = array_values($variantParse);

                        foreach ($orderProducts as $orderProduct) {
                            if ($orderProduct->template_id === $pricing->product_id) {
                                if ($template->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                                    $options = json_decode($orderProduct->options, true);

                                    if (!empty($options['color'])) {
                                        $options['color'] = 'white';
                                    }

                                    $variantKey = getVariantKey($options);

                                    if ($pricing->variant_key != $variantKey) {
                                        continue;
                                    }
                                }

                                if ($orderProduct->adjust_test_price == 0) {
                                    $pricing->sell_price_order_item_count += $orderProduct->quantity;
                                    $pricing->sell_price_profit += $orderProduct->seller_profit;
                                } else {
                                    $pricing->test_price_order_item_count += $orderProduct->quantity;
                                    $pricing->test_price_profit += $orderProduct->seller_profit;
                                }
                            }
                        }

                        $pricing->test_order_item_count = $pricing->sell_price_order_item_count + $pricing->test_price_order_item_count;
                        $pricing->sell_price_profit *= $currency->rate;
                        $pricing->test_price_profit *= $currency->rate;
                        $pricing->test_profit = $pricing->sell_price_profit + $pricing->test_price_profit;

                        return $pricing;
                    }));

                return $template;
            });

            return $this->successResponse([
                'currency_code' => $currencyCode,
                'locale' => $currency->locale,
                'rate' => $currency->rate,
                'products' => $templates
            ]);
        }

        return $this->successResponse();
    }

    protected static function sortedTemplatesStatic()
    {
        return cacheAlt()->remember(CacheKeys::PRICING_STATS, CacheKeys::CACHE_24H, function () {
            return OrderProduct::query()
                ->select('template_id', 'price')
                ->whereHas('order', function ($query) {
                    $query->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                        ->whereDate('order.paid_at', '>=', today()->subMonth());
                })
                ->get()
                ->groupBy('template_id')
                ->sortByDesc(fn($template) => $template->count())
                ->map(fn($template, $templateId) => [
                    'template_id' => $templateId,
                    'popular_price' => $template->avg('price'),
                    'lowest_price' => $template->min('price'),
                    'highest_price' => $template->max('price')
                ]);
        });
    }
}
