<?php

namespace App\Http\Controllers\Seller;

use App\Enums\StoreHeadTagAllowPositionsEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Store\HeadTagStoreRequest;
use App\Models\StoreHeadTag;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class StoreHeadTagController extends Controller
{
    use ApiResponse;

    public function index($storeId): JsonResponse
    {
        $tags = StoreHeadTag::query()
            ->where([
                'store_id' => $storeId,
                'seller_id' => currentUser()->getUserId()
            ])
            ->paginate();

        return $this->successResponse($tags);
    }

    public function store(HeadTagStoreRequest $request, $storeId): JsonResponse
    {
        $path = $request->post('path');

        $tag = StoreHeadTag::query()
            ->create([
                'store_id' => $storeId,
                'seller_id' => currentUser()->getUserId(),
                'name' => $request->post('name'),
                'tag' => $request->post('tag'),
                'code' => $request->post('code'),
                'enabled' => $request->boolean('enabled', true),
                'position' => $request->post('position', StoreHeadTagAllowPositionsEnum::HEAD),
                'priority' => $request->post('priority'),
                'additional_properties' => json_encode($request->post('additional_properties')),
                'path' => $path,
            ]);

        return $tag->wasRecentlyCreated
            ? $this->successResponse()
            : $this->errorResponse();
    }

    public function show($storeId, $tagId): JsonResponse
    {
        $tag = StoreHeadTag::query()
            ->select(['name', 'tag', 'code', 'additional_properties', 'path'])
            ->where([
                'id' => $tagId,
                'store_id' => $storeId,
                'seller_id' => currentUser()->getUserId(),
            ])
            ->first();

        if ($tag) {
            $tag->additional_properties = json_decode($tag->additional_properties, true);
        }

        return $tag
            ? $this->successResponse($tag)
            : $this->errorResponse();
    }

    public function update(HeadTagStoreRequest $request, $storeId, $tagId): JsonResponse
    {
        $path = $request->post('path');

        $updated = StoreHeadTag::query()
            ->where([
                'id' => $tagId,
                'store_id' => $storeId,
                'seller_id' => currentUser()->getUserId(),
            ])
            ->update([
                'name' => $request->post('name'),
                'tag' => $request->post('tag'),
                'code' => $request->post('code'),
                'enabled' => $request->boolean('enabled', true),
                'position' => $request->post('position', StoreHeadTagAllowPositionsEnum::HEAD),
                'priority' => $request->post('priority'),
                'additional_properties' => json_encode($request->post('additional_properties')),
                'path' => $path,
            ]);

        return $updated
            ? $this->successResponse()
            : $this->errorResponse();
    }

    public function destroy($storeId, $tagId): JsonResponse
    {
        $deleted = StoreHeadTag::query()
            ->where([
                'id' => $tagId,
                'store_id' => $storeId,
                'seller_id' => currentUser()->getUserId(),
            ])
            ->delete();

        return $deleted
            ? $this->successResponse()
            : $this->errorResponse();
    }

    public function toggle(Request $request, $storeId, $tagId): JsonResponse
    {
        $enabled = $request->boolean('enabled', true);
        $updated = StoreHeadTag::query()
            ->where([
                'id' => $tagId,
                'store_id' => $storeId,
                'seller_id' => currentUser()->getUserId(),
            ])
            ->update(['enabled' => $enabled]);

        return $updated
            ? $this->successResponse()
            : $this->errorResponse();
    }
}
