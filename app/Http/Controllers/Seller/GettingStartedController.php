<?php

namespace App\Http\Controllers\Seller;

use App\Enums\OrderStatus;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class GettingStartedController extends Controller
{
    use ApiResponse;

    public function __invoke(): JsonResponse
    {
        $orderCount = Order::where('seller_id', currentUser()->getUserId())
            ->whereIn('status', [
                OrderStatus::PROCESSING,
                OrderStatus::COMPLETED
            ])
            ->count();

        return $this->successResponse(['order_count' => $orderCount]);
    }
}
