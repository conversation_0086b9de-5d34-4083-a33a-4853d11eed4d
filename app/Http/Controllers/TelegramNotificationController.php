<?php

namespace App\Http\Controllers;

use App\Models\TelegramNotification;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TelegramNotificationController extends Controller
{
    use ApiResponse;

    public function activate(): JsonResponse
    {
        $token = md5(Str::random());

        $result = TelegramNotification::query()->create([
            'seller_id' => currentUser()->getUserId(),
            'token' => $token
        ]);

        return $result->wasRecentlyCreated
            ? $this->successResponse($token)
            : $this->errorResponse();
    }

    public function deactivate(): JsonResponse
    {
        $deleted = TelegramNotification::query()
            ->where('seller_id', currentUser()->getUserId())
            ->delete();

        return $deleted ? $this->successResponse() : $this->errorResponse();
    }

    public function update(Request $request): JsonResponse
    {
        $updated = TelegramNotification::query()
            ->where('seller_id', currentUser()->getUserId())
            ->update(['enable' => (bool)$request->post('enable')]);

        return $updated ? $this->successResponse() : $this->errorResponse();
    }

    public function checkState(): JsonResponse
    {
        $result = TelegramNotification::query()
            ->select('token', 'enable')
            ->firstWhere('seller_id', currentUser()->getUserId());

        return $result ? $this->successResponse($result) : $this->errorResponse();
    }
}
