<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Models\SystemLocation;
use App\Traits\ApiResponse;

class SystemLocationController extends Controller
{
    use ApiResponse;

    private $cacheTime = 60 * 60 * 24 * 30;

    public function index() //todo: remove this (duplicate with System Config)
    {
        return cacheAlt()->remember(
            CacheKeys::COUNTRIES,
            $this->cacheTime,
            function () {
                return SystemLocation::all();
            }
        );
    }
}
