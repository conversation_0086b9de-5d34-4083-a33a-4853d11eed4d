<?php

namespace App\Http\Controllers;

use App\Enums\PaymentAccountStatus;
use App\Enums\PaymentAccountTypeEnum;
use App\Enums\PingPongX\PingPongXWhiteListTagEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\UserStatusEnum;
use App\Enums\WebHooks\PayoneerType;
use App\Events\PaymentAccountCreated;
use App\Library\Payoneer\Payoneer;
use App\Library\PingPongX\PingPongX;
use App\Models\PaymentAccount;
use App\Models\SellerBilling;
use App\Models\User;
use App\Services\UserService;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Throwable;

class PaymentAccountController extends Controller
{
    use ApiResponse;

    /**
     * List all payment accounts
     *
     * @return LengthAwarePaginator
     */
    public function index(): LengthAwarePaginator
    {
        return PaymentAccount::query()
            ->select(['id', 'payment_type', 'account_name', 'account_id', 'status', 'created_at'])
            ->where('seller_id', currentUser()->getUserId())
            ->where('status', '<>', PaymentAccountStatus::ARCHIVED)
            ->latest()
            ->paginate(15);
    }

    /**
     * Create new payment account
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $type = $request->post('type');
        abort_if(empty($type), 403);

        $type = strtolower($type);
        $token = Str::random(32);
        $currentUser = currentUser();

        // only trusted user can add SenPrints account
        if ($type === PaymentAccountTypeEnum::SENPRINTS && $currentUser->getInfo()->status !== UserStatusEnum::TRUSTED) {
            return $this->errorResponse();
        }

        $data = [
            'seller_id' => $currentUser->getUserId(),
            'payment_type' => $type,
            'account_name' => $request->post('account_name'),
            'account_id' => $request->post('account_id'),
            'confirm_token' => $token,
            'status' => PaymentAccountStatus::UNVERIFIED,
        ];
        $payoneerId = Str::uuid();

        if (in_array($type, [
            PaymentAccountTypeEnum::PAYPAL,
            PaymentAccountTypeEnum::PAYONEER,
            PaymentAccountTypeEnum::PINGPONG,
            PaymentAccountTypeEnum::SENPRINTS,
            PaymentAccountTypeEnum::LIANLIAN
        ], true)) {
            $rules = [
                'account_name' => 'bail|required|string',
                'account_id' => 'required|email',
                'confirm_email' => 'required|email|same:account_id'
            ];
            // Payoneer Only: Add payoneer id for payment account
            if ($type === PaymentAccountTypeEnum::PAYONEER && POMassPayOutIsEnabled()) {
                $data['additional_info'] = json_encode(['payoneer_id' => $payoneerId]);
            }
        } else {
            // bank transfer
            $rules = [
                'account_name' => 'bail|required|string',
                'bank_code' => 'required',
                'account_id' => 'required',
                'bank_name' => 'required',
                'bank_address' => 'required'
            ];

            try {
                $data['additional_info'] = json_encode([
                    'bank_code' => $request->post('bank_code'),
                    'bank_name' => $request->post('bank_name'),
                    'bank_address' => $request->post('bank_address')
                ], JSON_THROW_ON_ERROR);
            } catch (\JsonException $e) {
                return $this->errorResponse();
            }
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $newAccount = PaymentAccount::query()->create($data);
        // free memory $data
        unset($data);

        if (!$newAccount->wasRecentlyCreated) {
            return $this->errorResponse();
        }

        // Send verify email
        $user = User::query()->where('id', $currentUser->getUserId())->first();
        if (!$user) {
            return $this->errorResponse();
        }
        PaymentAccountCreated::dispatch($user, $newAccount);
        UserService::updateStepProductTour($user->id, 'complete-product-tour');
        return $this->successResponse([
            'id' => $newAccount->id,
            'created_at' => $newAccount->created_at
        ]);
    }

    /**
     * Delete a payment account
     *
     * @param $accountId
     * @return JsonResponse
     * @throws \Exception
     */
    public function destroy($accountId): JsonResponse
    {
        $query = PaymentAccount::query()
            ->where([
                'id' => $accountId,
                'seller_id' => currentUser()->getUserId()
            ]);

        $account = $query->select('status', 'payment_type', 'additional_info')->first();

        if (is_null($account)) {
            return $this->errorResponse();
        }

        $isPingPongX = false;
        if ($account->payment_type === PaymentAccountTypeEnum::PINGPONG) {
            $isPingPongX = true;
        }

        // if account is used, change status instead of delete from DB
        if ($account->isUsed()) {
            $updated = $query->update(['status' => PaymentAccountStatus::ARCHIVED]);
            if ($isPingPongX && $updated) {
                $bizId = $account->pingPongBizId();
                if (!is_null($bizId)) {
                    PingPongX::instance()->deleteRecipient($bizId);
                }
            }
            return $updated
                ? $this->successResponse()
                : $this->errorResponse();
        }

        // now we can delete it
        $deleted = $query->delete();

        if ($isPingPongX && $deleted) {
            $bizId = $account->pingPongBizId();
            if (!is_null($bizId)) {
                PingPongX::instance()->deleteRecipient($bizId);
            }
        }

        return $deleted ? $this->successResponse() : $this->errorResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function confirm(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $confirmToken = $request->post('confirm_token');
        $paymentAccount = PaymentAccount::query()
            ->where('confirm_token', $confirmToken)
            ->where('seller_id', $userId)
            ->whereIn('status', [PaymentAccountStatus::PENDING, PaymentAccountStatus::UNVERIFIED])
            ->first();

        if (!$paymentAccount) {
            return $this->errorResponse('Cannot found payment account!');
        }

        $data = [];

        if ($paymentAccount->payment_type === PaymentAccountTypeEnum::PAYONEER) {
            if (POMassPayOutIsEnabled()) {
                $payoneer = Payoneer::instance();
                $additionalInfo = json_decode($paymentAccount->additional_info, true);
                $hasVerify = false;
                if (!is_null($additionalInfo) && isset($additionalInfo['payoneer_id'])) {
                    $payeeStatus = $payoneer->payeeStatus($additionalInfo['payoneer_id']);
                    if (is_null($payeeStatus)) {
                        $payoneerCreateRegistrationLink = $payoneer->createRegistrationLink($additionalInfo['payoneer_id']);
                        if (!is_null($payoneerCreateRegistrationLink)) {
                            $data['payoneerCreateRegistrationLink'] = $payoneerCreateRegistrationLink;
                        }
                    } else if ($payeeStatus === 'active') {
                        $hasVerify = true;
                    }
                }
                if (!$hasVerify) {
                    $paymentAccount->status = PaymentAccountStatus::PENDING;
                } else {
                    $paymentAccount->status = PaymentAccountStatus::VERIFIED;
                    $paymentAccount->confirm_token = '';
                }
            } else {
                $paymentAccount->status = PaymentAccountStatus::VERIFIED;
                $paymentAccount->confirm_token = '';
            }
        } elseif ($paymentAccount->payment_type === PaymentAccountTypeEnum::PINGPONG) {
            $pingpongx = PingPongX::instance();
            $pingpongxRecipient = $pingpongx->createRecipient($paymentAccount->account_id);
            if (!is_null($pingpongxRecipient)) {
                if ($pingpongxRecipient['status'] === 'AVAILABLE') {
                    $paymentAccount->additional_info = json_encode($pingpongxRecipient);
                    $paymentAccount->status = PaymentAccountStatus::VERIFIED;
                    $paymentAccount->confirm_token = '';
                } elseif (isset($pingpongxRecipient['createRecipientError'])) {
                    $data['pingPongCreateRecipientError'] = true;
                    $data['pingPongErrorMessage'] = $pingpongxRecipient['message'];
                } else {
                    $paymentAccount->status = PaymentAccountStatus::PENDING;
                }
            } else {
                $paymentAccount->status = PaymentAccountStatus::PENDING;
            }
        } else {
            $paymentAccount->status = PaymentAccountStatus::VERIFIED;
            $paymentAccount->confirm_token = '';
        }

        return $paymentAccount->save()
            ? $this->successResponse($data)
            : $this->errorResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function reconfirm(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $accountId = $request->post('account_id');
        $isVerifyPayoneer = $request->post('isVerifyPayoneer', false);

        if (!$accountId) {
            return $this->errorResponse('Cannot found payment account!');
        }

        $paymentAccount = PaymentAccount::query()
            ->where('id', $accountId)
            ->where('seller_id', $userId)
            ->whereIn('status', [PaymentAccountStatus::PENDING, PaymentAccountStatus::UNVERIFIED])
            ->first();

        if (!$paymentAccount) {
            return $this->errorResponse();
        }

        if (
            $isVerifyPayoneer
            && $paymentAccount->payment_type === PaymentAccountTypeEnum::PAYONEER
            && $paymentAccount->status === PaymentAccountStatus::PENDING
            && POMassPayOutIsEnabled()
        ) {
            $payoneer = Payoneer::instance();
            $payeeId = Str::uuid();
            $hasPayeeId = false;
            if (!is_null($paymentAccount->additional_info)) {
                $additionalInfo = json_decode($paymentAccount->additional_info, true);
                if (!is_null($additionalInfo) && isset($additionalInfo['payoneer_id'])) {
                    $payeeId = $additionalInfo['payoneer_id'];
                    $hasPayeeId = true;
                }
            } else {
                $paymentAccount->additional_info = json_encode([
                    'payoneer_id' => $payeeId
                ]);
                $paymentAccount->save();
            }
            if (!$payoneer) {
                return $this->errorResponse('Cannot instantiate Payoneer!');
            }

            $payeeStatus = null;
            if ($hasPayeeId) {
                $payeeStatus = $payoneer->payeeStatus($payeeId);
            }

            if (is_null($payeeStatus)) {
                $payoneerCreateRegistrationLink = $payoneer->createRegistrationLink($payeeId);
                if (!is_null($payoneerCreateRegistrationLink)) {
                    return $this->successResponse([
                        'payoneerCreateRegistrationLink' => $payoneerCreateRegistrationLink
                    ]);
                }
                return $this->errorResponse("Error! Can't create a payee registration for Payoneer.");
            }

            if ($payeeStatus === 'active') {
                return $this->successResponse();
            }
        }
        $user = User::query()->where('id', $userId)->first();
        if ($user) {
            PaymentAccountCreated::dispatch($user, $paymentAccount);
            return $this->successResponse();
        }
        return $this->errorResponse();
    }

    /**
     * Handle callback from payoneer
     * @param Request $request
     * @return JsonResponse|void
     * @throws Throwable
     */
    public function webHookHandle(Request $request)
    {
        if (!POMassPayOutIsEnabled()) {
            return $this->errorResponse();
        }
        $type = $request->get('type');
        $apuid = $request->get('apuid'); //I'll be back and use it
        $payoneerid = $request->get('payoneerid');
        $sessionid = $request->get('sessionid'); //I'll be back and use it
        if (in_array($type, PayoneerType::getValues())) {
            $rules = [];
            if ($type === PayoneerType::APPROVED) {
                $rules = [
                    'apuid' => 'required',
                    'payoneerid' => 'required',
                    'sessionid' => 'required'
                ];
            }
            switch ($type) {
                case PayoneerType::APPROVED :
                    $rules = [
                        'apuid' => 'required',
                        'payoneerid' => 'required',
                        'sessionid' => 'required'
                    ];
                    break;
                case PayoneerType::ACCOUNT_LOADED_CONFIRMATION :
                    $rules = [
                        'payoneerid' => 'required',
                        'amount' => 'required',
                    ];
                    break;
                case PayoneerType::PAYMENT_CANCELED :
                    $rules = [
                        'IntPaymentId' => 'required'
                    ];
                    break;
            }

            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }

            try {
                if ($type === PayoneerType::APPROVED) {
                    $paymentAccount = PaymentAccount::query()
                        ->where([
                            'payment_type' => PaymentAccountTypeEnum::PAYONEER,
                            'additional_info->payoneer_id' => $apuid
                        ])
                        ->whereIn('status', [PaymentAccountStatus::PENDING, PaymentAccountStatus::UNVERIFIED]);

                    $paymentAccount = $paymentAccount->first();
                    if (!$paymentAccount) {
                        return $this->errorResponse("Payment account not found!");
                    }

                    /**
                     * Payment Account Status
                     * If current status is pending, update status to verified
                     * If current status is unverified, update status to pending
                     */
                    $status = PaymentAccountStatus::VERIFIED;
                    if ($paymentAccount->status === PaymentAccountStatus::UNVERIFIED) {
                        $status = PaymentAccountStatus::PENDING;
                    }

                    $dataUpdated = [
                        'status' => $status
                    ];

                    if ($paymentAccount->confirm_token !== '' && $status === PaymentAccountStatus::VERIFIED) {
                        $dataUpdated['confirm_token'] = '';
                    }

                    $paymentAccount->update($dataUpdated);
                } elseif ($type === PayoneerType::ACCOUNT_LOADED_CONFIRMATION || $type === PayoneerType::PAYMENT_CANCELED) {
                    $IntPaymentId = $request->get('IntPaymentId');
                    if (is_null($IntPaymentId)) {
                        return $this->errorResponse();
                    }
                    $payoneer = Payoneer::instance();
                    $transactionStatus = $payoneer->getPayoutStatus($IntPaymentId);
                    if (is_null($transactionStatus)) {
                        return $this->errorResponse();
                    }
                    if (isset($transactionStatus['status'])) {
                        if ($type === PayoneerType::ACCOUNT_LOADED_CONFIRMATION && strtolower($transactionStatus['status']) !== 'transferred') {
                            return $this->errorResponse();
                        }

                        if ($type === PayoneerType::PAYMENT_CANCELED && strtolower($transactionStatus['status']) !== 'cancelled') {
                            return $this->errorResponse();
                        }
                    }
                    $oldIntPaymentId = $IntPaymentId;
                    $IntPaymentId = str_replace(Payoneer::MASSPAYOUT_CLIENT_REF_ID_PREFIX, '', $IntPaymentId);
                    $IntPaymentId = explode('_', $IntPaymentId);
                    if (count($IntPaymentId) !== 2) {
                        return $this->errorResponse();
                    }
                    [$billId, $sellerId] = $IntPaymentId;
                    $billId = trim($billId);
                    $sellerId = trim($sellerId);
                    $sellerBilling = SellerBilling::query()
                        ->firstWhere([
                            'id' => $billId,
                            'seller_id' => $sellerId,
                            'type' => 'payout',
                            'status' => SellerBillingStatus::PROCESSING,
                        ]);
                    if (is_null($sellerBilling)) {
                        return $this->errorResponse();
                    }
                    if ($type === PayoneerType::ACCOUNT_LOADED_CONFIRMATION) {
                        $logMsg = "IntPaymentId: {$oldIntPaymentId}";
                        if (!is_null($payoneerid)) {
                            $logMsg .= ". PayoneerId: {$payoneerid}";
                        }
                        $sellerBilling->update([
                            'status' => SellerBillingStatus::COMPLETED,
                            'log' => $logMsg
                        ]);
                    } elseif ($type === PayoneerType::PAYMENT_CANCELED) {
                        $seller = User::query()->firstWhere('id', $sellerId);

                        if ($seller) {
                            $seller->updateBalance(
                                (0 - (float)$sellerBilling->amount),
                                SellerBillingType::PAYOUT,
                                'Cancel payout on ' . Carbon::now()->toFormattedDateString(),
                                null,
                                SellerBillingStatus::COMPLETED,
                                $sellerBilling->payment_account_id
                            );
                            $sellerBilling->update(['status' => SellerBillingStatus::CANCELLED]);
                        }
                    }
                }
                return $this->successResponse();
            } catch (\Exception $e) {
                logToDiscord("PaymentAccountController: Payoneer Webhook Error! - Callback Info: Payoneer Id ({$payoneerid}) ApUID ({$apuid}) SessionId ($sessionid) - File: {$e->getFile()} - Message: {$e->getMessage()} - \n\r Trace info: {$e->getTraceAsString()}");
                return $this->errorResponse();
            }
        }
    }

    public function checkSenPrintsEmail(Request $request): JsonResponse
    {
        $email = $request->input('email');
        $currentUserEmail = currentUser()->getEmail();

        if ($email === $currentUserEmail) {
            return $this->errorResponse('Email is invalid.');
        }

        $user = User::query()
            ->select(['name', 'email'])
            ->firstWhere('email', $email);

        if ($user === null) {
            return $this->errorResponse('Email not found.');
        }

        return $this->successResponse($user);
    }

    public function getPaymentTypes(): JsonResponse
    {
        try {
            $data = PaymentAccount::query()
                ->select([
                    'payment_type',
                ])
                ->where('status', '<>', PaymentAccountStatus::ARCHIVED)
                ->distinct()
                ->get();

            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    /**
     * @return JsonResponse
     */
    public function pingPongAvailable(): JsonResponse
    {
        if (!PingPongX::whiteListEnabled()) {
            return $this->successResponse();
        }

        $currentUser = currentUser();

        if (in_array(PingPongXWhiteListTagEnum::PINGPONG_PAYOUT, $currentUser->getInfo()->getTags())) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }
}
