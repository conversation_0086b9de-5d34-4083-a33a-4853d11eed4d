<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CurrencyEnum;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SystemConfigController;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Store;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\GetStoreDomain;
use App\Traits\Product;
use Illuminate\Http\Request;


class AffiliateController extends Controller
{
    use ApiResponse;
    use ElasticClient;
    use Product;
    use GetStoreDomain;

    public function campaigns (Request $request) {
        $spsid = $request->get('spsid');
        $keywords = $request->get('keywords');
        $collectionSlug = $request->get('collection');
        $categorySlug = $request->get('category');
        $storeSubdomain = $request->get('store');
        $store = null;
        if (isset($storeSubdomain)) {
            $store = Store::query()->where('sub_domain', $storeSubdomain)->first();
        }
        if (!isset($store)) {
            $store = Store::query()->where('id', Store::SENPRINTS_STORE_ID)->first();
        }
        $currencyCode = strtoupper($store->default_currency) ?? CurrencyEnum::USD;
        $uiColor = $store->default_color;
        $storeMainUrl = self::getStoreUrlByStoreId($store->id);
        $collection = Collection::query()->select('id')->where('slug', $collectionSlug)->first();
        $category = Category::query()->select('id')->where('slug', $categorySlug)->first();
        $keywords = urldecode($keywords);
        if (!isset($store)) {
            return $this->errorResponse('Could not find store');
        }
        if (!isset($spsid)) {
            return $this->errorResponse('Could not find affiliate program');
        }

        $collectionId = isset($collection) ? $collection->id : null;

        $categoryId = isset($category) ? $category->id  : null;

        $products = $this->elasticAffiliateListingProductsRaw($spsid, $store, $collectionId, $categoryId, $keywords);
        self::mappingCorrectPricing($products);
        $this->convertPriceSetupCurrency($products, $currencyCode);
        foreach ($products as &$product) {
            $product['domain'] = $storeMainUrl;
            if (!isset($product['campaign_name'])) {
                $product['campaign_name'] = $product['name'];
            }
        }
        $repsonse = [
            'products' => $products,
            'currency' => $currencyCode,
            'color' => $uiColor
        ];
        return $this->successResponse($repsonse);
    }

    public function convertPriceSetupCurrency (&$products, $toCurrency) {
        if ($toCurrency) {
            foreach ($products as &$product) {
                if ($toCurrency != $product['currency_code']) {
                    if ($toCurrency == CurrencyEnum::USD) {
                        $rate = SystemConfigController::findOrDefaultCurrency($product['currency_code'])->rate;
                        $product['price'] = round($product['price'] / $rate, 2, PHP_ROUND_HALF_UP);
                    } else if ($product['currency_code'] == CurrencyEnum::USD) {
                        $rate = SystemConfigController::findOrDefaultCurrency($toCurrency)->rate;
                        $product['price'] = round($product['price'] * $rate, 2, PHP_ROUND_HALF_UP);
                    } else {
                        $rateToUsd = SystemConfigController::findOrDefaultCurrency($product['currency_code'])->rate;
                        $rateToFinalCurrency = SystemConfigController::findOrDefaultCurrency($toCurrency)->rate;
                        $priceInUsd = $product['price'] / $rateToUsd;
                        $priceToFinalCurrency = $priceInUsd * $rateToFinalCurrency;
                        $product['price'] = round($priceToFinalCurrency, 2, PHP_ROUND_HALF_UP);
                    }
                }
            }
        }
    }
}
