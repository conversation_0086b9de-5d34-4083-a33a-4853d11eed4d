<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\SmartRemarketingEnum;
use App\Enums\UpsellTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SEOContentController;
use App\Http\Requests\Storefront\UpsellRequest;
use App\Models\BoughtTogetherLog;
use App\Models\Campaign;
use App\Models\Collection;
use App\Models\Elastic;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product as ProductModel;
use App\Models\ProductCategory;
use App\Models\Slug;
use App\Models\Store;
use App\Models\Upsell;
use App\Models\User;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\Product;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;


class UpsellController extends Controller
{
    use ApiResponse, ElasticClient, Product;

    public const LIMIT_PRODUCT_RESPONSE = 8;

    private int $limitProduct;
    private int $countProduct;
    private array $arrIdsExcept;
    private $currentStore = null;

    public function __construct($storeId = null, $limit = self::LIMIT_PRODUCT_RESPONSE)
    {
        if (is_null($storeId)) {
            $this->currentStore = StoreService::getCurrentStoreInfo();
        } else {
            $this->currentStore = Store::query()->select(['id', 'seller_id', 'market_place_upsell'])
                ->with('seller:id,custom_payment,smart_remarketing,status')
                ->firstWhere('id', $storeId);
        }
        $this->limitProduct = $limit;
    }

    /**
     * @param UpsellRequest $request
     *
     * @return JsonResponse
     * @throws \Psr\SimpleCache\InvalidArgumentException|\Exception
     */
    public function index(UpsellRequest $request): JsonResponse
    {
        if (empty($this->currentStore)) {
            return $this->errorResponse();
        }

        $arrFilters = $request->get('filter');
        $type = $request->get('type');
        $cache = $request->get('cache');
        $orderId = $request->input('order_id');
        $source = $request->input('source');

        try {
            $workerResult = $this->process($arrFilters, $type, $cache, $orderId, $source);
            $tags = '';
            foreach ($arrFilters as $each) {
                if ($each['campaign_id']) {
                    if ($tags !== '') {
                        $tags .= ',';
                    }
                    $tags .= 'campaign=' . $each['campaign_id'];
                }
            }

            StoreService::getVariantOfProducts($workerResult);

            self::mappingCorrectPricing($workerResult);
            $successResponse = $this->successResponse($workerResult)->withHeaders([
                'Cache-Tags' => $tags,
                'Cache-Expire-Time' => CacheTime::CACHE_24H
            ]);
            return $workerResult
                ? $successResponse : $this->errorResponse();
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * @param UpsellRequest $request
     *
     * @return JsonResponse
     */
    public function indexByCampaignUrl(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'url' => ['required', 'url']
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        try {
            $url = $request->get('url');
            $parsedUrl = parse_url($url);
            $scheme = $parsedUrl['scheme'];
            $domain = $parsedUrl['host'];
            $storeInfo = StoreService::getCurrentStoreInfo($domain);
            if (!$storeInfo) {
                return $this->errorResponse('Store not found');
            }
            $this->currentStore = $storeInfo;
            if (empty($parsedUrl['path']) || $parsedUrl['path'] === '/') {
                return $this->errorResponse('Invalid campaign url');
            }
            $product = $request->get('product');
            $slug = preg_replace("#^(/[a-z]{2})?/+#", "", $parsedUrl['path']);
            $seller = User::query()->find($storeInfo->seller_id);
            if (!$seller) {
                return $this->errorResponse('Seller not found');
            }
            /** @var Campaign $campaign */
            $campaign = Campaign::query()->onSellerConnection($seller)->select(['id', 'template_id', 'default_product_id'])->where('slug', $slug)->first();
            if (!$campaign) {
                return $this->errorResponse('Campaign not found');
            }
            $slugInfo = Slug::query()->where('slug', $slug)->first();
            if (!$slugInfo) {
                Slug::query()->insertOrIgnore(['seller_id' => $seller->id, 'campaign_id' => $campaign->id, 'slug' => $campaign->slug]);
            }
            $product_id = $campaign->default_product_id;
            if ($product) {
                $product = ProductModel::query()->onSellerConnection($seller)->select(['id'])->where([
                    'campaign_id' => $campaign->id,
                    'name' => $product
                ])->first();
                if ($product) {
                    $product_id = $product->id;
                }
            }
            $arrFilters[] = [
                'campaign_id' => $campaign->id,
                'template_id' => $campaign->template_id,
                'product_id' => $product_id
            ];
            $workerResult = $this->process($arrFilters, 'related');
            if (empty($workerResult)) {
                return $this->successResponse([]);
            }
            $tags = implode(',', array_map(static fn($each) => 'campaign=' . $each['campaign_id'], array_filter($arrFilters, static fn($each) => !empty($each['campaign_id']))));
            StoreService::getVariantOfProducts($workerResult);
            self::mappingCorrectPricing($workerResult);

            $result = [];
            $filterColumns = [
                'campaign_name',
                'thumb_url',
                'price',
                'old_price',
                'slug'
            ];
            foreach ($workerResult as $each) {
                $each['slug'] = ($scheme ?? 'https') . '://' . $domain . '/' . $each['slug'];
                $each['thumb_url'] = imgUrl($each['thumb_url'], 'full_hd');
                $result[] = Arr::only($each, $filterColumns);
            }
            return $this->successResponse($result)->withHeaders([
                'Cache-Tags' => $tags,
                'Cache-Expire-Time' => CacheTime::CACHE_24H
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Process Upsell
     *
     * @param $arrFilters
     * @param $type
     * @param string|null $cache
     * @param null $orderId
     * @param null $source
     * @return array|null
     * @throws \Exception
     */
    public function process($arrFilters, $type, ?string $cache = null, $orderId = null, $source = null): ?array
    {
        $arrResponse = [];

        if ($type === UpsellTypeEnum::POST_SALE && $this->currentStore && $orderId) {
            $seller = User::query()->find($this->currentStore->seller_id);

            if ($seller?->smart_remarketing === SmartRemarketingEnum::ENABLED_WITH_UPSELL_MP) {
                return $this->getSmartUpsell($orderId, $source);
            }
        }

        $sellerId = $this->currentStore->seller_id;
        $sellerIdForGetUpsell = !empty($this->currentStore->market_place_listing) || !empty($this->currentStore->market_place_upsell) ? $sellerId : null;
        foreach ($arrFilters as $key => $each) {
            $productId = $each['product_id'] ?? null;
            $campaignId = $each['campaign_id'];
            $templateId = $each['template_id'] ?? null;
            // for same collection
            $productIds = [];
            $cacheKey = CacheKeys::getUpsellName($type, $productId, $this->currentStore->id);

            $tags = [
                CacheKeys::getCampaignId($campaignId),
                'seller_id_' . $this->currentStore->seller_id,
            ];

            if ($this->currentStore->id) {
                $tags[] = CacheKeys::getStoreId($this->currentStore->id);
            }
            if ($cache === 'sp_clear_cache') {
                cache()->tags($tags)->flush();
                cacheAlt()->tags($tags)->flush();
            }

            $data = getDataByDefaultCache($cacheKey, $tags);
            if (!is_null($data)) {
                $arrResponse[] = $data;
                continue;
            }
            $cacheMethod = cacheAlt();
            if ($cacheMethod->has($cacheKey)) {
                try {
                    $arrResponse[] = $cacheMethod->get($cacheKey);
                } catch (\Throwable $e) {}
                continue;
            }
            $product = $this->getProduct($productId, $campaignId, $templateId);

            if (empty($product)) {
                // cache empty if don't have
                $cacheMethod->put($cacheKey, [], CacheKeys::CACHE_24H);
                unset($arrFilters[$key]);
                continue;
            }

            // set default value and limit
            $responseProduct = collect([]);
            $this->limitProduct = self::LIMIT_PRODUCT_RESPONSE;
            $this->countProduct = 0;
            $this->arrIdsExcept = [];

            $this->arrIdsExcept[] = (int) $campaignId;

            if (!is_null($productId)) {
                $this->arrIdsExcept[] = (int) $productId;
            }
            $upsells = Upsell::findByIdAndType($productId, $campaignId, $type, $sellerIdForGetUpsell);
            $categoryIds = ProductCategory::getAndCacheByTemplateId($templateId)
                ->pluck('category_id')
                ->toArray();

            switch ($type) {
                case UpsellTypeEnum::RELATED:
                    $upsellProducts = $this->getUpsellProducts($upsells, $categoryIds, $sellerIdForGetUpsell);
                    $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($upsellProducts, 'id'));

                    $responseProduct = $responseProduct->concat($upsellProducts);

                    unset($upsellProducts);
                    break;
                case UpsellTypeEnum::CART:
                    $upsellProducts = $this->getUpsellProducts($upsells, $categoryIds, $sellerIdForGetUpsell);
                    $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($upsellProducts, 'id'));

                    $productsAlsoBought = $this->getProductsAlsoBought($productId, $sellerIdForGetUpsell);
                    $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($productsAlsoBought, 'id'));

                    $responseProduct = $responseProduct->concat($upsellProducts);
                    $responseProduct = $responseProduct->concat($productsAlsoBought);

                    unset($upsellProducts, $productsAlsoBought);
                    break;
                case UpsellTypeEnum::POST_SALE:
                    $productsAlsoBought = $this->getProductsAlsoBought($productId, $sellerIdForGetUpsell);
                    $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($productsAlsoBought, 'id'));

                    $upsellProducts = $this->getUpsellProducts($upsells, $categoryIds, $sellerIdForGetUpsell);
                    $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($upsellProducts, 'id'));

                    $responseProduct = $responseProduct->concat($productsAlsoBought);
                    $responseProduct = $responseProduct->concat($upsellProducts);

                    unset($productsAlsoBought, $upsellProducts);
                    break;
            }
            // get with upsell_collection_id
            $collectionIds = Collection::query()->select('id')->limit(3)->whereIn('id', $product['collection_ids'])->orderBy('popularity', 'asc')->pluck('id')->toArray();

            $priorityConditions = [
                'same_collection_same_template',
                'same_collection_diff_template'
            ];
            foreach ($priorityConditions as $priorityCondition) {
                $templateCondition = ($priorityCondition === 'same_collection_same_template') ? $templateId : null;
                $isSameCollectionDiffTemplate = ($priorityCondition === 'same_collection_diff_template') ? true : false;

                foreach ($collectionIds as $collectIndex => $collectionId) {
                    if ($this->countProduct < $this->limitProduct) {
                        $products = $this->getUpsellCollectionProducts($upsells, $categoryIds, $collectionIds, $templateCondition);
                        $responseProduct = $responseProduct->concat($products);
                        $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($products, 'id'));
                        if ($this->countProduct < $this->limitProduct) {
                            $this->loadProductWithCollectionId($categoryIds, [$collectionId], $responseProduct, $templateCondition, $isSameCollectionDiffTemplate);
                        }
                        if ($this->countProduct < $this->limitProduct) {
                            $this->loadProductWithCollectionId([], [$collectionId], $responseProduct, $templateCondition, $isSameCollectionDiffTemplate);
                        }
                    }
                }
            }

            if ($this->countProduct < $this->limitProduct) {
                $commonWords = [
                    't-shirt',
                    't shirt',
                    'tshirt',
                    'shirt',
                    'hoodie',
                    'sweatshirt',
                    'mug',
                    'tank top',
                    'blanket',
                    'canvas',
                    'phone case',
                ];
                try {
                    if (!empty($templateId)) {
                        $templateInfos = ProductModel::query()
                            ->select('name')
                            ->where('id', $templateId)
                            ->pluck('name')
                            ->toArray();
                    } else {
                        $templateInfos = ProductModel::query()
                            ->select('name')
                            ->limit(8)
                            ->whereHas('order', function ($query) {
                                $query->select('order.id')->where('payment_status', OrderPaymentStatus::PAID)->where('status', '!=', OrderStatus::DELETED)->where('paid_at', '>=', Carbon::now()->subDays(30));
                            })
                            ->pluck('name')
                            ->unique()
                            ->toArray();
                    }
                    $keywords = [];
                    foreach ($templateInfos as $templateInfo) {
                        foreach (preg_split("/(\s)/", $templateInfo) as $keyword) {
                            $keywords[] = $keyword;
                        }
                    }

                    //Get Camp's info
                    $campInfo = ProductModel::query()->select('slug')->where('id', $campaignId)->value('slug');
                    $campNames = explode('-', $campInfo);
                    $keywords = array_unique(array_merge($keywords, $campNames));

                    //get product's info for getting exception
                    $productName = ProductModel::query()->select('name')->where('id', $productId)->value('name');
                    $productNameKeywords = preg_split("/(\s)/", $productName);
                    $keywords = array_map('strtolower', $keywords);
                    $productNameKeywords = array_map('strtolower', $productNameKeywords);
                    $keywords = array_diff($keywords, $productNameKeywords);
                    ksort($keywords);

                    // replace common words to empty string
                    $keywords = collect($keywords)->map(function ($keyword) use ($commonWords) {
                        return trim(str_replace($commonWords, '', $keyword));
                    })->filter(function ($keyword) {
                        return !empty($keyword) && strlen($keyword) > 2;
                    })->values()->toArray();
                    $productNameKeywords = collect($productNameKeywords)->map(function ($keyword) use ($commonWords) {
                        return trim(str_replace($commonWords, '', $keyword));
                    })->filter(function ($keyword) {
                        return !empty($keyword) && strlen($keyword) > 2;
                    })->values()->toArray();
                    $productsSameCollection = $this->getProductsByRelatedCampName($keywords, $productNameKeywords, $templateId);
                    $responseProduct = $responseProduct->concat($productsSameCollection);
                    $this->arrIdsExcept = array_merge($this->arrIdsExcept, array_column($productsSameCollection, 'id'));
                    unset($productsSameCollection);
                } catch (\Exception $e) {}
            }

            $cacheMethod->put($cacheKey, $responseProduct, CacheKeys::CACHE_24H);
            if ($responseProduct->isNotEmpty()) {
                $arrResponse[] = $responseProduct;
            }

            unset($productIds, $responseProduct);
        }
        $arrResponseFinal = [];
        $count = 0;
        $index = 0;
        $length = count($arrResponse);
        // check empty response
        if (empty($arrResponse)) {
            return null;
        }

        $arrId = [];
        while ($count < self::LIMIT_PRODUCT_RESPONSE) {
            $product = [];
            while (true) {
                if (empty($arrResponse[$index])) {
                    break;
                }
                $product = is_array($arrResponse[$index]) ? array_shift($arrResponse[$index]) : $arrResponse[$index]->shift();
                if (empty($product)) {
                    break;
                }
                if (!isset($product['id'])) {
                    $product = $product instanceof \Illuminate\Support\Collection ? $product->toArray() : $product;
                    if (empty($product)) {
                        break;
                    }
                }
                $product_id = (int) $product['id'];
                if (in_array($product_id, $arrId, true)) {
                    continue;
                }

                if (empty($arrResponseFinal[$count])) {
                    $arrResponseFinal[$count] = $product;
                    $arrId[] = $product_id;
                    $count++;
                    break;
                }
            }
            $index++;
            if ($index >= $length) {
                if (empty($product)) {
                    break;
                }
                $index = 0;
            }
        }

        return $arrResponseFinal;
    }

    private function loadProductWithCollectionId($categoryIds, $collectionIds, &$responseProduct, $templateId = null, $isSameCollectionDiffTemplate = false)
    {
        $productsSameCollection = $this->getProductsByProductCollectionIds(
            [],
            $categoryIds,
            $collectionIds,
            $templateId,
            $isSameCollectionDiffTemplate
        );

        $responseProduct = $responseProduct->concat($productsSameCollection);
        $this->arrIdsExcept = array_merge(
            $this->arrIdsExcept,
            array_column($productsSameCollection, 'id')
        );

        unset($productsSameCollection);
    }

    private function getProduct($productId, $campaignId, $templateId): array
    {
        $arrSearch = [];
        $arrSearch['listing'] = [
            'collection_ids',
        ];

        if ($productId) {
            $arrSearch['id'] = $productId;
            $arrSearch['campaign_id'] = $campaignId;
            $arrSearch['template_id'] = $templateId;
        } else {
            $arrSearch['id'] = $campaignId;
        }
        $arrSearch['search_all_index'] = 1;
        $arrSearch['limit'] = 1;
        // get first product
        return Arr::get((new Elastic())->getProduct($arrSearch, false), 0, []);
    }

    /**
     * Get Filter For Upsell Products
     *
     * @param mixed $productId
     *
     * @return array|null
     */
    private function getFilterForUpsell($productId = null): ?array
    {
        $orderProduct = OrderProduct::query()
            ->select('id', 'campaign_id', 'template_id')
            ->firstWhere('product_id', $productId);

        if ($orderProduct) {
            return [
                'template_id' => $orderProduct->template_id,
                'campaign_id' => $orderProduct->campaign_id,
                'product_id' => $productId
            ];
        }

        return null;
    }

    /**
     * Get Related Upsell Products For Email
     *
     * @param array $productIds
     *
     * @return array|null
     * @throws \Exception
     */
    public function getUpsellProductsForEmail(array $productIds, $type = UpsellTypeEnum::RELATED): ?array
    {
        $filters = collect();
        $arrProductId = [];

        foreach ($productIds as $productId) {
            // remove duplicate product
            if (!in_array($productId, $arrProductId)) {
                $arrProductId[] = $productId;
                // Get product template
                $filter = $this->getFilterForUpsell($productId);

                if ($filter) {
                    $filters->push($filter);
                }
            }
        }

        return $this->process(arrFilters: $filters, type: $type);
    }

    private function getUpsellProducts($upsells, array $categoryIds = [],  $sellerId = null): array
    {
        // get array upsell_product_ids
        $arrIds = [];
        foreach ($upsells as $each) {
            if (is_null($each->upsell_product_ids)) {
                continue;
            }
            $productIds = explode(',', $each->upsell_product_ids);

            $productIds = array_filter($productIds);
            $arrIds = array_merge($arrIds, $productIds);
        }
        if (empty($arrIds)) {
            return [];
        }

        return $this->getProductsByArrIds($arrIds, $categoryIds, false, false, $sellerId);
    }

    private function getProductsByArrIds(array $arrIds, array $categoryIds = [], $isArrIdProduct = false, $smartRemarketing = false, $sellerId = null): array
    {
        if (empty($arrIds)) {
            return [];
        }
        // get filter Elastic
        $arrFilter = [];

        // filter Active Product
        $arrFilter['bool']['filter'][]['term'] = [
            'status' => ProductStatus::ACTIVE
        ];

        if ($this->currentStore->id == Store::SENPRINTS_STORE_ID) {
            $arrFilter['bool']['filter'][]['terms'] = [
                'tm_status' => [
                    CampaignTrademarkStatusEnum::VERIFIED,
                    CampaignTrademarkStatusEnum::UNVERIFIED,
                ]
            ];
        }

        $productType = $this->currentStore->isExpressListing() ? ProductType::CAMPAIGN : ProductType::PRODUCT;
        $arrFilter['bool']['filter'][]['term'] = [
            'product_type' => $productType
        ];

        if (isset($sellerId)) {
            $arrFilter['bool']['filter'][]['term'] = [
                'seller_id' => $sellerId
            ];
        }

        if ($isArrIdProduct) {
            // filter by Product Ids
            $arrFilter['bool']['filter'][]['terms'] = [
                'id' => $arrIds
            ];
        } else {
            // filter by Product or Campaign Ids
            $queryCondition = &$arrFilter['bool']['filter'][]['bool'];
            $queryCondition['should'][]['terms'] = [
                'id' => $arrIds
            ];
            $queryCondition['should'][]['terms'] = [
                'campaign_id' => $arrIds
            ];
            $queryCondition['minimum_should_match'] = 1;
        }

        // filterTemplateId
        if (!empty($categoryIds)) {
            // filter by  categories
            $queryCondition = &$arrFilter['bool']['should'];
            foreach ($categoryIds as $categoryId) {
                $queryCondition[]['term'] = [
                    'category_ids' => $categoryId
                ];
            }
            $arrFilter['bool']['minimum_should_match'] = 1;
        }

        $this->arrIdsExcept = array_filter_empty($this->arrIdsExcept);
        // filter not in exists array
        $queryCondition = &$arrFilter['bool']['filter'][]['bool'];
        $queryCondition['should'][]['bool']['must_not']['terms'] = [
            'id' => $this->arrIdsExcept
        ];
        $queryCondition['should'][]['bool']['must_not']['terms'] = [
            'campaign_id' => $this->arrIdsExcept
        ];
        $queryCondition['minimum_should_match'] = 1;

        // filter by Store or (Marketplace or Approved)
        $queryCondition = &$arrFilter['bool']['filter'][]['bool'];
        if (!$smartRemarketing && $this->currentStore->id !== Store::SENPRINTS_STORE_ID) {
            $queryCondition['should'][]['term'] = [
                'seller_id' => $this->currentStore->seller_id
            ];
        }
        if (!$smartRemarketing && checkLoadMarketPlace($this->currentStore)) {
            $queryCondition['should'][]['term'] = [
                'public_status' => 1
            ];
        }
        $queryCondition['minimum_should_match'] = 1;

        $size = $this->limitProduct - $this->countProduct;
        $products = $this->elasticGetProductUpsell($arrFilter, $size);

        $this->countProduct += count($products);

        return $products;
    }

    private function getProductsAlsoBought($productId, $sellerId = null): array
    {
        $arrIds = BoughtTogetherLog::query()
            ->select('product_id2')
            ->where(
                [
                    'product_id1' => $productId,
                    'store_id' => $this->currentStore->id
                ]
            )
            ->limit($this->limitProduct - $this->countProduct)
            ->orderByRaw('count(product_id2) desc')
            ->groupBy('product_id2')
            ->get()
            ->pluck('product_id2')
            ->toArray();

        return $this->getProductsByArrIds($arrIds, [], true, false, $sellerId);
    }

    private function getProductsByProductCollectionIds(
        array $arrIds = [],
        array $categoryIds = [],
        array $collectionIds = [],
        $templateId = null,
        $isSameCollectionDiffTemplate = false
    ): array {
        // required
        if (empty($arrIds) && empty($collectionIds)) {
            return [];
        }

        // get filter Elastic
        $arrFilter = [];

        $arrFilter['bool']['filter'][]['term'] = [
            'status' => ProductStatus::ACTIVE,

        ];
        if ($this->currentStore->id == Store::SENPRINTS_STORE_ID) {
            $arrFilter['bool']['filter'][]['terms'] = [
                'tm_status' => [
                    CampaignTrademarkStatusEnum::VERIFIED,
                    CampaignTrademarkStatusEnum::UNVERIFIED,
                ]
            ];
        }
        $collectionIds = array_filter_empty($collectionIds);
        if (!empty($collectionIds)) {
            $queryCondition = &$arrFilter['bool']['must'];
            foreach ($collectionIds as $collectionId) {
                $queryCondition[]['term'] = [
                    'collection_ids' => $collectionId
                ];
            }
        }

        $categoryIds = array_filter_empty($categoryIds);
        if (!empty($categoryIds)) {
            $productType = $this->currentStore->isExpressListing() ? ProductType::CAMPAIGN : ProductType::PRODUCT;
            $arrFilter['bool']['filter'][]['term'] = [
                'product_type' => $productType
            ];

            // filter by categories
            $queryCondition = &$arrFilter['bool']['should'];
            foreach ($categoryIds as $categoryId) {
                $queryCondition[]['term'] = [
                    'category_ids' => $categoryId
                ];
            }
            $arrFilter['bool']['minimum_should_match'] = 1;
        } else if ($isSameCollectionDiffTemplate || empty($categoryIds)) {
            $arrFilter['bool']['filter'][]['term'] = [
                'product_type' => ProductType::PRODUCT
            ];
        } else {
            $arrFilter['bool']['filter'][]['term'] = [
                'product_type' => ProductType::CAMPAIGN
            ];
        }

        // filter by Store or (Marketplace or Approved)
        $queryCondition = &$arrFilter['bool']['filter'][]['bool'];
        if ($this->currentStore->id !== Store::SENPRINTS_STORE_ID) {
            $queryCondition['should'][]['term'] = [
                'seller_id' => $this->currentStore->seller_id
            ];
        }
        if (checkLoadMarketPlace($this->currentStore)) {
            $queryCondition['should'][]['term'] = [
                'public_status' => 1
            ];
        }
        $queryCondition['minimum_should_match'] = 1;

        // filter by Product or Campaign Ids
        if (!empty($arrIds)) {
            $queryCondition = &$arrFilter['bool']['filter'][]['bool'];
            $queryCondition['should'][]['terms'] = [
                'id' => $arrIds
            ];
            $queryCondition['should'][]['terms'] = [
                'campaign_id' => $arrIds
            ];
            $queryCondition['minimum_should_match'] = 1;
        }

        if(isset($templateId)) {
            $arrFilter['bool']['filter'][]['term'] = [
                'template_id' => $templateId,
            ];
        }

        // filter not in exists array
        $this->arrIdsExcept = array_filter_empty($this->arrIdsExcept);
        $queryCondition = &$arrFilter['bool']['must_not'];
        $queryCondition[]['terms'] = [
            'id' => $this->arrIdsExcept
        ];
        $queryCondition[]['terms'] = [
            'campaign_id' => $this->arrIdsExcept
        ];

        $size = $this->limitProduct - $this->countProduct;
        $products = $this->elasticGetProductUpsell($arrFilter, $size);
        $products = collect($products)->map(function ($product) {
            $product['id'] = $product['id'] ?: $product['express_product_id'];
            unset($product['express_product_id']);
            return $product;
        })->toArray();
        $this->countProduct += count($products);

        return $products;
    }

    private function getUpsellCollectionProducts($upsells, array $categoryIds, array $collectionIdsInput = [], $templateId = null): array
    {
        $collectionIds = isset($upsells->upsell_collection_id) ? $upsells->pluck('upsell_collection_id')->toArray() : $collectionIdsInput;
        $upsellCollectionProducts = $this->getProductsByProductCollectionIds([], $categoryIds, $collectionIds, $templateId);
        unset($collectionIds);
        return $upsellCollectionProducts;
    }

    public function getSmartUpsell($orderId, $source): array
    {
        $order = Order::query()
            ->select(['customer_name'])
            ->find($orderId);

        if ($order) {
            $customerName = explode('-', Str::slug($order->customer_name));
            $customerName = !empty($customerName[0]) ? ucfirst($customerName[0]) : 'Your_Name';
            $request = new Request(['path' => 'custom-name/' . $customerName . '-unisex_standard_tshirt']);
            $srUpsellProductsList = (new SEOContentController())->generateSeoContent($request)->getData(true);

            if (!empty($srUpsellProductsList['data']['data_json'])) {
                $this->countProduct = 0;
                $this->arrIdsExcept = [];
                $srUpsellProductsList = $srUpsellProductsList['data']['data_json'];
                $productIds = app()->environment(EnvironmentEnum::PRODUCTION)
                    ? Upsell::PROD_SR_UPSELL_PRODUCTS
                    : Upsell::DEV_SR_UPSELL_PRODUCTS;
                $products = $this->getProductsByArrIds($productIds, [], true, true);

                return array_map(function ($product) use ($srUpsellProductsList, $source) {
                    $srProduct = array_filter($srUpsellProductsList, function ($sr) use ($product) {
                        return !empty($sr['slug']) && str_contains($sr['slug'], $product['slug']);
                    });

                    $srProduct = array_pop($srProduct);

                    if ($srProduct) {
                        $product['thumb_url'] = $srProduct['thumb_url'];
                        $product['currency_code'] = $srProduct['currency_code'];
                        $product['campaign_name'] = $srProduct['campaign_name'];
                        $product['product_type'] = $srProduct['product_type'];
                        $product['price'] = $srProduct['price'];
                        $product['slug'] = $srProduct['slug'] . $this->smartUpsellParameters($source);
                    }

                    return $product;
                }, $products);
            }
        }

        return [];
    }

    public function smartUpsellParameters($source): ?string
    {
        return match ($source) {
            'thank_you' => '&utm_source=thank_you&utm_medium=web&utm_campaign=smart_remarketing',
            'order_status' => '&utm_source=order_status&utm_medium=web&utm_campaign=smart_remarketing',
            'order_email' => '&utm_source=order_email&utm_medium=email&utm_campaign=smart_remarketing',
            'review_email' => '&utm_source=review_email&utm_medium=email&utm_campaign=smart_remarketing',
            'upsell_sms' => '&utm_source=upsell_sms&utm_medium=sms&utm_campaign=smart_remarketing',
            default => null,
        };
    }


    private function getProductsByRelatedCampName(
        array $campNames = [],
        array $campNameKeywordsMustNot = [],
        $templateId = null
    ): array {
        if (empty($campNames)) {
            return [];
        }

        // get filter Elastic
        $arrFilter = [];

        $arrFilter['bool']['filter'][]['term'] = [
            'status' => ProductStatus::ACTIVE,

        ];
        foreach ($campNames as $campName) {
            if (isset($campName) && $campName != '') {
                $arrFilter['bool']['should'][]['wildcard'] = [
                    'name' => "*$campName*"
                ];
            }
        }

        foreach ($campNameKeywordsMustNot  as $keywordMustNot) {
            if (isset($keywordMustNot) && $keywordMustNot != '') {
                $arrFilter['bool']['must_not'][]['wildcard'] = [
                    'name' => "*$keywordMustNot*"
                ];
            };
        }

        if ($this->currentStore->id == Store::SENPRINTS_STORE_ID) {
            $arrFilter['bool']['filter'][]['terms'] = [
                'tm_status' => [
                    CampaignTrademarkStatusEnum::VERIFIED,
                    CampaignTrademarkStatusEnum::UNVERIFIED,
                ]
            ];
        }

        $arrFilter['bool']['filter'][]['terms'] = [
            'product_type' => [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ]
        ];

        if ($this->currentStore->id !== Store::SENPRINTS_STORE_ID) {
            $arrFilter['bool']['filter'][]['term'] = [
                'seller_id' => $this->currentStore->seller_id
            ];
        }

        if (!$this->currentStore->list_all_my_campaigns) {
            $arrFilter['bool']['filter'][]['term'] = [
                'store_ids' => $this->currentStore->id
            ];
        }

        $this->arrIdsExcept = array_filter_empty($this->arrIdsExcept);
        $queryCondition = &$arrFilter['bool']['must_not'];
        $queryCondition[]['terms'] = [
            'id' => $this->arrIdsExcept
        ];
        $queryCondition[]['terms'] = [
            'campaign_id' => $this->arrIdsExcept
        ];
        if (isset($templateId)) {
            $arrFilter['bool']['filter'][]['term'] = [
                'template_id' => $templateId,
            ];
        }
        $size = $this->limitProduct - $this->countProduct;
        $products = $this->elasticGetProductUpsell($arrFilter, $size);
        $this->countProduct += count($products);
        return $products;
    }
}
