<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CacheTime;
use App\Http\Controllers\Controller;
use App\Models\Elastic;
use App\Models\Sitemaps;
use App\Models\SitemapUrls;
use App\Models\StoreNavigation;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\SitemapIndex;
use Spatie\Sitemap\Tags\Sitemap as SitemapTag;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class SitemapController extends Controller
{
    use ApiResponse;

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response|SymfonyResponse
     */
    public function index(Request $request)
    {
        // get store domain from header
        $domain = StoreService::getDomain();
        $store = StoreService::getStore($domain, ['id', 'auto_sitemap'], true);

        if (!$store) {
            abort(403);
        }

        if (!Str::contains($domain, '.')) {
            $domain = $store->base_url;
        }

        $sitemaps = Sitemaps::query()
            ->select('id', 'updated_at')
            ->where('store_id', $store->id)
            ->get();

        if ($sitemaps->count() === 0) {
            return self::fallbackSitemap($request, $domain, $store->id);
        }

        $sitemapIndex = SitemapIndex::create();

        // always have sitemap generated from store navigations
        $lastMonth = now()->subMonth()->startOfMonth();

        if ((bool)$store->auto_sitemap) {
            $sitemapIndex->add(
                self::getSitemapTag($domain, 'auto', $lastMonth)
            );

            $sitemapIndex->add(
                self::getSitemapTag($domain, 'new_arrivals', $lastMonth)
            );
        }

        $sitemaps->each(fn($sitemap) => $sitemapIndex->add(
            self::getSitemapTag($domain, $sitemap->id, $sitemap->updated_at)
        ));

        return Response::make($sitemapIndex->render(), 200, [
            'Content-Type' => 'text/xml',
            'Cache-Expire-Time' => CacheTime::CACHE_30D
        ]);
    }

    private static function getSitemapTag($domain, $sitemapId, $updatedAt): SitemapTag
    {
        $url = 'https://' . $domain . '/sitemap.xml?id=' . $sitemapId . '&type=.xml';

        return SitemapTag::create($url)->setLastModificationDate($updatedAt);
    }

    /**
     * @param Request $request
     * @param $sitemapId
     * @return \Illuminate\Http\Response|SymfonyResponse
     * @throws \Throwable
     */
    public function show(Request $request, $sitemapId)
    {
        // get store domain from header
        $domain = StoreService::getDomain();
        $store = StoreService::getStore($domain, ['id', 'auto_sitemap'], true);

        // store is not belong to this domain
        if (!$store) {
            abort(403);
        }

        $storeId = $store->id;

        if (!Str::contains($domain, '.')) {
            $domain = $store->base_url;
        }

        $isAutoSitemap = (bool)$store->auto_sitemap;

        switch ($sitemapId) {
            case 'auto':
                if ($isAutoSitemap) {
                    $urls = self::getAutoSitemap($domain, $storeId);
                }
                break;

            case 'new_arrivals':
                if ($isAutoSitemap) {
                    $urls = self::getNewArrivalsCampaigns($storeId);
                }
                break;

            default:
                // get all urls
                $urls = SitemapUrls::query()
                    ->where('sitemap_id', $sitemapId)
                    ->get('url')
                    ->pluck('url');
                break;
        }

        abort_if(!isset($urls), 403);

        // Disable inspection because we already abort if $urls is not set
        /** @noinspection PhpUndefinedVariableInspection */
        if ($urls->isEmpty()) {
            return self::fallbackSitemap($request, $domain, $storeId);
        }

        $sitemap = Sitemap::create();
        $urls->each(fn($url) => $sitemap->add(self::getUrl($url, $domain)));

        return Response::make($sitemap->render(), 200, [
            'Content-Type' => 'text/xml',
            'Cache-Tags' => 'sitemap=' . $sitemapId,
            'Cache-Expire-Time' => CacheTime::CACHE_30D
        ]);
    }

    /**
     * @param $storeId
     * @return Collection
     * @throws \Throwable
     */
    private static function getNewArrivalsCampaigns($storeId): Collection
    {
        $filters = [];
        $filters['store_id'] = $storeId;

        $arr = (new Elastic())->getNewestCampaigns($filters);
        return collect($arr ?? [])->pluck('slug');
    }

    private static function getStoreNavigations($storeId)
    {
        return StoreNavigation::query()
            ->where([
                'store_id' => $storeId,
                'status' => 1
            ])
            ->get('link_url');
    }

    private static function getAutoSitemap($domain, $storeId): Collection
    {
        $baseUrl = self::getUrl('/', $domain);

        if (!Str::endsWith($baseUrl, '/')) {
            $baseUrl .= '/';
        }

        $defaultPages = [
            $baseUrl . 'collection',
            $baseUrl . 'page/about',
            $baseUrl . 'page/faq',
            $baseUrl . 'page/contact-us',
            $baseUrl . 'page/return-policy',
            $baseUrl . 'page/shipping-policy',
            $baseUrl . 'page/terms-of-service',
            $baseUrl . 'page/privacy',
            $baseUrl . 'page/dmca',
        ];

        $navigations = self::getStoreNavigations($storeId);

        if ($navigations->isEmpty()) {
            return collect($defaultPages);
        }

        $navigations = $navigations->pluck('link_url');
        return collect($defaultPages)->merge($navigations);
    }

    private static function fallbackSitemap(Request $request, $domain, $storeId): SymfonyResponse
    {
        $sitemap = Sitemap::create();
        $urls = self::getAutoSitemap($domain, $storeId);
        $urls->each(fn($url) => $sitemap->add(self::getUrl($url, $domain)));

        return $sitemap->toResponse($request);
    }

    private static function getUrl($url, $domain): string
    {
        if (Str::startsWith($url, 'http')) {
            return $url;
        }

        $newUrl = 'https://' . $domain;

        if (!Str::startsWith($url, '/')) {
            $newUrl .= '/';
        }

        return $newUrl . $url;
    }
}
