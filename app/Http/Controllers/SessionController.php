<?php

namespace App\Http\Controllers;

use App\Models\UserSessions;
use App\Services\SessionService;
use App\Traits\ApiResponse;

class SessionController extends Controller
{
    use ApiResponse;

    public function list(): \Illuminate\Http\JsonResponse
    {
        $sessions = UserSessions::query()
            ->where('user_id', auth()->id())
            ->when(currentUser()->isSeller(), function ($q) {
                return $q->where(function ($q2) {
                    $q2->where('user_access_type', 'seller')
                        ->orWhereNull('user_access_type');
                });
            })
            ->orderBy('last_activity', 'desc')
            ->get();

        $sessions = $sessions->map(function ($session) {
            return [
                'id' => $session->id,
                'is_current' => $session->token === request()->session_token,
                'last_activity' => $session->last_activity->diffForHumans(),
                'location' => $session->location,
                'ip_address' => $session->ip_address,
                'user_agent' => $session->user_agent,
                'os' => $session->os,
                'browser' => $session->browser,
                'logged_by' => app(SessionService::class)->getLoggedBy($session),
                'created_at' => $session->created_at,
            ];
        });

        return $this->successResponse($sessions);
    }

    public function logoutAll(): \Illuminate\Http\JsonResponse
    {
        UserSessions::query()
            ->where('user_id', currentUser()->getUserId())
            ->where('token', '!=', request()->session_token)
            ->delete();

        return $this->successResponse('Logout all sessions successfully');
    }

    public function logout(int $sessionId): \Illuminate\Http\JsonResponse
    {
        UserSessions::query()
            ->where('id', $sessionId)
            ->where('user_id', currentUser()->getUserId())
            ->delete();

        return $this->successResponse('Logout session successfully');
    }
}
