<?php

namespace App\Http\Controllers;

use App\Enums\DesignTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Jobs\RenderPrintJob;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use App\Services\FulfillmentService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class RenderCanvasController extends Controller
{
    use ApiResponse;

    public function index(Request $request)
    {
        $url = config('senprints.render_canvas_url');
        $baseImgUrl = config('senprints.base_img_url');
        $fileId = $request->get('file_id');
        $file = File::query()->find($fileId);

        if (!$file) {
            return $this->errorResponse('Invalid file');
        }

        $mockupId = $file->mockup_id;
        $designId = $file->design_id;
        $productId = $file->product_id;
        $campaignId = $file->campaign_id;

        if (empty($file->file_url) && !empty($mockupId) && !empty($designId)) {
            $designImg = File::query()
                ->select('file_url')
                ->find($designId);

            if (!$designImg || empty($designImg->file_url)) {
                return $this->errorResponse('Invalid design image');
            }

            // Add local proxy AMZ S3 for file design image
            $imgUrl = $baseImgUrl . '/' . $designImg->file_url;

            $designJson = File::query()
                ->select('design_json')
                ->find($mockupId);

            if (!$designJson || empty($designJson->design_json)) {
                return $this->errorResponse('Invalid design');
            }

            // Replace AMZ S3 bucket to local
            $designJsonConvertUrl = str_replace('https://sendev.s3-ap-southeast-1.amazonaws.com', $baseImgUrl, $designJson->design_json);
            [$designJsonConvertUrl,] = FulfillmentService::updateUrlOnDesignJson($designJsonConvertUrl);


            $response = Http::withoutVerifying()
                ->post($url, [
                    'file_id' => $file->id,
                    'img_url' => $imgUrl,
                    'design_json' => $designJsonConvertUrl,
                    'campaign_id' => $campaignId
                ])
                ->json();

            if (empty($response)) {
                return $this->errorResponse('Render image failed');
            }

            $fileUrl = $response['file_url'];
            $filePath = parse_url($fileUrl, PHP_URL_PATH);
            $filePath = ltrim($filePath, '/');

            $file->update(['file_url' => $filePath]);

            self::updateCampaignSyncStatus($file, $productId, $filePath, $campaignId);

            return redirect($fileUrl);
        }

        if (empty($mockupId) || empty($designId)) {
            return $this->errorResponse('Mockup ID or Design ID not found');
        }

        return redirect($baseImgUrl . '/' . $file->file_url);
    }

    /**
     * @param Request $request
     * @return mixed
     *
     * @deprecated TODO remove this method
     */
    public function render3d(Request $request)
    {
        $baseImgUrl = config('senprints.base_img_url');
        $fileToken = $request->get('file_token');
        $file = File::query()->firstWhere('token', $fileToken);

        if (!$file) {
            return $this->errorResponse();
        }

        $fileUrl = self::renderImage3d($file);

        if ($fileUrl) {
            return redirect($baseImgUrl . '/' . $fileUrl);
        }

        return $this->errorResponse(null, 404);
    }

    public static function renderImage3d(File $file)
    {
        $url = config('senprints.render_canvas_3d_url');

        if (!is_null($file->file_url)) {
            return $file->file_url;
        }

        $fileId = $file->id;
        $mockup = File::query()->find($file->mockup_id);

        if (empty($mockup) || empty($mockup->design_json)) {
            return null;
        }

        if (empty($file->design_id)) {
            $templateImage = File::query()
                ->firstWhere([
                    'mockup_id' => $file->mockup_id,
                    'product_id' => $mockup->product_id,
                    'option' => $file->option,
                ]);

            if ($templateImage !== null) {
                $file->update(['file_url' => $templateImage->file_url]);
                return $templateImage->file_url;
            }
        }

        $designImg = File::query()
            ->select('file_url')
            ->find($file->design_id);

        $productId = $file->product_id;
        $campaignId = $file->campaign_id;
        $designJson = json_decode($mockup->design_json);


        $colorImg = $designJson->color ?? null;
        $shadowImg = $designJson->shadow ?? null;
        $cropImg = $designJson->crop ?? null;
        $color = color2hex($file->option);

        $design3DImgUrl = $designImg->file_url ?? null;
        $productIdParam = empty($design3DImgUrl) ? $mockup->product_id : $productId;

        $renderParams = [
            'fileId' => $fileId,
            'productId' => $productIdParam,
            'mockupId' => $mockup->id,
            'backgroundImg' => $mockup->file_url,
            'colorImg' => $colorImg,
            'cropImg' => $cropImg,
            'shadowImg' => $shadowImg,
            'design3DImg' => $design3DImgUrl,
            'color' => $color,
            'colorName' => $file->option
        ];

        $response = Http::withoutVerifying()
            ->post($url, $renderParams)
            ->json();

        if (!isset($response['Location']) || empty($response['Location'] || empty($response))) {
            logToDiscord('Render image failed: ' . json_encode($response));
            return null;
        }

        $fileUrl = $response['Location'];
        $filePath = parse_url($fileUrl, PHP_URL_PATH);
        $filePath = ltrim($filePath, '/');

        $file->update(['file_url' => $filePath]);

        // save template image
        if (empty($designImg)) {
            File::query()->create([
                'mockup_id' => $file->mockup_id,
                'product_id' => $mockup->product_id,
                'option' => $file->option,
                'file_url' => $filePath,
                'type' => FileTypeEnum::IMAGE,
            ]);
        }

        self::updateCampaignSyncStatus($file, $productId, $filePath, $campaignId);

        return $filePath;
    }

    public function renderPrint(Request $request): JsonResponse
    {
        $fileId = $request->get('file_id');
        $designId = $request->get('design_id');
        $force = $request->boolean('force');
        $sellerId = $request->get('seller_id');
        $seller = User::find($sellerId);

        if (is_null($fileId) && is_null($designId)) {
            return $this->errorResponse('File ID null');
        }

        if ($fileId) {
            $file = File::query()
                ->onSellerConnection($seller)
                ->find($fileId);

            if (is_null($file) || $file->type !== FileTypeEnum::DESIGN || $file->option !== FileRenderType::PRINT) {
                return $this->errorResponse('File is invalid');
            }

            if (!is_null($file->file_url_2) && !$force) {
                return $this->errorResponse('File is rendered');
            }

            logToDiscord('Render file job: ' . $fileId, 'job');
            RenderPrintJob::dispatch($file, 'file')->onQueue('render');
        }

        if (!is_null($designId)) {
            $design = Design::query()->find($designId);

            if (is_null($design) || is_null($design->design_json) || $design->type !== DesignTypeEnum::PRINT) {
                return $this->errorResponse('Design is invalid');
            }

            if (!is_null($design->file_url_2) && !$force) {
                return $this->errorResponse('Design is rendered');
            }
            /*
            if ($force) {
                $orderProduct = OrderProduct::find($design->order_product_id);
                $options = json_decode($orderProduct->options, true);
                $color = $options['color'] ?? null;

                if ($orderProduct && !$orderProduct->personalized) {
                    $file = File::query()
                        ->where('product_id', $orderProduct->product_id)
                        ->where([
                            'type' => FileTypeEnum::DESIGN,
                            'option' => FileRenderType::PRINT
                        ])
                        ->where('print_space', $design->print_space)
                        ->whereNotNull('design_json')
                        ->first();

                    if ($file) {
                        $designJson = json_decode($file->design_json);

                        if ($color) {
                            $designJson->background = color2hex($color);
                            $designJson->custom_background = color2hex($color);
                        }

                        $design->design_json = json_encode($designJson, JSON_UNESCAPED_SLASHES);
                        $design->save();
                    }
                }
            }
            */
            [$designJson, $isChanged] = FulfillmentService::updateUrlOnDesignJson($design->design_json);
            if ($isChanged) {
                $design->design_json = $designJson;
                $design->save();
            }
            logToDiscord('Render design job: ' . $designId, 'job');
            RenderPrintJob::dispatch($design, 'design')->onQueue('render');
        }

        return $this->successResponse();
    }

    private static function updateCampaignSyncStatus(File $file, $productId, string $filePath, $campaignId): void
    {
        if (!$file->isDefault()) {
            return;
        }

        $product = Product::query()->firstWhere('id', $productId);

        if (!is_null($product)) {
            $product->update([
                'thumb_url' => $filePath,
                'sync_status' => 0
            ]);
        }

        $campaign = Campaign::query()
            ->firstWhere([
                'id' => $campaignId,
                'default_product_id' => $productId
            ]);

        if (!is_null($campaign)) {
            $campaign->update([
                'thumb_url' => $filePath,
                'sync_status' => 0
            ]);
        }
    }
}
