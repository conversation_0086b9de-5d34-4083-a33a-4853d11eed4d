<?php

namespace App\Http\Controllers;

use App\Models\ProductCollection;
use App\Traits\ApiResponse;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CollectionCampaignController extends Controller
{
    use ApiResponse;

    /**
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function index(Request $request): LengthAwarePaginator
    {
        $searchQuery = $request->get('q');
        $collections = ProductCollection::query()
            ->select(['collection.id', 'collection.name', 'collection.created_at'])
            ->leftJoin('collection', 'collection.id', '=', 'product_collection.collection_id')
            ->leftJoin('product', 'product.id', '=', 'product_collection.product_id')
            ->leftJoin('user', 'user.id', '=', 'product_collection.seller_id')
            ->where('product_collection.seller_id', currentUser()->getUserId())
            ->when($searchQuery, function ($query, $searchQuery) {
                $query->where('collection.name', 'like', '%' . $searchQuery . '%');
            });

        return $collections->paginate(15);
    }

    /**
     * @throws \Throwable
     */
    public function removeCampaign($collectionId, $campaignId): JsonResponse
    {
        $deleted = ProductCollection::query()
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'collection_id' => $collectionId,
                'product_id' => $campaignId
            ])
            ->delete();

        CampaignController::updateProductSyncStatus($campaignId);

        return $deleted ? $this->successResponse() : $this->errorResponse();
    }
}
