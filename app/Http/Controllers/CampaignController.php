<?php

namespace App\Http\Controllers;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\CacheKeys;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignRenderModeEnum;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\CurrencyEnum;
use App\Enums\CustomOptionTypeEnum;
use App\Enums\DataUpdateLogActionEnum;
use App\Enums\DataUpdateLogDataTypeEnum;
use App\Enums\DateRangeEnum;
use App\Enums\DiscordUserIdEnum;
use App\Enums\EventLogsTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\PersonalizedType;
use App\Enums\PricingModeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\PromptTypeEnum;
use App\Enums\QueueName;
use App\Enums\StorageDisksEnum;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Events\CampaignCreated;
use App\Exports\BulkUploadLogExport;
use App\Http\Controllers\Analytic3\SellerController;
use App\Http\Requests\BulkCreateCampaignV2Request;
use App\Http\Requests\Campaign\BulkAddToStoreRequest;
use App\Http\Requests\Campaign\SaveCampaignDesign;
use App\Http\Requests\Campaign\SlugRequest;
use App\Http\Requests\Campaign\UpdateProductRequest;
use App\Http\Requests\Campaign\UpdatePublicStatusRequest;
use App\Http\Requests\ChangeCampaignStatusRequest;
use App\Http\Requests\ChangeProductDefaultColorRequest;
use App\Http\Requests\CustomOptionMockupUploadRequest;
use App\Http\Requests\ReviewTrademarkCampaignRequest;
use App\Http\Requests\SaveProductThumbnailRequest;
use App\Http\Requests\SaveSellerCustomDesignRequest;
use App\Http\Requests\Seller\Campaign\CalcExtraPrintCostRequest;
use App\Jobs\ScanCampaignCopyright;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\AiPrompt;
use App\Models\BulkUploadLog;
use App\Models\Campaign;
use App\Models\Category;
use App\Models\Collection;
use App\Models\DataUpdateLog;
use App\Models\Elastic;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\IndexUserCleanSettings;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductCollection;
use App\Models\ProductPromotion;
use App\Models\ProductVariant;
use App\Models\SellerCollection;
use App\Models\Slug;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\SystemConfig;
use App\Models\Template;
use App\Models\TrademarkList;
use App\Models\TrademarkResult;
use App\Models\Upsell;
use App\Models\User;
use App\Responses\CustomPaginator;
use App\Rules\CheckExistsIdRule;
use App\Services\CampaignService;
use App\Services\ExpressMockupRenderService;
use App\Services\FulfillmentService;
use App\Services\InactiveService;
use App\Services\MailService;
use App\Services\SenPrintsAuth;
use App\Services\StoreService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Carbon\Carbon;
use Cloudinary\Cloudinary;
use Cloudinary\Transformation\Argument\Color;
use Cloudinary\Transformation\Effect;
use Cloudinary\Transformation\Format;
use Cloudinary\Transformation\ImageTransformation;
use Cloudinary\Transformation\Overlay;
use Cloudinary\Transformation\Reshape;
use Cloudinary\Transformation\Resize;
use Cloudinary\Transformation\Source;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignSystemStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Campaign\Jobs\SyncSlugJob;
use Modules\Campaign\Jobs\VerifyCampaignDesignAfterRenderJob;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\Campaign\Services\CustomCampaignService;
use Modules\Campaign\Services\ImportCampaignDataService;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;
use function DeepCopy\deep_copy;

class CampaignController extends BaseCampaignController
{
    use ApiResponse, ElasticClient;

    /**
     * @param Request $request
     * @return Paginator
     * @throws Throwable
     */
    public function index(Request $request): Paginator
    {
        $user = currentUser();
        $auth = User::query()->find($user->getUserId());

        $user->hasPermissionOrAbort('get_campaigns|manage_public_campaign');

        $isAdmin = $user->isAdmin();
        $tokenInactiveCampaign = $request->get('token_inactive_campaign');
        $limit = (int)$request->query('per_page', 15);
        $search = $request->get('q');
        $campaignId = $request->get('campaign_id');
        $email = $request->get('email');
        $collectionId = $request->get('collection');
        $campaignStatus = $request->get('campaign_status');
        $tmStatus = $request->get('tm_status');
        // set default
        $dateRange = $request->get('date_range_type', DateRangeEnum::TODAY);
        $orderBy = $request->get('sort_by');
        $publicStatus = $request->get('public_status');
        $sellerId = $request->query('seller_id');
        $storeId = $request->get('store');
        $isDesigner = $request->boolean('is_designer');
        $currentPage = $request->get('page');
        $campaignType = $request->get('type');
        $bulkType = $request->get('bulk_type');
        $currentPage = max($currentPage, 1);
        $includeFromMarketPlace = $request->boolean('include_from_market_place');
        $includeAnalytic = $request->boolean('include_analytic', true);
        $cacheAnalytic = $request->boolean('cache_analytic', true);

        if ($user->can('manage_public_campaign') && !$user->can('get_campaigns')) {
            if ($publicStatus === CampaignPublicStatusEnum::NO || $sellerId) {
                abort(403);
            }
        }

        if (!empty($tokenInactiveCampaign) && !empty($user->getUserId())) {
            $this->extendCleanAtInactiveCampaign($tokenInactiveCampaign, $user->getUserId());
        }

        if (!in_array($orderBy, array_merge(CampaignSortByAllowEnum::asArray(), EventLogsTypeEnum::asArray()))) {
            $orderBy = CampaignSortByAllowEnum::getDefault();
        }
        // for analytic
        $filterRequest = new Request();
        if ($dateRange === DateRangeEnum::CUSTOM) {
            $filterRequest['start_date'] = $request->get('start_date', date('Y-m-d'));
            $filterRequest['end_date'] = $request->get('end_date', date('Y-m-d'));
        }

        $arrListing = [
            'id',
            'name',
            'slug',
            'thumb_url',
            'status',
            'seller_id',
            'tm_status',
            'public_status_detail',
            'created_at',
            'product_type',
            'system_product_type',
            'personalized',
            'system_type',
            'system_product_type',
            'template_id',
            'template_name',
            'full_printed',
            'collections',
            'description',
            'attribute'
        ];
        $arrIdSortBy = [];

        // filter in Elastic
        $arrFilterElastic = [];

        // filter by search
        if (!empty($search)) {
            $search = cleanSpecialCharacters($search);
            $arrFilterElastic['search'] = $search;
        }

        // check search by campaign id or product id
        if (is_numeric($search)) {
            $seller = $email ? User::query()->whereKey($email)->first() : $auth;
            $obj = Product::query()
                ->onSellerConnection($seller)
                ->select([
                    'product_type',
                    'campaign_id',
                ])
                ->whereKey($search)
                ->when($user->isSeller(), function ($query) use ($user) {
                    $query->where('seller_id', $user->getUserId());
                })
                ->when($email, function ($query) use ($user, $email) {
                    $query->where('seller_id', $email);
                })
                ->first();

            if (!is_null($obj)) {
                switch ($obj->product_type) {
                    case ProductType::CAMPAIGN:
                    case ProductType::CAMPAIGN_TEMPLATE:
                    case ProductType::CAMPAIGN_EXPRESS:
                        $arrFilterElastic['id'] = $search;
                        unset($arrFilterElastic['search']);
                        break;
                    case ProductType::PRODUCT:
                        $arrFilterElastic['id'] = $obj->campaign_id;
                        unset($arrFilterElastic['search']);
                        break;
                }
            }
        }

        if (!empty($campaignId)) {
            $arrFilterElastic['id'] = $campaignId;
        }

        if (!empty($bulkType) && in_array($bulkType, ImportCampaignTypeEnum::asArray(), true)) {
            $arrFilterElastic['full_printed'] = [
                ProductPrintType::PRINT_2D
            ];
            if ($bulkType === ImportCampaignTypeEnum::REGULAR) {
                $arrFilterElastic['system_types'] = [
                    ProductSystemTypeEnum::REGULAR,
                    ProductSystemTypeEnum::EXPRESS,
                ];
            } else if (in_array($bulkType, [ImportCampaignTypeEnum::EXPRESS, ImportCampaignTypeEnum::MOCKUP], true)) {
                $campaignType = ProductType::CAMPAIGN_TEMPLATE;
            } else if ($bulkType === ImportCampaignTypeEnum::EMBROIDERED) {
                $arrFilterElastic['full_printed'] = [
                    ProductPrintType::EMBROIDERY
                ];
            }
        }

        if ($includeFromMarketPlace) {
            $publicStatus = CampaignPublicStatusEnum::APPROVED;
        }

        if (!empty($publicStatus)) {
            $arrFilterElastic['public_status_detail'] = $publicStatus;
            $excludeFlaggedSeller = false;
        } else {
            $excludeFlaggedSeller = true;
        }
        $searchAllIndex = true;
        // Check current user role
        if ($user->isSeller()) {
            // filter by seller id
            $arrFilterElastic['seller_id'] = $user->getUserId();
            $filterRequest['seller_id'] = $user->getUserId();
            $searchAllIndex = false;
        } elseif ($isAdmin) {
            if (!empty($email)) {
                $arrFilterElastic['seller_id'] = $email;
                $filterRequest['seller_id'] = $email;
            } elseif ($sellerId) {
                $arrFilterElastic['seller_id'] = $sellerId;
                $filterRequest['seller_id'] = $sellerId;
            }
            if ($isDesigner) {
                // filter by designer ids
                $designerIds = User::query()
                    ->select('id')
                    ->where('role', UserRoleEnum::DESIGNER)
                    ->whereNotIn('status', UserStatusEnum::getLimitedStatuses($excludeFlaggedSeller))
                    ->pluck('id')
                    ->toArray();

                $arrFilterElastic['seller_ids'] = $designerIds;
                $filterRequest['seller_id'] = $designerIds;
            }

            if (!empty($publicStatus) && $publicStatus === CampaignPublicStatusEnum::YES) {
                $arrFilterElastic['not_in_seller_ids'] = User::query()
                    ->select('id')
                    ->where('role', '<>', UserRoleEnum::CUSTOMER)
                    ->whereNotIn('status', UserStatusEnum::getLimitedStatuses($excludeFlaggedSeller))
                    ->where('custom_payment', '=', 1)
                    ->pluck('id')
                    ->toArray();
            }
        }

        if ($includeFromMarketPlace) {
            unset($arrFilterElastic['seller_id']);
        }

        if (!empty($campaignStatus)) {
            $arrFilterElastic['status'] = $campaignStatus;
        } elseif ($isAdmin) {
            $arrFilterElastic['not_status'] = CampaignStatusEnum::DRAFT;
        }

        if (!empty($tmStatus)) {
            if ($tmStatus === 'review_trademark') {
                $arrFilterElastic['tm_statuses'] = [
                    CampaignTrademarkStatusEnum::FLAGGED,
                    CampaignTrademarkStatusEnum::UNVERIFIED
                ];
            } else {
                $arrFilterElastic['tm_statuses'] = [
                    $tmStatus
                ];
            }
        }

        $authId = $user->getAuthorizedAccountId();
        if (!empty($storeId)) {
            if (!$isAdmin && !empty($authId)) {
                $store_ids = get_team_seller_stores($user->getUserId(), $authId);
                if (!empty($store_ids) && !in_array((int)$storeId, $store_ids, true)) {
                    $storeId = $store_ids[0];
                }
            }
            $isListAllMyCampaign = Store::where('id', $storeId)->value('list_all_my_campaigns');
            if (!$isListAllMyCampaign) {
                $arrFilterElastic['store_id'] = $storeId;
                $filterRequest['store_id'] = $storeId;

                $arrFilterElastic['collection_ids'] = Arr::pluck(StoreService::getCollectionIdsByStoreId($storeId), 'collection_id');
            }
        } else if (!$isAdmin && !empty($authId)) {
            $store_ids = get_team_seller_stores($user->getUserId(), $authId);
            if (!empty($store_ids)) {
                $arrFilterElastic['store_id'] = $store_ids;
                $filterRequest['store_id'] = $store_ids;
            }
        }

        $collections = null;
        if (!empty($collectionId)) {
            $arrFilterElastic['collection_id'] = $collectionId;
            $collections = Collection::query()->where('id', $collectionId)->get();
        }

        if (!empty($campaignType)) {
            switch ($campaignType) {
                case ProductType::CAMPAIGN:
                    $arrFilterElastic['product_type'] = ProductType::CAMPAIGN;
                    break;
                case ProductType::CAMPAIGN_TEMPLATE:
                    $arrFilterElastic['product_type'] = ProductType::CAMPAIGN_TEMPLATE;
                    break;
                case ProductType::CAMPAIGN_EXPRESS:
                    $arrFilterElastic['product_type'] = ProductType::CAMPAIGN;
                    $arrFilterElastic['system_product_type'] = ProductType::CAMPAIGN_EXPRESS;
                    break;
            }
        }


        $filterRequest['date_type'] = $dateRange;
        $controller = new SellerController();
        $controller->setCommonFilter($filterRequest);

        $typeAnalytic = 'campaign_id';

        // get analytic for ordering
        if ($includeAnalytic && $orderBy !== CampaignSortByAllowEnum::getDefault()) {
            $cacheTime = CacheKeys::CACHE_1H;
            // don't cache if today
            if ($dateRange === DateRangeEnum::TODAY || empty($cacheAnalytic)) {
                $cacheTime = 0;
            }
            $analyticsSorted = cacheAlt()->remember(
                CacheKeys::getCampaignAnalyticBySortAndDateRange($orderBy, $dateRange, $storeId),
                $cacheTime,
                function () use ($controller, $typeAnalytic, $orderBy) {
                    return $controller->getOverViewAndViews($typeAnalytic, $orderBy);
                }
            );
            if (!empty($analyticsSorted) && is_array($analyticsSorted) && in_array($orderBy, EventLogsTypeEnum::getArrayForAnalytic(), true)) {
                $analyticsSorted = collect($analyticsSorted)->sortByDesc($orderBy)->values()->toArray();
            }
            $arrIdSortBy = array_column($analyticsSorted, $typeAnalytic);
            $orderByFieldInElastic = false;
        } else {
            $orderByFieldInElastic = true;
        }
        if (!$user->can(['list_campaigns', 'manage_public_campaign'])) {
            $currentPage = 1;
            $limit = 15;
        }
        [$campaigns, $total] = (new Elastic())->getCampaign(
            $arrListing,
            $arrFilterElastic,
            $limit,
            $currentPage,
            $orderByFieldInElastic,
            $arrIdSortBy,
            $searchAllIndex
        );
        $uri = preg_replace("/([?&])page=\d+(&|$)/", '$1', $request->getRequestUri());
        if (empty($campaigns)) {
            return new CustomPaginator(
                [],
                0,
                $limit,
                $currentPage,
                [
                    'path' => $uri,
                    'collections' => $collections
                ]
            );
        }

        if ($includeAnalytic) {
            $campaignIds = Arr::pluck($campaigns, 'id');
            $filterRequest['campaign_id'] = $campaignIds;
            $controller->setCommonFilter($filterRequest);
            $analytics = $controller->getOverViewAndViews($typeAnalytic, $orderBy);
            $arrCampaignForSearching = $campaigns;

            // mapping analytic
            foreach ($analytics as $analytic) {
                // find by id
                $keyCampaign = null;
                foreach ($arrCampaignForSearching as $index => $each) {
                    if ($each['id'] === $analytic['campaign_id']) {
                        $keyCampaign = $index;
                        unset($arrCampaignForSearching[$index]);
                        break;
                    }
                }
                // prevent not found (but never happen unless this campaign had been deleted in MySQL but not in PostgreSQL)
                if (is_null($keyCampaign)) {
                    continue;
                }

                // ignore campaign_id column
                unset($analytic['campaign_id']);

                // mapping each attribute
                foreach ($analytic as $key => $value) {
                    $campaigns[$keyCampaign][$key] = $value;
                }
            }
        }

        // add relation seller
        if ($isAdmin) {
            $sellerIds = Arr::pluck($campaigns, 'seller_id');
            $sellers = User::query()
                ->select(
                    [
                        'id',
                        'name',
                        'email',
                        'custom_payment',
                    ]
                )
                ->whereIn('id', $sellerIds)
                ->get();

            foreach ($campaigns as &$campaign) {
                $campaign['seller'] = $sellers->firstWhere('id', $campaign['seller_id']);
            }
            unset($campaign);
        }
        unset($analytics, $arrCampaignForSearching);
        if (!$user->can(['list_campaigns', 'manage_public_campaign'])) {
            $total = $limit;
        }
        return new CustomPaginator(
            $campaigns,
            $total,
            $limit,
            $currentPage,
            [
                'path' => $uri,
                'collections' => $collections
            ]
        );
    }

    public function show($id, Request $request): JsonResponse
    {
        $id = (int)$id;
        if (empty($id)) {
            return $this->errorResponse();
        }
        $user = currentUser();

        if ($user->isSeller()) {
            $user->hasPermissionOrAbort('get_campaigns');
            $seller = currentUser()->getInfoAccess();
        } else {
            $seller = currentUser($request->get('seller_id'))->getInfoAccess();
        }
        $query = Campaign::query()
            ->onSellerConnection($seller)
            ->with(['products' => static function ($query) use ($seller) {
                $query->onSellerConnection($seller);
                $query->where('product_type', ProductType::PRODUCT);
                $query->with(['designs'])->orderBy('priority', 'asc');
            }])
            ->with(['designs' => function ($query) use ($seller) {
                $selectFields = [
                    'id',
                    'product_id',
                    'campaign_id',
                    'print_space',
                    'file_url',
                    'option'
                ];

                return $query->onSellerConnection($seller)
                    ->select($selectFields)
                    ->where([
                        'type' => FileTypeEnum::DESIGN,
                    ])
                    ->whereIn('option', [
                        FileRenderType::CUSTOM,
                        FileRenderType::PRINT,
                        FileRenderType::PB
                    ]);
            }])
            ->with([
                'stores' => function ($query) use ($seller) {
                    $query->select(['id', 'name']);
                    $query->where('store.seller_id', $seller->id);
                },
                'promotions:campaign_id,id,discount_code,end_time,start_time,status,type,used_count'
            ])
            ->with(['collections' => static function ($query) use ($seller) {
                $query->select(['id', 'name'])->where('product_collection.seller_id', data_get($seller, 'id'));
            }])
            ->with(['upsell' => static function ($query) use ($seller) {
                $query->select([
                    'product_id',
                    'type',
                    'upsell_collection_id',
                    'upsell_product_ids',
                    'collection.name as collection_name'
                ])
                    ->leftJoin('collection', 'collection.id', '=', 'upsell.upsell_collection_id') // to get collection name
                    ->where('upsell.seller_id', $seller->id);
            }]);

        if ($user->isAdmin()) {
            $query->with('seller');
        } else {
            $query->where('seller_id', $seller->id);
        }

        $result = $query->firstWhere('id', $id);
        if ($result === null) {
            return $this->errorResponse();
        }

        $filterRequest = new Request();
        $filterRequest['seller_id'] = $seller->id;
        $filterRequest['campaign_id'] = $id;
        $controller = new SellerController();
        $controller->setCommonFilter($filterRequest);
        $analytics = $controller->getOverViewAndViews('campaign_id');

        foreach ($analytics as $analytic) {
            // ignore campaign_id column
            unset($analytic['campaign_id']);

            // mapping each attribute
            foreach ($analytic as $key => $value) {
                $result->$key = $value;
            }
        }

        $result->processing_fee = getProcessingFee();

        $result->stores->makeHidden(['laravel_through_key']);
        $result->collections->makeHidden(['laravel_through_key']);

        $result->team_store_ids = get_team_seller_stores(currentUser()->getUserId(), currentUser()->getAuthorizedAccountId(), currentUser()->getInfo());

        // If this is an AI campaign, include the saved prompt
        if ($result->system_type === ProductSystemTypeEnum::AI_MOCKUP) {
            $savedPrompt = AiPrompt::query()
                ->where([
                    'campaign_id' => $id,
                    'seller_id' => $seller->id,
                    'type' => PromptTypeEnum::DESIGN
                ])
                ->first([
                    'prompt',
                    'print_space'
                ]);

            if ($savedPrompt) {
                $result->ai_mockup_data = [
                    'prompt' => $savedPrompt->prompt,
                    'print_space' => $savedPrompt->print_space
                ];
            }
        }

        // get all campaign ids from upsell
        $upsellCampaignIds = '';

        if (!empty($result->upsell)) {
            foreach ($result->upsell as $upsell) {
                if ($upsell->upsell_product_ids) {
                    $upsellCampaignIds .= $upsell->upsell_product_ids . ',';
                }
            }

            if ($upsellCampaignIds) {
                $upsellCampaignIds = rtrim($upsellCampaignIds, ',');
                $upsellCampaignIds = explode(',', $upsellCampaignIds);

                if (count($upsellCampaignIds) > 0) {
                    $upsellCampaignIds = array_unique($upsellCampaignIds);

                    // get name of upsell campaigns
                    $upsellMapping = Product::query()
                        ->onSellerConnection($seller)
                        ->whereIn('id', $upsellCampaignIds)
                        ->get(['id', 'name']);

                    $result->upsell_data = $upsellMapping;
                }
            }
        }
        $result->common_options = null;
        $isCampaignMockupCustom = in_array($result->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
        if ($result->personalized === PersonalizedType::CUSTOM_OPTION || $isCampaignMockupCustom) {
            $options = json_decode($result->options, true) ?? [];
            if (!empty($options['options'])) {
                if (isset($options['options']['text']) || isset($options['options']['dropdown']) || isset($options['options']['image'])) {
                    $newOptions = [];
                    foreach ($options['options'] as $k => $op) {
                        if ($op['active']) {
                            $op['type'] = $k;
                            $newOptions[] = $op;
                        }
                    }
                    $options['options'] = $newOptions;
                }
                $result->options = json_encode($options);
            }
            if (!empty($options['common_options'])) {
                $result->common_options = json_encode($options['common_options']);
            }
        }

        // Check sleeve printspaces
        $infoAccess = $user->getInfoAccess();
        if (isset($infoAccess) &&
            $infoAccess instanceof User &&
            !in_array('sleeve printspace', $infoAccess->getTags() ?? [], true)) {
            $result->products->map(function ($product) {
                CampaignService::refactorProductAdditionalPrintSpaces($product);
            });
        }

        if ($result->system_type === ProductSystemTypeEnum::AOP && $result->products->isNotEmpty()) {
            $result->products->map(function ($product) use ($id, $seller) {
                unset($product->designs);
                $product->designs = File::query()
                    ->onSellerConnection($seller)
                    ->select([
                        'id',
                        'product_id',
                        'campaign_id',
                        'print_space',
                        'file_url',
                    ])
                    ->where([
                        'campaign_id' => $id,
                        'product_id' => $product->id,
                        'option' => 'print',
                        'print_space' => PrintSpaceEnum::DEFAULT,
                        'type' => FileTypeEnum::DESIGN,
                    ])->get();
                return $product;
            });
        }

        return $this->successResponse($result);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $currentUser = currentUser();
        $seller = currentUser()->getInfoAccess();
        $connection = $seller->getPrivateConnection();

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->firstWhere([
                'id' => $id,
                'seller_id' => $seller->id
            ]);

        if (!$campaign) {
            return $this->errorResponse();
        }

        $rules = [
            'title' => ['required', 'string', 'max:200'],
            'slug' => ['required', 'string', 'max:200'],
            'description' => ['nullable'],
            'endTime' => ['nullable', 'date'],
            'pixel' => [],
            // nullable design_id for testing because design upload api still not working
            'default_product_id' => [
                'nullable',
                new CheckExistsIdRule('product', ['seller_id' => $seller->id], [], connection: $connection),
            ],
            'thumb_url' => ['string ', 'nullable'],
            'public_status' => ['required', 'boolean'],
            'collections' => ['nullable', 'array', 'max:50'],
            'storefronts' => ['nullable', 'array', 'max:50']
        ];
        if (!$seller->hasPrivateConnection()) {
            $rules['slug'][] = Rule::unique('product', 'slug')->ignore($id);
        }

        $storeIds = $request->json('storefronts');
        $teamSellerStoreIds = get_team_seller_stores($currentUser->getUserId(), $currentUser->getAuthorizedAccountId(), $currentUser->getInfo());

        if (!empty($teamSellerStoreIds) && $seller->isSeller()) {
            if (empty($storeIds)) {
                $rules['storefronts'] = ['required', 'array', 'max:50'];
            } else {
                foreach ($storeIds as $storeId) {
                    if (!in_array((int)$storeId, $teamSellerStoreIds, true)) {
                        $rules['storefronts'] = ['required', 'array', 'max:50'];
                        break;
                    }
                }
            }
        }

        // Add validation rules for AI Mockup customer upload options
        if ($campaign->system_type === ProductSystemTypeEnum::AI_MOCKUP) {
            $rules['allow_customer_upload_image'] = ['nullable', 'boolean'];
            $rules['customer_upload_image_label'] = ['nullable', 'string', 'max:50'];
        }

        $isCampaignMockupCustom = in_array($campaign->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
        $hasCustomOptions = $campaign->personalized === PersonalizedType::CUSTOM_OPTION || $isCampaignMockupCustom;
        if ($hasCustomOptions) {
            $nullable = $isCampaignMockupCustom ? 'nullable' : 'required';
            $rules = array_merge($rules, [
                'options.group.name' => 'max:120',
                'options.group.limit' => $nullable . '|min:1|max:20',
                'options.group.extra_custom_fee' => 'nullable|numeric|min:0',
                'options.options' => [
                    $nullable,
                    'array'
                ],
                'options.options.*.type' => [
                    $nullable,
                    Rule::in(CustomOptionTypeEnum::getValues())
                ],
                'options.options.*.label' => $nullable . '|max:120',
                'options.options.*.value' => function ($attribute, $value, $fail) use ($request) {
                    $type = $request->input(str_replace('.value', '.type', $attribute));

                    if ($type === CustomOptionTypeEnum::TEXT && strlen($value) > 120) {
                        $fail(__('The value may not be greater than 120 characters.'));
                    }

                    if ($type === CustomOptionTypeEnum::DROPDOWN) {
                        if (empty($value)) {
                            $fail(__('The select options field is required.'));
                        }

                        if (!is_array($value)) {
                            $fail(__('The select options must be an array.'));
                        }
                    }
                },
                'common_options.extra_custom_fee' => 'nullable|numeric|min:0',
                'common_options.options' => [
                    'nullable',
                    'array'
                ],
                'common_options.options.*.type' => [
                    'nullable',
                    Rule::in(CustomOptionTypeEnum::getValues())
                ],
                'common_options.options.*.label' => 'nullable|max:120',
                'common_options.options.*.value' => function ($attribute, $value, $fail) use ($request) {
                    $type = $request->input(str_replace('.value', '.type', $attribute));

                    if ($type === CustomOptionTypeEnum::TEXT && strlen($value) > 120) {
                        $fail(__('The value may not be greater than 120 characters.'));
                    }

                    if ($type === CustomOptionTypeEnum::DROPDOWN) {
                        if (empty($value)) {
                            $fail(__('The select options field is required.'));
                        }

                        if (!is_array($value)) {
                            $fail(__('The select options must be an array.'));
                        }
                    }
                },
            ]);
        }

        $validator = Validator::make($request->all(), $rules, [ // Custom message
            'endTime.max' => 'End time cannot be greater than :size characters.',
            'options.group.name.required' => 'The group name field is required.',
            'options.group.name.max' => 'The group name may not be greater than :max.',
            'options.group.limit.required' => 'The group limit field is required.',
            'options.group.limit.min' => 'The group limit must be at least :min.',
            'options.group.limit.max' => 'The group limit may not be greater than :max.',
            'options.group.extra_custom_fee.numeric' => 'The extra custom fee must be a number.',
            'options.group.extra_custom_fee.min' => 'The extra custom fee must be at least :min.',
            'options.options.required' => 'Custom option campaign needs at least one option.',
            'options.options.array' => 'The options must be an array.',
            'options.options.*.type.required' => 'The type field is required.',
            'options.options.*.type.in' => 'The selected type is invalid.',
            'options.options.*.label.required' => 'The label field is required.',
            'options.options.*.label.max' => 'The label may not be greater than :max characters.',
            'common_options.extra_custom_fee.numeric' => 'The extra custom fee must be a number.',
            'common_options.extra_custom_fee.min' => 'The extra custom fee must be at least :min.',
            'common_options.options.array' => 'The options must be an array.',
            'common_options.options.*.type.required' => 'The type field is required.',
            'common_options.options.*.type.in' => 'The selected type is invalid.',
            'common_options.options.*.label.required' => 'The label field is required.',
            'common_options.options.*.label.max' => 'The label may not be greater than :max characters.',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        // Update basic info
        #TODO: Add collection v2
        $collections = $request->json('collections');

        // remove old collection
        ProductCollection::query()
            ->where([
                'product_id' => $id,
                'seller_id' => $seller->id
            ])
            ->forceDelete();

        // add new collections
        if ($collections) {
            $collectionIds = [];

            foreach ($collections as $collection) {
                if (isset($collection['id'])) {
                    // kiem tra xem collection co trong db khong?
                    if (Collection::query()->where('id', $collection['id'])->exists()) {
                        $collectionIds[] = $collection['id'];
                    }
                    // neu khong co thi don't care
                } elseif (isset($collection['name'])) {
                    // neu ten da co trong db thi lay id
                    $exists = Collection::query()
                        ->select(['id', 'name'])
                        ->firstWhere('name', '=', $collection['name']);

                    if ($exists) {
                        // neu exist thi get id
                        $collectionIds[] = $exists['id'];
                    } else {
                        // neu chua co thi tao moi
                        $newCollection = Collection::addCollection($collection['name']);

                        if ($newCollection) {
                            $collectionIds[] = $newCollection->id;
                        }
                    }
                }
            }

            foreach ($collectionIds as $collectionId) {
                self::addCampaignToCollection($id, $seller->id, $collectionId);
            }
        }

        // Add store id to campaign id
        if ($storeIds) {
            $totalIds = count($storeIds);
            // Get total found by list id
            $totalRows = Store::query()
                ->whereIn('id', $storeIds)
                ->where('seller_id', $seller->id)
                ->count();

            if ($totalIds === $totalRows) {
                if (!empty($currentUser->getAuthorizedAccountId()) && !empty($teamSellerStoreIds)) {
                    StoreProduct::query()
                        ->where('product_id', $id)
                        ->whereIn('store_id', $teamSellerStoreIds)
                        ->delete();
                } else {
                    // Delete all store id of campaign from store campaign
                    StoreProduct::query()
                        ->where('product_id', $id)
                        ->delete();
                }
                $data = [];
                foreach ($storeIds as $storeId) {
                    $data[$storeId] = [
                        'store_id' => $storeId,
                        'product_id' => $id
                    ];
                }
                StoreProduct::query()->insertOrIgnore($data);
            }
        }

        // Update
        try {
            $campaignId = $campaign->id;
            $campaign->name = $request->json('title');
            if ($campaign->status !== CampaignStatusEnum::DRAFT) {
                $slug = $campaign->slug; // for cache key
            } else {
                $slug = $request->json('slug');
                $campaign->slug = strtolower($slug);
            }

            $campaign->description = $request->json('description');
            $campaign->end_time = $request->json('endTime');
            $campaign->tracking_code = $request->json('tracking_code');

            $defaultProductId = $request->json('default_product_id');

            $campaign->default_product_id = $defaultProductId;
            $campaign->show_countdown = $request->json('show_countdown');
            $campMetaData = $request->json('meta_data');
            if (empty($campaign->show_countdown)) {
                $campaign->show_countdown = 0;
            }

            if ($request->json('public_status')) {
                // do not merge to above condition
                // it's will create a new bug (toggle public_status)
                if (in_array($campaign->public_status, [
                    CampaignPublicStatusEnum::NO,
                    CampaignPublicStatusEnum::REJECTED
                ], true)) {
                    $campaign->public_status = CampaignPublicStatusEnum::YES;
                }
            } elseif (in_array($campaign->public_status, [
                CampaignPublicStatusEnum::YES,
                CampaignPublicStatusEnum::APPROVED
            ], true)) {
                $campaign->public_status = CampaignPublicStatusEnum::NO;
            }
            if (!empty($seller->custom_payment)) {
                $campaign->public_status = CampaignPublicStatusEnum::NO;
            }
            // get thumbnail from default product
            $defaultProduct = Product::query()
                ->onSellerConnection($seller)
                ->select(['thumb_url', 'price', 'old_price', 'default_option', 'template_id'])
                ->firstWhere('id', $defaultProductId);

            $thumbUrl = $request->input('thumb_url');

            if ($campaign->system_type === ProductSystemTypeEnum::COMBO // Camp combo dung  thumb cua seller thay vi thumb cua default product
                || (!empty($thumbUrl) && !str_starts_with($thumbUrl, 'p/'))) {
                $campaign->thumb_url = $thumbUrl;
            } else if (!is_null($defaultProduct)) {
                $campaign->thumb_url = $defaultProduct->thumb_url;
            }

            if (!is_null($defaultProduct)) {
                // set price & old price to display on campaign listing
                $campaign->price = $defaultProduct->price;
                $campaign->old_price = $defaultProduct->old_price;
                $campaign->template_id = $defaultProduct->template_id;
            }

            if ($hasCustomOptions) {
                $customOption = $request->input('options');
                $commonOptions = $request->input('common_options');
                if (!empty($customOption)) {
                    if (empty($customOption['group']['extra_custom_fee'])) {
                        $customOption['group']['extra_custom_fee'] = 0;
                    }
                    if (empty($customOption['options'])) {
                        $customOption = [];
                    }
                }
                if (!empty($commonOptions)) {
                    if (empty($commonOptions['extra_custom_fee'])) {
                        $commonOptions['extra_custom_fee'] = 0;
                    }
                    if (!empty($commonOptions['options'])) {
                        $customOption['common_options'] = $commonOptions;
                    }
                }
                if (!empty($customOption)) {
                    $campaign->personalized = PersonalizedType::CUSTOM_OPTION;
                }
                $campaign->options = json_encode($customOption, JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE);
            }
            // update public_status of all products in campaign
            Product::query()
                ->onSellerConnection($seller)
                ->whereIn('id', $campaign->products->pluck('id'))
                ->update([
                    'public_status' => $campaign->public_status,
                    'personalized' => $campaign->personalized,
                ]);

            $attributes = isset($campaign->attributes) ? json_decode($campaign->attributes, true) : [];
            $attributeImport = [];
            if (!empty($campMetaData['meta_title'])) {
                $attributeImport['meta_title'] = $campMetaData['meta_title'];
            }
            if (!empty($campMetaData['meta_description'])) {
                $attributeImport['meta_description'] = $campMetaData['meta_description'];
            }
            if (!empty($campMetaData['meta_keywords'])) {
                $attributeImport['meta_keywords'] = $campMetaData['meta_keywords'];
            }
            if (!empty($attributeImport)) {
                $attributes['meta_data'] = $attributeImport;
            }

            // Handle customer upload image options only for AI Mockup campaigns
            if ($campaign->system_type === ProductSystemTypeEnum::AI_MOCKUP) {
                $allowCustomerUploadImage = $request->json('allow_customer_upload_image', false);
                $customerUploadImageLabel = $request->json('customer_upload_image_label', 'Upload photo');

                if ((bool)$allowCustomerUploadImage) {
                    $attributes['customer_upload_options'] = [
                        'allow_customer_upload_image' => (bool)$allowCustomerUploadImage,
                        'customer_upload_image_label' => (string)$customerUploadImageLabel
                    ];

                    $campaign->personalized = PersonalizedType::CUSTOM_OPTION;
                } else {
                    unset($attributes['customer_upload_options']);

                    // Only set to NONE if there are no custom options
                    $hasCustomOptions = !empty($request->input('options'));

                    if (!$hasCustomOptions) {
                        $campaign->personalized = PersonalizedType::NONE;
                    }
                }
            }

            $campaign->attributes = json_encode($attributes);
            $campaign->temp_status = TempStatusEnum::DEFAULT;
            $campaign->save();

            // TODO: UPDATED DATA CAMPAIGN
            // Update data campaign in elasticsearch
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, sellerId: $seller?->id);
            // Insert data into table slugs
            (new SyncSlugJob(ids: [$campaign->id], seller: $seller, isUpsert: (bool)$campaign->slug))->handle();

            // clear cache
            $cacheKeys = [];
            $cacheKeys['tags'][] = CacheKeys::getCampaignId($campaign->id);
            $cacheKeys[] = CacheKeys::getProductCacheKey($slug);

            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
            clearHtmlCacheSeller($seller->id, [$campaignId]);

            return $this->successResponse('Success');
        } catch (Exception $ex) {
            return $this->errorResponse($ex->getMessage());
        }
    }

    public function downloadDesign($id)
    {
        if (currentUser()->isSeller() && !$this->isCampaignBelongSeller($id)) {
            return $this->errorResponse('Campaign not found');
        }
        $seller = currentUser()->getInfoAccess();
        $design = File::query()
            ->onSellerConnection($seller)
            ->where('campaign_id', $id)
            ->where('type', FileTypeEnum::DESIGN)
            ->where('option', [FileRenderType::CUSTOM, FileRenderType::PB])
            ->first();

        if (empty($design) || empty($design->file_url)) {
            return $this->errorResponse('Design not found');
        }

        $fileUrl = imgUrl($design->file_url, 'download_design');
        $response = Http::get($fileUrl);
        if ($response->failed()) {
            return $this->errorResponse('Failed to download the design');
        }
        $fileContent = $response->body();
        $fileName = basename($fileUrl);
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        $fileName = 'design_' . $id . '.' . $fileExtension;

        $contentType = match ($fileExtension) {
            'jpg', 'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            default => null,
        };

        if (empty($contentType)) {
            return $this->errorResponse('Unsupported file type');
        }

        return response($fileContent, 200, [
            'Content-Type' => $contentType,
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Content-Length' => strlen($fileContent)
        ]);
    }

    public function downloadFont($id): JsonResponse
    {
        if (currentUser()->isSeller() && !$this->isCampaignBelongSeller($id)) {
            return $this->errorResponse('Campaign not found');
        }
        $seller = currentUser()->getInfoAccess();
        $file = File::query()
            ->onSellerConnection($seller)
            ->where('campaign_id', $id)
            ->where('option', [FileRenderType::CUSTOM, FileRenderType::PB])
            ->firstOrFail();

        $design = json_decode($file->design_json, true);
        $objects = $design['objects'] ?? [];
        $font = null;
        foreach ($objects as $object) {
            if ($object['type'] === 'i-text') {
                $font = [
                    'fontFamily' => $object['fontFamily'],
                    'fontUrl' => $object['fontUrl'] ?? null,
                ];
                break;
            }
        }

        if (empty($font)) {
            return $this->errorResponse('Font not found');
        }

        if (empty($font['fontUrl'])) {
            return $this->errorResponse('Font url not found');
        }
        return $this->successResponse($font['fontUrl']);
    }

    private function isCampaignBelongSeller($id): bool
    {
        $seller = currentUser()->getInfoAccess();
        return Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', $id)
            ->where('seller_id', $seller->id)
            ->exists();
    }

    public function extendCleanAtInactiveCampaign($tokenInactiveCampaign, $userId): void
    {
        $userCleanSettings = IndexUserCleanSettings::query()
            ->where('seller_id', '=', $userId)
            ->where('token', '=', $tokenInactiveCampaign)
            ->first();

        if (!empty($userCleanSettings)) {
            $cleanAt = Carbon::parse($userCleanSettings->clean_at);
            $now = Carbon::now();

            //clean_at is not due yet
            if ($cleanAt > $now) {
                $newToken = InactiveService::generateToken();
                $extendTime = $now->clone()->addMonth();
                $userCleanSettings->update(['clean_at' => $extendTime, 'token' => $newToken]);

                graylogInfo("Extend clean_at time, seller_id: " . $userId, [
                    'category' => 'inactive_campaign',
                    'seller_id' => $userId,
                    'token' => $tokenInactiveCampaign,
                    'newToken' => $newToken,
                    'type' => 'Extend clean_at time',
                    'extendTime' => $extendTime->toDateTimeString()
                ]);
            }
        } else {
            graylogInfo("userCleanSettings doesn't exist", [
                'category' => 'inactive_campaign',
                'seller_id' => $userId,
                'token' => $tokenInactiveCampaign,
                'type' => 'Extend clean_at time'
            ]);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function removeProductDesign(Request $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $printSpace = $request->input('print_space');
        $seller = currentUser()->getInfoAccess();
        try {
            $deleted = File::query()
                ->onSellerConnection($seller)
                ->whereIn('type', [FileTypeEnum::DESIGN, FileTypeEnum::IMAGE])
                ->where([
                    'product_id' => $productId,
                    'print_space' => $printSpace
                ])
                ->delete();

            return $deleted ? $this->successResponse() : $this->errorResponse();
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function removeCampaignDesign(Request $request): JsonResponse
    {
        $campaignId = $request->input('campaign_id');
        $productIds = $request->input('product_ids');
        $printSpace = $request->input('print_space');

        if (empty($campaignId) || empty($printSpace)) {
            return $this->errorResponse('Data invalid');
        }

        if (empty($productIds)) {
            $productIds = [];
        }

        try {
            $seller = currentUser()->getInfoAccess();
            $deleted = File::query()
                ->onSellerConnection($seller)
                ->where([
                    'type' => FileTypeEnum::DESIGN,
                    'campaign_id' => $campaignId,
                    'print_space' => $printSpace
                ])
                ->when(count($productIds) > 0, function ($query) use ($productIds) {
                    $query->whereIn('product_id', $productIds);
                })
                ->delete();

            if ($deleted) {
                $campaign = Campaign::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'seller_id' => $seller->id,
                        'id' => $campaignId
                    ])
                    ->firstOrFail();

                // generate image file for product
                $products = Product::query()
                    ->onSellerConnection($seller)
                    ->with('images', function ($images) use ($seller) {
                        $images->onSellerConnection($seller);
                        $images->where('type_detail', FileRenderType::CUSTOM);
                    })
                    ->where([
                        'seller_id' => $seller->id,
                        'campaign_id' => $campaignId
                    ])
                    ->whereIn('id', $productIds)
                    ->get();

                $mockups = File::query()
                    ->where([
                        'type' => FileTypeEnum::MOCKUP,
                        'render_type' => FileRenderType::RENDER_3D
                    ])
                    ->whereIn('product_id', $products->pluck('template_id')->toArray())
                    ->orderBy('position')
                    ->get();

                // drop old imageFiles
                File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'campaign_id' => $campaign->id,
                        'type' => FileTypeEnum::IMAGE,
                    ])
                    ->whereIn('product_id', $products->pluck('id')->toArray())
                    ->whereNull('type_detail')
                    ->delete();

                $products->map(function ($product) use ($campaign, $mockups, $seller) {
                    if ($product->extra_print_cost > 0) {
                        $product->extra_print_cost = 0;
                        $product->save();
                    }

                    self::generateImageFilesProduct2($product, $campaign, $seller, $mockups->filter(fn($mockup) => $mockup->product_id = $product->template_id));
                });
            }

            return $deleted ? $this->successResponse() : $this->errorResponse();
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * Update status
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function updateStatus(Request $request): JsonResponse
    {
        $user = currentUser();

        $user->hasPermissionOrAbort('update_campaign');

        $userId = $user->getUserId();

        $validator = Validator::make($request->all(), [
            'campaign_id' => ['required', 'integer'],
            'status' => ['required', 'in:draft,active,inactive,reviewing,blocked,accepted']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $campaignId = $request->json('campaign_id');
        $status = $request->json('status');

        if ($user->isAdmin()) {
            $seller = User::query()->find($request->get('seller_id'));
        } else {
            $seller = currentUser()->getInfoAccess();
        }
        try {
            $trademark_result = null;
            if (in_array($status, ['blocked', 'accepted'], true)) {
                $trademark_result = TrademarkResult::query()
                    ->whereNotNull('flagged')
                    ->where('status', '!=', 'good')
                    ->where('campaign_id', $campaignId)
                    ->where('seller_id', $seller?->id)
                    ->orderByDesc('updated_at')
                    ->first();

                $campaign = Campaign::query()
                    ->onSellerConnection($seller)
                    ->find($trademark_result->campaign_id);
            }

            if ($status === 'accepted') {
                if (!empty($trademark_result)) {
                    if ($trademark_result->status === 'violated') {
                        $campaign->tm_status = null;
                        $campaign->sync_status = Product::SYNC_DATA_STATS_ENABLED;
                        $campaign->status = CampaignStatusEnum::ACTIVE;
                        $campaign->save();
                        // Sync campaign to elasticsearch
                        (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, sellerId: $seller?->id);
                    } else if (!empty($trademark_result->logos)) {
                        $texts = preg_replace('/\s*\([^)]*\)/', '', $trademark_result->logos);
                        $texts = explode(',', $texts);
                        foreach ($texts as $text) {
                            TrademarkList::query()->updateOrInsert(['text' => trim($text)], [
                                'accept_logo' => 1,
                                'block_logo' => 0,
                                'block_text' => 0,
                            ]);
                        }
                        try {
                            syncClearCache('trademarks');
                        } catch (\Throwable $e) {
                            return $this->errorResponse($e->getMessage());
                        }
                    }
                    $trademark_result->status = 'good';
                    $trademark_result->updated_at = currentTime();
                    $trademark_result->save();
                }
                return $this->successResponse();
            }
            $query = Product::query()
                ->onSellerConnection($seller)
                ->where(function ($query) use ($campaignId) {
                    $query->where('id', $campaignId)
                        ->orWhere('campaign_id', $campaignId);
                })
                ->when($user->isSeller(), function ($query) use ($userId) {
                    $query->where('seller_id', $userId);
                });

            if ($query->update(['status' => $status])) {
                $slug = Campaign::query()
                    ->onSellerConnection($seller)
                    ->where('id', $campaignId)
                    ->value('slug');

                // clear cache
                $cacheKey = CacheKeys::getProductCacheKey($slug);
                syncClearCache([$cacheKey], CacheKeys::CACHE_TYPE_ALTERNATIVE);
                clearHtmlCacheSeller($userId, [$campaignId]);

                (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, sellerId: $seller?->id);
                if (!empty($trademark_result->logos)) {
                    $texts = preg_replace('/\s*\([^)]*\)/', '', $trademark_result->logos);
                    $texts = explode(',', $texts);
                    foreach ($texts as $text) {
                        TrademarkList::query()->updateOrInsert(['text' => trim($text)], [
                            'block_logo' => 1,
                            'accept_logo' => 0,
                        ]);
                    }
                    try {
                        syncClearCache('trademarks');
                    } catch (\Throwable $e) {
                        return $this->errorResponse($e->getMessage());
                    }
                }
                return $this->successResponse();
            }
        } catch (Throwable $ex) {
            return $this->errorResponse($ex->getMessage());
        }

        return $this->errorResponse('Unknown errors.');
    }

    /**
     * Get campaign id
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function createDraftCampaign(Request $request): JsonResponse
    {
        $user = currentUser();
        if (!$this->checkCanCreate($user)) {
            return $this->errorResponse($this->getErrorMessageLimitCreating($user));
        }
        $personalized = $request->get('personalized', PersonalizedType::NONE);
        $personalized = (int)$personalized;

        if (!PersonalizedType::hasValue($personalized)) {
            return $this->errorResponse('Invalid personalized value');
        }

        $sellerId = $user->getUserId();

        $type = $request->get('type');
        $full_printed = $type === 'embroidered' ? ProductPrintType::EMBROIDERY : ProductPrintType::PRINT_2D;
        $system_type = ProductSystemTypeEnum::REGULAR;

        if (!empty($type) && ProductSystemTypeEnum::hasValue($type)) {
            $system_type = $type;
        }

        // switch: seller_id = 10102, auth_id = 10101
        // no switch: seller_id = auth_id = 10101
        $authId = $user->getAuthorizedAccountId();

        $userInfo = $user->getInfoAccess();

        $public_status = 'yes';
        if ($personalized === PersonalizedType::CUSTOM_OPTION ||
            in_array($system_type, [
                ProductSystemTypeEnum::CUSTOM,
                ProductSystemTypeEnum::MOCKUP,
                ProductSystemTypeEnum::COMBO
            ], true)) {
            $public_status = 'no';
        }

        $campaign = Campaign::query()
            ->onSellerConnection($userInfo)
            ->create([
                'name' => null, // Name is handle by model: Campaign > getCampaignNameAttribute()
                'seller_id' => $sellerId,
                'auth_id' => $authId ?? $sellerId,
                'market_location' => $userInfo->market_location,
                'pricing_mode' => $userInfo->pricing_mode,
                'currency_code' => $userInfo->currency,
                'product_type' => ProductType::CAMPAIGN,
                'status' => 'draft',
                'personalized' => $personalized,
                'system_type' => $system_type,
                'full_printed' => $full_printed,
                'public_status' => $public_status,
            ]);

        if ($campaign) {
            //log personalized info
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaign->id, sellerId: $userInfo?->id);

            return $this->successResponse([
                'id' => $campaign->id,
                'market_location' => $userInfo->market_location,
                'pricing_mode' => $userInfo->pricing_mode,
                'currency_code' => $userInfo->currency,
                'personalized' => $personalized,
                'type' => $system_type,
                'full_printed' => $full_printed,
                'combo_discount' => $system_type === ProductSystemTypeEnum::COMBO ? Campaign::COMBO_DISCOUNT : null
            ]);
        }

        return $this->errorResponse();
    }

    /**
     * Add collection for campaign
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function addCollection(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();

        $validator = Validator::make($request->all(), [
            'collections' => ['nullable', 'array']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $collections = $request->json('collections');

        if ($collections) {
            try {
                foreach ($collections as $collection) {
                    // Insert Collection
                    $newCollection = Collection::addCollection($collection);

                    if ($newCollection) {
                        // Insert Seller Collection
                        SellerCollection::query()
                            ->firstOrCreate([
                                'collection_id' => $newCollection->id,
                                'seller_id' => $userId
                            ]);
                    }
                }

                return $this->successResponse();
            } catch (Exception $ex) {
                return $this->errorResponse($ex->getMessage());
            }
        }

        return $this->successResponse();
    }

    /**
     * Check slug is available
     *
     * @param SlugRequest $request
     * @return JsonResponse
     */
    public function isSlugAvailable(SlugRequest $request): JsonResponse
    {
        return $this->successResponse(
            [
                'id' => $request->post('id'),
                'slug' => $request->post('slug'),
            ],
            'Slug is available.'
        );
    }

    /**
     * List seller stores
     */
    public function listStores(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();

        //Validate
        if ($user->isAdmin()) {
            $validator = Validator::make($request->all(), [
                'search' => ['nullable', 'min:3']
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'search' => ['nullable']
            ]);
        }

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $search = $request->get('search');

        $stores = Store::query()
            ->when($search, function ($query, $search) {
                $query->where('name', 'like', '%' . $search . '%');
            })
            ->where('store.seller_id', $userId)
            ->get(['id', 'name']);

        return $stores->isNotEmpty()
            ? $this->successResponse($stores)
            : $this->errorResponse();
    }

    /**
     * Get Products by campaign id
     */
    public function listCollections(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'search' => ['nullable', 'min:3']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $search = $request->get('search');
        $collections = Collection::query()
            ->when($search, function ($query, $search) {
                $query->where('name', 'like', '%' . $search . '%');
            })
            ->limit(15)
            ->orderBy('popularity', 'DESC')
            ->get(['id', 'name']);

        return $this->successResponse($collections);
    }

    /**
     * Move temp file on create campaign to original path
     */
    public function moveTempFileToOriginalPath(Request $request): JsonResponse
    {
        // request validate
        $validator = Validator::make($request->all(), [
            'file_path' => 'required',
            'campaign_id' => 'required'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        // check authorize
        $seller = currentUser()->getInfoAccess();
        $campaignId = $request->json('campaign_id');
        $campaignExists = Product::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ])
            ->exists();

        if ($campaignExists === false) {
            return $this->errorResponse('Unauthorized', 403);
        }

        $filePath = $request->json('file_path');

        if (preg_match('/^p\//', $filePath)) {
            return $this->successResponse('');
        }

        $validateSize = validateFileUploadedSize($filePath);
        if (!$validateSize['accept']) {
            return $this->errorResponse($validateSize['message']);
        }

        $fileName = pathinfo($filePath, PATHINFO_BASENAME);
        $newFilePath = 'p/' . $campaignId . '/' . $fileName;
        try {
            $movedPath = saveTempFileAws($filePath, $newFilePath);
            if (!empty($movedPath)) {
                return $this->successResponse(['file_path' => $movedPath], 'Move file successfully');
            }
            return $this->errorResponse('Move file failed', 403);
        } catch (Exception $ex) {
            Log::error($ex);
            return $this->errorResponse('Move file failed', 403);
        }
    }

    private function replaceFileUrlProcess(File $file, string $newFilePath): bool
    {
        $jsonData = json_decode($file->design_json, true); // true for decode to array

        if (!empty($jsonData) && !empty($jsonData['objects'])) {
            foreach ($jsonData['objects'] as $index => $object) {
                if ($object['type'] === FileTypeEnum::IMAGE) {
                    $jsonData['objects'][$index]['src'] = s3Url($newFilePath);
                }
            }
        }

        $designJson = json_encode($jsonData);
        return $file->update([
            'file_url' => $newFilePath,
            'design_json' => $designJson
        ]);
    }

    /**
     * @throws \JsonException
     */
    public function selectProducts(Request $request, $campaignId): JsonResponse
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $seller = currentUser()->getInfoAccess();

        $authId = $user->getAuthorizedAccountId();

        // Validate
        $validator = Validator::make($request->all(), [
            'products' => ['required', 'array', 'min:1']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ])
            ->first();

        if (!$campaign) {
            return $this->errorResponse('Access Denied.');
        }

        // Process
        $templateProducts = $request->json('products');

        if ($campaign->system_type === ProductSystemTypeEnum::CUSTOM && count($templateProducts) > 10) {
            return $this->errorResponse('You can only select up to 10 products.');
        }
        // Disable all product do not selected
        Product::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $sellerId
            ])
            ->whereNotIn('template_id', $templateProducts)
            ->delete();

        // Enable all product selected
        Product::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $sellerId
            ])
            ->whereIn('template_id', $templateProducts)
            ->restore();

        // Filter new and old product
        $query = Product::query()
            ->onSellerConnection($seller)
            ->select('template_id')
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $sellerId
            ]);
        $productList = $query->get();
        $listNewTemplateId = [];

        foreach ($templateProducts as $templateId) {
            if (!in_array($templateId, $productList->toArray(), true)) {
                $listNewTemplateId[] = $templateId;
            }
        }

        $newTemplateList = [];
        foreach ($listNewTemplateId as $templateId) {
            $query = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'campaign_id' => $campaignId,
                    'seller_id' => $sellerId,
                    'template_id' => $templateId
                ]);
            $result = $query->exists();

            if ($result === false) {
                // Create new
                $newTemplateList[] = $templateId;
            }
        }

        $insertProducts = [];
        foreach ($newTemplateList as $templateId) {
            $insertProducts[] = $this->getDataInsertProduct($templateId, $campaign);
        }

        if (count($insertProducts) > 0) {
            DB::beginTransaction();
            try {
                $created = [];

                foreach ($insertProducts as $insertProduct) {
                    $created[] = Product::query()
                        ->onSellerConnection($seller)
                        ->create($insertProduct)->refresh();
                }

                DB::commit();
                return $this->successResponse($created, 'Changed');
            } catch (Exception $ex) {
                DB::rollBack();
                return $this->errorResponse($ex->getMessage());
            }
        } else {
            $results = Product::query()
                ->onSellerConnection($seller)
                ->select(['id', 'template_id'])
                ->where('campaign_id', $campaignId)
                ->whereIn('template_id', $templateProducts)
                ->get();

            if ($results->count() > 0) {
                return $this->successResponse($results);
            }

            return $this->errorResponse('[2] An error occurred while retrieving product list.');
        }
    }

    /**
     * @throws \JsonException
     */
    public function selectProductsForComboCampaign(Request $request, $campaignId)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $seller = currentUser()->getInfoAccess();

        $validator = Validator::make($request->all(), [
            'products' => ['required', 'array', 'min:1']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ])
            ->first();

        if (!$campaign) {
            return $this->errorResponse('Access Denied.');
        }

        $templateProducts = $request->json('products');
        $ids = [];
        $templateIds = [];
        foreach ($templateProducts as $product) {
            if (!empty($product['id'])) {
                $ids[] = $product['id'];
            }
            $templateIds[] = $product['template_id'];
        }

        $productQuery = Product::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $sellerId
            ]);

        $productQuery->clone()
            ->whereNotIn('template_id', $templateIds)
            ->delete();
        $productQuery->clone()
            ->whereIn('template_id', $templateIds)
            ->restore();

        $insertProducts = [];
        foreach ($templateProducts as $product) {
            if (empty($product['id'])) {
                $insertProducts[] = $this->getDataInsertProduct($product['template_id'], $campaign);
            }
        }

        DB::beginTransaction();
        try {
            $created = [];

            foreach ($insertProducts as $insertProduct) {
                $new = Product::query()
                    ->onSellerConnection($seller)
                    ->create($insertProduct)
                    ->refresh();
                $ids[] = $new->id;
                $created[] = $new;
            }

            $productQuery->whereNotIn('id', $ids)->delete();

            DB::commit();
            return $this->successResponse($created, 'Changed');
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse($ex->getMessage());
        }
    }

    private function getDataInsertProduct($templateId, Campaign $campaign): array
    {
        $authId = currentUser()->getAuthorizedAccountId();
        $product = Product::query()
            ->where('product_type', ProductType::TEMPLATE)
            ->where('id', $templateId)
            ->first();

        if (!$product) {
            throw new \RuntimeException('Product not found');
        }

        $jsonOptions = json_decode($product->options, false, 512, JSON_THROW_ON_ERROR);
        if (isset($jsonOptions->color) && count($jsonOptions->color) > 0) {
            $jsonOptions->color = [$jsonOptions->color[0]];
        }
        $productId = $product->id;
        $name = $product->name;
        $defaultOption = $product->default_option;
        $thumbUrl = $product->thumb_url;
        $printSpaces = $product->print_spaces;
        $price = $product->price;
        $options = json_encode($jsonOptions, JSON_THROW_ON_ERROR);
        $sku = $product->sku;
        return [
            'name' => $name,
            'default_option' => $defaultOption,
            'campaign_id' => $campaign->id,
            'seller_id' => $campaign->seller_id,
            'auth_id' => $authId ?? $campaign->seller_id,
            'template_id' => $productId,
            'product_type' => ProductType::PRODUCT,
            'thumb_url' => $thumbUrl,
            'print_spaces' => $printSpaces,
            'price' => $price,
            'pricing_mode' => $product->pricing_mode,
            'status' => ProductStatus::DRAFT,
            'options' => $options,
            'sku' => $sku,
            'base_cost' => $product->base_cost,
            'shipping_cost' => $product->shipping_cost,
            'description' => $product->description,
            'description_ts' => $product->description_ts,
            'priority' => (int)$product->priority,
            'full_printed' => $product->full_printed,
            'personalized' => $campaign->personalized,
            'system_type' => $campaign->system_type
        ];
    }

    /**
     * @param $product
     * @param null $location
     * @return bool
     */
    private function generateVariants($product, $location = null): bool
    {
        $seller = currentUser()->getInfoAccess();
        // Get variants
        $variants = $this->getTemplateVariant($product);
        ProductVariant::query()
            ->onSellerConnection($seller)
            ->where('product_id', $product->id)
            ->delete();

        if ($variants->count() > 0) {
            $productPrice = $product->price;
            $productOldPrice = $product->old_price;
            if (is_null($location)) {
                $location = getLocationByCode($product->market_location);
            }
            $baseCost = getBaseCostsByLocation($product->template, $location);
            $arrInsert = [];
            foreach ($variants as $variant) {
                $variantBaseCost = getBaseCostsByLocation($variant, $location);
                $adjustPrice = $variantBaseCost - $baseCost;
                if ($variant->out_of_stock || ($product->pricing_mode !== PricingModeEnum::FIXED_PRICE && $adjustPrice > 0)) {
                    $price = $productPrice + $adjustPrice;
                    $oldPrice = $productOldPrice > 0 ? $productOldPrice + $adjustPrice : 0;

                    $newVariants = deep_copy($variant);
                    $newVariants->product_id = $product->id;
                    $newVariants->campaign_id = $product->campaign_id;

                    if (!$variant->out_of_stock) {
                        $newVariants->price = $price;
                        $newVariants->old_price = $oldPrice;
                        $newVariants->adjust_price = $adjustPrice;
                    } else {
                        $newVariants->price = 0;
                        $newVariants->adjust_price = 0;
                        $newVariants->old_price = 0;
                    }
                    $newVariants->base_cost = 0;
                    $newVariants->location_code = $product->market_location;
                    $arrInsert[] = $newVariants->toArray();
                }
            }
            ProductVariant::query()
                ->onSellerConnection($seller)
                ->insert($arrInsert);
        }

        return true;
    }

    public function updateCustomVariants($product)
    {
        $seller = currentUser()->getInfoAccess();
        $variantKeys = generateVariantKeysByProductOptions(json_decode($product->options, true));
        // Get variants
        $variants = ProductVariant::query()
            ->onSellerConnection($seller)
            ->where('product_id', $product->id)
            ->get();

        if ($variants->count() > 0) {
            foreach ($variants as $variant) {
                $variant->old_price = $product->old_price > 0 ? calculateCompareAtPrice($variant->price) : 0;
                $variant->save();
                if (($index = array_search($variant->variant_key, $variantKeys)) !== false) {
                    unset($variantKeys[$index]);
                }
            }
        }

        // Create missed variants
        if (count($variantKeys) > 0) {
            $variants = ProductVariant::query()
                ->onSellerConnection($seller)
                ->where([
                    'product_id' => $product->template_id,
                    'location_code' => $product->market_location
                ])
                ->whereIn('variant_key', $variantKeys)
                ->get();

            $arrInsert = [];
            foreach ($variants as $variant) {
                $variant->product_id = $product->id;
                $variant->campaign_id = $product->campaign_id;
                $variant->old_price = $product->old_price > 0 ? calculateCompareAtPrice($variant->price) : 0;
                $arrInsert[] = $variant->toArray();
            }
            ProductVariant::query()
                ->onSellerConnection($seller)
                ->insert($arrInsert);
        }

        return true;
    }

    private function getTemplateVariant($product)
    {
        $seller = currentUser()->getInfoAccess();
        return ProductVariant::query()
            ->onSellerConnection($seller)
            ->select([
                'variant_key',
                'sku',
                'out_of_stock',
                'adjust_price',
                'base_cost',
                'location_code'
            ])
            ->where([
                'product_id' => $product->template_id,
                'location_code' => $product->market_location
            ])
            ->when(
                $product->pricing_mode === PricingModeEnum::ADJUST_PRICE,
                function ($query) {
                    $query->where(
                        function ($q) {
                            $q->where('out_of_stock', 1)
                                ->orWhere('adjust_price', '>', 0);
                        }
                    );
                }
            )
            ->when(
                $product->pricing_mode === PricingModeEnum::FIXED_PRICE,
                function ($query) {
                    $query->where('out_of_stock', 1);
                }
            )
            ->get();
    }

    private function getTemplateProducts($type = null, $includeEmbroidered = false, $listTag = [])
    {
        $tag = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
        $categoryIds = [];
        if ($listTag) {
            $categoryIds = Category::query()
                ->select('id')
                ->whereIn('name', $listTag)
                ->pluck('id')
                ->toArray();
        }
        return cacheAlt()->tags([$tag])->remember(
            CacheKeys::SYSTEM_PRODUCT_TEMPLATES . '_' . $type . implode('_', $categoryIds),
            CacheKeys::CACHE_24H,
            function () use ($includeEmbroidered, $type, $listTag) {
                $categoriesCollection = Category::query()
                    ->select('id', 'name', 'full_name', 'slug')
                    ->where('show_dashboard', 1) // only load category to show on dashboard
                    ->orWhereIn('name', $listTag)
                    ->get();

                if ($categoriesCollection->isEmpty()) {
                    return collect();
                }

                $topTemplateIds = topTemplateIds();
                $templatesCount = count($topTemplateIds);

                $promotionProductModel = ProductPromotion::query()->where('end_time', '>=', now());
                $promotionProductIds = $promotionProductModel->pluck('product_id')->toArray();
                $promotionProducts = $promotionProductModel->get();
                if ($promotionProducts->isNotEmpty()) {
                    //on promotion products
                    $promotionCategory = new Category();
                    $promotionCategory->id = 999;
                    $promotionCategory->name = 'On Promotion';
                    $promotionCategory->full_name = 'On Promotion';
                    $promotionCategory->slug = 'on-promotion';
                    $categoriesCollection->push($promotionCategory);
                }
                return $categoriesCollection->map(function ($category) use ($includeEmbroidered, $promotionProductIds, $promotionProducts, $topTemplateIds, &$templatesCount, $type) {
                    $isPromotionTab = false;

                    if ($category->id === 999 && $category->slug === 'on-promotion') {
                        $product_ids = $promotionProductIds;
                        $isPromotionTab = true;
                    } else {
                        $product_ids = ProductCategory::query()->where('category_id', $category->id)->get()->pluck('product_id')->toArray();
                    }

                    $category->products = collect();
                    if (!empty($product_ids)) {
                        $category->products = Product::query()->select([
                            'id',
                            'name',
                            'sku',
                            'thumb_url',
                            'base_cost',
                            'base_costs',
                            'price as suggested_price',
                            'options',
                            'print_spaces',
                            'extra_print_cost',
                            'priority',
                            'mockup_type',
                            'attributes',
                            'pricing_mode',
                            'full_printed',
                            'status',
                            'market_location',
                            'system_type',
                            'quantity',
                        ])
                            ->where('product_type', ProductType::TEMPLATE)
                            ->where('status', ProductStatus::ACTIVE)
                            ->whereIn('id', $product_ids)
                            ->when($type === ProductSystemTypeEnum::AOP, function ($query) {
                                $query->where('full_printed', ProductPrintType::AOP);
                            })
                            ->when(!in_array($type, [ProductSystemTypeEnum::CUSTOM, 'custom_option', 'fulfill', 'all'], true), function ($query) {
                                $query->where('system_type', '!=', ProductSystemTypeEnum::CUSTOM);
                            })
                            // Với loại camp thêu thì chỉ lấy sp thêu,
                            // ngược lại cần loại bỏ sản phẩm thêu
                            ->when(
                                $type === 'embroidered',
                                static fn($q) => $q->where('full_printed', ProductPrintType::EMBROIDERY)
                            )
                            ->when(
                                empty($type) && !$includeEmbroidered,
                                static fn($q) => $q->where('full_printed', '!=', ProductPrintType::EMBROIDERY)
                            )
                            ->orderBy('priority')
                            ->orderBy('name')
                            ->get();
                    }
                    if ($category->products->isEmpty()) {
                        return $category;
                    }

                    if ($isPromotionTab) {
                        $category->products = $category->products->map(function ($product) use ($promotionProducts) {
                            $promotion = $promotionProducts->where('product_id', $product->id)->first();
                            if ($promotion) {
                                // add more promotion info (start_time, end_time)
                                $product->start_time = $promotion->start_time;
                                $product->end_time = $promotion->end_time;
                                $product->on_promotion = true;
                            }
                            return $product;
                        });
                    } else {
                        $promotionProducts = $promotionProducts->keyBy('product_id');
                        $category->products = $category->products->map(function ($product) use ($promotionProducts) {
                            $promotion = $promotionProducts->get($product->id);
                            if ($promotion) {
                                // add more promotion info (start_time, end_time)
                                $product->start_time = $promotion->start_time;
                                $product->end_time = $promotion->end_time;
                                $product->on_promotion = true;
                            }
                            return $product;
                        });
                    }

                    $sortedProducts = $category->products->sortBy(function ($template) use ($topTemplateIds, $templatesCount) {
                        $key = array_search($template->id, $topTemplateIds);

                        if ($key === false) {
                            return $templatesCount++;
                        }

                        return $key;
                    });
                    unset($category->products);

                    $sortedProducts->map(function ($product) {
                        $options = json_decode($product->options);
                        $product->custom_options = null;
                        if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options->custom_options)) {
                            $product->custom_options = $options->custom_options;
                            unset($options->custom_options);
                            $product->options = json_encode($options);
                        }
                        $colors = [];

                        // check if we have color option
                        if (isset($options->color) && count($options->color) > 0) {
                            foreach ($options->color as $color) {
                                $colors[] = [
                                    'name' => $color,
                                    'hex_code' => color2hex($color)
                                ];
                            }
                        }
                        $product->options = Str::isJson($product->options) ? json_decode($product->options, false, 512, JSON_THROW_ON_ERROR) : null;
                        $product->print_spaces = Str::isJson($product->print_spaces) ? json_decode($product->print_spaces, false, 512, JSON_THROW_ON_ERROR) : null;
                        $product->attributes = Str::isJson($product->attributes) ? json_decode($product->attributes, false, 512, JSON_THROW_ON_ERROR) : null;
                        $product->colors = $colors;
                        $product->mockups = [];
                        $product->selected = false;
                        return $product;
                    });

                    $category['products'] = $sortedProducts->values()->all();
                    return $category;
                });
            }
        );
    }

    public function listFullTemplateProducts(): JsonResponse
    {
        $templateProducts = $this->getTemplateProducts('all');
        if ($templateProducts->isEmpty()) {
            return $this->errorResponse('No template products found');
        }

        return $this->successResponse($templateProducts);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function listTemplateProducts(Request $request): JsonResponse
    {
        $hasFulfill = $request->boolean('has_fulfill');
        $hasFba = $request->boolean('has_fba');
        $type = $request->get('type');
        $infoAccess = currentUser()->getInfoAccess();
        if ($hasFulfill) {
            $systemTypes = array_filter(ProductSystemTypeEnum::getValues(), function ($value) {
                return $value !== ProductSystemTypeEnum::FULFILL_FBA;
            });
            $type = 'fulfill';
        } else if ($hasFba) {
            $systemTypes = [ProductSystemTypeEnum::FULFILL_FBA];
        } else if ($type === ProductSystemTypeEnum::AOP) {
            $systemTypes = [ProductSystemTypeEnum::REGULAR];
        } else if ($type === ProductSystemTypeEnum::CUSTOM) {
            $systemTypes = array_filter(ProductSystemTypeEnum::getValues(), function ($value) {
                return $value !== ProductSystemTypeEnum::FULFILL && $value !== ProductSystemTypeEnum::FULFILL_FBA;
            });
        } else {
            $systemTypes = array_filter(ProductSystemTypeEnum::getValues(), function ($value) {
                return $value !== ProductSystemTypeEnum::FULFILL && $value !== ProductSystemTypeEnum::FULFILL_FBA && $value !== ProductSystemTypeEnum::AOP;
            });
        }
        $templateProducts = $this->getTemplateProducts($type, $hasFba, listTag: $infoAccess?->getTags());
        if ($templateProducts->isEmpty()) {
            return $this->errorResponse('No template products found');
        }

        $templateProducts = $templateProducts->map(function ($category) use ($systemTypes, $infoAccess) {
            $products = [];
            foreach ($category['products'] as $product) {
                if (isset($infoAccess) && $infoAccess instanceof User && !in_array('sleeve printspace', $infoAccess?->getTags() ?? [], true)) {
                    CampaignService::refactorProductAdditionalPrintSpaces($product);
                }
                if (in_array($product->system_type, $systemTypes, true)) {
                    $products[] = $product;
                }
            }
            $category['products'] = $products;
            return $category;
        });
        $templateProducts = $templateProducts->filter(function ($category) {
            if ($category['products'] instanceof \Illuminate\Database\Eloquent\Collection) {
                return $category['products']->isNotEmpty();
            }
            return !empty($category['products']);
        });
        return $this->successResponse($templateProducts->values());
    }

    public function deleteProduct(Request $request, $campaignId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        // Process input data
        $templateId = $request->post('template_id');

        $exists = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'status' => ProductStatus::DRAFT,
                'seller_id' => $seller->id
            ])
            ->exists();

        if ($exists === false) {
            return $this->errorResponse('Access Denied.');
        }

        try {
            $deleted = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'template_id' => $templateId,
                    'seller_id' => $seller->id,
                    'campaign_id' => $campaignId
                ])
                ->delete();

            if ($deleted) {
                return $this->successResponse();
            }

            return $this->errorResponse('You can not delete this product.');
        } catch (Exception $ex) {
            return $this->errorResponse($ex->getMessage());
        }
    }

    /**
     * Tính phí in thêm cho sản phẩm
     *
     * @param \App\Http\Requests\Seller\Campaign\CalcExtraPrintCostRequest $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function calcExtraPrintCost(CalcExtraPrintCostRequest $request): JsonResponse
    {
        try {
            $products = $request->all();

            $typeTemplates = Product::typeTemplates(
                array_column($products, 'id')
            )->keyBy('id');

            $products = array_map(function ($product) use ($typeTemplates) {
                /** @var Product $templateProduct */
                if (!$templateProduct = $typeTemplates->get($product['id'])) {
                    return $this->errorResponse("Template product `${product['id']}` not found.");
                }

                $product['design'] = Product::correctDesigns($product['design'] ?? []);
                $product['template_extra_print_cost']
                    = $product['extra_print_cost']
                    = $templateProduct->calcExtraPrintCost($product['design']);

                return $product;
            }, $products);

            return $this->successResponse($products);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update product
     *
     * @param \App\Http\Requests\Campaign\UpdateProductRequest $request
     * @param                                                          $campaignId
     *
     * @return JsonResponse
     * @throws \Throwable
     */
    public function updateProducts(UpdateProductRequest $request, $campaignId): JsonResponse
    {
        // Get list product price
        $products = $request->json('products');
        $type = $request->json('type');
        $currencyCode = $request->json('currency_code', CurrencyEnum::USD);
        $pricingMode = $request->json('pricing_mode');
        $marketLocation = $request->json('market_location');
        $oldPrice = $request->json('old_price');
        $renderMode = $request->json('render_mode');
        $mockupType = $request->json('mockup_type');
        $isPersonalizedCamp = $request->json('personalized');
        $comboPrice = $request->json('combo_price');

        $seller = currentUser()->getInfoAccess();
        // Process input data
        // Validate
        $query = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ]);
        $campaign = $query->first();

        if (!$campaign) {
            return $this->errorResponse('Campaign not found.');
        }

        $currency = SystemConfigController::systemCurrencies();

        if ($currency) {
            $findCode = $currency->firstWhere('code', $currencyCode);

            if ($findCode) {
                $currencyCode = $findCode->code;
            }
        }
        $renderMode = !empty($renderMode) ? $renderMode : CampaignRenderModeEnum::NATURE;
        User::query()
            ->where('id', $seller->id)
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->update([
                'pricing_mode' => $pricingMode,
                'market_location' => $marketLocation,
                'currency' => $currencyCode,
            ]);

        $updateData = [
            'currency_code' => $currencyCode,
            'pricing_mode' => $pricingMode,
            'market_location' => $marketLocation,
            'old_price' => $oldPrice,
            'render_mode' => $renderMode,
            'mockup_type' => $mockupType
        ];

        if (!is_null($comboPrice) && $campaign->isCombo()) {
            $updateData['combo_price'] = $comboPrice;
        }

        if ($isPersonalizedCamp) {
            $updateData['personalized'] = $isPersonalizedCamp;
        }

        $query->update($updateData);

        if (!$products) {
            return $this->errorResponse('Product prices is required.');
        }

        try {
            //Delete designs that doesn't belong to in any product
            File::query()
                ->onSellerConnection($seller)
                ->where([
                    'campaign_id' => $campaignId,
                    'type' => FileTypeEnum::DESIGN,
                    'seller_id' => $seller->id
                ])->whereNotIn('product_id', collect($products)->pluck('insertedId'))
                ->delete();

            if (in_array($campaign->system_type, [ProductSystemTypeEnum::AOP, ProductSystemTypeEnum::CUSTOM], true)) {
                // Disable all product do not selected
                $templateIds = collect($products)->pluck('id');
                Product::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'campaign_id' => $campaignId,
                        'seller_id' => $seller->id
                    ])
                    ->whereNotIn('template_id', $templateIds)
                    ->delete();
                File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'campaign_id' => $campaignId,
                        'type' => FileTypeEnum::IMAGE,
                        'seller_id' => $seller->id
                    ])
                    ->whereNotIn('product_id', collect($products)->pluck('insertedId'))
                    ->delete();
            }

            // Lấy danh sách product trong camp
            $typeProducts = Product::typeProducts(
                array_column($products, 'insertedId')
            )->keyBy('id');

            // Lấy danh sách template trong camp
            $typeTemplates = Product::typeTemplates(
                array_column($products, 'id')
            )->keyBy('id');

            $changedProductAopDesign = [];
            if (!CampaignService::checkPersonalizedProductsMatched($products, $isPersonalizedCamp)) {
                return $this->errorResponse("Products's personalized are not matched.");
            }
            foreach ($products as $productIdx => $eachProduct) {
                /** @var Product $templateProduct */
                if (!$templateProduct = $typeTemplates->get($eachProduct['id'])) {
                    return $this->errorResponse("Template product `${eachProduct['id']}` not found.");
                }

                /** @var Product $product */
                if (!$product = $typeProducts->get($eachProduct['insertedId'])) {
                    return $this->errorResponse("Product `${eachProduct['insertedId']}` not found.");
                }

                $productPricing = get_base_min_max_price_of_product($templateProduct, $marketLocation, $currencyCode);
                $min_price = $productPricing['min_price'];
                $max_price = $productPricing['max_price'];
                $price = (float)$eachProduct['sell_price'];
                if ($price < $min_price || $price > $max_price) {
                    return $this->errorResponse('Can\'t update sell price for product id: ' . $eachProduct['id']);
                }

                // Create design file
                if (in_array($type, ['design', 'pricing'], true) && $campaign->status === CampaignStatusEnum::DRAFT) {
                    $designs = Product::correctDesigns($eachProduct['design'] ?? []);
                    $countDesigns = $designs->count();
                    if ($campaign->system_type !== ProductSystemTypeEnum::AOP && $countDesigns > 0) {
                        // delete design files by passed print spaces to prevent lost design
                        File::deleteCampaignProductDesigns(
                            $campaignId,
                            $eachProduct['insertedId'],
                            collect($eachProduct['design'])->pluck('print_space')->toArray()
                        );

                        // loop designs to create design files
                        foreach ($designs as $design) {
                            // use firstOrCreate to prevent duplicate designs
                            $newFileCreated = File::query()
                                ->onSellerConnection($seller)
                                ->firstOrCreate([
                                    'product_id' => $eachProduct['insertedId'],
                                    'type' => FileTypeEnum::DESIGN,
                                    'option' => Arr::get($design, 'option', FileRenderType::PRINT),
                                    'print_space' => Arr::get($design, 'print_space'),
                                    'campaign_id' => $campaignId,
                                ], [
                                    'type' => FileTypeEnum::DESIGN,
                                    'product_id' => $eachProduct['insertedId'], // product inserted id
                                    'campaign_id' => $campaignId,
                                    'print_space' => Arr::get($design, 'print_space'),
                                    'option' => Arr::get($design, 'option', FileRenderType::PRINT),
                                    'file_url' => $design['url'],
                                    'design_json' => Arr::get($design, 'design_json'),
                                    'type_detail' => $design['type_detail'] ?? null,
                                    'seller_id' => $seller->id,
                                    'position' => 0,
                                ]);
                        }
                    }
                    $product->extra_print_cost = $templateProduct->calcExtraPrintCost($designs);
                    $product->save();
                }

                if ($campaign->system_type === ProductSystemTypeEnum::AOP && !empty($eachProduct['changed_design'])) {
                    $changedProductAopDesign[] = $eachProduct['insertedId'];
                }

                // Update product info
                $product = Product::query()
                    ->onSellerConnection($seller)
                    ->with('images', function ($query) use ($seller) {
                        $query->onSellerConnection($seller);
                        $query->where('type_detail', FileRenderType::CUSTOM);
                    })
                    ->firstWhere([
                        'id' => $eachProduct['insertedId'],
                        'seller_id' => $seller->id,
                        'campaign_id' => $campaignId
                    ]);

                if (is_null($product)) {
                    return $this->errorResponse('Can\'t update price for product id: ' . $eachProduct['id']);
                }
                $updateData = [];
                $updateData['currency_code'] = $currencyCode;

                if ($templateProduct->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                    $updateData['pricing_mode'] = PricingModeEnum::CUSTOM_PRICE;
                    $product->pricing_mode = PricingModeEnum::CUSTOM_PRICE;
                } else {
                    $updateData['pricing_mode'] = $pricingMode;
                    $product->pricing_mode = $pricingMode;
                }

                $updateData['market_location'] = $marketLocation;
                $product->market_location = $marketLocation;
                $updateData['price'] = $price;
                $updateData['old_price'] = $eachProduct['old_price'];
                $updateData['render_mode'] = $renderMode;
                $updateData['mockup_type'] = $eachProduct['mockup_type'];
                if (isset($eachProduct['personalized'])) {
                    $updateData['personalized'] = $eachProduct['personalized'];
                }

                if (isset($eachProduct['default_option'])) {
                    $updateData['default_option'] = $eachProduct['default_option'];
                }

                if (isset($eachProduct['old_price'])) {
                    $updateData['old_price'] = $eachProduct['old_price'];
                }

                if (isset($eachProduct['default_mockup_id'])) {
                    $updateData['default_mockup_id'] = $eachProduct['default_mockup_id'];
                }

                $updateData['priority'] = $productIdx + 1;
                if (isset($eachProduct['priority'])) {
                    $updateData['priority'] = $eachProduct['priority'];
                }

                // check if we have selected colors
                $colors = [];
                if (isset($eachProduct['selectedColors'])) {
                    foreach ($eachProduct['selectedColors'] as $color) {
                        $colors[] = $color['name'];
                    }
                }

                if (count($colors) > 0) {
                    $options = json_decode($product->options, true);
                    $options['color'] = $colors;
                    $updateData['options'] = json_encode($options, JSON_THROW_ON_ERROR);
                }

                if ($isPersonalizedCamp === PersonalizedType::CUSTOM_OPTION) {
                    $updateData['personalized'] = PersonalizedType::CUSTOM_OPTION;
                }
                // Update for campaign custom mockups
                if (!empty($eachProduct['default_thumb'])) {
                    $updateData['thumb_url'] = $eachProduct['default_thumb'];
                }
                // Update system type for products
                $updateData['system_type'] = $campaign->system_type;

                $product->update($updateData);

                if ($campaign && isset($eachProduct['color_edited']) && $campaign->system_type !== ProductSystemTypeEnum::CUSTOM && $eachProduct['full_printed'] !== ProductPrintType::HANDMADE) {
                    self::generateImageFilesProduct($product, $campaign);
                }
            }
            if (!empty($changedProductAopDesign) && !in_array($campaign->status, [CampaignStatusEnum::DRAFT, CampaignStatusEnum::PENDING], true)) {
                if (count($products) === count($changedProductAopDesign)) {
                    CustomCampaignService::createMockupForAopCampaign($campaignId);
                } else {
                    foreach ($changedProductAopDesign as $productId) {
                        CustomCampaignService::createMockupForAopCampaign($campaignId, $productId);
                    }
                }
            }

            return $this->successResponse();
        } catch (Exception $ex) {
            return $this->errorResponse($ex->getMessage());
        }
    }

    /**
     * Bulk update
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        try {
            $user = currentUser();
            $user->hasPermissionOrAbort('update_campaign|manage_public_campaign');
            $validator = Validator::make($request->all(), [
                'seller_campaign_ids' => [
                    'required',
                    'array',
                    'max:500'
                ],
                'status' => ['nullable', 'in:draft,active,inactive,reviewing,blocked']
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }

            $sellerCampaignIds = $request->json('seller_campaign_ids', []);
            $status = $request->json('status');
            $totalCampaigns = count($sellerCampaignIds);
            if ($sellerCampaignIds && $totalCampaigns > 0) {
                $sellerCampaignIds = collect($sellerCampaignIds)
                    ->groupBy('seller_id')
                    ->map(fn($items) => $items->pluck('id'))
                    ->toArray();
                $sellerIds = array_keys($sellerCampaignIds);
                $cacheKeys = [];
                User::query()->whereIn('id', $sellerIds)->get()->each(function ($seller) use (&$cacheKeys, $sellerCampaignIds, $status, $user) {
                    $campaignIds = data_get($sellerCampaignIds, $seller->id);
                    $campaigns = Campaign::query()
                        ->onSellerConnection($seller)
                        ->select(['id', 'slug'])
                        ->whereIn('id', $campaignIds)
                        ->where('status', '<>', $status)
                        ->get()
                        ->each(function ($campaign) use (&$cacheKeys) {
                            $cacheKeys[] = CacheKeys::PRODUCT_PREFIX . $campaign['slug'];
                        });
                    $campaignIds = $campaigns->pluck('id')->toArray();
                    $sellerId = $seller->id;
                    if ($user->isAdmin()) {
                        $sellerId = User::SENPRINTS_SELLER_ID;
                        if ($status === ProductStatus::BLOCKED) {
                            MailService::sendNotificationBlockedCampaign($campaignIds, $seller);
                        }
                        $this->staffLogModifyCampaignStatus($campaignIds, $status);
                    }
                    Product::query()
                        ->onSellerConnection($seller)
                        ->whereIn('id', $campaignIds)
                        ->orWhereIn('campaign_id', $campaignIds)
                        ->update(['status' => $status]);
                    self::updateProductSyncStatus($campaignIds, true, sellerId: $seller->id);
                    clearHtmlCacheSeller($sellerId, $campaignIds);
                });

                if (count($cacheKeys) <= 0) {
                    return $this->errorResponse('No valid campaign found.');
                }

                syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
                return $this->successResponse(null, 'Updated total ' . count($cacheKeys) . ' campaign(s).');

            }
            return $this->successResponse();
        } catch (Exception $e) {
            logException($e, 'CampaignController@bulkUpdate');
            return $this->errorResponse('Update failed.');
        }
    }

    private function staffLogModifyCampaignStatus(array $campaignIds, string $status): void
    {
        $user = currentUser();
        if ($user->isAdmin()) {
            $reportedCampaigns = Campaign::query()
                ->select(['id', 'status'])
                ->whereIn('id', $campaignIds)
                ->get();
            foreach ($reportedCampaigns as $reported) {
                DataUpdateLog::query()
                    ->create([
                        'action' => DataUpdateLogActionEnum::CHANGE_CAMPAIGN_STATUS,
                        'data_type' => DataUpdateLogDataTypeEnum::REPORTED_CAMPAIGN,
                        'status' => 1,
                        'data' => json_encode([
                            'admin' => $user->getEmail() ?? $user->getUserId(),
                            'from' => $reported->status,
                            'to' => $status,
                        ]),
                        'data_id' => $reported->id,
                    ]);
            }
        }
    }

    /**
     * Get Seller Id List
     * @param Request $request
     * @return JsonResponse
     */
    public function listSellerId(Request $request): JsonResponse
    {
        $keyword = $request->get('keyword');
        $options = [
            'q' => $keyword,
            'select' => ['id', 'email', 'name']
        ];
        $result = User::getSellers($options, 15, false);

        return $result ? $this->successResponse($result) : $this->errorResponse();
    }

    /**
     * Save up-sell data and set campaign status to active
     *
     * @param Request $request
     * @param $campaignId
     * @return JsonResponse
     * @throws Throwable
     */
    public function launchCampaign(Request $request, $campaignId): JsonResponse
    {
        // $request->validate(['promotions' => 'bail|required']);
        $seller = currentUser()->getInfoAccess();

        $campaignId = (int)$campaignId;
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->firstWhere([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ]);

        if (is_null($campaign)) {
            return $this->errorResponse('Campaign not found.');
        }

        // save auto-upselling
        $promotions = $request->post('promotions');
        $arrUpsell = [];
        $related = [];
        $shopping = [];
        $postSale = [];

        // to check authorization
        $upsellProductIds = [];

        // check related
        if (isset($promotions['related_campaign']) && count($promotions['related_campaign']) > 0) {
            $relatedCampaign = array_filter($promotions['related_campaign']);
            $upsellProductIds = [...$relatedCampaign];
            $related['upsell_product_ids'] = implode(',', $relatedCampaign);
        } else {
            $related['upsell_product_ids'] = null;
        }

        if (!empty($promotions['related_collection'])) {
            $related['upsell_collection_id'] = (int)$promotions['related_collection'];
        } else {
            $related['upsell_collection_id'] = null;
        }

        if ($related['upsell_product_ids'] || $related['upsell_collection_id']) {
            $related['product_id'] = $campaignId;
            $related['seller_id'] = $seller->id;
            $related['type'] = 'related';
            $arrUpsell[] = $related;
        }

        // check shopping
        if (isset($promotions['shopping_campaign']) && count($promotions['shopping_campaign']) > 0) {
            $shoppingCampaign = array_filter($promotions['shopping_campaign']);
            $upsellProductIds = [...$upsellProductIds, ...$shoppingCampaign];
            $shopping['upsell_product_ids'] = implode(',', $shoppingCampaign);
        } else {
            $shopping['upsell_product_ids'] = null;
        }

        if (!empty($promotions['shopping_collection'])) {
            $shopping['upsell_collection_id'] = (int)$promotions['shopping_collection'];
        } else {
            $shopping['upsell_collection_id'] = null;
        }

        if ($shopping['upsell_product_ids'] || $shopping['upsell_collection_id']) {
            $shopping['product_id'] = $campaignId;
            $shopping['seller_id'] = $seller->id;
            $shopping['type'] = 'cart';
            $arrUpsell[] = $shopping;
        }

        // check post-sale
        if (isset($promotions['post_sale_campaign']) && count($promotions['post_sale_campaign']) > 0) {
            $postSaleCampaign = array_filter($promotions['post_sale_campaign']);
            $upsellProductIds = [...$upsellProductIds, ...$postSaleCampaign];
            $postSale['upsell_product_ids'] = implode(',', $postSaleCampaign);
        } else {
            $postSale['upsell_product_ids'] = null;
        }

        if (!empty($promotions['post_sale_collection'])) {
            $postSale['upsell_collection_id'] = (int)$promotions['post_sale_collection'];
        } else {
            $postSale['upsell_collection_id'] = null;
        }

        if ($postSale['upsell_product_ids'] || $postSale['upsell_collection_id']) {
            $postSale['product_id'] = $campaignId;
            $postSale['seller_id'] = $seller->id;
            $postSale['type'] = 'post_sale';
            $arrUpsell[] = $postSale;
        }

        if (count($upsellProductIds) > 0 && !self::isValidUpsellCampaigns($upsellProductIds, $seller->id)) {
            return $this->errorResponse('Invalid up-sell campaign.');
        }

        // save up-sell data
        $upsells = Upsell::query()
            ->where('product_id', $campaignId)
            ->where('seller_id', $seller->id)
            ->get()
            ->toArray();

        try {
            Upsell::query()
                ->where('product_id', $campaignId)
                ->where('seller_id', $seller->id)
                ->delete();

            Upsell::query()->insert($arrUpsell);
        } catch (Throwable $e) {
            // insert old data
            Upsell::query()->insert($upsells);
        }

        // clear cache
        $cacheKeys = [];
        $cacheKeys['tags'][] = CacheKeys::getCampaignId($campaign->id);
        $cacheKeys[] = CacheKeys::PRODUCT_PREFIX . $campaign->slug;
        syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);

        // change campaign status to active
        if (!$request->post('save_draft') && (!$request->post('is_edit') || $request->post('is_draft'))) {
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->firstWhere([
                    'id' => $campaignId,
                    'seller_id' => $seller->id
                ]);

            if (!$campaign) {
                return $this->errorResponse('Campaign is not exists.');
            }

            if ($campaign->tm_status === CampaignTrademarkStatusEnum::TM) {
                $updated = Product::query()
                    ->onSellerConnection($seller)
                    ->where('id', $campaignId)
                    ->orWhere('campaign_id', $campaignId)
                    ->update(['status' => ProductStatus::BLOCKED]);
                if ($updated) {
                    (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, 1, sellerId: $seller?->id);
                }
                return $this->errorResponse('Your campaign has been blocked due to trademark.');
            }

            if ($campaign->status !== ProductStatus::DRAFT || empty($campaign->name) || empty($campaign->slug)) {
                return $this->errorResponse('Campaign is not ready to launch');
            }

            try {
                $updateData = ['status' => ProductStatus::ACTIVE];
                if ((int)$campaign->personalized === PersonalizedType::CUSTOM_OPTION || $campaign->system_type === ProductSystemTypeEnum::CUSTOM || $campaign->system_type === ProductSystemTypeEnum::MOCKUP) {
                    $updateData['public_status'] = 'no';
                }
                // change campaign and all products of this campaign to active
                if ($campaign->system_type === ProductSystemTypeEnum::AOP) {
                    $updateData = ['status' => ProductStatus::PENDING];
                }

                $updated = Product::query()
                    ->onSellerConnection($seller)
                    ->where('id', $campaignId)
                    ->orWhere('campaign_id', $campaignId)
                    ->update($updateData);

                // update score
                $arrayScore = getScoreProductBySales();
                Product::query()
                    ->onSellerConnection($seller)
                    ->where('id', $campaignId)
                    ->orWhere('campaign_id', $campaignId)
                    ->where('score', 0)
                    ->update(
                        [
                            'time_score' => $arrayScore['timestamp'],
                            'score' => $arrayScore['score'],
                        ]
                    );

                // increment + 1 score default product id
                Product::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'id' => $campaign->default_product_id,
                        'campaign_id' => $campaignId
                    ])
                    ->increment('score');

                if ($campaign->system_type === ProductSystemTypeEnum::AOP) {
                    $createdMockup = CustomCampaignService::createMockupForAopCampaign($campaignId, seller: $seller);
                    if (!$createdMockup['status']) {
                        return $this->errorResponse($createdMockup['message']);
                    }
                }
                if ($updated) {
                    // Sync campaign to Elasticsearch
                    // Do this later because product has no image rendered
                    (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, 1, sellerId: $seller?->id);
                    SyncSlugJob::dispatchSync(ids: [$campaignId], seller: $seller);
                    CampaignCreated::dispatch($campaignId, $seller);
                    UserService::updateStepProductTour($seller->id, 'manage-order');
                    return $this->successResponse();
                }
            } catch (Exception $ex) {
                logToDiscord('Launch campaign error: ' . $ex->getMessage());
                return $this->errorResponse();
            }

            return $this->errorResponse();
        }

        return $this->successResponse();
    }

    private static function isValidUpsellCampaigns(array $campaignIds, $userId): bool
    {
        $seller = currentUser()->getInfoAccess();

        // should not contain campaign of other seller
        return Campaign::query()
                ->onSellerConnection($seller)
                ->where('seller_id', '<>', $userId)
                ->whereIn('id', $campaignIds)
                ->count() === 0;
    }

    /**
     * Update multi-campaigns
     *
     * @param Request $request
     * @param array $data
     * @return JsonResponse
     * @throws Throwable
     */
    protected function updateBulk(Request $request, array $data): JsonResponse
    {
        $ids = explode(',', $request->post('ids'));
        $seller = currentUser()->getInfoAccess();
        $updated = Campaign::query()
            ->onSellerConnection($seller)
            ->where('seller_id', currentUser()->getUserId())
            ->whereIn('id', $ids)
            ->update($data);

        self::updateProductSyncStatus($ids, true, sellerId: $seller->id);

        return $updated > 0 ? $this->successResponse() : $this->errorResponse();
    }

    /**
     * Bulk edit campaigns
     *
     * @param Request $request
     * @return JsonResponse
     * @throws \JsonException|Throwable
     */
    public function bulkEdit(Request $request): JsonResponse
    {
        $request->validate(['ids' => 'bail|required|string']);

        $data = [];
        $title = $request->post('title');

        if ($title) {
            $data['name'] = $title;
        }

        $description = $request->post('description');

        if ($description) {
            $data['description'] = $description;
        }

        $endTime = $request->post('end_time');

        if ($endTime) {
            $data['end_time'] = $endTime;
        }

        $countdown = $request->post('show_countdown');

        if ($countdown) {
            $data['show_countdown'] = $countdown;
        }

        $facebookPixel = $request->post('facebook_pixel');

        if ($facebookPixel) {
            $data['tracking_code'] = json_encode(['facebook_pixel' => $facebookPixel], JSON_THROW_ON_ERROR);
        }

        return $this->updateBulk($request, $data);
    }

    public static function addCampaignToCollection($productId, $sellerId, $collectionId): bool
    {
        $seller = User::query()->find($sellerId);
        $collection = Collection::query()->find($collectionId);
        $product = Product::query()
            ->onSellerConnection($seller)
            ->find($productId);
        if (!$collection || !$product) {
            return false;
        }

        // use upsert instead of firstOrCreate to not make duplicate exception for multiple connection
        // and faster for not make one more query select
        SellerCollection::query()->upsert([
            'collection_id' => $collectionId,
            'seller_id' => $sellerId
        ], ['collection_id', 'seller_id']);

        ProductCollection::query()->upsert([
            'collection_id' => $collectionId,
            'product_id' => $productId,
            'seller_id' => $sellerId,
            'created_by' => $sellerId,
        ], ['collection_id', 'product_id', 'seller_id']);

        return true;
    }

    /**
     * Add multi-campaign to collection
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function bulkAddToCollection(Request $request): JsonResponse
    {
        $campaignIds = $request->json('campaignIds');
        $collections = $request->json('collections');
        $sellerId = currentUser()->getUserId();

        $newCollectionNameList = [];
        $oldCollectionIds = [];
        foreach ($collections as $collection) {
            // if we have collection id then check if collection exists in collection table
            if (!isset($collection['id'])) {
                if (isset($collection['name'])) {
                    $newCollectionNameList[] = $collection['name'];
                }
            } else {
                $oldCollectionIds[] = $collection['id'];
            }
        }
        $newCollections = CollectionController::bulkCreate($sellerId, $newCollectionNameList);

        $totalSuccess = 0;
        foreach ($campaignIds as $campaignId) {
            foreach ($oldCollectionIds as $collectionId) {
                if (self::addCampaignToCollection($campaignId, $sellerId, $collectionId)) {
                    $totalSuccess++;
                }
            }
            foreach ($newCollections as $collection) {
                $collectionId = $collection->id;
                if (self::addCampaignToCollection($campaignId, $sellerId, $collectionId)) {
                    $totalSuccess++;
                }
            }
        }

        self::updateProductSyncStatus($campaignIds, true, sellerId: $sellerId);
        clearHtmlCacheSeller($sellerId, $campaignIds);
        return $this->successResponse(['total_success' => $totalSuccess]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function bulkRemoveFromCollection(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $campaignIds = $request->json('campaignIds');
        $collections = $request->json('collections');

        if (empty($campaignIds) || empty($collections)) {
            return $this->errorResponse();
        }

        $totalCollections = collect($collections)->count();
        $totalSuccess = 0;
        foreach ($collections as $collection) {
            if (!isset($collection['id'])) {
                continue;
            }
            $collectionId = $collection['id'];
            $success = ProductCollection::query()
                ->whereIn('product_id', $campaignIds)
                ->where([
                    'collection_id' => $collectionId,
                    'seller_id' => $userId
                ])
                ->delete();
            if ($success !== false) {
                $totalSuccess++;
            }
        }
        if ($totalCollections === $totalSuccess) {
            self::updateProductSyncStatus($campaignIds, true, sellerId: $userId);
            clearHtmlCacheSeller($userId, $campaignIds);
            return $this->successResponse();
        }
        return $this->errorResponse();
    }

    /**
     * Add multi-campaign to store
     *
     * @param BulkAddToStoreRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function bulkAddToStore(BulkAddToStoreRequest $request): JsonResponse
    {
        $ids = explode(',', $request->json('ids'));
        $storeId = $request->json('store_id');
        $data = [];
        $seller = currentUser()->getInfoAccess();
        collect($ids)->each(static function ($id) use ($seller, $storeId, &$data) {
            // check if campaign belongs to current user
            $isExist = Campaign::query()
                ->onSellerConnection($seller)
                ->where([
                    'seller_id' => $seller->id,
                    'id' => $id
                ])
                ->exists();

            if ($isExist) {
                $data[] = [
                    'store_id' => (int)$storeId,
                    'product_id' => (int)$id
                ];
            }
        });

        if (count($data) === 0) {
            return $this->errorResponse();
        }

        try {
            // use insertOrIgnore to skip duplicated items
            $result = StoreProduct::query()->insertOrIgnore($data);
        } catch (QueryException $e) {
            $result = false;
        }

        if ((bool)$result === false) {
            return $this->errorResponse();
        }

        self::updateProductSyncStatus($ids, true, sellerId: $seller->id);
        clearStoreCache($storeId);
        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function bulkRemoveFromStore(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $ids = $request->json('ids');
        $storeId = $request->json('store_id');
        $result = StoreProduct::query()
            ->whereIn('product_id', $ids)
            ->where([
                'store_product.store_id' => $storeId,
                'store.seller_id' => $userId
            ])
            ->join('store', 'store.id', '=', 'store_product.store_id')
            ->delete();

        if (!$result) {
            return $this->errorResponse();
        }

        self::updateProductSyncStatus($ids, true, sellerId: $userId);
        clearStoreCache($storeId);
        return $this->successResponse();
    }

    /**
     * Delete draft campaigns
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function bulkDeleteDraftCampaigns(Request $request): JsonResponse
    {
        $ids = $request->json('campaign_ids') ?? $request->json('ids');
        $seller = currentUser()->getInfoAccess();
        if (!empty($ids) && is_string($ids)) {
            $ids = Str::of($ids)->explode(',')->filter()->map(fn ($id) => (int)$id)->toArray();
        }
        if (count($ids) === 0) {
            return $this->errorResponse();
        }

        try {
            $orderedCampaignIds = OrderProduct::query()
                ->whereIn('campaign_id', $ids)
                ->where('seller_id', $seller->id)
                ->groupBy('campaign_id')
                ->pluck('campaign_id')->toArray();

            $expressCampaignIds = ExpressCampaign::query()
                ->onSellerConnection($seller)
                ->whereIn('template_id', $ids)
                ->get()
                ->pluck('template_id')->toArray();

            $queryNotInIds = array_merge($orderedCampaignIds, $expressCampaignIds);
            $query = Product::query()
                ->onSellerConnection($seller)
                ->where(function ($query) use ($ids) {
                    $query->whereIn('id', $ids)
                        ->orWhereIn('campaign_id', $ids);
                })
                ->where(function ($query) use ($queryNotInIds) {
                    $query->whereNotIn('id', $queryNotInIds)
                        ->whereNotIn('campaign_id', $queryNotInIds);
                })
                ->where([
                    'seller_id' => $seller->id,
                ])
                ->whereIn('status', [ProductStatus::DRAFT, ProductStatus::INACTIVE])
                ->withTrashed();
            $productIds = $query->pluck('id')->toArray();
            $productModel = new Product();
            $status = $productModel->elasticDeleteProductsByProductIds($productIds, $seller->getElasticSearchIndex());
            $deleted = false;
            if ($status) {
                $query->update(['slug' => null]);
                $deleted = $query->delete();
                (new SyncSlugJob(ids: $productIds, seller: $seller, isUpsert: false))->handle();
            }
            return $deleted ? $this->successResponse() : $this->errorResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Change sync status of products to update Elastic Search data
     *
     * @param $ids
     * @param bool $async
     * @return bool
     * @throws Throwable
     */
    public static function updateProductSyncStatus($ids, $async = false, $sellerId = null)
    {
        if (!is_null($ids)) {
            if ($async) {
                dispatch(new SyncProductsToElasticSearchJob($ids, sellerId: $sellerId));
            } else {
                (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($ids, sellerId: $sellerId);
            }
            return true;
        }
        return false;
    }

    /**
     * Duplicate campaign
     *
     * @param Request $request
     * @param $campaignId
     * @return JsonResponse
     * @throws Throwable
     */
    public function duplicateCampaign(Request $request, $campaignId): JsonResponse
    {
        $withDesign = $request->post('with_design');
        $user = currentUser();
        $seller = $user->getInfoAccess();

        if (!$this->checkCanCreate($user)) {
            return $this->errorResponse($this->getErrorMessageLimitCreating($user));
        }

        // Get all data from campaign ID
        $targetCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->firstWhere([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ]);

        // todo @long: validate
        if (is_null($targetCampaign)) {
            return $this->errorResponse();
        }

        // Create object data for new campaign
        // https://laravel.com/docs/8.x/eloquent#replicating-models
        $arrayScore = getScoreProductBySales();

        $dataNewCampaign = $targetCampaign->replicate([
            'slug',
            'default_product_id',
            'tm_status',
            'sync_status',
            'elastic_document_id',
            'created_at',
            'updated_at',
            'visited_at',
            'score',
            'sales_score',
            'time_score',
        ])->fill([
            'slug' => $targetCampaign->slug . '-' . self::randomSlugSuffix(),
            'product_type' => ProductType::CAMPAIGN,
            'status' => ProductStatus::DRAFT,
            'score' => $arrayScore['score'],
            'time_score' => $arrayScore['timestamp'],
        ])->toArray();
        $newCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->create($dataNewCampaign);

        $publicStatus = CampaignPublicStatusEnum::NO;

        if (in_array($targetCampaign->public_status, [
            CampaignPublicStatusEnum::YES,
            CampaignPublicStatusEnum::APPROVED
        ], true)) {
            $publicStatus = CampaignPublicStatusEnum::YES;
        }

        // auto approve if current user is trusted seller
        // temporarily disabled, see issue:
        // https://discord.com/channels/874562708392005672/1006415246530969653/1006415249420857354
        //        if ($user->getInfo()->status === UserStatusEnum::TRUSTED) {
        //            $newCampaign->public_status = CampaignPublicStatusEnum::APPROVED;
        //        } else {
        //            $newCampaign->public_status = $publicStatus;
        //        }
        $newCampaign->public_status = $publicStatus;

        // Check option if we need to override something
        $options = $request->post('options');

        if ($options) {
            try {
                $options = json_decode($options, true, 512, JSON_THROW_ON_ERROR);

                if (isset($options['campaign_name'])) {
                    $newCampaign['name'] = $options['campaign_name'];
                }

                if (isset($options['description'])) {
                    $newCampaign['description'] = $options['description'];
                }

                if (isset($options['url_prefix'])) {
                    $newCampaign['slug'] = $options['url_prefix'] . $newCampaign->slug;
                }
            } catch (\JsonException $e) {
                // do nothing
            }
        }

        $defaultProductId = $targetCampaign->default_product_id;
        $defaultProduct = null;
        $currentCampaignId = $targetCampaign->id;

        try {
            $newCampaignId = null;

            // Check slug
            $slug = $newCampaign['slug'];
            $tmpSlug = $slug;
            $count = 1;
            while (true) {
                if (Campaign::query()->onSellerConnection($seller)->where('slug', $tmpSlug)->exists()) {
                    $tmpSlug = $slug . '-' . $count;
                    $count++;
                    continue;
                }
                $newCampaign['slug'] = $tmpSlug;
                break;
            }

            if ($newCampaign->save()) {
                $newCampaignId = $newCampaign->id;
                (new SyncSlugJob(ids: [$newCampaign->id], seller: $seller))->handle();
            }

            if (is_null($newCampaignId)) {
                return $this->errorResponse();
            }

            // Clone collection
            if (!CampaignService::cloneCollections($campaignId, $newCampaignId)) {
                return $this->errorResponse();
            }
            // Clone store
            if (!CampaignService::cloneStores($campaignId, $newCampaignId)) {
                return $this->errorResponse();
            }

            // Get upsell data
            $upsells = Upsell::query()
                ->where('product_id', $campaignId)
                ->where('seller_id', $seller->id)
                ->get();

            if ($upsells->count() > 0) {
                foreach ($upsells as $upsell) {
                    $newUpsell = $upsell->replicate()->fill([
                        'product_id' => $newCampaignId
                    ]);
                    $result = $newUpsell->save();

                    if (!$result) {
                        return $this->errorResponse('Duplicate upsell campaign failed');
                    }
                }
            }

            if (!$campaignId) {
                return $this->errorResponse();
            }

            // clone all products
            $products = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'product.seller_id' => $targetCampaign->seller_id,
                    'product.campaign_id' => $campaignId,
                    'product_type' => ProductType::PRODUCT
                ])
                ->with(['template:id,thumb_url,base_cost,base_costs'])
                ->get();

            $resultAddProducts = false;
            $totalProducts = $products->count();

            if ($totalProducts > 0) {
                $successProducts = 0;

                $moveFiles = [];
                foreach ($products as $product) {
                    $currentProductId = $product->id;
                    $templateProduct = $product->template;
                    $filled = [
                        'campaign_id' => $newCampaignId,
                        'status' => ProductStatus::DRAFT,
                        'score' => ($currentProductId === $defaultProductId) ? $arrayScore['score'] + 1 : $arrayScore['score'],
                        'time_score' => $arrayScore['timestamp'],
                    ];

                    if (!empty($templateProduct) && $product->system_type !== ProductSystemTypeEnum::CUSTOM) {
                        $filled['thumb_url'] = $templateProduct->thumb_url;
                    }

                    $newProduct = Product::query()
                        ->onSellerConnection($seller)
                        ->create($product->replicate([
                            'extra_print_cost',
                            'tm_status',
                            'sync_status',
                            'elastic_document_id',
                            'created_at',
                            'updated_at',
                            'visited_at',
                            'score',
                            'sales_score',
                            'time_score',
                        ])->fill($filled)->toArray());

                    $newProduct->public_status = $publicStatus;

                    $newProduct->save();
                    // reset product id to null
                    $product->id = null;
                    // add new product and get new product id
                    $newProductId = $newProduct->id;

                    if ($newProductId && $newProductId > 0) {
                        // Clone variant
                        if ($newProduct->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                            $this->cloneVariants($currentCampaignId, $currentProductId, $newCampaignId, $newProductId);
                        }

                        if ($withDesign == true) {
                            // clones files
                            $oldFiles = File::query()
                                ->onSellerConnection($seller)
                                ->whereProductId($currentProductId)->get();

                            if ($oldFiles->count() > 0) {
                                /** @var File $oldFile */
                                foreach ($oldFiles as $oldFile) {
                                    // replace for file_url
                                    $file = File::query()
                                        ->onSellerConnection($seller)
                                        ->create($oldFile->replicate()->fill([
                                            'product_id' => $newProductId,
                                            'campaign_id' => $newCampaignId,
                                        ])->toArray());
                                    if (isset($file->file_url) && !(Str::isUrl($file->file_url) && $file->type === FileTypeEnum::DESIGN && $file->option === FileRenderType::PB)) {
                                        $oldUrl = $file->file_url;
                                        $newUrl = preg_replace("#p/\d+/#", "p/$newProduct->campaign_id/", $oldUrl);
                                        $file->file_url = $newUrl;
                                        $oldPath = self::getS3PathFromUrl($oldUrl);
                                        $newPath = self::getS3PathFromUrl($newUrl);
                                        $moveFiles[] = [$oldPath, $newPath];
                                    }
                                    // replace for file_url_2
                                    if (isset($file->file_url_2)) {
                                        $oldUrl = $file->file_url_2;
                                        $newUrl = preg_replace("#p/\d+/#", "p/$newProduct->campaign_id/", $oldUrl);
                                        $file->file_url_2 = $newUrl;
                                        $oldPath = self::getS3PathFromUrl($oldUrl);
                                        $newPath = self::getS3PathFromUrl($newUrl);
                                        $moveFiles[] = [$oldPath, $newPath];
                                    }
                                    // replace for file url in design json
                                    if (isset($file->design_json)) {
                                        $designJson = json_decode($file->design_json, true);
                                        if (isset($designJson['objects'])) {
                                            // find element with type image and is custom
                                            $objects = $designJson['objects'];
                                            foreach ($objects as $index => $object) {
                                                if ($object['type'] === 'image') {
                                                    // replace campaign id in url
                                                    $oldUrl = $object['src'];
                                                    if (preg_match("#p/\d+/#", $oldUrl)) {
                                                        $newUrl = preg_replace("#p/\d+/#", "p/$newProduct->campaign_id/", $oldUrl);
                                                        $objects[$index]['src'] = $newUrl;
                                                        $oldPath = self::getS3PathFromUrl($oldUrl);
                                                        $newPath = self::getS3PathFromUrl($newUrl);
                                                        $moveFiles[] = [$oldPath, $newPath];
                                                    }
                                                }
                                            }
                                            // update design json for file model
                                            $designJson['objects'] = $objects;
                                            [$designJson,] = FulfillmentService::updateUrlOnDesignJson($designJson, false);
                                            $file->design_json = json_encode($designJson, JSON_UNESCAPED_SLASHES);
                                        }
                                    }
                                    // save file
                                    if ($file->save()) {
                                        continue;
                                    }
                                    // debug if copy failed
                                    if (!app()->isProduction()) {
                                        logToDiscordNow("[Clone Design] Save file failed. " . mentionDiscord(DiscordUserIdEnum::JAMES));
                                    }
                                }
                            }

                            // move files
                            if (count($moveFiles) > 0) {
                                foreach ($moveFiles as $moveFile) {
                                    [$oldPath, $newPath] = $moveFile;
                                    foreach (StorageDisksEnum::activeStorages() as $disk) {
                                        $client = Storage::disk($disk);
                                        $fileExists = $client->exists($newPath);
                                        if ($fileExists) {
                                            break;
                                        }
                                        if ($client->exists($oldPath) && !$client->copy($oldPath, $newPath)) {
                                            // debug if copy failed
                                            if (!app()->isProduction()) {
                                                logToDiscordNow("[Clone Design] Copy from {$oldPath} to {$newPath} failed. " . mentionDiscord(DiscordUserIdEnum::JAMES));
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        //Clone all mockups
                        if ($newProduct->system_type === ProductSystemTypeEnum::CUSTOM) {
                            CampaignService::cloneMockups($currentCampaignId, $currentProductId, $newCampaignId, $newProductId, $seller);
                        }
                        if ($currentProductId === $defaultProductId) {
                            $defaultProduct = $newProduct;
                        }
                        $successProducts++;
                    } else {
                        // if insert new product failed
                        break;
                    }
                }

                // if total products equal success products then set resultAddProduct is true
                if ($totalProducts === $successProducts) {
                    $resultAddProducts = true;
                }
            }

            if ($resultAddProducts === true) {
                // default product is not null then update
                Campaign::query()
                    ->onSellerConnection($seller)
                    ->where('id', $newCampaignId)
                    ->update([
                        'default_product_id' => $defaultProduct->id,
                        'thumb_url' => $defaultProduct->thumb_url,
                        'template_id' => $defaultProduct->template_id
                    ]);

                // Return the new campaign ID (so client can redirect)
                $result = Product::query()
                    ->select('id')
                    ->onSellerConnection($seller)
                    ->firstWhere('campaign_id', $newCampaignId);

                if ($result) {
                    $newProductId = $result->id;

                    Campaign::query()
                        ->onSellerConnection($seller)
                        ->where('id', $newCampaignId)
                        ->update(['default_product_id' => $newProductId]);
                }

                return $this->successResponse(['id' => $newCampaignId]);
            }

            // Return the new campaign ID (so client can redirect) and message about can't add products to this campaign
            return $this->successResponse(['id' => $newCampaignId], "Can't add products to this campaign.");
        } catch (Exception $e) {
            logToDiscord(
                'Delete campaign failed: ' . $e->getMessage()
                . '. CampaignId: ' . $campaignId,
                'error',
                true
            );
            return $this->errorResponse("Can't duplicate campaign.");
        }
    }

    /**
     * @param string $url
     * @return string
     */
    private static function getS3PathFromUrl(string $url): string
    {
        if (preg_match("/\/?(p\/\d+\/[a-z0-9]+(\/[a-z0-9]{1,2}\/)?[a-z0-9]+\.[A-Za-z0-9]{3,5})/", $url, $matches)) {
            return trim($matches[1]);
        }
        return "";
    }

    /**
     * @param array $moveFiles
     * @param string $newUrl
     * @return bool
     */
    private static function isUrlIn(array $moveFiles, string $newUrl): bool
    {
        foreach ($moveFiles as $mappingFiles) {
            if (in_array($newUrl, $mappingFiles)) {
                return true;
            }
        }
        return false;
    }

    private static function cloneDesigns($fromCampId, $fromProdId, $toCampId, $toProdId): bool
    {
        $seller = currentUser()->getInfoAccess();
        $files = File::query()
            ->onSellerConnection($seller)
            ->where([
                'product_id' => $fromProdId,
                'campaign_id' => $fromCampId,
                'type' => FileTypeEnum::DESIGN
            ])
            ->get();

        if ($files->count() > 0) {
            $movedFiles = new \Illuminate\Support\Collection();
            foreach ($files as $file) {
                $newFile = $file->replicate()->fill([
                    'product_id' => $toProdId,
                    'campaign_id' => $toCampId,
                ]);
                if ($newFile->type == FileTypeEnum::DESIGN) {
                    $oldFilePath = $newFile->file_url;
                    $newFilePath = preg_replace("#p/(\d+)/#", "p/{$toCampId}/", $oldFilePath);
                    if ($newFilePath && $movedFiles->contains($newFilePath) === false) {
                        $isSuccess = Storage::disk(StorageDisksEnum::DEFAULT)->copy($oldFilePath, $newFilePath);
                        if ($isSuccess) {
                            $newFile->file_url = $newFilePath;
                            $movedFiles->push($newFilePath);
                        }
                    }
                    if (isset($newFile->design_json)) {
                        $designJson = preg_replace("#/p/(\d+)/#", "/p/{$toCampId}/", $newFile->design_json);
                        if (isset($designJson)) {
                            [$designJson,] = FulfillmentService::updateUrlOnDesignJson($designJson);
                            $newFile->design_json = $designJson;
                        }
                    }
                }
                $newFile->save();
            }
        }
        return true;
    }

    /**
     * @param File $file
     * @param string $newFilePath
     * @return false|string
     */
    public static function replaceFileUrlDesignJson(File $file, string $newFilePath)
    {
        if (!str_starts_with($newFilePath, 'http')) {
            $baseImgUrl = config('senprints.base_img_url');
            $newFilePath = $baseImgUrl . '/rx/1500,q_90,ofmt_webp/s2/' . $newFilePath;
        }
        $jsonData = json_decode($file->design_json, true);
        $objects = $jsonData['objects'];
        foreach ($objects as $index => $object) {
            if ($object['type'] === 'image') {
                $object['src'] = $newFilePath;
            }
            $objects[$index] = $object;
        }
        return json_encode($objects, JSON_UNESCAPED_SLASHES);
    }

    public function cloneVariants($fromCampId, $fromProdId, $toCampId, $toProdId): bool
    {
        $seller = currentUser()->getInfoAccess();
        $variants = ProductVariant::query()
            ->onSellerConnection($seller)
            ->where([
                'product_id' => $fromProdId,
                'campaign_id' => $fromCampId
            ])
            ->get();

        if ($variants->count() > 0) {
            $data = [];
            foreach ($variants as $variant) {
                $variantData = $variant->toArray();
                $variantData['product_id'] = $toProdId;
                $variantData['campaign_id'] = $toCampId;
                $data[] = $variantData;
            }
            ProductVariant::query()
                ->onSellerConnection($seller)
                ->insert($data);
        }
        return true;
    }

    private static function randomSlugSuffix(): string
    {
        return strtolower(Str::random(6));
    }

    /**
     * @param Request $request `
     * @return JsonResponse
     * @throws Throwable
     */
    public function bulkCreateCampaigns(Request $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();

        $validator = Validator::make($request->all(), [
            'campaignId' => ['bail', 'required'],
            'artworks' => ['required', 'array']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $campaignId = $request->post('campaignId');
        $artworks = $request->post('artworks');

        if (!$artworks) {
            return $this->errorResponse();
        }

        $campaigns = [];
        $errors = [];

        // if artworks not null
        foreach ($artworks as $artwork) {
            $artworkId = $artwork['id'];
            $name = $artwork['name'];
            $slug = $artwork['slug'];
            $artworkUrl = $artwork['artwork'];
            $collectionName = $artwork['collection'];

            if (Slug::isInvalid($slug)) {
                $errors[] = ['artwork_id' => $artworkId, 'slug' => true];
                continue;
            }

            $newCampaign = CampaignService::duplicateCampaignWithFile((int)$campaignId, $artworkUrl, $name, $slug, $seller);

            if ($newCampaign !== false) {
                if (!is_null($collectionName)) {
                    // success
                    $collection = CollectionSellerController::createCollection($collectionName, $seller->id);

                    if (
                        $collection
                        && self::addCampaignToCollection($newCampaign['id'], $seller->id, $collection['collection_id'])
                    ) {
                        $newCampaign['collection'] = $collection;
                    }
                }

                $newCampaign['artwork_id'] = $artworkId;
                $campaigns[] = $newCampaign;
            }
        }

        return $this->successResponse([
            'campaigns' => $campaigns,
            'errors' => $errors
        ]);
    }

    public function changeCampaignStatus(ChangeCampaignStatusRequest $request, $campaignId): JsonResponse
    {
        $status = $request->json('status');
        $seller = currentUser()->getInfoAccess();

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->firstWhere([
                'seller_id' => $seller->id,
                'id' => $campaignId
            ]);

        if (is_null($campaign)) {
            return $this->errorResponse();
        }

        try {
            $campaign->status = $status;

            if ($campaign->save()) {
                // change all products status to same campaign
                $result = Product::query()
                    ->where('campaign_id', $campaignId)
                    ->update(['status' => $status]);

                if ($result) {
                    return $this->successResponse(['status' => $status]);
                }
            }
        } catch (Throwable $ex) {
            return $this->errorResponse('Cannot update campaign status.');
        }

        return $this->errorResponse('Cannot update campaign status.');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function launchBulkCampaigns(Request $request): JsonResponse
    {
        try {
            $seller = currentUser()->getInfoAccess();
            $connection = $seller->getPrivateConnection();

            $validator = Validator::make($request->all(), [
                'campaign_ids' => 'required|array',
                'campaign_ids.*' => [
                    'required',
                    'integer',
                    new CheckExistsIdRule('product', connection: $connection),
                ]
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }

            $campaignIds = $request->post('campaign_ids');

            $draftCampaigns = Campaign::query()
                ->onSellerConnection($seller)
                ->whereIn('id', $campaignIds)
                ->where('status', ProductStatus::DRAFT)
                ->get();

            $tradeMarkCampaignIds = array();
            if ($draftCampaigns->count() > 0) {
                foreach ($draftCampaigns as $draftCampaign) {
                    if ($draftCampaign->tm_status === CampaignTrademarkStatusEnum::TM && isset($campaignIds[$draftCampaign->id])) {
                        unset($campaignIds[$draftCampaign->id]);
                        $tradeMarkCampaignIds[] = $draftCampaign->id;
                    }
                    self::generateImageFilesCampaign($draftCampaign, $seller);
                }
                if (!empty($tradeMarkCampaignIds)) {
                    Product::query()
                        ->whereIn('id', $tradeMarkCampaignIds)
                        ->orWhereIn('campaign_id', $tradeMarkCampaignIds)
                        ->update(['status' => ProductStatus::BLOCKED]);
                }
            }

            $countUpdated = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'seller_id' => $seller->id,
                    'status' => ProductStatus::DRAFT
                ])
                ->where(function ($query) use ($campaignIds) {
                    $query->whereIn('id', $campaignIds)
                        ->orWhereIn('campaign_id', $campaignIds);
                })
                ->update([
                    'status' => ProductStatus::ACTIVE
                ]);

            // File::query()->where([
            //     'type' => FileTypeEnum::DESIGN,
            //     'option' => FileRenderType::PRINT,
            // ])
            //     ->whereNotNull('design_json')
            //     ->whereIn('campaign_id', $campaignIds)
            //     ->get()->map(fn($file) => RenderPrintJob::dispatch($file));
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch(array_merge($campaignIds, $tradeMarkCampaignIds), sellerId: $seller->id);
            return $this->successResponse(['count_updated' => $countUpdated]);
        } catch (Throwable $ex) {
            return $this->errorResponse('Cannot update campaign status.');
        }
    }

    public static function getCdnMockupImageUrl2($background, $color, $crop, $shadow, $design, $hexColor, $renderMode, $pathPrefix = '/s4'): string
    {
        $config = config('senprints.cloudinary_config');
        $cloudinary = new Cloudinary($config);

        if (!empty($design)) {
            $image = $cloudinary->image($design);
            if (!empty($crop)) {
                $image->reshape(Reshape::cutByImage(Source::image($crop)));
            }
        } else {
            $image = $cloudinary->image($background);
        }

        if (!empty($design) && !empty($shadow) && $renderMode === CampaignRenderModeEnum::DYNAMIC) {
            $image->underlay(Overlay::source(Source::image($shadow)));
        }

        if (!empty($color)) {
            $transformationColor = Source::image($color)
                ->transformation((new ImageTransformation())
                    ->effect(Effect::colorize()
                        ->level(100)
                        ->color(Color::rgb($hexColor))));
            if (!empty($design)) {
                $image->underlay(Overlay::source($transformationColor));
            } else {
                $image->overlay(Overlay::source($transformationColor));
            }
        }

        if (!empty($shadow) && $renderMode !== CampaignRenderModeEnum::DYNAMIC || empty($design)) {
            $image->overlay(Overlay::source(Source::image($shadow)));
        }

        if (!empty($background) && !empty($design)) {
            $image->underlay(Overlay::source(Source::image($background)));
        }

        $image->resize(Resize::thumbnail()->width(1280))->format(Format::jpg());

        $url = preg_replace('/https:\/\/res\.cloudinary\.com\/\w+\/image\/upload/', $pathPrefix, $image->toUrl());
        $hashName = substr(md5($url), 0, 16);
        $url = preg_replace('/\?_a=.+$/', '/t/' . $hashName . '.jpg', $url); // remove version param and add file name

        return $url;
    }

    public static function getCdnMockupImage($mockup, $design, $colorName, $renderMode = CampaignRenderModeEnum::NATURE): string
    {
        try {
            $designJson = json_decode($mockup->design_json, true); // to array
            if (is_null($designJson)) {
                return '';
            }
            if (isset($designJson['expressOptions'])) {
                // parse express options
                $expressOptions = $designJson['expressOptions'];
                $expressHeight = $expressOptions['height'] ?? 0;
                $expressWidth = $expressOptions['width'] ?? 0;
                $expressX = $expressOptions['x'] ?? 0;
                $expressY = $expressOptions['y'] ?? 0;
            }
            $layoutColor = $designJson['color'] ?? '';
            $layoutColor = str_replace('.png', '', $layoutColor);
            $layoutCrop = $designJson['crop'] ?? '';
            $layoutCrop = str_replace('.png', '', $layoutCrop);
            $layoutShadow = $designJson['shadow'] ?? '';
            $layoutShadow = str_replace('.png', '', $layoutShadow);
            $background = str_replace('.png', '', $mockup->file_url);
            $pathPrefix = ($design && is_null($design->file_url)) ? '/s3' : '/s4';
            $layoutDesign = !is_null($design) ? $design->file_url ?? $design->file_url_2 : '';
            $layoutDesign = str_replace('.png', '', $layoutDesign);
            $hexColor = !is_null($colorName) ? color2hex($colorName) : '';
            return self::getCdnMockupImageUrl2($background, $layoutColor, $layoutCrop, $layoutShadow, $layoutDesign, $hexColor, $renderMode, $pathPrefix);
        } catch (Exception $ex) {
            return '';
        }
    }

    /**
     * @param SaveCampaignDesign $request
     * @return JsonResponse
     */
    public function saveDesignExpress(SaveCampaignDesign $request): JsonResponse
    {
        $expressRenderService = new ExpressMockupRenderService();
        try {
            $data = $request->json('data');
            $storage = $request->json('storage');
            $campaignId = $request->json('campaignId');
            $currentUserId = currentUser()->getUserId();
            $campaign = Campaign::query()->whereKey($campaignId)->withoutTrashed()->firstOrFail();
            if (!$campaign) {
                return $this->errorResponse('Campaign not found');
            }

            // save design files from request
            $count = $expressRenderService->saveDesigns($data, $currentUserId, $storage);
            if (!$count) {
                return $this->errorResponse('Error when save design file');
            }

            if (!empty($campaignId)) {
                $campaign = Campaign::query()
                    ->firstWhere([
                        'seller_id' => $currentUserId,
                        'id' => $campaignId
                    ]);

                if ($campaign) {
                    // generate mockup image files for campaign
                    $expressRenderService->renderMockupForCampaign($campaign);
                }
            }
            ScanCampaignCopyright::dispatch($campaignId, sellerId: $currentUserId);
            return $this->successResponse(['uploaded' => $count]);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function saveDesign3d(SaveCampaignDesign $request): JsonResponse
    {
        try {
            $data = $request->json('data');
            $storage = $request->json('storage');
            $campaignId = $request->json('campaignId');
            $count = 0;
            $currentUser = currentUser();
            $seller = $currentUser->getInfoAccess();
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->when($currentUser->isSeller(), function ($query) use ($currentUser) {
                    $query->where('seller_id', $currentUser->getUserId());
                })
                ->whereKey($campaignId)->withoutTrashed()->firstOrFail();
            if (!$campaign) {
                return $this->errorResponse('Campaign not found');
            }
            $newFilesCreate = [];
            foreach ($data as $uploadedFile) {
                $fileUrl = $uploadedFile['file_url'];
                $productId = $uploadedFile['product_id'];
                $campaignId = $uploadedFile['campaign_id'];
                $mockupId = $uploadedFile['mockup_id'];
                $printSpace = $uploadedFile['print_space'];
                $isDefaultMockup = $uploadedFile['is_default_mockup'];
                $count++;

                File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'product_id' => $productId,
                        'type' => FileTypeEnum::DESIGN,
                        'option' => FileRenderType::RENDER_3D,
                        'mockup_id' => $mockupId,
                        'print_space' => $printSpace,
                    ])
                    ->delete();

                if (!empty($fileUrl)) {
                    $fileUrl1 = null;
                    $fileUrl2 = null;

                    if ($storage === 's3') {
                        $fileUrl1 = self::saveTempDesignFile($fileUrl, $campaignId);
                    } else {
                        $fileUrl2 = $fileUrl;
                    }

                    try {
                        if ($isDefaultMockup) {
                            $product = Product::query()
                                ->onSellerConnection($seller)
                                ->when($currentUser->isSeller(), function ($query) use ($currentUser) {
                                    $query->where('seller_id', $currentUser->getUserId());
                                })
                                ->find($productId);
                            if ($product) {
                                $product->update(['thumb_url' => $fileUrl1]);
                            }
                            $campaign = Campaign::query()
                                ->onSellerConnection($seller)
                                ->when($currentUser->isSeller(), function ($query) use ($currentUser) {
                                    $query->where('seller_id', $currentUser->getUserId());
                                })
                                ->find($campaignId);
                            if ($campaign && $campaign->default_product_id === $productId) {
                                $campaign->update(['thumb_url' => $fileUrl1]);
                            }
                        }

                        $newFilesCreate[] = [
                            'file_url' => $fileUrl1,
                            'file_url_2' => $fileUrl2,
                            'campaign_id' => $campaignId,
                            'mockup_id' => $mockupId,
                            'product_id' => $productId,
                            'type' => FileTypeEnum::DESIGN,
                            'option' => FileRenderType::RENDER_3D,
                            'print_space' => $printSpace,
                            'seller_id' => $seller->id,
                            'position' => 0,
                        ];
                    } catch (Throwable $ex) {
                        return $this->errorResponse('Error when save design file');
                    }
                }
            }
            if (!empty($newFilesCreate)) {
                File::query()->onSellerConnection($seller)->insert($newFilesCreate);
            }
            if (!empty($campaignId)) {
                $campaign = Campaign::query()
                    ->onSellerConnection($seller)
                    ->firstWhere([
                        'seller_id' => $seller->id,
                        'id' => $campaignId
                    ]);

                if ($campaign) {
                    // generate image file for product
                    self::generateImageFilesCampaign($campaign, $seller);
                }
            }
            ScanCampaignCopyright::dispatch($campaignId, sellerId: $seller->id);
            return $this->successResponse(['uploaded' => $count]);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updatePublicStatus(UpdatePublicStatusRequest $request): JsonResponse
    {
        $status = strtolower($request->post('status'));
        $sellerCampaignIds = $request->json('seller_campaign_ids', []);
        try {
            $sellerCampaignIds = collect($sellerCampaignIds)
                ->groupBy('seller_id')
                ->map(fn($items) => $items->pluck('id'))
                ->toArray();
            $sellerIds = array_keys($sellerCampaignIds);
            User::query()->whereIn('id', $sellerIds)->get()->each(function ($seller) use ($sellerCampaignIds, $status) {
                $campaignIds = data_get($sellerCampaignIds, $seller->id);
                if (Product::setPublicStatus($campaignIds, $status, $seller)) {
                    self::updateProductSyncStatus($campaignIds, true, $seller->id);
                }
            });
            return $this->successResponse();
        } catch (Throwable $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Campaign $campaign
     * @param null $seller
     * @return void
     */
    public static function generateImageFilesCampaign(Campaign $campaign, $seller = null): void
    {
        $currentUser = currentUser();
        if (!$seller) {
            $seller = $currentUser->getInfoAccess();
        }
        $products = Product::query()
            ->onSellerConnection($seller)
            ->when($currentUser->isSeller(), function ($query) use ($seller) {
                $query->where('seller_id', $seller->id);
            })
            ->with('template')
            ->with('images', function ($images) use ($seller) {
                $images->onSellerConnection($seller);
                $images->where('type_detail', FileRenderType::CUSTOM);
            })
            ->where('campaign_id', $campaign->id)
            ->get();

        $mockups = File::query()
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::RENDER_3D
            ])
            ->whereIn('product_id', $products->pluck('template_id')->toArray())
            ->orderBy('position')
            ->get();

        if ($mockups->count() === 0) {
            return;
        }

        // drop old imageFiles
        File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaign->id,
                'type' => FileTypeEnum::IMAGE,
            ])
            ->whereIn('product_id', $products->pluck('id')->toArray())
            ->whereNull('type_detail')
            ->delete();

        if ($products->count() > 0) {
            /* @var Product $product */
            $products->each(fn($product) => self::generateImageFilesProduct2($product, $campaign, $seller, $mockups->filter(fn($mockup) => (int)$mockup->product_id === (int)$product->template_id)));
        }
    }

    /**
     * @param Product $product
     * @param $campaign
     * @param $seller
     * @param $mockups
     * @return void
     */
    public static function generateImageFilesProduct2(Product $product, $campaign, $seller, $mockups): void
    {
        $currentUser = currentUser();
        if (is_null($campaign)) {
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->when($currentUser->isSeller(), function ($query) use ($currentUser) {
                    $query->where('seller_id', $currentUser->getUserId());
                })
                ->find($product->campaign_id);
        }

        $productMockupType = $product->mockup_type;

        if ($mockups->count() === 0) {
            return;
        }

        $defaultImages = null;
        $customMockup = $product->images;

        if ($customMockup->isNotEmpty()) {
            if (!in_array($product->thumb_url, $customMockup->pluck('file_url')->toArray())) {
                $product->update([
                    'thumb_url' => $customMockup->first()->file_url,
                    'sync_status' => 0
                ]);
            }

            if ($campaign->default_product_id === $product->id) {
                $campaign->update([
                    'thumb_url' => $product->thumb_url,
                    'sync_status' => 0
                ]);
            }

            $defaultImages = $product->thumb_url;
        }

        $renderMode = ($campaign->render_mode === $product->template->render_mode) ? $campaign->render_mode : CampaignRenderModeEnum::NATURE;
        $mockups->map(function ($mockup) use ($product, $campaign, &$defaultImages, $productMockupType, $renderMode, $seller) {
            if ($productMockupType === $mockup['type_detail'] || empty($mockup['type_detail'])) {
                $designFile = File::query()
                    ->onSellerConnection($seller)
                    ->firstWhere([
                        'product_id' => $product->id,
                        'type' => FileTypeEnum::DESIGN,
                        'mockup_id' => $mockup->id,
                        'option' => FileRenderType::RENDER_3D,
                    ]);

                // put image without design to bottom
                $position = is_null($designFile) ? $mockup->position * 10 : $mockup->position;
                $defaultColor = $product->default_option ?? 'white';
                $fileUrl2 = self::getCdnMockupImage($mockup, $designFile, $defaultColor, $renderMode);

                // set default campaign/product image
                if ((is_null($defaultImages) && !is_null($designFile) && empty($product->default_mockup_id))
                    || $product->default_mockup_id === $mockup->id
                ) {
                    $defaultImages = $fileUrl2;

                    Product::query()
                        ->onSellerConnection($seller)
                        ->where(['id' => $product->id])
                        ->update([
                            'thumb_url' => $fileUrl2,
                            'sync_status' => 0
                        ]);

                    if ($campaign->default_product_id === $product->id) {
                        Campaign::query()
                            ->onSellerConnection($seller)
                            ->where(['id' => $campaign->id])
                            ->update([
                                'thumb_url' => $fileUrl2,
                                'sync_status' => 0
                            ]);
                    }
                }

                File::query()
                    ->onSellerConnection($seller)
                    ->insertOrIgnore([
                        'campaign_id' => $product->campaign_id,
                        'product_id' => $product->id,
                        'seller_id' => $campaign->seller_id,
                        'type' => FileTypeEnum::IMAGE,
                        'design_id' => optional($designFile)->id,
                        'mockup_id' => $mockup->id,
                        'print_space' => $mockup->print_space,
                        'position' => $position,
                        'file_url_2' => $fileUrl2,
                    ]);
            }
        });
    }

    public static function generateImageFilesProduct(Product $product, $campaign): void
    {
        $seller = currentUser()->getInfoAccess();

        if (is_null($campaign)) {
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->find($product->campaign_id);
        }

        $productMockupType = $product->mockup_type;
        $mockups = File::query()
            ->onSellerConnection($seller)
            ->where([
                'product_id' => $product->template_id,
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::RENDER_3D
            ])
            ->orderBy('position')
            ->get();

        if ($mockups->count() === 0) {
            return;
        }

        // drop old imageFiles
        File::query()
            ->onSellerConnection($seller)
            ->where([
                'product_id' => $product->id,
                'campaign_id' => $campaign->id,
                'type' => FileTypeEnum::IMAGE,
            ])
            ->whereNull('type_detail')
            ->delete();

        $defaultImages = null;
        $customMockup = $product->images;

        if ($customMockup->isNotEmpty()) {
            if (!in_array($product->thumb_url, $customMockup->pluck('file_url')->toArray())) {
                $product->fill([
                    'thumb_url' => $customMockup->first()->file_url,
                    'sync_status' => 0
                ])->update();
            }

            if ($campaign->default_product_id === $product->id) {
                $campaign->fill([
                    'thumb_url' => $product->thumb_url,
                    'sync_status' => 0
                ])->update();
            }

            $defaultImages = $product->thumb_url;
        }

        $renderMode = ($campaign->render_mode === $product->template->render_mode) ? $campaign->render_mode : CampaignRenderModeEnum::NATURE;
        $mockups->map(function ($mockup) use ($product, $campaign, &$defaultImages, $productMockupType, $renderMode, $seller) {
            if ($productMockupType === $mockup['type_detail'] || empty($mockup['type_detail'])) {
                $designFile = File::query()
                    ->onSellerConnection($seller)
                    ->firstWhere([
                        'product_id' => $product->id,
                        'type' => FileTypeEnum::DESIGN,
                        'mockup_id' => $mockup->id,
                        'option' => FileRenderType::RENDER_3D,
                    ]);

                $listColors = [];

                if (!empty($product->options)) {
                    $options = json_decode($product->options);
                    $listColors = $options->color ?? [];
                }

                // put image without design to bottom
                $position = is_null($designFile) ? $mockup->position * 10 : $mockup->position;

                if (count($listColors) > 0) {
                    foreach ($listColors as $color) {
                        $fileUrl2 = self::getCdnMockupImage($mockup, $designFile, $color, $renderMode);

                        // set default campaign/product image
                        if ((is_null($defaultImages) && $color === $product->default_option && !is_null($designFile) && empty($product->default_mockup_id))
                            || $product->default_mockup_id === $mockup->id
                        ) {
                            $defaultImages = $fileUrl2;

                            Product::query()
                                ->onSellerConnection($seller)
                                ->where(['id' => $product->id])
                                ->update([
                                    'thumb_url' => $fileUrl2,
                                    'sync_status' => 0
                                ]);

                            if ($campaign->default_product_id === $product->id) {
                                Campaign::query()
                                    ->onSellerConnection($seller)
                                    ->where(['id' => $campaign->id])
                                    ->update([
                                        'thumb_url' => $fileUrl2,
                                        'sync_status' => 0
                                    ]);
                            }
                        }

                        File::query()
                            ->onSellerConnection($seller)
                            ->insertOrIgnore([
                                'seller_id' => $campaign->seller_id,
                                'product_id' => $product->id,
                                'campaign_id' => $product->campaign_id,
                                'type' => FileTypeEnum::IMAGE,
                                'design_id' => optional($designFile)->id,
                                'mockup_id' => $mockup->id,
                                'print_space' => $mockup->print_space,
                                'position' => $position,
                                'option' => $color,
                                'file_url' => null,
                                'file_url_2' => $fileUrl2,
                                'is_default' => 0,
                            ]);
                    }
                } else {
                    $fileUrl2 = self::getCdnMockupImage($mockup, $designFile, '');

                    // set default campaign/product image
                    if (is_null($defaultImages) && !is_null($designFile) && $product->default_mockup_id === $mockup->id) {
                        $defaultImages = $fileUrl2;

                        Product::query()
                            ->onSellerConnection($seller)
                            ->where(['id' => $product->id])
                            ->update([
                                'thumb_url' => $fileUrl2,
                                'sync_status' => 0
                            ]);

                        if ($campaign->default_product_id === $product->id) {
                            Campaign::query()
                                ->onSellerConnection($seller)
                                ->where(['id' => $campaign->id])
                                ->update([
                                    'thumb_url' => $fileUrl2,
                                    'sync_status' => 0
                                ]);
                        }
                    }

                    File::query()
                        ->onSellerConnection($seller)
                        ->insertOrIgnore([
                            'seller_id' => $campaign->seller_id,
                            'product_id' => $product->id,
                            'campaign_id' => $product->campaign_id,
                            'type' => FileTypeEnum::IMAGE,
                            'design_id' => optional($designFile)->id,
                            'mockup_id' => $mockup->id,
                            'print_space' => $mockup->print_space,
                            'position' => $position,
                            'file_url' => null,
                            'file_url_2' => $fileUrl2,
                            'is_default' => 1,
                        ]);
                }
            }
        });
    }

    public function bulkUploadSaveOriginDesign(Request $request): JsonResponse
    {
        $designJson = $request->post('design_json');
        $fileUrl = $request->post('file_url');
        $productId = $request->post('product_id');
        $campaignId = $request->post('campaign_id');
        $printSpace = $request->post('print_space');
        $seller = currentUser()->getInfoAccess();
        try {
            File::query()
                ->onSellerConnection($seller)
                ->create([
                    'type' => FileTypeEnum::DESIGN,
                    'option' => FileRenderType::PRINT,
                    'product_id' => $productId,
                    'campaign_id' => $campaignId,
                    'design_json' => $designJson,
                    'file_url' => $fileUrl,
                    'print_space' => $printSpace,
                    'seller_id' => $seller->id,
                ]);

            ScanCampaignCopyright::dispatch($campaignId, sellerId: $seller->id);
            return $this->successResponse();
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    private function checkCanCreate($user): bool
    {
        return $user->getNumberCampaignCanCreate() > 0;
    }

    private function getErrorMessageLimitCreating($user): string
    {
        return 'You can only create '
            . $user->getInfo()->campaign_limit
            . ' campaigns in 24 hours. Contact support to increase the limit.';
    }

    public function checkCanCreateAPI(Request $request): JsonResponse
    {
        $campaignId = $request->get('campaign_id');
        $user = currentUser();

        // if user is authorized account
        // set current logged-in user to that account
        if ($user->isAuthorizedAccount()) {
            $user = new SenPrintsAuth($user->getUserId());
        }

        if (!$this->checkCanCreate($user)) {
            return $this->errorResponse($this->getErrorMessageLimitCreating($user));
        }

        if (!empty($campaignId)) {
            if (!is_numeric($campaignId)) {
                return $this->errorResponse('Invalid campaign id');
            }
            $notAllowBulk = Product::query()
                ->onSellerConnection($user)
                ->where([
                    'campaign_id' => $campaignId,
                    'allow_bulk' => 0
                ])
                ->count();

            if ($notAllowBulk > 0) {
                $message = 'Some old products does not support bulk upload anymore. Please create new campaign to bulk.';
                return $this->errorResponse($message);
            }

            $hasArchived = ImportCampaignDataService::checkArchivedProducts($campaignId, $user);
            if ($hasArchived) {
                return $this->errorResponse('Some old products does not support bulk upload anymore. Please create new campaign to bulk');
            }
        }

        return $this->successResponse();
    }

    public function changeProductDefaultColor(ChangeProductDefaultColorRequest $request): JsonResponse
    {
        $productId = $request->post('product_id');
        $campaignId = $request->post('campaign_id');
        $colorName = $request->post('color');
        $seller = currentUser()->getInfoAccess();
        try {
            $product = Product::query()
                ->onSellerConnection($seller)
                ->firstWhere([
                    'id' => $productId,
                    'campaign_id' => $campaignId
                ]);

            if (!$product) {
                return $this->errorResponse('Invalid params');
            }

            // get main print spaces
            $printSpaces = json_decode($product->print_spaces);
            $mainPrintSpace = $printSpaces[0]->name;

            // get default image via color name & main print space
            $defaultImage = File::query()
                ->onSellerConnection($seller)
                ->where([
                    'type' => FileTypeEnum::IMAGE,
                    'product_id' => $productId,
                    'print_space' => $mainPrintSpace,
                    'option' => $colorName
                ])
                ->orderBy('position')
                ->first();

            if (!empty($defaultImage)) {
                // update product default color
                $product->update([
                    'default_option' => $colorName,
                    'thumb_url' => $defaultImage->file_url_2,
                    'sync_status' => 0
                ]);

                $campaign = Campaign::query()
                    ->onSellerConnection($seller)
                    ->find($campaignId);

                if ((int)$campaign->default_product_id === (int)$productId) {
                    $campaign->update([
                        'thumb_url' => $defaultImage->file_url_2,
                        'sync_status' => 0
                    ]);
                }
            }

            return $this->successResponse();
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function getCampaignDesigns(Request $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $campaignId = $request->get('campaign_id');

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->with(['designs' => function ($query) use ($seller) {
                $selectFields = [
                    'id',
                    'product_id',
                    'campaign_id',
                    'print_space',
                ];

                $query->onSellerConnection($seller);

                $query->select($selectFields)
                    ->where([
                        'type' => FileTypeEnum::DESIGN,
                        'option' => 'print'
                    ]);
            }])
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id,
            ])
            ->first();

        if (empty($campaign)) {
            return $this->errorResponse();
        }

        $designs = $campaign->designs;
        return $this->successResponse($designs);
    }

    public function canBulkCreate(Request $request): JsonResponse
    {
        $campaignId = $request->get('campaign_id');
        $templateIds = Product::query()
            ->where('campaign_id', $campaignId)
            ->pluck('template_id');
        $oldTemplateCount = Product::query()
            ->where([
                'product_type' => ProductType::TEMPLATE,
                'status' => ProductStatus::ARCHIVED
            ])
            ->whereIn('id', $templateIds)
            ->count();

        if ($oldTemplateCount > 0) {
            return $this->errorResponse('Some old products does not support bulk upload anymore. Please create new campaign to bulk');
        }

        return $this->successResponse();
    }

    /**
     * @param BulkCreateCampaignV2Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function bulkCreateCampaignV2(BulkCreateCampaignV2Request $request): JsonResponse
    {
        $bulkId = $request->post('bulk_id');
        $bulk = null;
        if (currentUser()->isAdmin()) {
            $sellerId = $request->post('seller_id');
            if (empty($sellerId)) {
                return $this->errorResponse('Empty seller id');
            }
            $user = currentUser($sellerId);
        } else {
            if (!currentUser()->hasPermission('update_campaign')) {
                return $this->errorResponse('Permission denied');
            }
            $user = currentUser();
        }
        $sellerId = $user->isAdmin() ? $request->post('seller_id') : $user->getUserId();
        $seller = User::query()->find($sellerId);
        $isSellName = $user->isSeller() && $user->getInfo()?->isSellName();

        $campaignId = $request->post('campaign_id');
        $designInfo = $request->post('design_info');
        if (!empty($bulkId)) {
            $bulk = ImportCampaignsData::query()
                ->whereKey($bulkId)
                ->where('seller_id', $sellerId)
                ->first();
            if (empty($bulk)) {
                return $this->errorResponse('Invalid bulk id');
            }
            if (!empty($bulk->campaign_id) && !empty($bulk->template_campaign_id)) {
                return $this->successResponse(null, 'Campaign is processing');
            }
        }

        if (!$this->checkCanCreate($user)) {
            return $this->errorResponse($this->getErrorMessageLimitCreating($user));
        }

        $existCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select(['id', 'name', 'slug', 'thumb_url', 'status'])
            ->where([
                'slug' => $designInfo['slug'],
                'seller_id' => $sellerId
            ])
            ->first();

        // delete draft camp to avoid duplicate slug
        if ($existCampaign) {
            if ($existCampaign->status === CampaignStatusEnum::DRAFT && !empty($existCampaign->id)) {
                $query = Product::query()
                    ->onSellerConnection($seller)
                    ->where(function ($query) use ($existCampaign) {
                        $query->where('id', $existCampaign->id)
                            ->orWhere('campaign_id', $existCampaign->id);
                    })
                    ->where([
                        'seller_id' => $sellerId,
                        'status' => ProductStatus::DRAFT
                    ]);
                $productIds = $query->pluck('id')->toArray();
                $query->update(['slug' => null]);
                $query->delete();
                $productModel = new Product();
                $productModel->elasticDeleteProductsByProductIds($productIds);
                (new SyncSlugJob(ids: $productIds, seller: $seller, isUpsert: false))->handle();
            } else if ($isSellName) {
                // don't create duplicate camp if seller sell name
                if (isset($bulk)) {
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::COMPLETED,
                        'logs' => 'Campaign already exist',
                        'campaign_id' => $existCampaign->id,
                        'campaign_slug' => $existCampaign->slug,
                        'campaign_description' => $existCampaign->description
                    ]);
                }
                return $this->successResponse([
                    'new_campaign' => $existCampaign,
                    'exist' => true
                ]);
            }
        }

        $name = trim($designInfo['name']);
        if (!empty($bulkId)) {
            $slug = CampaignService::generateCampaignSlugAndClearDraftCampaign($designInfo['slug'], '', '', $seller->id);
        } else {
            $slug = CampaignService::processBulkUploadSlug($designInfo['slug']);
        }
        $fileUrl = $designInfo['file_url'];
        $collectionName = $designInfo['collection'];
        if (!empty($bulk)) {
            if ($bulk->indefinite_article) {
                $firstChar = strtolower($name[0]);
                $isVowel = in_array($firstChar, ['a', 'e', 'i', 'o', 'u']);
                $name = ($isVowel ? 'an ' : 'a ') . $name;
            }
            if (!empty($bulk->name_prefix)) {
                $name = $bulk->name_prefix . ' ' . $name;
            }
            if (!empty($bulk->name_suffix)) {
                $name .= ' ' . $bulk->name_suffix;
            }
        }
        if ($isSellName && empty($collectionName)) {
            $collectionName = $name;
        }

        try {
            if (str_starts_with($fileUrl, 'http') && filter_var($fileUrl, FILTER_VALIDATE_URL)) {
                $action = new ImgDirectLinkAction();
                $directLink = $action->handle($fileUrl);
                $loadImage = UploadController::uploadS3FromDirectLink($directLink, 'tmp/');
                $fileUrl = $loadImage['path'];
                if (empty($loadImage)) {
                    if ($bulk) {
                        $bulk->logs = 'File artwork invalid ' . $fileUrl;
                        $bulk->status = ImportCampaignStatusEnum::FAILED;
                        $bulk->save();
                        logToDiscord('Can not download file from url: ' . $fileUrl, 'bulk_campaign');
                    }
                    return $this->errorResponse('File artwork invalid ' . $fileUrl);
                }
                if ($bulk) {
                    $design = json_decode($bulk->designs, true);
                    $design['new_url'] = $fileUrl;
                    $bulk->update([
                        'design' => json_encode($design),
                    ]);
                }
            }
            $newCampaign = CampaignService::duplicateCampaignWithFile((int)$campaignId, $fileUrl, $name, $slug, $user);
            if ($newCampaign === false) {
                logToDiscord('Duplicate failed: ' . $sellerId . ' / ' . $slug, 'bulk_campaign');
                if (!empty($bulk)) {
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::FAILED,
                        'logs' => 'Duplicate failed'
                    ]);
                }
                return $this->errorResponse('Duplicate failed');
            }

            if (!is_null($collectionName)) {
                // success
                $collection = CollectionSellerController::createCollection($collectionName, $sellerId);

                if (
                    $collection
                    && self::addCampaignToCollection($newCampaign['id'], $sellerId, $collection['collection_id'])
                ) {
                    $newCampaign['collection'] = $collection;
                }
            }

            // save log
            $ipAddress = $request->post('ip_address');
            $uploadedAt = $request->post('uploaded_at');
            try {
                if ($ipAddress && $uploadedAt) {
                    BulkUploadLog::query()->create([
                        'seller_id' => $sellerId,
                        'parent_campaign_id' => $campaignId,
                        'campaign_id' => $newCampaign['id'],
                        'file_name' => $name,
                        'ip_address' => $ipAddress,
                        'uploaded_at' => $uploadedAt
                    ]);
                }
            } catch (Exception $exception) {
                Log::error($exception->getMessage());
            }

            if (!empty($bulk)) {
                $design = json_decode($bulk->designs, true);
                $design['artwork_url'] = $newCampaign['artwork_url'];
                $templateCampaignId = $bulk->template_campaign_id ?? $bulk->campaign_id; //handle old data
                $bulk->update([
                    'template_campaign_id' => $templateCampaignId,
                    'campaign_id' => $newCampaign['id'],
                    'campaign_slug' => $newCampaign['slug'],
                    'campaign_description' => $newCampaign['description'] ?? null,
                    'designs' => json_encode($design),
                ]);
            }

            return $this->successResponse([
                'new_campaign' => $newCampaign
            ]);
        } catch (Exception $e) {
            if (!empty($bulk)) {
                $bulk->update([
                    'status' => ImportCampaignStatusEnum::FAILED,
                    'logs' => $e->getMessage()
                ]);
            }
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param bool $callFromSchedule
     * @return JsonResponse|array
     */
    public function saveCampaignDesignsBulkV2(Request $request, bool $callFromSchedule = false)
    {
        $result = [
            'success' => true,
            'message' => null,
            'data' => null
        ];
        $validator = Validator::make($request->all(), [
            'bulk_id' => 'nullable|integer',
            'data' => 'required|array',
            'campaignId' => 'required|integer',
            'storage' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            $result['success'] = false;
            $result['message'] = $validator->getMessageBag();
            if ($callFromSchedule) {
                logToDiscord('saveCampaignDesignsBulkV2 bulk_id: ' . $validator->getValue('bulk_id') . ' validate failed: ' . $validator->getMessageBag()->toJson(), 'bulk_campaign');
            }
            return $callFromSchedule ? $result : $this->errorResponse($validator->getMessageBag());
        }
        $t1 = microtime(true);
        $bulkId = $request->get('bulk_id');
        $data = $request->get('data');
        $campaignId = $request->get('campaignId');
        $storage = $request->get('storage');
        $key = $request->get('__key');
        $sellerId = currentUser()->isAdmin() || $callFromSchedule ? $request->get('seller_id') : currentUser()->getUserId();
        $seller = User::query()->find($sellerId);
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->whereKey($campaignId)
            ->withoutTrashed()
            ->first();
        if (empty($campaign)) {
            $result['status'] = false;
            $result['message'] = 'Invalid campaign id';
            return $callFromSchedule ? $result : $this->errorResponse('Invalid campaign id');
        }
        if (!$callFromSchedule) {
            if (currentUser()->isAdmin()) {
                $sellerId = $request->get('seller_id');
                if (empty($sellerId)) {
                    return $this->errorResponse('Empty seller id', 403);
                }
            } else {
                if (!currentUser()->hasPermission('update_campaign')) {
                    return $this->errorResponse('Permission denied', 403);
                }
                $sellerId = currentUser()->getUserId();
            }

            if (!empty($bulkId)) {
                $bulkData = ImportCampaignsData::query()
                    ->whereKey($bulkId)
                    ->where('seller_id', $sellerId)
                    ->where('status', ImportCampaignStatusEnum::PROCESSING)
                    ->where('type', ImportCampaignTypeEnum::REGULAR)
                    ->first();
                if (empty($bulkData)) {
                    return $this->errorResponse('Invalid bulk id');
                }
                try {
                    if (!empty($bulkData->mockups)) {
                        $bulkData->system_status = ImportCampaignSystemStatusEnum::SAVE_DESIGN;
                        $bulkData->save();
                        graylogInfo('Finish Save Design', [
                            'category' => 'bulk_campaigns',
                            'campaign_id' => $campaignId,
                            'bulk_id' => $bulkId,
                            'time' => now(),
                        ]);
                        return $this->successResponse();
                    }
                    if (!empty($bulkData->campaign_id) && (int)$bulkData->campaign_id !== (int)$campaignId) {
                        return $this->errorResponse('Invalid campaign id');
                    }
                    if (empty($bulkData->template_campaign_id)) { // handle old bulk data
                        $bulkData->template_campaign_id = $bulkData->campaign_id;
                        $bulkData->campaign_id = $campaignId;
                    }

                    $bulkData->mockups = json_encode($data);
                    $bulkData->system_status = ImportCampaignSystemStatusEnum::SAVE_DESIGN;
                    $bulkData->save();
                    graylogInfo('Finish Save Design', [
                        'category' => 'bulk_campaigns',
                        'campaign_id' => $campaignId,
                        'bulk_id' => $bulkId,
                        'time' => now(),
                    ]);
                    return $this->successResponse();
                } catch (Exception $e) {
                    logToDiscord('Save design failed: ' . $e->getMessage(), 'bulk_campaign');
                    $bulkData->update([
                        'status' => ImportCampaignStatusEnum::FAILED,
                        'system_status' => ImportCampaignSystemStatusEnum::FAILED_EXCEPTION,
                        'logs' => 'Save design failed'
                    ]);
                    return $this->errorResponse('Save design failed');
                }
            }
        } else {
            $bulkData = ImportCampaignsData::query()->whereKey($bulkId)->first();
            if (empty($bulkData)) {
                $result['status'] = false;
                $result['message'] = 'Invalid bulk id';
                return $result;
            }
            $sellerId = $bulkData->seller_id;
        }
        $t2 = microtime(true);
        try {
            $skip = false;
            $_campaignId = $campaignId;
            foreach ($data as $item) {
                if (empty($item) || !is_array($item)) {
                    $skip = true;
                    continue;
                }
                if (!empty($item['campaignId'])) {
                    $_campaignId = $item['campaignId'];
                    if ($_campaignId != $campaignId) {
                        $skip = true;
                        continue;
                    }
                }
                $this->saveOriginDesignBulkV2($item['dataOriginalDesign'], $sellerId);
                $this->save3dDesignBulkV2($item['listDesign3D'], $storage, $sellerId);
            }
            $t3 = microtime(true);
            if (!$skip) {
                self::launchBulkCampaignV2($campaignId, $seller);
            } else {
                graylogInfo("Save campaign designs bulk failed.", [
                    'category' => 'bulk_campaigns',
                    'campaign_id' => $campaignId,
                    'bulk_id' => $bulkId,
                    'data_invalid' => 1,
                    'request_designs' => $data,
                    'schedule' => $callFromSchedule ? 1 : 0
                ]);
            }
            $t4 = microtime(true);
            if ($callFromSchedule && $bulkData) {
                if ($skip) {
                    if ($_campaignId != $campaignId) {
                        $bulkData->update([
                            'status' => ImportCampaignStatusEnum::PENDING,
                            'system_status' => null,
                            'mockups' => null,
                            'logs' => 'Invalid campaign id'
                        ]);
                    }
                    $result['status'] = false;
                    $result['data'] = $data;
                    return $result;
                }
                $images = File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'campaign_id' => $bulkData->campaign_id,
                        'type' => FileTypeEnum::IMAGE,
                    ])
                    ->whereNull('type_detail')
                    ->exists();
                $design_3d = File::query()
                    ->onSellerConnection($seller)
                    ->select('file_url')->where([
                        'campaign_id' => $bulkData->campaign_id,
                        'type' => FileTypeEnum::DESIGN,
                        'option' => FileRenderType::RENDER_3D
                    ])->whereNull('type_detail')->first();
                if (empty($images) || empty($design_3d)) {
                    $logs = 'Invalid image data';
                    if (empty($design_3d)) {
                        $logs = 'Invalid design 3d data';
                    }
                    $bulkData->update([
                        'status' => ImportCampaignStatusEnum::PENDING,
                        'system_status' => null,
                        'mockups' => null,
                        'logs' => $logs
                    ]);
                    Product::query()
                        ->onSellerConnection($seller)
                        ->filterByProductOrCampaignIds([$bulkData->campaign_id])->update([
                            'status' => ProductStatus::DRAFT,
                            'sync_status' => 0,
                        ]);
                    $result['status'] = false;
                    $result['message'] = $logs;
                    return $result;
                }
                if (empty(file_exists_on_storage($design_3d->file_url)) || (file_size_on_storage($design_3d->file_url) < to_byte(50, 'KB') && CampaignService::isDesignTransparent($design_3d->file_url))) {
                    $bulkData->update([
                        'status' => ImportCampaignStatusEnum::PENDING,
                        'system_status' => null,
                        'mockups' => null,
                        'logs' => 'Design 3D file is too small'
                    ]);
                    cache()->put($bulkData->session_id, 1, CacheKeys::CACHE_1H);
                    Product::query()
                        ->onSellerConnection($seller)
                        ->filterByProductOrCampaignIds([$bulkData->campaign_id])->update([
                            'status' => ProductStatus::DRAFT,
                            'sync_status' => 0,
                        ]);
                    graylogInfo('Design 3D file is too small', [
                        'category' => 'bulk_campaigns',
                        'campaign_id' => $bulkData->campaign_id,
                        'bulk_id' => $bulkData->id,
                        'seller_id' => $bulkData->seller_id,
                        'file_url' => $design_3d->file_url,
                    ]);
                    $result['status'] = false;
                    $result['message'] = 'Design 3D file is too small';
                    return $result;
                }
            }
            if (!empty($bulkData) && !$skip) {
                $bulkData->update([
                    'status' => ImportCampaignStatusEnum::COMPLETED,
                    'campaign_id' => $campaignId,
                    'campaign_slug' => $campaign->slug,
                    'campaign_description' => $campaign->description,
                    'logs' => null,
                    'temp_status' => 0
                ]);
                VerifyCampaignDesignAfterRenderJob::dispatch($bulkData->id, $seller, 2)->onQueue(QueueName::BULK_CAMPAIGN);
            }
            $t5 = microtime(true);
            $response = [];
            $duration = [
                't0' => $t5 - $t1,
                't1' => $t2 - $t1,
                't2' => $t3 - $t2,
                't3' => $t4 - $t3,
                't4' => $t5 - $t4,
            ];
            graylogInfo("Save campaign designs bulk completed.", [
                'category' => 'bulk_campaigns',
                'campaign_id' => $campaignId,
                'bulk_id' => $bulkId,
                'duration' => $duration,
                'schedule' => $callFromSchedule ? 1 : 0
            ]);
            if ($key === 'd8016d262b99af04f6b115b2277df725') { //__debug__
                $response['duration'] = $duration;
            }
            $result['data'] = $response;
            return $callFromSchedule ? $result : $this->successResponse($response);
        } catch (Exception $e) {
            if (!empty($bulkData)) {
                $bulkData->update([
                    'status' => ImportCampaignStatusEnum::FAILED,
                    'system_status' => ImportCampaignSystemStatusEnum::FAILED_EXCEPTION,
                    'logs' => 'Process design failed'
                ]);
                logException($e, 'saveCampaignDesignsBulkV2:bulkId -> ' . $bulkData->id, 'bulk_campaign', true);
            } else {
                logException($e, 'saveCampaignDesignsBulkV2', 'bulk_campaign', true);
            }
            $result['status'] = false;
            $result['message'] = "{$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}";
            return $callFromSchedule ? $result : $this->errorResponse("{$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}");
        }
    }

    private function saveOriginDesignBulkV2($originDesigns, $sellerId = null): void
    {
        $userId = $sellerId ?? currentUser()->getUserId();
        $seller = User::query()->find($userId);
        if (isset($originDesigns['print_space'])) { // put in array if submit single design
            $originDesigns = [$originDesigns];
        }
        $campaignIds = [];
        $newFilesCreate = [];
        foreach ($originDesigns as $originDesign) {
            if (!isset($originDesign['file_url'])) {
                continue;
            }
            $designJson = $originDesign['design_json'];
            $fileUrl = $originDesign['file_url'];
            $productId = $originDesign['product_id'];
            $campaignId = $originDesign['campaign_id'];
            $printSpace = $originDesign['print_space'];
            $option = Arr::get($originDesign, 'option', FileRenderType::PRINT);
            if (!in_array($campaignId, $campaignIds)) {
                $campaignIds[] = $campaignId;
            }
            $newFilesCreate[] = [
                'type' => FileTypeEnum::DESIGN,
                'option' => $option,
                'product_id' => $productId,
                'campaign_id' => $campaignId,
                'design_json' => $designJson,
                'file_url' => $fileUrl,
                'print_space' => $printSpace,
                'seller_id' => $userId
            ];
        }
        if (!empty($newFilesCreate)) {
            foreach (array_chunk($newFilesCreate, 2) as $newFileCreate) {
                File::query()->onSellerConnection($seller)->insert($newFileCreate);
            }
        }
        foreach ($campaignIds as $campaignId) {
            ScanCampaignCopyright::dispatch($campaignId, sellerId: $userId);
        }
    }

    private function save3dDesignBulkV2($data, $storage = '', $sellerId = null): void
    {
        $sellerId = $sellerId ?? currentUser()->getUserId();
        $seller = User::query()->find($sellerId);
        $newFilesCreate = [];
        foreach ($data as $uploadedFile) {
            if (!isset($uploadedFile['file_url'])) {
                continue;
            }
            $fileUrl = $uploadedFile['file_url'];
            $productId = $uploadedFile['product_id'];
            $campaignId = $uploadedFile['campaign_id'];
            $mockupId = $uploadedFile['mockup_id'];
            $printSpace = $uploadedFile['print_space'];

            File::query()
                ->onSellerConnection($seller)
                ->where([
                    'product_id' => $productId,
                    'type' => FileTypeEnum::DESIGN,
                    'option' => FileRenderType::RENDER_3D,
                    'mockup_id' => $mockupId,
                    'print_space' => $printSpace,
                ])
                ->delete();

            if (!empty($fileUrl)) {
                $fileUrl1 = null;
                $fileUrl2 = null;
                if ($storage === 's3') {
                    $fileUrl1 = self::saveTempDesignFile($fileUrl, $campaignId);
                } else {
                    $fileUrl2 = $fileUrl;
                }

                $newFilesCreate[] = [
                    'file_url' => $fileUrl1,
                    'file_url_2' => $fileUrl2,
                    'campaign_id' => $campaignId,
                    'mockup_id' => $mockupId,
                    'product_id' => $productId,
                    'type' => FileTypeEnum::DESIGN,
                    'option' => FileRenderType::RENDER_3D,
                    'print_space' => $printSpace,
                    'seller_id' => $sellerId
                ];
            }
        }
        if (!empty($newFilesCreate)) {
            foreach (array_chunk($newFilesCreate, 2) as $newFileCreate) {
                File::query()->onSellerConnection($seller)->insert($newFileCreate);
            }
        }
    }

    /**
     * @param $campaignId
     * @param null $seller
     * @return bool
     */
    public static function launchBulkCampaignV2($campaignId, $seller = null): bool
    {
        $draftCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', $campaignId)
            ->where('status', ProductStatus::DRAFT)
            ->first();

        if (empty($draftCampaign)) {
            return false;
        }

        self::generateImageFilesCampaign($draftCampaign, $seller);

        if ($draftCampaign->tm_status === CampaignTrademarkStatusEnum::TM) {
            Product::query()
                ->onSellerConnection($seller)
                ->where('id', $campaignId)
                ->orWhere('campaign_id', $campaignId)
                ->update(['status' => ProductStatus::BLOCKED]);
        } else {
            $query = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'seller_id' => $seller->id,
                    'status' => ProductStatus::DRAFT,
                ])
                ->where(function ($query) use ($campaignId) {
                    $query->where('id', $campaignId)
                        ->orWhere('campaign_id', $campaignId);
                });
            $arrayScore = getScoreProductBySales();
            $query->update([
                'status' => ProductStatus::ACTIVE,
                'time_score' => $arrayScore['timestamp'],
                'score' => $arrayScore['score'],
            ]);

            Product::query()
                ->where('id', $draftCampaign->default_product_id)
                ->update([
                    'score' => $arrayScore['score'] + 1
                ]);
        }
        // sync to elastic
        try {
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, sellerId: $seller->id);
        } catch (Throwable $e) {
            Product::query()
                ->onSellerConnection($seller)->filterByProductOrCampaignIds([$campaignId])->update(['sync_status' => 0]);
        }

        return true;
    }

    /**
     * @param ReviewTrademarkCampaignRequest $request
     * @return JsonResponse
     */
    public function reviewTradeMarkCampaign(ReviewTrademarkCampaignRequest $request): JsonResponse
    {
        $campaignId = $request->input('campaign_id');
        $approved = $request->input('approved');
        $tmStatus = !is_null($approved) && $approved > 0
            ? CampaignTrademarkStatusEnum::VERIFIED
            : CampaignTrademarkStatusEnum::TM;
        $seller = currentUser()->getInfoAccess();
        try {
            $updated = Campaign::query()
                ->onSellerConnection($seller)
                ->where('id', $campaignId)
                ->update(
                    [
                        'tm_status' => $tmStatus,
                        'sync_status' => 0,
                    ]
                );

            return $updated ? $this->successResponse() : $this->errorResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getFonts(Request $request): JsonResponse
    {
        $currentUserId = currentUser()->getUserId();
        $seller = User::query()->find($currentUserId);
        $keywords = $request->get('q');
        $limit = 100;

        $query = File::query()
            ->select('file_name', 'file_url')
            ->when($keywords, function ($query) use ($keywords) {
                $query->where('file_name', 'like', '%' . $keywords . '%');
            })
            ->where('type', FileTypeEnum::FONT)
            ->where(function ($query) use ($currentUserId) {
                $query->whereNull('seller_id')
                    ->orWhere('seller_id', '=', $currentUserId);
            })
            ->orderByDesc('seller_id')
            ->orderBy('file_name')
            ->limit($limit);
        $fontSeller = $query->clone()->onSellerConnection($seller)->get();
        $fonts = collect();
        if ($fontSeller->count() < $limit) {
            $fontSystem = $query->clone()->get();
            $fonts = $fontSeller->concat($fontSystem);
        }
        return $this->successResponse($fonts);
    }

    public function getReviewCampaignInfo(Request $request, string $campaignId): JsonResponse
    {
        $sellerId = $request->get('seller_id');
        $seller = User::query()->find($sellerId);
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->with([
                'designs' => function ($query) {
                    $query->where([
                        'type' => FileTypeEnum::DESIGN,
                        'option' => 'print'
                    ]);
                },
                'stores:id,name',
                'collections' => fn($query) => $query->select(['id', 'name', 'slug'])->where('product_collection.seller_id', $sellerId)
            ])
            ->where('id', $campaignId)
            ->first();
        if ($campaign) {
            return $this->successResponse($campaign);
        }
        return $this->errorResponse();
    }

    public function setCampaignStatus(Request $request, $campaignId): JsonResponse
    {
        $status = $request->json('status');
        $sellerId = $request->get('seller_id');
        $seller = User::query()->find($sellerId);
        $query = Product::query()
            ->onSellerConnection($seller)
            ->where('id', $campaignId)
            ->orWhere('campaign_id', $campaignId);

        if ($status === 'active') {
            $updated = $query->update([
                'public_status' => CampaignPublicStatusEnum::APPROVED,
                'sync_status' => 0,
            ]);
        } else {
            $updated = $query->update([
                'public_status' => CampaignPublicStatusEnum::REJECTED,
                'sync_status' => 0,
            ]);
        }

        if (is_numeric($updated) && $updated > 0) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    /**
     * Delete draft campaigns
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function editCampaignDesign(Request $request): JsonResponse
    {
        $campaignId = $request->json('campaign_id');
        $seller = currentUser()->getInfoAccess();
        if (empty($campaignId)) {
            return $this->errorResponse();
        }

        try {
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->whereNotIn('product_type', [ProductType::CAMPAIGN_EXPRESS, ProductType::CAMPAIGN_TEMPLATE])
                ->where([
                    'id' => $campaignId,
                    'seller_id' => $seller->id
                ])
                ->first();

            if (is_null($campaign)) {
                return $this->errorResponse('Campaign not found.');
            }

            if ($campaign->status === CampaignStatusEnum::DRAFT) {
                return $this->successResponse();
            }

            if (!in_array($campaign->status, [CampaignStatusEnum::INACTIVE, CampaignStatusEnum::PENDING, CampaignStatusEnum::ERROR])) {
                return $this->errorResponse('Can not edit active campaign');
            }

            $campaign->update(['status' => CampaignStatusEnum::DRAFT]);

            $orderCount = OrderProduct::query()
                ->join('order', 'order.id', '=', 'order_product.order_id')
                ->where('campaign_id', $campaignId)
                ->whereIn('order.payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->groupBy('campaign_id')
                ->count();

            // allow editing design if it has no order
            if ($orderCount === 0) {
                Product::query()
                    ->where('campaign_id', $campaignId)
                    ->update(['status' => CampaignStatusEnum::DRAFT]);
            }

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public static function saveTempDesignFile($tempPath, $campaignId): string
    {
        if (!$tempPath || !$campaignId) {
            return '';
        }
        if (str_starts_with($tempPath, 'p/' . $campaignId)) {
            return $tempPath;
        }
        $filePaths = explode('/', $tempPath);
        $fileName = end($filePaths);
        $newPath = 'p/' . $campaignId . '/' . $fileName;
        saveTempFileAws($tempPath, $newPath);
        return $newPath;
    }

    public function getProductImagesFromCampaign($campaignId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $campaignQuery = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id
            ]);
        if (!$campaignQuery->exists()) {
            return $this->errorResponse('Invalid campaign');
        }
        $images = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'file_url',
                'file_url_2',
                'option',
                'design_id',
                'campaign_id',
                'product_id',
                'token',
                'type_detail',
                'mockup_id'
            ])
            ->where([
                'type' => FileTypeEnum::IMAGE,
                'campaign_id' => $campaignId
            ])
            ->where(function ($query) {
                $query->whereNotNull('design_id')
                    ->orWhere('type_detail', FileRenderType::CUSTOM);
            })
//            ->whereNull('option')
            ->groupBy('design_id')
            ->get()
            ->map(function ($image) {
                $image->mapped_file_url = $image->type_detail === FileRenderType::CUSTOM ? $image->file_url : $image->file_url_2;
                return $image;
            });

        return $this->successResponse($images);
    }

    /**
     * @param SaveProductThumbnailRequest $request
     * @return JsonResponse
     */
    public function saveProductsThumbnail(SaveProductThumbnailRequest $request): JsonResponse
    {
        $data = $request->input('data');
        $data = collect($data)->map(function ($item) {
            $newData = [];
            if (!empty($item['product_id'])) {
                $newData['id'] = $item['product_id'];
            }
            if (!empty($item['campaign_id'])) {
                $newData['campaign_id'] = $item['campaign_id'];
            }
            if (!empty($item['thumb_url'])) {
                $newData['thumb_url'] = $item['thumb_url'];
            }
            if (!empty($item['default_color'])) {
                $newData['default_option'] = $item['default_color'];
            }
            return $newData;
        });

        try {
            DB::beginTransaction();
            $countUpdatedProducts = batch()->updateWithTwoIndex(new Product([], true), $data->toArray(), 'id', 'campaign_id');
            DB::commit();
            return $this->successResponse($countUpdatedProducts);
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage(), 403);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkListOnMarketplace(Request $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $campaignIds = $request->json('campaign_ids');

        if (count($campaignIds) === 0) {
            return $this->errorResponse();
        }

        try {
            DB::beginTransaction();
            $query = Campaign::query()
                ->onSellerConnection($seller)
                ->whereIn('id', $campaignIds)
                ->where([
                    'public_status' => CampaignPublicStatusEnum::NO,
                    'seller_id' => $seller->id,
                ]);

            $campaigns = $query->get();

            if ($campaigns->count() === 0) {
                return $this->errorResponse('Please select campaigns');
            }

            $query->update(['public_status' => CampaignPublicStatusEnum::YES]);

            self::updateProductSyncStatus($campaignIds, true, sellerId: $seller->id);
            DB::commit();
            return $this->successResponse();
        } catch (Throwable $e) {
            DB::rollBack();
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param SaveSellerCustomDesignRequest $request
     * @return JsonResponse
     */
    public function saveSellerCustomDesigns(SaveSellerCustomDesignRequest $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $campaignId = $request->input('campaign_id');
        $designs = $request->input('designs');
        $query = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'seller_id' => $seller->id,
                'id' => $campaignId
            ]);
        if (!$query->exists()) {
            return $this->errorResponse('Campaign not found', 403);
        }
        $newFilePath = 'p/' . $campaignId . '/custom/';
        try {
            $responseData = [];
            foreach ($designs as $design) {
                $designName = strtolower($design['name']);
                $responseData[] = explode('.', $designName, 2)[0];
                $tempPath = $design['path'];
                saveTempFileAws($tempPath, $newFilePath);
            }
            return $this->successResponse($responseData, 'Save files success');
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage(), 401);
        }
    }

    /**
     * @return JsonResponse|BinaryFileResponse
     */
    public function downloadBulkLogs()
    {
        $userId = currentUser()->getUserId();
        $logs = BulkUploadLog::query()
            ->select([
                'seller_id',
                'campaign_id',
                'parent_campaign_id',
                'file_name',
                'ip_address',
                'uploaded_at',
                'created_at',
            ])
            ->where('seller_id', $userId)
            ->where('created_at', '>=', now()->subHours(24))
            ->get();

        if ($logs->isEmpty()) {
            return $this->errorResponse();
        }

        $logs->transform(function (BulkUploadLog $log) {
            $log->makeHidden(['seller_id']);
            return $log;
        });

        $exporter = new BulkUploadLogExport($logs, [
            'Campaign ID',
            'Parent ID',
            'File Name',
            'IP Address',
            'Uploaded At',
            'Created At'
        ]);

        return Excel::download($exporter, 'bulk_upload_logs.xlsx');
    }

    public function customOptionMockupUpload(CustomOptionMockupUploadRequest $request, $campaignId): JsonResponse
    {
        $user = currentUser();
        $seller = currentUser()->getInfoAccess();

        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }

        $productId = $request->input('product_id');
        $printSpace = $request->input('print_space', 'front');
        $filePath = $request->input('file_path');

        $query = File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $user->getUserId(),
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
                'status' => FileStatusEnum::ACTIVE
            ]);

        $mockupNumbers = $query->clone()->where('product_id', $productId)->count();

        if ($mockupNumbers >= 5) {
            return $this->errorResponse('The maximum number of mockups is 5');
        }

        DB::beginTransaction();
        try {
            $newPath = 'p/' . $campaignId;
            $newThumbUrl = saveTempFileAws($filePath, $newPath);

            File::query()
                ->onSellerConnection($seller)->create([
                    'file_url' => $newThumbUrl,
                    'type' => FileTypeEnum::IMAGE,
                    'type_detail' => FileRenderType::CUSTOM,
                    'print_space' => $printSpace,
                    'seller_id' => $user->getUserId(),
                    'campaign_id' => $campaignId,
                    'product_id' => $productId,
                    'position' => $mockupNumbers + 1
                ]);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Upload mockup failed' . $exception->getMessage());
        }

        $mockups = $query->addSelect([
            'id',
            'product_id',
            'file_url',
            'design_json',
            'option',
            'type',
            'type_detail',
            'color_fillable',
            'print_space',
            'position',
        ])->orderBy('position')
            ->get()
            ->map(function ($item) {
                $item->selected = false;
                return $item;
            });

        return $this->successResponse($mockups);
    }

    public function getCustomOptionMockups($campaignId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $mockups = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'product_id',
                'file_url',
                'design_json',
                'option',
                'type',
                'type_detail',
                'color_fillable',
                'print_space',
                'position',
            ])
            ->where([
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
                'campaign_id' => $campaignId,
                'status' => FileStatusEnum::ACTIVE,
            ])
            ->orderBy('position')
            ->get()
            ->map(function ($item) {
                $item->selected = false;
                return $item;
            });

        return $this->successResponse($mockups);
    }

    public function customOptionMockupRemove(Request $request, $campaignId): JsonResponse
    {
        $user = currentUser();
        $seller = currentUser()->getInfoAccess();

        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }

        $mockupId = $request->input('mockup_id');

        $query = File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $user->getUserId(),
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
            ]);

        $mockup = $query->clone()->findOrFail($mockupId);

        DB::beginTransaction();
        try {
            delete_s3_files([$mockup->toArray()]);
            $mockup->delete();

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Remove mockup failed');
        }

        $mockups = $query->addSelect([
            'id',
            'product_id',
            'file_url',
            'design_json',
            'option',
            'type',
            'type_detail',
            'color_fillable',
            'print_space',
            'position',
        ])->orderBy('position')
            ->get()
            ->map(function ($item) {
                $item->selected = false;
                return $item;
            });

        return $this->successResponse($mockups);
    }

    /**
     * @throws \JsonException
     */
    public function saveBlockedCampaigns(Request $request): JsonResponse
    {
        $campaignIds = $request->input('ids');
        if (!is_array($campaignIds)) {
            $campaignIds = [$campaignIds];
        }
        $campaignIds = array_filter_empty($campaignIds);
        if (empty($campaignIds)) {
            return $this->errorResponse('Invalid campaign ids');
        }

        try {
            $campaignBlocked = SystemConfig::query()
                ->where('key', CacheKeys::BLOCKED_CAMP_IDS)
                ->first();

            if (!is_null($campaignBlocked)) {
                $campaignBlockedIds = json_decode($campaignBlocked->json_data, true);

                if (is_null($campaignBlockedIds)) {
                    $campaignBlockedIds = [];
                }

                $campaignIds = array_diff($campaignIds, $campaignBlockedIds);
                $campaignIds = array_merge($campaignIds, $campaignBlockedIds);
            } else {
                $campaignBlocked = new SystemConfig();
                $campaignBlocked->key = CacheKeys::BLOCKED_CAMP_IDS;
            }
            $campaignIds = array_slice($campaignIds, 0, 1000);

            $campaignBlocked->json_data = json_encode($campaignIds, JSON_THROW_ON_ERROR);
            $campaignBlocked->save();

            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @throws \JsonException
     */
    public function saveBlockedCampaigns2($campaignId): JsonResponse
    {
        try {
            $campaignBlocked = SystemConfig::query()
                ->where('key', CacheKeys::BLOCKED_CAMP_IDS)
                ->first();

            $campaignBlockedIds = [$campaignId];

            if (!is_null($campaignBlocked)) {
                $campaignBlockedIds = json_decode($campaignBlocked->json_data, true);

                if (is_null($campaignBlockedIds)) {
                    $campaignBlockedIds = [];
                }

                if (!in_array($campaignId, $campaignBlockedIds)) {
                    //push $campaignId to head of array
                    array_unshift($campaignBlockedIds, $campaignId);
                }
            } else {
                $campaignBlocked = new SystemConfig();
                $campaignBlocked->key = CacheKeys::BLOCKED_CAMP_IDS;
            }
            $campaignBlockedIds = array_slice($campaignBlockedIds, 0, 1000);

            $campaignBlocked->json_data = json_encode($campaignBlockedIds, JSON_THROW_ON_ERROR);
            $campaignBlocked->save();
            return $this->successResponse($campaignId);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getBlockedCampaigns(): JsonResponse
    {
        try {
            $campaignBlocked = SystemConfig::query()
                ->where('key', CacheKeys::BLOCKED_CAMP_IDS)
                ->first();

            $campaignBlockedIds = json_decode($campaignBlocked->json_data, true);

            return $this->successResponse($campaignBlockedIds);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function resyncCampaigns(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required',
                'index' => 'nullable|in:products,products_archived,products_sub'
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }
            $campaignIds = $request->input('ids');
            $index = $request->input('index', 'products');
            $tempStatus = $request->input('temp_status', 10);
            $campaignIds = explode(',', $campaignIds);
            $campaignIds = array_filter($campaignIds, function ($id) {
                return (int)$id > 0;
            });
            $campaignIds = array_unique($campaignIds);
            if (empty($campaignIds)) {
                return $this->errorResponse('Invalid campaign ids');
            }
            $updatedIds = [];
            if (count($campaignIds) <= 50) {
                $productModel = new Product();
                $productModel->elasticDeleteProductsByProductIds($campaignIds, $index);
                foreach ($campaignIds as $campaignId) {
                    (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId);
                    $updatedIds[] = $campaignId;
                }
                return $this->successResponse($updatedIds);
            }
            foreach (array_chunk($campaignIds, 500) as $campaignId) {
                $query = Product::query()->withTrashed();
                $query->where(function ($q) use ($campaignId) {
                    $q->orWhereIn('id', $campaignId);
                    $q->orWhereIn('campaign_id', $campaignId);
                });
                $updated = $query->update([
                    'sync_status' => 0,
                    'temp_status' => $tempStatus
                ]);
                if ($updated) {
                    $updatedIds = array_merge($updatedIds, $campaignId);
                }
            }
            return $this->successResponse($updatedIds);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteCampaigns(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required',
                'index' => 'required|in:products,products_archived,products_sub'
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }
            $campaignIds = $request->input('ids');
            $index = $request->input('index', 'products_archived');
            $campaignIds = explode(',', $campaignIds);
            $campaignIds = array_filter($campaignIds, function ($id) {
                return (int)$id > 0;
            });
            $campaignIds = array_unique($campaignIds);
            if (empty($campaignIds)) {
                return $this->errorResponse('Invalid campaign ids');
            }
            if (empty($index)) {
                $index = 'products_archived';
            }
            $productModel = new Product();
            $productModel->elasticDeleteProductsByProductIds($campaignIds, $index);
            return $this->successResponse($campaignIds);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    //clone \App\Http\Controllers\Admin\FulfillController::getFiles function
    public function getFileDesign(int $campaignId): JsonResponse
    {
        $sellerId = (new Elastic())->getCampaignDetail(['seller_id'], ['id' => $campaignId])['seller_id'] ?? '';
        $seller = User::query()->find($sellerId ?? currentUser()->getUserId());
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->whereKey($campaignId)
            ->first();
        if (empty($campaign) || empty($campaign->default_product_id)) {
            return $this->errorResponse('Campaign not found');
        }
        $product = Product::query()
            ->onSellerConnection($seller)
            ->select(
                [
                    'id',
                    'thumb_url',
                    'supplier_id',
                    'personalized',
                    'template_id',
                    'campaign_id',
                    'full_printed',
                ]
            )
            ->whereKey($campaign->default_product_id)
            ->first();
        if (empty($product)) {
            return $this->errorResponse('Product not found');
        }
        $files = File::query()
            ->onSellerConnection($seller)
            ->select(
                [
                    'id',
                    'file_url',
                    'file_url_2',
                    'type',
                    'print_space',
                    'design_json',
                    'seller_id',
                ]
            )
            ->where('product_id', $product->id)
            ->where('type', FileTypeEnum::DESIGN)
            ->where('option', FileRenderType::PRINT)
            ->orderBy('print_space')
            ->get();

        $expressCampDesign = null;
        if ($campaign->product_type === ProductType::CAMPAIGN_EXPRESS) {
            $expressCampDesign = File::query()
                ->onSellerConnection($seller)
                ->select([
                    'id',
                    'file_url',
                    'file_url_2',
                    'type',
                    'print_space',
                ])
                ->where([
                    'campaign_id' => $campaignId,
                    'option' => FileRenderType::PRINT,
                    'type' => FileTypeEnum::DESIGN
                ])
                ->first();
        }

        if (!empty($expressCampDesign)) {
            $files->push($expressCampDesign);
        }

        $files->map(function ($file) use ($product, $campaign) {
            $file->type = FileTypeEnum::DESIGN;
            $file->table = 'file';
            if (empty($file->file_url_2) && $this->shouldUseUrl2($product, $campaign)) {
                $file->file_url_2 = $file->file_url;
            }
            $file->design_url = $file->design_to_print_url;
            return $file;
        });

        $mockups = File::query()
            ->onSellerConnection($seller)
            ->select(
                [
                    'id',
                    'file_url',
                    'file_url_2',
                    'print_space',
                ]
            )
            ->where('product_id', $product->id)
            ->where('type', FileTypeEnum::IMAGE)
            ->groupBy('print_space')
            ->orderBy('print_space')
            ->orderBy('position')
            ->get();

        $templateId = $product->template_id;
        $printSpace = cacheAlt()->remember(CacheKeys::getTemplateProductByTemplateId($templateId), CacheKeys::CACHE_30D, function () use ($templateId) {
            return Template::query()->whereKey($templateId)->first();
        });
        $printSpacesTemplate = json_decode(
            $printSpace->print_spaces ?? '[]',
            true
        );

        foreach ($files as &$file) {
            $mockup = null;
            foreach ($mockups as $index => $each) {
                if ($each->print_space === $file->print_space) {
                    $mockup = $each;
                    unset($mockups[$index]);
                    break;
                }
            }
            // get print space info to validate design size
            $printSpace = !empty($printSpacesTemplate) ? $printSpacesTemplate[0] : null;

            if (isset($printSpacesTemplate)) {
                foreach ($printSpacesTemplate as $each) {
                    if ($each['name'] === $file->print_space) {
                        $printSpace = $each;
                        break;
                    }
                }
            }

            $file->printSpace = adjustPrintSpace($printSpace);
            $file->mockup_url = $this->getMockupUrl($mockup, $file, $product);
            $file->has_design_json = false;
            if (!empty($file->design_json)) {
                $file->has_design_json = true;
            }
            unset($file->design_json);
        }

        $mockups->map(function ($mockup) {
            $mockup->mockup_url = $mockup->mockup_to_print_url;
            return $mockup;
        });

        return $this->successResponse([
            'files' => $files,
            'mockups' => $mockups,
            'product_id' => $product->id,
        ]);
    }

    private function shouldUseUrl2(Product $product, Campaign $campaign): bool
    {
        return ($product->full_printed === ProductPrintType::PRINT_3D_FULL ||
            ($product->full_printed === ProductPrintType::PRINT_2D && $product->personalized === PersonalizedType::NONE) ||
            (in_array($campaign->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true))
        );
    }

    private function getMockupUrl($mockup, $file, $product)
    {
        if (!empty($mockup)) {
            return $mockup->mockup_to_print_url;
        }

        $mockupUrl = $product->thumb_url;
        if (empty($mockupUrl)) {
            $mockupUrl = $file->design_to_print_url;
        }
        return $mockupUrl;
    }

    public function updateCampMetaData(Request $request, $campaignId)
    {
        try {
            $seller = currentUser()->getInfoAccess();
            $metaData = $request->input('meta_data');
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->firstWhere('id', $campaignId);
            $metaTitle = $metaData['meta_title'] ?? [];
            $metaDescription = $metaData['meta_description'] ?? [];
            $metaKeywords = $metaData['meta_keywords'] ?? [];

            $attributes = isset($campaign->attributes) ? json_decode($campaign->attributes, true) : [];

            $attributeImport = [
                'meta_title' => $metaTitle,
                'meta_description' => $metaDescription,
                'meta_keywords' => $metaKeywords,
            ];
            $attributes['meta_data'] = $attributeImport;
            $campaign->attributes = json_encode($attributes);
            $campaign->save();
            return $this->successResponse();
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function uploadThumbnailForComboCampaign(Request $request, int $campaignId): JsonResponse
    {
        if (!$this->isCampaignBelongSeller($campaignId)) {
            return $this->errorResponse('Campaign not found');
        }

        $tmp = $request->get('file_path');
        $fileName = pathinfo($tmp, PATHINFO_BASENAME);
        $newFilePath = 'p/' . $campaignId . '/' . $fileName;
        $seller = currentUser()->getInfoAccess();
        try {
            $movedPath = saveTempFileAws($tmp, $newFilePath);
            Campaign::query()
                ->onSellerConnection($seller)
                ->where('id', $campaignId)
                ->update(['thumb_url' => $movedPath]);
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, sellerId: $seller?->id);
            return $this->successResponse($movedPath);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function detailCampaign(Request $request, int $campaignId)
    {
        $sellerId = $request->get('seller_id');
        $seller = User::query()->find($sellerId);
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select(['id', 'name', 'attributes', 'seller_id', 'description'])
            ->with(['pivotCollections:id,name'])
            ->find($campaignId);
        $campaign->setRelation('collections', $campaign->pivotCollections->map(function ($collection) {
            return collect(array_merge($collection->toArray(), $collection->pivot->toArray()));
        }));
        return $this->successResponse($campaign);
    }

    public function updateCampaign(Request $request, int $campaignId): JsonResponse
    {
        try {
            $request->validate([
                'name' => ['required', 'string', 'max:200'],
                'description' => ['nullable'],
                'collections' => ['nullable', 'array', 'max:50'],
            ]);
            $seller = User::query()->find($request->get('seller_id'));
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->firstWhere('id', $campaignId);
            $campaign->name = $request->input('name');
            $campaign->description = $request->input('description');
            $requestCollections = $request->json('collections');
            if ($requestCollections) {
                $newCollections = Collection::query()->select('id')
                    ->whereIn('id', array_column($requestCollections, 'id'))
                    ->whereNotIn('id', $campaign->collections()->where('created_by', $campaign->seller_id)->pluck('id'))
                    ->get()
                    ->keyBy('id')
                    ->map(function ($item) use ($campaign) {
                        return [
                            'seller_id' => $campaign->seller_id,
                            'product_id' => $campaign->id,
                            'created_by' => 1,
                        ];
                    });
                $oldCollections = $campaign->pivotCollections()
                    ->wherePivot('created_by', $campaign->seller_id)
                    ->get()
                    ->keyBy('collection_id')
                    ->map(fn($item) => ['seller_id' => $item->seller_id, 'product_id' => $item->product_id, 'created_by' => $item->created_by]);
                $campaign->pivotCollections()->sync($newCollections->concat($oldCollections));
                $seller->pivotCollections()->syncWithoutDetaching($newCollections->keys());
            }
            $metaData = $request->input('meta_data');
            $attributes = isset($campaign->attributes) ? json_decode($campaign->attributes, true) : [];
            $attributeImport = [
                'meta_title' => $metaData['meta_title'] ?? [],
                'meta_description' => $metaData['meta_description'] ?? [],
                'meta_keywords' => $metaData['meta_keywords'] ?? [],
            ];
            $attributes['meta_data'] = $attributeImport;
            $campaign->attributes = json_encode($attributes);
            $campaign->save();
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignId, sellerId: $campaign->seller_id);
            return $this->successResponse();
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
