<?php

namespace App\Http\Controllers\Admin;

use App\Enums\CacheKeys;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminUpdateStaffStatusRequest;
use App\Http\Requests\Admin\ChangeStaffInfo;
use App\Http\Requests\Admin\CreateNewStaffRequest;
use App\Http\Requests\Admin\GetRoleOfCurrentStaff;
use App\Models\Staff;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;

class ManageStaffController extends Controller
{
    use ApiResponse;
    use HasRoles;

    public function getAllStaffs(Request $request): JsonResponse
    {
        $keywords = $request->get('q');
        $status = $request->get('status');
        $role = $request->get('role');

        $query = Staff::query()
            ->select('id', 'name', 'email', 'status')
            ->with(['roles:name', 'permissions:name']);

        if (!empty($keywords)) {
            $query->where('name', 'like', '%' . $keywords . '%')
                ->orWhere('email', 'like', '%' . $keywords . '%');
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }
        if (!empty($role)) {
            $query->role([$role]);
        }
        $staffs = $query->paginate(20)->toArray();
        $listRoles = Role::query()->select('name')->where('guard_name', 'admin')->get()->pluck('name');
        $staffs['list_roles'] = $listRoles;

        return $this->successResponse($staffs);
    }


    public function getStaffCurrentRole(GetRoleOfCurrentStaff $request): JsonResponse
    {
        $staffId = $request->input('staff_id');
        $staff = Staff::query()->find($staffId);
        $roleName = $staff->getRoleNames()->first();

        return $this->successResponse(['role' => $roleName]);
    }

    public function updateStaffInfo(ChangeStaffInfo $request): JsonResponse
    {
        $staffId = $request->input('staff_id');
        $name = $request->input('name');
        $email = $request->input('email');
        $password = $request->input('password');
        $role = $request->input('role');

        try {
            DB::beginTransaction();
            $newInfo = [];

            $newInfo['name'] = $name;
            $newInfo['email'] = $email;

            if (!empty($password)) {
                $newInfo['password'] = Hash::make($password);
            }

            $staff = Staff::query()->find($staffId);
            $staff->update($newInfo);

            $staff->syncRoles([$role]);
            $staff->syncPermissions(
                $request->input('direct_permissions')
            );

            DB::commit();

            self::clearCache();

            return $this->successResponse();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse($e->getMessage());
        }
    }

    public function createStaff(CreateNewStaffRequest $request): JsonResponse
    {
        $name = $request->input('name');
        $email = $request->input('email');
        $password = $request->input('password');
        $role = $request->input('role');

        try {
            DB::beginTransaction();
            $newInfo = [];
            $newInfo['name'] = $name;
            $newInfo['email'] = $email;
            $newInfo['password'] = Hash::make($password);

            $staff = Staff::query()->create($newInfo);

            $staff->assignRole([$role]);
            $staff->givePermissionTo(
                $request->input('direct_permissions')
            );

            DB::commit();

            self::clearCache();

            return $this->successResponse();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param AdminUpdateStaffStatusRequest $request
     * @return JsonResponse
     */
    public function changeStaffStatus(AdminUpdateStaffStatusRequest $request): JsonResponse
    {
        $staffId = $request->input('staff_id');
        $status = $request->input('status');

        $updated = Staff::query()
            ->where('id', $staffId)
            ->update(['status' => $status]);

        if (!$updated) {
            return $this->errorResponse();
        }

        self::clearCache();

        return $this->successResponse();
    }

    private static function clearCache(): void
    {
        syncClearCache([
            CacheKeys::SUPPORTERS,
            CacheKeys::ALL_STAFF
        ]);
    }
}
