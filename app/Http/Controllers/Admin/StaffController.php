<?php

namespace App\Http\Controllers\Admin;

use App\Enums\CacheKeys;
use App\Enums\DateRangeEnum;
use App\Enums\FirstOrderTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\SaleReportChartTimeRange;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\StaffStatus;
use App\Enums\SystemRole;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Analytic\Admin\GetSaleReportChartRequest;
use App\Http\Requests\SellerSaleExpiredRequest;
use App\Models\Campaign;
use App\Models\Order;
use App\Models\SellerHistory;
use App\Models\Staff;
use App\Models\User;
use App\Rules\IsCurrentPassword;
use App\Rules\IsNotCurrentPassword;
use App\Services\SaleService;
use App\Services\UserLog;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class StaffController extends Controller
{
    use ApiResponse;

    const BONUS_MARKETING_1 = 2;
    const BONUS_MARKETING_2 = 3;
    const BONUS_MARKETING_3 = 5;

    private const SS_ROLES = [
        'Seller Support',
        'SS Manager',
    ];
    private const SUPPORTER_SUPPORT_RATE = 0.05 / 100;
    private const LEADER_SUPPORT_RATE = 30 / 100;
    private const MEMBER_SUPPORT_RATE = 20 / 100;
    private const SUPPORT_REMAIN_RATE = 50 / 100;
    private const LEADER_SALE_RATE = 30 / 100;
    private const MEMBER_SALE_RATE = 70 / 100;
    private const SALE_RATE = [
        [100000, 0.2 / 100], // 0 - 100k: 0.2%
        [200000, 0.3 / 100], // 100k - 300k: 0.3%
        [300000, 0.4 / 100], // 300k - 600k: 0.4%
    ];
    private const MAX_SALE_RATE = 0.5 / 100; // > 600k: 0.5%
    private const BONUS_FF_ITEMS = 10;
    private const BONUS_PLATFORM_1 = 15;
    private const BONUS_PLATFORM_2 = 10;
    private const BONUS_PLATFORM_3 = 5;
    private int $totalSupportAccount = 0;
    private float $totalSalesOfSupport = 0;
    private float $totalSalesOfStaff = 0;
    private float $totalSalesBonusOfStaffs = 0;
    private float $totalSalesBonusOfLeader = 0;


    /**
     * @return JsonResponse
     */
    public function getInfo(): JsonResponse
    {
        $staff = Staff::query()
            ->select('id', 'email', 'name')
            ->firstWhere('id', currentUser()->getUserId());

        return $staff ? $this->successResponse($staff) : $this->errorResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function changeInfo(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();

        if (!$user->isAdmin()) {
            return $this->errorResponse('Access denied.', 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => ['required', 'email'],
            'name' => ['required', 'max:255', 'min:3']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $email = $request->json('email');
        $name = $request->json('name');

        try {
            DB::beginTransaction();
            $data = [];

            if ($email) {
                $data['email'] = $email;
            }

            if ($name) {
                $data['name'] = $name;
            }

            $result = Staff::query()
                ->where('id', $userId)
                ->update($data);

            if ($result) {
                DB::commit();

                return $this->successResponse();
            }

            DB::rollBack();
            return $this->errorResponse('Can not change info.');
        } catch (\Exception $ex) {
            DB::rollBack();
            return $this->errorResponse($ex->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function changePassword(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();

        $validator = Validator::make($request->all(), [
            'old_password' => ['required', new IsCurrentPassword()],
            'password' => ['required', new IsNotCurrentPassword()],
            'confirm_password' => ['required', 'same:password']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $password = $request->json('password');

        try {
            DB::beginTransaction();
            $updated = Staff::query()
                ->where('id', $userId)
                ->update(['password' => Hash::make($password)]);

            if ($updated) {
                DB::commit();

                return $this->successResponse();
            }

            DB::rollBack();
        } catch (\Exception $ex) {
            DB::rollBack();
        }

        return $this->errorResponse('Can\'t update password');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getSaleSupportReport(Request $request): JsonResponse
    {
        $dateRange = $request->query('date_range', DateRangeEnum::THIS_MONTH);
        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');
        $isSS = $request->boolean('is_ss');
        $timeout = CacheKeys::CACHE_30m;
        $report = cache()->remember(CacheKeys::getSsReport($dateRange, $startDate, $endDate, $isSS), $timeout, function () use ($dateRange, $startDate, $endDate, $isSS) {
            $staffs = Staff::query()
                ->select([
                    'id',
                    'name',
                    'email'
                ])
                ->with('roles:id,name')
                ->where('status', StaffStatus::ACTIVATED)
                ->when($isSS, function ($query) {
                    $query->role(self::SS_ROLES);
                })
                ->get();
            if ($staffs->isEmpty()) {
                return [];
            }
            $dateTimeQuery = [
                'date_range' => $dateRange,
                'start_date' => $startDate,
                'end_date' => $endDate
            ];
            $totalSales = $this->getTotalSales($dateTimeQuery);
            $totalSupport = $this->getTotalSales($dateTimeQuery, isSaleSupportQuery: true);
            if ($totalSales->isEmpty() && $totalSupport->isEmpty()) {
                return [];
            }
            $dateRangeFilter = SaleService::convertDateRange($dateRange, $startDate, $endDate, false);
            $sellerIdsWithoutOrders = [];
            $totalNewSales = collect();
            if ($totalSales->isNotEmpty()) {
                $sellerIds = array_map('intval', array_column($totalSales->toArray(), 'seller_id'));
                $totalNewSales = Order::query()
                    ->select(['seller_id', 'paid_at'])
                    ->whereIn('seller_id', $sellerIds)
                    ->whereIn('payment_status', [
                        OrderPaymentStatus::PAID,
                        OrderPaymentStatus::PARTIALLY_REFUNDED,
                    ])
                    ->whereNotIn('status', [
                        OrderStatus::CANCELLED,
                        OrderStatus::REFUNDED
                    ])
                    ->filterQueryDateInPreviousRangeToCurrentEndDate($dateRange, $startDate, $endDate, 'paid_at', null, false, 90)
                    ->orderBy('paid_at')
                    ->get()
                    ->groupBy('seller_id')
                    ->map(function ($orders) use ($dateRangeFilter) {
                        [$startDateFilter, $endDateFilter] = $dateRangeFilter;
                        $firstOrder = null;
                        $closestOrderBeforeFirst = null;
                        foreach ($orders->toArray() as &$order) {
                            unset($order['seller_id']);
                            $paidAt = Carbon::parse($order['paid_at']);
                            if ($paidAt->gte($startDateFilter) && $paidAt->lte($endDateFilter)) {
                                if ($firstOrder === null || $paidAt->lt(Carbon::parse($firstOrder['paid_at']))) {
                                    $firstOrder = $order;
                                }
                            } elseif ($paidAt->lt($startDateFilter)) {
                                if ($closestOrderBeforeFirst === null || $paidAt->gt(Carbon::parse($closestOrderBeforeFirst['paid_at']))) {
                                    $closestOrderBeforeFirst = $order;
                                }
                            }
                        }
                        unset($order);
                        if (!$firstOrder) {
                            return collect();
                        }
                        if (!$closestOrderBeforeFirst) {
                            return collect([$firstOrder]);
                        }
                        if (Carbon::parse($closestOrderBeforeFirst['paid_at'])->diffInDays(Carbon::parse($firstOrder['paid_at'])) < 90) {
                            return collect();
                        }
                        return collect([$firstOrder, $closestOrderBeforeFirst]);
                    });
                [$startDateFilter, $endDateFilter] = $dateRangeFilter;
                $sellerIdsWithNewSale = [];
                foreach ($sellerIds as $sellerId) {
                    if (empty($totalNewSales[$sellerId]) || count($totalNewSales[$sellerId]) === 0) {
                        continue;
                    }

                    if (count($totalNewSales[$sellerId]) === 1 && Carbon::parse($totalNewSales[$sellerId][0]['paid_at'])->between($startDateFilter, $endDateFilter)) {
                        $sellerIdsWithNewSale[] = $sellerId;
                    }

                    if (count($totalNewSales[$sellerId]) === 2) {
                        if (Carbon::parse($totalNewSales[$sellerId][0]['paid_at'])->between($startDateFilter, $endDateFilter) && Carbon::parse($totalNewSales[$sellerId][1]['paid_at'])->between($startDateFilter, $endDateFilter)) {
                            $sellerIdsWithNewSale[] = $sellerId;
                            continue;
                        }

                        if (Carbon::parse($totalNewSales[$sellerId][0]['paid_at'])->diffInDays(Carbon::parse($totalNewSales[$sellerId][1]['paid_at'])) > 90) {
                            $sellerIdsWithNewSale[] = $sellerId;
                        }
                    }
                }
                $sellerIdsWithoutOrders = $sellerIdsWithNewSale;
                $totalNewSales = $totalSales;
            }
            $staffs->each(function ($staff, $key) use (&$staffs, $totalSales, $totalSupport, $dateRange, $startDate, $endDate, $sellerIdsWithoutOrders, $totalNewSales) {
                $staff->total_support_accounts = 0;
                $staff->total_sale_accounts = 0;
                $staff->sales_of_support_staff = 0;
                $staff->sales_of_sale_staff = 0;
                $staff->support_items = 0;
                $staff->sale_items = 0;
                $staff->first_sale_accounts = 0;
                $staff->new_sale_items = 0;
                $sellerOfSaleStaff = [];
                $sellerOfSupportStaff = [];

                if ($totalSales->isNotEmpty()) {
                    $totalSales->each(function ($sale) use ($staff, &$sellerOfSaleStaff) {
                        if ($sale->sale_staff_id && (int) $staff->id === (int) $sale->sale_staff_id) {
                            $staff->sales_of_sale_staff += $sale->sales_of_sale_staff;
                            $staff->sale_items += $sale->sale_items;
                            $this->totalSalesOfStaff += $sale->sales_of_sale_staff;
                            $staff->total_sale_accounts++;
                            $sellerOfSaleStaff[] = $sale->seller_id;
                        }
                    });
                }

                if ($totalSupport->isNotEmpty()) {
                    $totalSupport->each(function ($sale) use ($staff, &$sellerOfSupportStaff) {
                        if ($sale->support_staff_id && (int) $staff->id === (int) $sale->support_staff_id) {
                            $staff->sales_of_support_staff += $sale->sales_of_support_staff;
                            $staff->support_items += $sale->support_items;
                            $this->totalSalesOfSupport += $sale->sales_of_support_staff;
                            $staff->total_support_accounts++;
                            $this->totalSupportAccount++;
                            $sellerOfSupportStaff[] = $sale->seller_id;
                        }
                    });
                }

                $staff->sale_items_with_total = SaleService::totalItemsWithUnavailableOrder($sellerOfSaleStaff, [
                    'date_range' => $dateRange,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'col' => 'paid_at'
                ]);

                $staff->support_items_with_total = SaleService::totalItemsWithUnavailableOrder($sellerOfSupportStaff, [
                    'date_range' => $dateRange,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'col' => 'paid_at'
                ]);

                if ($totalNewSales->isNotEmpty()) {
                    $totalNewSales->each(function ($sale) use ($staff, $sellerIdsWithoutOrders) {
                        if ($staff->id === $sale->sale_staff_id && isset($sale->sale_expired_at) && in_array((int)$sale->seller_id, $sellerIdsWithoutOrders, true)) {
                            $staff->first_sale_accounts++;
                            $staff->new_sale_items += $sale->sale_items;
                        }
                    });
                }

                // if zero, remove it
                /** @noinspection PhpConditionAlreadyCheckedInspection */
                if ($staff->total_support_accounts === 0 && $staff->total_sale_accounts === 0) {
                    $staffs->forget($key);
                }
            });

            $staffs->each(function ($staff) {
                $staff->support_bonus = $this->getSupportBonus($staff);
                $staff->sale_bonus = $this->getSaleBonus($staff);

                // update if SS
                $staff->is_ss = false;
                if ($staff->roles->contains('name', self::SS_ROLES[0])) {
                    $this->totalSalesBonusOfLeader += $staff->sale_bonus * self::LEADER_SALE_RATE;
                    $staff->sale_bonus *= self::MEMBER_SALE_RATE;
                    $this->totalSalesBonusOfStaffs += $staff->sale_bonus;
                    $staff->is_ss = true;
                }
            });

            $data = [];
            $data['staffs'] = $staffs->sortByDesc('support_bonus')->values()->all();
            $data['support_bonus'] = $this->totalSalesOfSupport * self::SUPPORTER_SUPPORT_RATE;
            $data['leader_support_bonus'] = $data['support_bonus'] * self::LEADER_SUPPORT_RATE;
            $data['sale_bonus'] = $this->getTotalSaleBonus();
            $data['total_sale_staffs'] = $this->totalSalesOfStaff;
            $data['total_sale_support_staffs'] = $this->totalSalesOfSupport;
            $data['leader_sale_bonus'] = $this->totalSalesBonusOfLeader;
            return $data;
        });
        return $this->successResponse($report);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getNewSellerSaleSupportReport(Request $request): JsonResponse
    {
        $dateRange = $request->get('date_range', DateRangeEnum::LAST_6_MONTHS);
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $saleStaffId = $request->get('sale_staff_id');
        $noCache = $request->boolean('no_cache');
        if (!in_array($dateRange, [DateRangeEnum::LAST_6_MONTHS, DateRangeEnum::THIS_MONTH, DateRangeEnum::LAST_MONTH, DateRangeEnum::CUSTOM], true)) {
            $dateRange = DateRangeEnum::LAST_6_MONTHS;
        }
        $timeout = CacheKeys::CACHE_5m;
        if ($noCache) {
            cache()->forget(CacheKeys::getNewSsReport($dateRange, $startDate, $endDate, $saleStaffId));
        }
        $report = cache()->remember(CacheKeys::getNewSsReport($dateRange, $startDate, $endDate, $saleStaffId), $timeout, function () use ($dateRange, $startDate, $endDate, $saleStaffId) {
            $staffs = Staff::query()
                ->select([
                    'id',
                    'name',
                    'email'
                ])
                ->role(self::SS_ROLES)
                ->with('roles:id,name')
                ->where('status', StaffStatus::ACTIVATED)
                ->when(!empty($saleStaffId), function ($query) use ($saleStaffId) {
                    $query->where('id', $saleStaffId);
                })
                ->get();
            if ($staffs->isEmpty()) {
                return [];
            }
            $now = now();
            $dateRangeFilter = SaleService::convertDateRange($dateRange, $startDate, $endDate, false);
            if (!$dateRangeFilter) {
                $dateRangeFilter = SaleService::convertDateRange(DateRangeEnum::LAST_6_MONTHS, $startDate, $endDate, false);
            }
            $startDate = $dateRangeFilter[0]->startOfDay();
            $endDate = $dateRangeFilter[1]->endOfDay();
            $sellers = User::query()->selectRaw('id, tier_id, name, email, first_order_at, sale_expired_at, last_order_at, support_staff_id, sale_staff_id, first_order_type, bonus_times, created_at')
                ->role([SystemRole::SELLER])
                ->with(['support_staff:id,name,email', 'sale_staff:id,name,email', 'tier:id,name'])
                ->where('status', '<>', UserStatusEnum::DELETED)
                ->whereNotNull(['first_order_at', 'sale_expired_at', 'first_order_type'])
                ->where('sale_expired_at', '>=', $now->toDateTimeString())
                ->when(!empty($dateRangeFilter), function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('first_order_at', [$startDate->toDateTimeString(), $endDate->toDateTimeString()]);
                })
                ->whereHas('orders', function ($query) {
                    $query->whereIn('payment_status', [
                        OrderPaymentStatus::PAID,
                        OrderPaymentStatus::PARTIALLY_REFUNDED,
                    ])
                    ->whereNotIn('status', [
                        OrderStatus::CANCELLED,
                        OrderStatus::REFUNDED
                    ]);
                })
                ->when(!empty($saleStaffId), function ($query) use ($saleStaffId) {
                    $query->where('sale_staff_id', $saleStaffId);
                })
                ->orderByDesc('first_order_at')
                ->get();
            if ($sellers->isEmpty()) {
                return [];
            }
            $sellers_by_type = $sellers->groupBy('first_order_type');
            $sellers = collect();
            $total_sale_bonus = 0;
            $sale_bonus = 0;
            $leader_sale_bonus = 0;
            $paid_sale_bonus = 0;
            $sellers_by_type->each(function ($group, $type) use (&$sellers, &$sale_bonus, &$total_sale_bonus, &$paid_sale_bonus) {
                $seller_ids = $group->pluck('id')->toArray();
                $seller_fulfill_orders = collect();
                if ($type === FirstOrderTypeEnum::FULFILL) {
                    $seller_fulfill_orders = Order::query()
                    ->selectRaw('seller_id, SUM(total_quantity) as total_items')
                    ->whereIn('seller_id', $seller_ids)
                    ->whereIn('payment_status', [
                        OrderPaymentStatus::PAID,
                        OrderPaymentStatus::PARTIALLY_REFUNDED,
                    ])
                    ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                    ->whereNotIn('status', [
                        OrderStatus::CANCELLED,
                        OrderStatus::REFUNDED
                    ])
                    ->whereRaw('paid_at >= DATE_SUB(now(), INTERVAL 180 DAY)')
                    ->whereExists(function ($query) {
                        $query->select(DB::raw(1))->from('user')->whereRaw('DATE_SUB(now(), INTERVAL 180 DAY) <= first_order_at AND `order`.seller_id = `user`.id');
                    })
                    ->groupBy('seller_id')
                    ->get()
                    ->map(function ($item) {
                        $item->total_items = (int) $item->total_items;
                        return $item;
                    });
                }

                $group->each(function ($seller) use (&$sellers, $seller_fulfill_orders, &$sale_bonus, &$total_sale_bonus, &$paid_sale_bonus) {
                    $seller->bonus_times = (int) $seller->bonus_times;
                    $seller->paid_sale_bonus = 0;
                    $seller->staff_sale_bonus = 0;
                    $seller->total_staff_sale_bonus = 0;
                    $seller->platform_bonus = [
                        'first_month' => [
                            'paid' => 0,
                            'bonus' => 0,
                        ],
                        'second_month' => [
                            'paid' => 0,
                            'bonus' => 0,
                        ],
                        'third_month' => [
                            'paid' => 0,
                            'bonus' => 0,
                        ],
                    ];
                    $seller->fulfillment_bonus = [
                        'ten_items' => [
                            'paid' => 0,
                            'bonus' => 0,
                        ],
                        'fifty_items' => [
                            'paid' => 0,
                            'bonus' => 0,
                        ],
                        'hundred_items' => [
                            'paid' => 0,
                            'bonus' => 0,
                        ],
                    ];
                    $first_order_at = $seller->first_order_at;
                    $monthly_dates = $this->getMonthlyDates($first_order_at, 3);
                    $seller_report = Order::query()
                        ->selectRaw("
                        SUM(CASE WHEN DATE_FORMAT(paid_at, '%Y%m') = ? THEN 1 ELSE 0 END) AS first_month,
                        SUM(CASE WHEN DATE_FORMAT(paid_at, '%Y%m') = ? THEN 1 ELSE 0 END) AS second_month,
                        SUM(CASE WHEN DATE_FORMAT(paid_at, '%Y%m') = ? THEN 1 ELSE 0 END) AS third_month
                        ", [
                            $monthly_dates[0]->format('Ym'),
                            $monthly_dates[1]->format('Ym'),
                            $monthly_dates[2]->format('Ym')
                        ])
                        ->where('seller_id', $seller->id)
                        ->whereIn('payment_status', [
                            OrderPaymentStatus::PAID,
                            OrderPaymentStatus::PARTIALLY_REFUNDED,
                        ])
                        ->whereIn('type', [OrderTypeEnum::CUSTOM, OrderTypeEnum::REGULAR])
                        ->whereNotIn('status', [
                            OrderStatus::CANCELLED,
                            OrderStatus::REFUNDED
                        ])
                        ->first();
                    if ($seller_report) {
                        if ($seller_report->first_month > 0) {
                            $total_sale_bonus += self::BONUS_PLATFORM_1;
                            $seller->total_staff_sale_bonus += self::BONUS_PLATFORM_1;
                            if ($seller->bonus_times === 0) {
                                $seller->staff_sale_bonus += self::BONUS_PLATFORM_1;
                            } else if($seller->bonus_times >= 1) {
                                $paid_sale_bonus += self::BONUS_PLATFORM_1;
                            }
                        }
                        if ($seller_report->second_month > 0) {
                            $total_sale_bonus += self::BONUS_PLATFORM_2;
                            $seller->total_staff_sale_bonus += self::BONUS_PLATFORM_2;
                            if ($seller->bonus_times <= 1) {
                                $seller->staff_sale_bonus += self::BONUS_PLATFORM_2;
                            } else if($seller->bonus_times >= 2) {
                                $paid_sale_bonus += self::BONUS_PLATFORM_2;
                            }
                        }
                        if ($seller_report->third_month > 0) {
                            $total_sale_bonus += self::BONUS_PLATFORM_3;
                            $seller->total_staff_sale_bonus += self::BONUS_PLATFORM_3;
                            if ($seller->bonus_times <= 2) {
                                $seller->staff_sale_bonus += self::BONUS_PLATFORM_3;
                            } else if($seller->bonus_times >= 3) {
                                $paid_sale_bonus += self::BONUS_PLATFORM_3;
                            }
                        }
                        $sale_bonus += $seller->staff_sale_bonus;
                        $seller->sale_bonus = $seller->staff_sale_bonus;
                        $seller->paid_sale_bonus = ($seller->total_staff_sale_bonus - $seller->sale_bonus) * self::MEMBER_SALE_RATE;
                        $seller->staff_sale_bonus *= self::MEMBER_SALE_RATE;
                        $seller->platform_bonus = [
                            'first_month' => [
                                'paid' => $seller_report->first_month > 0 && $seller->bonus_times >= 1 ? 1 : 0,
                                'bonus' => $seller_report->first_month > 0 ? self::BONUS_PLATFORM_1 : 0,
                            ],
                            'second_month' => [
                                'paid' => $seller_report->second_month > 0 && $seller->bonus_times >= 2 ? 1 : 0,
                                'bonus' => $seller_report->second_month > 0 ? self::BONUS_PLATFORM_2 : 0,
                            ],
                            'third_month' => [
                                'paid' => $seller_report->third_month > 0 && $seller->bonus_times >= 3 ? 1 : 0,
                                'bonus' => $seller_report->third_month > 0 ? self::BONUS_PLATFORM_3 : 0,
                            ],
                        ];
                    }
                    if ($seller_fulfill_orders->isNotEmpty()) {
                        $seller_fulfill_order = $seller_fulfill_orders->where('seller_id', $seller->id)->first();
                        if ($seller_fulfill_order) {
                            if ($seller_fulfill_order->total_items >= 10) {
                                $total_sale_bonus += self::BONUS_FF_ITEMS;
                                $seller->total_staff_sale_bonus += self::BONUS_FF_ITEMS;
                                if ($seller->bonus_times === 0) {
                                    $seller->staff_sale_bonus += self::BONUS_FF_ITEMS;
                                } else if($seller->bonus_times >= 1) {
                                    $paid_sale_bonus += self::BONUS_FF_ITEMS;
                                }
                            }
                            if ($seller_fulfill_order->total_items >= 50) {
                                $total_sale_bonus += self::BONUS_FF_ITEMS;
                                $seller->total_staff_sale_bonus += self::BONUS_FF_ITEMS;
                                if ($seller->bonus_times <= 1) {
                                    $seller->staff_sale_bonus += self::BONUS_FF_ITEMS;
                                } else if($seller->bonus_times >= 2) {
                                    $paid_sale_bonus += self::BONUS_FF_ITEMS;
                                }
                            }
                            if ($seller_fulfill_order->total_items >= 100) {
                                $total_sale_bonus += self::BONUS_FF_ITEMS;
                                $seller->total_staff_sale_bonus += self::BONUS_FF_ITEMS;
                                if ($seller->bonus_times <= 2) {
                                    $seller->staff_sale_bonus += self::BONUS_FF_ITEMS;
                                } else if($seller->bonus_times >= 3) {
                                    $paid_sale_bonus += self::BONUS_FF_ITEMS;
                                }
                            }
                            $sale_bonus += $seller->staff_sale_bonus;
                            $seller->sale_bonus = $seller->staff_sale_bonus;
                            $seller->paid_sale_bonus = ($seller->total_staff_sale_bonus - $seller->sale_bonus) * self::MEMBER_SALE_RATE;
                            $seller->staff_sale_bonus *= self::MEMBER_SALE_RATE;
                            $seller->fulfillment_bonus = [
                                'ten_items' => [
                                    'paid' => $seller_fulfill_order->total_items >= 10 && $seller->bonus_times >= 1 ? 1 : 0,
                                    'bonus' => $seller_fulfill_order->total_items >= 10 ? self::BONUS_FF_ITEMS : 0,
                                ],
                                'fifty_items' => [
                                    'paid' => $seller_fulfill_order->total_items >= 50 && $seller->bonus_times >= 2 ? 1 : 0,
                                    'bonus' => $seller_fulfill_order->total_items >= 50 ? self::BONUS_FF_ITEMS : 0,
                                ],
                                'hundred_items' => [
                                    'paid' => $seller_fulfill_order->total_items >= 100 && $seller->bonus_times >= 3 ? 1 : 0,
                                    'bonus' => $seller_fulfill_order->total_items >= 100 ? self::BONUS_FF_ITEMS : 0,
                                ],
                            ];
                        }
                    }
                    $sellers->push($seller);
                });
            });

            $staffs->each(function ($staff) use ($sellers, &$leader_sale_bonus) {
                if (!isset($staff->sale_bonus)) {
                    $staff->sale_bonus = 0;
                }
                if (!isset($staff->total_sale_bonus)) {
                    $staff->total_sale_bonus = 0;
                }
                if (!isset($staff->paid_sale_bonus)) {
                    $staff->paid_sale_bonus = 0;
                }
                $staff->is_ss = true;
                $sellers->each(function ($seller) use ($staff, &$leader_sale_bonus) {
                    if ($staff->id === $seller->sale_staff_id) {
                        $staff->total_sale_bonus += $seller->total_staff_sale_bonus;
                        $staff->paid_sale_bonus += ($seller->total_staff_sale_bonus * self::MEMBER_SALE_RATE);
                        $staff->sale_bonus += $seller->staff_sale_bonus;
                        $leader_sale_bonus += ($seller->sale_bonus * self::LEADER_SALE_RATE);
                    }
                });
            });
            $sellers = $sellers->filter(function ($seller) {
                return $seller->first_order_type === 'fulfill' ||
                    (
                        $seller->platform_bonus['first_month']['bonus'] > 0 ||
                        $seller->platform_bonus['second_month']['bonus'] > 0 ||
                        $seller->platform_bonus['third_month']['bonus'] > 0 ||
                        $seller->fulfillment_bonus['ten_items']['bonus'] > 0 ||
                        $seller->fulfillment_bonus['fifty_items']['bonus'] > 0 ||
                        $seller->fulfillment_bonus['hundred_items']['bonus'] > 0
                    );
            })->values();
            return [
                'sellers' => $sellers,
                'sale_bonus' => $sale_bonus,
                'total_sale_bonus' => $total_sale_bonus,
                'leader_sale_bonus' => $leader_sale_bonus,
                'total_paid_leader_sale_bonus' => $paid_sale_bonus * self::LEADER_SALE_RATE,
                'total_paid_sale_bonus' => $paid_sale_bonus,
                'staffs' => $staffs->sortByDesc('sale_bonus')->values()->all(),
            ];
        });
        return $this->successResponse($report);
    }

    public function getNewSellerSaleMarketingReport(Request $request): LengthAwarePaginator
    {
        $dateRange = $request->get('date_range', DateRangeEnum::THIS_MONTH);
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $type = $request->get('type');
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 15);
        if (!in_array($dateRange, [DateRangeEnum::LAST_6_MONTHS, DateRangeEnum::THIS_MONTH, DateRangeEnum::LAST_MONTH, DateRangeEnum::CUSTOM], true)) {
            $dateRange = DateRangeEnum::THIS_MONTH;
        }
        $sellersHaveCampActive = collect();
        $query = User::query()
            ->where('role', '<>', UserRoleEnum::CUSTOMER)
            ->when($type === 'verified_account', function ($query) {
                $query->where('email_verified_at', '>', Carbon::now()->subDays(90));
            })
            ->filterDateRange($dateRange, $startDate, $endDate, 'created_at');
        if ($type !== 'sale_account') {
            $query->get()
                ->groupBy('db_connection')
                ->each(function ($sellerGroupBy, $dbConnection) use (&$sellersHaveCampActive) {
                    $sellersHaveCampActive = $sellersHaveCampActive->merge(Campaign::query()
                        ->on($dbConnection)
                        ->select(['seller_id', DB::raw('COUNT(id) as active_campaign_count')])
                        ->where('status', ProductStatus::ACTIVE)
                        ->whereIn('seller_id', $sellerGroupBy->pluck('id'))
                        ->groupBy('seller_id')
                        ->get());
                });
        }
        return $query
            ->with([
                'register_ads:seller_id,ads_campaign_id',
                'register_ads.ads_campaign:id,utm_campaign,extra',
                'country:code,name',
            ])
            ->withCount([
                'stores',
                'fulfillOrders as draft_fulfill_orders_count' => function ($query) {
                    $query->whereIn('status', [OrderStatus::DRAFT, OrderStatus::PENDING]);
                },
                'fulfillOrders as paid_fulfill_orders_count' => function ($query) {
                    $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                    $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                },
                'orders' => function ($query) {
                    $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                    $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                    $query->whereIn('type', OrderTypeEnum::platform());
                },
            ])
            ->withSum([
                'seller_order_products as total_fulfill_item' => function ($query) {
                    $query->whereHas('order', function ($query) {
                        $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                        $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                        $query->whereIn('type', OrderTypeEnum::fulFill());
                    });
                }
            ], 'quantity')
            ->where(function ($query) use ($sellersHaveCampActive, $type) {
                $query->whereNotNull('email_verified_at');
                $query->when($type !== 'sale_account', function ($query) use ($sellersHaveCampActive) {
                    $query->orWhereIn('id', $sellersHaveCampActive->pluck('seller_id'));
                    $query->orwhereHas('stores');
                    $query->orWhereHas('fulfillOrders');
                });
                $query->when($type !== 'active_account', function ($query) {
                    $query->orWhereHas('orders', function ($query) {
                        $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                        $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                    });
                });
            })
            ->when($type === 'sale_account', function ($query) {
                $query->having('total_fulfill_item', '>', 10);
            })
            ->paginate(perPage: $perPage, page: $page)
            ->through(function ($seller) use ($sellersHaveCampActive, $type) {
                $seller->total_fulfill_item = (int) $seller->total_fulfill_item;
                $seller->active_campaign_count = $sellersHaveCampActive->firstWhere('seller_id', $seller->id)?->active_campaign_count ?? 0;
                $seller->marketing_bonus_1 = $seller->marketing_bonus_2 = $seller->marketing_bonus_3 = 0;
                $seller->marketing_bonus_1 += self::BONUS_MARKETING_1;
                ($sellersHaveCampActive->contains('seller_id', $seller->id) || $seller->stores_count > 0 || $seller->draft_fulfill_orders_count + $seller->paid_fulfill_orders_count > 0) && $seller->marketing_bonus_2 += self::BONUS_MARKETING_2;
                ($seller->orders_count > 0 || $seller->total_fulfill_item > 10) && $seller->marketing_bonus_3 += self::BONUS_MARKETING_3;
                if ($seller->email_verified_at < Carbon::now()->subDays(90)) {
                    $seller->marketing_bonus_times < 1 && $seller->marketing_bonus_1 = 0;
                    $seller->marketing_bonus_times < 2 && $seller->marketing_bonus_2 = 0;
                    $seller->marketing_bonus_times < 3 && $seller->marketing_bonus_3 = 0;
                }
                return $seller;
            });
    }

    public function markPaidMarketingCommission(Request $request): JsonResponse
    {
        try {
            $currentUser = currentUser();
            if (!$currentUser->hasRole(SystemRole::ADMIN) && !$currentUser->hasRole(SystemRole::OPERATIONS_ADMIN)) {
                return $this->errorResponse('Access denied.', 403);
            }
            $validSellerIds = [];
            $sellersHaveCampActive = collect();
            $bonus3SellerIds = $request->get('bonus3_seller_ids');
            $bonus2SellerIds = array_diff($request->get('bonus2_seller_ids'), $bonus3SellerIds);
            $bonus1SellerIds = array_diff($request->get('bonus1_seller_ids'), $bonus2SellerIds);

            $query = User::query()
                ->where('role', '<>', UserRoleEnum::CUSTOMER)
                ->where('email_verified_at', '>', Carbon::now()->subDays(90))
                ->where('marketing_bonus_times', '<', 3)
                ->whereIn('id', [...$bonus1SellerIds, ...$bonus2SellerIds, ...$bonus3SellerIds]);
            $query->get()
                ->groupBy('db_connection')
                ->each(function ($sellerGroupBy, $dbConnection) use (&$sellersHaveCampActive) {
                    $sellersHaveCampActive = $sellersHaveCampActive->merge(Campaign::query()
                        ->on($dbConnection)
                        ->select(['seller_id', DB::raw('COUNT(id) as active_campaign_count')])
                        ->where('status', ProductStatus::ACTIVE)
                        ->whereIn('seller_id', $sellerGroupBy->pluck('id'))
                        ->groupBy('seller_id')
                        ->get());
                });
            $query->withCount([
                    'stores',
                    'fulfillOrders',
                    'orders' => function ($query) {
                        $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                        $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                        $query->whereIn('type', OrderTypeEnum::platform());
                    },
                ])
                ->withSum([
                    'seller_order_products as total_fulfill_item' => function ($query) {
                        $query->whereHas('order', function ($query) {
                            $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                            $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                            $query->whereIn('type', OrderTypeEnum::fulFill());
                        });
                    }
                ], 'quantity')
                ->where(function ($query) use ($sellersHaveCampActive) {
                    $query->orWhereIn('id', $sellersHaveCampActive->pluck('seller_id'));
                    $query->orwhereHas('stores');
                    $query->orWhereHas('fulfillOrders');
                    $query->orWhereHas('orders', function ($query) {
                        $query->whereNotIn('status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED]);
                        $query->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                    });
                })
                ->get()
                ->each(function ($seller) use (&$validSellerIds, $sellersHaveCampActive, $bonus1SellerIds, $bonus2SellerIds, $bonus3SellerIds)  {
                    $seller->total_fulfill_item = (int) $seller->total_fulfill_item;
                    $seller->active_campaign_count = $sellersHaveCampActive->firstWhere('seller_id', $seller->id)?->active_campaign_count ?? 0;
                    if ($seller->email_verified_at > Carbon::now()->subDays(90)) {
                        $seller->marketing_bonus_times < 1 && in_array($seller->id, $bonus1SellerIds) && $validSellerIds[1][] = $seller->id;
                        $seller->marketing_bonus_times < 2 && ($sellersHaveCampActive->contains('seller_id', $seller->id) || $seller->stores_count > 0 || $seller->draft_fulfill_orders_count + $seller->paid_fulfill_orders_count > 0) && in_array($seller->id, $bonus2SellerIds) &&  $validSellerIds[2][] = $seller->id;
                        $seller->marketing_bonus_times < 3 && ($seller->orders_count > 0 || $seller->total_fulfill_item > 10) && in_array($seller->id, $bonus3SellerIds) && $validSellerIds[3][] = $seller->id;
                    }
                    return $seller;
                });
            foreach($validSellerIds as $newMarketingBonusTimes => $sellerIds) {
                User::query()->whereIn('id', $sellerIds)->update(['marketing_bonus_times' => $newMarketingBonusTimes]);
            }
            return $this->successResponse();
        } catch (\Exception $exception) {
            logException($exception, 'StaffController@markPaidMarketingCommission');
            return $this->errorResponse();
        }
    }

    public function getSaleDiffReport(Request $request) {
        $dateRange1 = $request->get('date_range1', DateRangeEnum::LAST_MONTH);
        $startDate1 = $request->get('start_date1');
        $endDate1 = $request->get('end_date1');
        $dateRange2 = $request->get('date_range2', DateRangeEnum::THIS_MONTH);
        $startDate2 = $request->get('start_date2');
        $endDate2 = $request->get('end_date2');
        $noCache = $request->boolean('no_cache');
        $dateRangeValid = [DateRangeEnum::TODAY, DateRangeEnum::YESTERDAY, DateRangeEnum::LAST_WEEK, DateRangeEnum::THIS_WEEK, DateRangeEnum::LAST_6_MONTHS, DateRangeEnum::THIS_MONTH, DateRangeEnum::LAST_MONTH, DateRangeEnum::CUSTOM];
        if (!in_array($dateRange1, $dateRangeValid, true)) {
            $dateRange1 = DateRangeEnum::LAST_MONTH;
        }
        if (!in_array($dateRange2, $dateRangeValid, true)) {
            $dateRange2 = DateRangeEnum::THIS_MONTH;
        }
        if ($noCache) {
            cache()->forget(CacheKeys::getSaleDiffReport($dateRange1, $dateRange2, $startDate1, $endDate1, $startDate2, $endDate2));
        }
        $report = cache()->remember(CacheKeys::getSaleDiffReport($dateRange1, $dateRange2, $startDate1, $endDate1, $startDate2, $endDate2), CacheKeys::CACHE_24H, function () use ($dateRange1, $dateRange2, $startDate1, $endDate1, $startDate2, $endDate2) {
            $query = User::query()
                ->selectRaw(
                    'user.id, user.email, user.name, user.support_staff_id, user.sale_staff_id, sale_expired_at,
                    SUM(total_amount) as total_sales,
                    SUM(total_quantity) as total_items,
                    COUNT(order.id) as total_orders'
                )
                ->where('role', '<>', UserRoleEnum::CUSTOMER)
                ->join('order', function ($join) {
                    $join->on('user.id', '=', 'order.seller_id')
                        ->whereNotIn('order.status', [OrderStatus::REFUNDED, OrderStatus::CANCELLED])
                        ->whereIn('order.payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                })
                ->with(['support_staff:id,name,email', 'sale_staff:id,name,email'])
                ->groupBy('user.id');

            $sellers1 = $query->clone()
                ->filterDateRange($dateRange1, $startDate1, $endDate1, 'order.paid_at')
                ->get()
                ->keyBY('id')
                ->toBase();

            $sellers2 = $query->clone()
                ->filterDateRange($dateRange2, $startDate2, $endDate2, 'order.paid_at')
                ->get()
                ->keyBy('id')
                ->toBase();

            $sellers3 = $sellers1->intersectByKeys($sellers2)
                ->mapWithKeys(function ($value, $key) use ($sellers2) {
                    return [$key => [
                        '1' => $value,
                        '2' => $sellers2[$key],
                    ]];
                });

            $sellers1 = $sellers1->except($sellers3->keys());
            $sellers2 = $sellers2->except($sellers3->keys());
            return ['sellers1' => $sellers1, 'sellers2' => $sellers2, 'sellers3' => $sellers3];
        });
        return $this->successResponse($report);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function markPaidStaffCommission(Request $request)
    {
        $currentUser = currentUser();
        if (!$currentUser->hasRole(SystemRole::ADMIN) && !$currentUser->hasRole(SystemRole::OPERATIONS_ADMIN)) {
            return $this->errorResponse('Access denied.', 403);
        }
        $dateRange = $request->get('date_range', DateRangeEnum::LAST_6_MONTHS);
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $saleStaffId = $request->get('sale_staff_id');
        if (!in_array($dateRange, [DateRangeEnum::LAST_6_MONTHS, DateRangeEnum::THIS_MONTH, DateRangeEnum::LAST_MONTH, DateRangeEnum::CUSTOM], true)) {
            $dateRange = DateRangeEnum::LAST_6_MONTHS;
        }
        $request->validate([
            'update_sellers' => 'required|array',
            'update_sellers.*.seller_id' => ['required', 'integer', Rule::exists('user', 'id')],
            'update_sellers.*.bonus_times' => 'required|integer|in:1,2,3'
        ]);
        $update_sellers = $request->get('update_sellers');
        foreach ($update_sellers as $update_seller) {
            $seller_id = $update_seller['seller_id'];
            $bonus_times = $update_seller['bonus_times'];
            $seller = User::query()
                ->where('id', $seller_id)
                ->where('status', '<>', UserStatusEnum::DELETED)
                ->where('bonus_times', '<', $bonus_times)
                ->whereNotNull(['first_order_at', 'sale_expired_at', 'first_order_type', 'sale_staff_id'])
                ->where('sale_expired_at', '>=', now()->toDateTimeString())
                ->first();
            if (!$seller) {
                continue;
            }
            $seller->bonus_times = $bonus_times;
            $seller->save();
            SellerHistory::query()->insert(array(
                'seller_id' => $seller->id,
                'action' => SellerHistoryActionEnum::UPDATE_ACCOUNT,
                'details' => '[' . ($currentUser->getName() ?? $currentUser->getEmail()) . '] has confirmed the bonus payment and updated the bonus count to ' . $bonus_times,
                'seller_status' => $seller->status,
                'staff_id' => $currentUser->getUserId()
            ));
        }
        cache()->forget(CacheKeys::getNewSsReport($dateRange, $startDate, $endDate, $saleStaffId));
        cache()->forget(CacheKeys::getNewSsReport($dateRange, $startDate, $endDate));
        UserLog::logStaffActivities(null, 1);
        return $this->successResponse();
    }

    /**
     * @param $staff
     * @return float
     */
    private function getSupportBonus($staff): float
    {
        $total = self::MEMBER_SUPPORT_RATE * self::SUPPORTER_SUPPORT_RATE * $staff->sales_of_support_staff;

        if ($this->totalSupportAccount > 0) {
            $total += $staff->total_support_accounts / $this->totalSupportAccount * self::SUPPORT_REMAIN_RATE * $this->totalSalesOfSupport * self::SUPPORTER_SUPPORT_RATE;
        }

        return $total;
    }

    /**
     * @param Carbon $startDate
     * @param $months
     * @return array
     */
    private function getMonthlyDates(Carbon $startDate, $months) {
        $dates = [];
        for ($i = 0; $i < $months; $i++) {
            $dates[] = $startDate->copy()->addMonthsNoOverflow($i);
        }
        return $dates;
    }

    /**
     * @param $staff
     * @return float
     */
    private function getSaleBonus($staff): float
    {
        return $this->getSaleBonusAmountPercentage($staff->sales_of_sale_staff);
    }

    /**
     * @return float
     */
    private function getTotalSaleBonus(): float
    {
        return $this->totalSalesBonusOfStaffs + $this->totalSalesBonusOfLeader;
    }

    /**
     * @param $amount
     * @return float
     */
    private function getSaleBonusAmountPercentage($amount): float
    {
        $total = 0;
        $remaining = $amount;
        foreach (self::SALE_RATE as [$limit, $rate]) {
            $value = min($remaining, $limit);
            $total += $value * $rate;
            $remaining -= $value;
            if ($remaining <= 0) {
                break;
            }
        }
        if ($remaining > 0) {
            $total += $remaining * self::MAX_SALE_RATE;
        }
        return $total;
    }

    /**
     * @param $dateTime
     * @param $userWithMonthRange
     * @param $userWithSubmonth
     * @param $isSaleSupportQuery
     * @return Collection
     */
    public function getTotalSales($dateTime, $userWithMonthRange = null, $userWithSubmonth = null, $isSaleSupportQuery = null): Collection
    {
        $dateRange = $dateTime['date_range'];
        $startDate = $dateTime['start_date'];
        $endDate = $dateTime['end_date'];
        $subQuery = Order::query()
            ->select([
                'seller_id',
                'total_amount',
                'paid_at',
            ])
            ->withSum(['products as total_quantity' => function ($query) {
                $query->where('fulfill_status', '!=', OrderFulfillStatus::CANCELLED);
            }], 'quantity')
            ->whereIn('payment_status', [
                OrderPaymentStatus::PAID,
                OrderPaymentStatus::PARTIALLY_REFUNDED,
            ])
            ->whereNotIn('status', [
                OrderStatus::CANCELLED,
                OrderStatus::REFUNDED,
            ]);
        if ($userWithMonthRange) {
            $subQuery = $subQuery->filterQueryDateInRangeWithRound($dateRange, $startDate, $endDate, 'paid_at', null, false, $userWithMonthRange, $userWithSubmonth);
        } else {
            $subQuery = $subQuery->filterDateRange($dateRange, $startDate, $endDate, 'paid_at', null, false, null, true);
        }
        $users = User::query()
            ->select([
                'user.id as seller_id',
                'support_staff_id',
                'sale_staff_id',
                'sale_expired_at'
            ])
            ->selectRaw('SUM(CASE WHEN `user`.support_staff_id IS NOT NULL THEN total_amount END) as sales_of_support_staff')
            ->selectRaw('SUM(CASE WHEN `user`.sale_staff_id IS NOT NULL THEN total_amount END) as sales_of_sale_staff')
            ->selectRaw('SUM(CASE WHEN `user`.support_staff_id IS NOT NULL THEN total_quantity END) as support_items')
            ->selectRaw('SUM(CASE WHEN `user`.sale_staff_id IS NOT NULL THEN total_quantity END) as sale_items')
            ->joinSub($subQuery, 'order', function ($j) use ($isSaleSupportQuery) {
                $j->on('order.seller_id', 'user.id');
                if (!isset($isSaleSupportQuery) || !$isSaleSupportQuery) {
                    $j->where(function ($q) {
                        $q->orWhereNull('user.sale_expired_at');
                        $q->orWhereRaw('`order`.paid_at < `user`.sale_expired_at');
                    });
                }
            })
            ->whereNotNull('support_staff_id')
            ->orWhereNotNull('sale_staff_id');
        return $users->groupBy('user.id')->get();
    }

    /**
     * @param GetSaleReportChartRequest $request
     * @return string
     */
    public function getSaleChart(GetSaleReportChartRequest $request)
    {
        $timeRange = $request->get('sale_chart_time_range');
        $staffId = $request->get('sale_chart_staff_id');
        $user = currentUser();
        $timeout = ($user->isAdmin()) ? CacheKeys::CACHE_1H : CacheKeys::CACHE_24H;
        $dateType = !isset($timeRange) || $timeRange === SaleReportChartTimeRange::RANGE_12_MONTHS ? 'month' : 'week';
        return cache()->remember(CacheKeys::getSsChartStats($dateType, $staffId), $timeout, function () use ($staffId, $timeRange) {
            return SaleService::analyzeAccountByConditions($timeRange, $staffId, false);
        });
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getSaleSupportNewSaleItemsReport(Request $request): JsonResponse {
        $dateRange = $request->query('date_range', DateRangeEnum::THIS_MONTH);
        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');
        $timeout = CacheKeys::CACHE_30m;
        return $this->successResponse(cache()->remember(
            CacheKeys::getNewSaleItemsReport($dateRange, $startDate, $endDate),
            $timeout,
            function () use ($dateRange, $startDate, $endDate) {
                $staffs = Staff::query()
                    ->select([
                        'id',
                        'name',
                        'email'
                    ])
                    ->with('roles:id,name')
                    ->where('status', StaffStatus::ACTIVATED)
                    ->role(self::SS_ROLES)
                    ->get();
                if ($staffs->isEmpty()) {
                    return $this->successResponse();
                }

                $dateRangeFilter = SaleService::convertDateRangeForNewSaleItems($dateRange, $startDate, $endDate, false);
                $dateTimeQuery = [
                    'date_range' => DateRangeEnum::CUSTOM,
                    'start_date' => $dateRangeFilter[0],
                    'end_date' => $dateRangeFilter[1]
                ];

                $totalSales = $this->getTotalSales($dateTimeQuery);
                if ($totalSales->isEmpty()) {
                    return $this->successResponse();
                }

                $sellerIds = array_map('intval', array_column($totalSales->toArray(), 'seller_id'));
                $totalNewSales = Order::query()
                    ->select(['seller_id', 'paid_at'])
                    ->whereIn('seller_id', $sellerIds)
                    ->whereIn('payment_status', [
                        OrderPaymentStatus::PAID,
                        OrderPaymentStatus::PARTIALLY_REFUNDED,
                    ])
                    ->whereNotIn('status', [
                        OrderStatus::CANCELLED,
                        OrderStatus::REFUNDED
                    ])
                    ->filterQueryDateInPreviousRangeToCurrentEndDate(DateRangeEnum::CUSTOM, $dateRangeFilter[0], $dateRangeFilter[1], 'paid_at', null, false, 90)
                    ->orderBy('paid_at')
                    ->get()
                    ->groupBy('seller_id')
                    ->map(function ($orders) use ($dateRangeFilter) {
                        [$startDateFilter, $endDateFilter] = $dateRangeFilter;
                        $firstOrder = null;
                        $closestOrderBeforeFirst = null;
                        foreach ($orders->toArray() as &$order) {
                            unset($order['seller_id']);
                            $paidAt = Carbon::parse($order['paid_at']);
                            if ($paidAt->gte($startDateFilter) && $paidAt->lte($endDateFilter)) {
                                if ($firstOrder === null || $paidAt->lt(Carbon::parse($firstOrder['paid_at']))) {
                                    $firstOrder = $order;
                                }
                            } elseif ($paidAt->lt($startDateFilter)) {
                                if ($closestOrderBeforeFirst === null || $paidAt->gt(Carbon::parse($closestOrderBeforeFirst['paid_at']))) {
                                    $closestOrderBeforeFirst = $order;
                                }
                            }
                        }
                        unset($order);
                        if (!$firstOrder) {
                            return collect();
                        }
                        if (!$closestOrderBeforeFirst) {
                            return collect([$firstOrder]);
                        }
                        if (Carbon::parse($closestOrderBeforeFirst['paid_at'])->diffInDays(Carbon::parse($firstOrder['paid_at'])) < 90) {
                            return collect();
                        }
                        return collect([$firstOrder, $closestOrderBeforeFirst]);
                    });
                $sellerIdsWithNewSale = [];
                foreach ($sellerIds as $sellerId) {
                    if (empty($totalNewSales[$sellerId]) || count($totalNewSales[$sellerId]) === 0) {
                        continue;
                    }

                    if (count($totalNewSales[$sellerId]) === 1 && Carbon::parse($totalNewSales[$sellerId][0]['paid_at'])->between($dateRangeFilter[0], $dateRangeFilter[1])) {
                        $sellerIdsWithNewSale[] = $sellerId;
                        continue;
                    }

                    if (count($totalNewSales[$sellerId]) === 2) {
                        if (Carbon::parse($totalNewSales[$sellerId][0]['paid_at'])->between($dateRangeFilter[0], $dateRangeFilter[1]) && Carbon::parse($totalNewSales[$sellerId][1]['paid_at'])->between($dateRangeFilter[0], $dateRangeFilter[1])) {
                            $sellerIdsWithNewSale[] = $sellerId;
                            continue;
                        }

                        if (Carbon::parse($totalNewSales[$sellerId][0]['paid_at'])->diffInDays(Carbon::parse($totalNewSales[$sellerId][1]['paid_at'])) > 90) {
                            $sellerIdsWithNewSale[] = $sellerId;
                        }
                    }
                }
                $sellerIdsWithoutOrders = $sellerIdsWithNewSale;
                $totalNewSales = $totalSales;

                $staffs->each(function ($staff) use ($sellerIdsWithoutOrders, $totalNewSales) {
                    $staff->new_sale_items = 0;
                    $sellerIdsArr = [];

                    $totalNewSales->each(function ($sale) use ($staff, $sellerIdsWithoutOrders, &$sellerIdsArr) {
                        if (($staff->id === $sale->sale_staff_id) && isset($sale->sale_expired_at) && in_array((int)$sale->seller_id, $sellerIdsWithoutOrders, true)) {
                            $staff->new_sale_items += $sale->sale_items;
                            $sellerIdsArr[] = $sale->seller_id;
                        }
                    });
                    $staff->seller_id_arr = $sellerIdsArr;
                });
                $data = [];
                $data['staffs'] = $staffs->sortByDesc('support_bonus')->values()->all();
                return $data;
            }
        ));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function sellerSaleExpiredReport (SellerSaleExpiredRequest $request): JsonResponse {
        $dateRange = $request->query('date_range');
        $saleStaffId = $request->query('sale_staff_id', null);
        try {
            $response = SaleService::querySellerSaleExpired($dateRange, $saleStaffId);
            return $this->successResponse($response);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
