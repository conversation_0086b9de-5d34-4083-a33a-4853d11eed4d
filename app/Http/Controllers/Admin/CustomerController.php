<?php

namespace App\Http\Controllers\Admin;

use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Events\CustomerAddressUpdated;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\SystemConfig;
use App\Services\AddressService;
use App\Traits\ApiResponse;
use App\Traits\SetOrderCustomerInfo;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    use ApiResponse, SetOrderCustomerInfo;

    public function update(Request $request, $orderId): JsonResponse
    {
        $request->validate([
            'customer_email' => 'nullable|email',
            'customer_name' => 'required|string',
            'address' => 'required|string',
            'address_2' => 'nullable|string'
        ]);
        $country = $request->post('country');
        $disabledCountry = SystemConfig::getConfig('disable_country', '');
        $disabledCountry = explode(',', $disabledCountry);
        if (!empty($country) && !empty($disabledCountry) && in_array($country, $disabledCountry, true)) {
            return $this->errorResponse('Country: ' . $country . ' is in the no-ship list.');
        }

        $order = Order::selectForHistory([
            'id',
            'customer_email',
            'customer_name',
            'customer_phone',
            'address',
            'address_2',
            'city',
            'state',
            'postcode',
            'country',
            'mailbox_number',
            'house_number',
        ])
            ->find($orderId);

        if (is_null($order)) {
            return $this->errorResponse();
        }

        $order->customer_email = $request->post('customer_email');
        self::setCustomerInfo($request, $order);

        // check if changed
        if ($order->isDirty()) {
            $order->save();

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::EDIT_ADDRESS,
                $order->getChangesDetail(),
                OrderHistoryDisplayLevelEnum::CUSTOMER
            );
        }

        CustomerAddressUpdated::dispatch($request->post('order_number'));

        return $this->successResponse();
    }

    public function lookupByEmail(Request $request): JsonResponse
    {
        $data = $request->validate(['email' => 'required|email']);
        $lastOrderIds = self::getLastOrders('customer_email', $data['email']);

        if ($lastOrderIds->isEmpty()) {
            return $this->errorResponse();
        }

        if ($lastOrderIds->count() === 1) {
            return $this->successResponse(['order_id' => $lastOrderIds->first()->id]);
        }

        return $this->successResponse();
    }

    public function lookupByPhone(Request $request): JsonResponse
    {
        $isPublicApi = $request->hasHeader('X-TOKEN');
        $token = $request->header('X-TOKEN');

        if ($isPublicApi && $token !== config('senprints.lookup_customer_phone_token')) {
            return $this->errorResponse('Invalid token');
        }

        $data = $request->validate(['phone' => 'required|string']);
        $lastOrders = self::getLastOrders('customer_phone', $data['phone'], $isPublicApi);

        if ($lastOrders->isEmpty()) {
            return $this->errorResponse('No order found');
        }

        $recentOrder = $lastOrders->first();

        if (!$recentOrder) {
            return $this->errorResponse();
        }

        // return order_id if only one order found
        // https://discord.com/channels/874562708392005672/1052956997303160892/1053352534921711716
        if (!$isPublicApi && $lastOrders->count() === 1) {
            return $this->successResponse(['order_id' => $recentOrder->id]);
        }

        return $this->successResponse(['email' => $recentOrder->customer_email]);
    }

    private static function getLastOrders(string $column, string $input, bool $isPublicApi = false, int $limit = 2)
    {
        if (!in_array($column, ['customer_phone', 'customer_email'])) {
            throw new \InvalidArgumentException('Invalid column');
        }

        $query = Order::query();

        if ($column === 'customer_phone') {
            $query->select(['id', 'customer_email'])
                ->where('customer_phone', $input);
        }

        if ($column === 'customer_email') {
            $query->select('id')
                ->where('customer_email', $input);
        }

        if ($isPublicApi) {
            $query->where('payment_status', OrderPaymentStatus::PAID);
        } else {
            $query->whereNotIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::DELETED
            ]);
        }

        return $query
            ->orderByDesc('paid_at')
            ->limit($limit)
            ->get();
    }

    public function verifyAddress(string $orderId): JsonResponse
    {
        $order = Order::find($orderId);

        if (is_null($order)) {
            return $this->errorResponse('Order not found');
        }

        $geoApifyLog = '';
        $geoApify = AddressService::verify($order, $geoApifyLog);

        $easyPostLog = '';
        $easyPost = AddressService::easyPostVerify($order, false, $easyPostLog);

        return $this->successResponse([
            'geoapify' => $geoApify,
            'geoapify_log' => $geoApifyLog,
            'easypost' => $easyPost,
            'easypost_log' => $easyPostLog
        ]);
    }
}
