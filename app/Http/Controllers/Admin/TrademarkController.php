<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Campaign;
use App\Models\SystemConfig;
use App\Models\TrademarkResult;
use App\Traits\ApiResponse;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TrademarkController extends Controller
{
    use ApiResponse;

    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'campaign_id' => 'nullable|integer',
            'type' => 'nullable|string|in:logo,text,both',
            'status' => 'nullable|string|in:violated,flagged'
        ]);

        $perPage = $request->query('per_page', 15);
        $type = $request->query('type');
        $campaign_id = $request->query('campaign_id');
        $status = $request->query('status', 'flagged');

        $trademarkResults = TrademarkResult::query()
            ->select([
                'id',
                'seller_id',
                'campaign_id',
                'logos',
                'tags',
                'flagged',
                'status',
                'flagged_logs',
                'created_at',
            ])
            ->with('seller:id,name,email,db_connection')
            ->where(function ($query) {
                $query->whereIn('status', ['violated', 'flagged']);
                $query->orWhereNotNull('flagged');
            })
            ->when($type, function ($query, $type) {
                $query->where('flagged', $type);
            })
            ->when($campaign_id, function ($query, $campaign_id) {
                $query->where('campaign_id', $campaign_id);
            })
            ->when($status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->orderByDesc('updated_at')
            ->paginate($perPage);
        $campaigns = collect();
        $trademarkResults->getCollection()
            ->groupBy('seller.db_connection')
            ->each(function ($trademarkGroup, $dbConnection) use (&$campaigns) {
                $dbConnection = empty($dbConnection) ? config('database.default') : $dbConnection;
                $campaigns = $campaigns->merge(Campaign::query()
                    ->on($dbConnection)
                    ->select(['id', 'seller_id', 'name', 'thumb_url'])
                    ->whereIn('id', $trademarkGroup->pluck('campaign_id'))
                    ->get());
            });
        $trademarkResults->through(function (TrademarkResult $trademarkResult) use (&$campaigns) {
            $trademarkResult->setRelation('campaign',
                $campaigns->first(fn($campaign) => $campaign->id === $trademarkResult->campaign_id
                    && $campaign->seller_id === $trademarkResult->seller_id));
            return $trademarkResult;
        });
        return $this->successResponse($trademarkResults);
    }

    public function getConfig(): JsonResponse
    {
        $config = SystemConfig::query()
            ->where('key', 'scan_trademark')
            ->value('value');

        // if not created yet
        if (!$config) {
            $config = false;
        }

        return $this->successResponse((bool)$config);
    }

    public function setConfig(Request $request): JsonResponse
    {
        $value = $request->boolean('enable', true);

        try {
            SystemConfig::query()
                ->updateOrCreate(
                    ['key' => 'scan_trademark'],
                    ['value' => $value]
                );

            return $this->successResponse();
        } catch (QueryException $e) {
            return $this->errorResponse('QueryException');
        }
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = TrademarkResult::query()
            ->where('id', $id)
            ->delete();

        if ($deleted) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }
}
