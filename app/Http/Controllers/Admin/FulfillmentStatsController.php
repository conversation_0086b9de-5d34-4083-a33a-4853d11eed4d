<?php

namespace App\Http\Controllers\Admin;

use App\Enums\DateRangeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ShippingMethodEnum;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FulfillmentStatsController extends Controller
{
    use ApiResponse;
    private const PERCENTAGE_THRESHOLD = 0.2; // 20%

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request)
    {
        $dateRange = $request->query('date_range', DateRangeEnum::LAST_30_DAYS);
        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');

        $orderStats = Order::query()
            ->selectRaw('count(*) as total_orders, sum(total_quantity) as total_items')
            ->selectRaw('count(CASE WHEN type = "custom" THEN 1 END) as custom_orders, sum(CASE WHEN type = "custom" THEN total_quantity END) as custom_items')
            ->selectRaw('count(CASE WHEN type in ("' . OrderTypeEnum::FULFILLMENT . '", "'. OrderTypeEnum::FBA .'") THEN 1 END) as fulfill_orders, sum(CASE WHEN type in ("' . OrderTypeEnum::FULFILLMENT . '", "'. OrderTypeEnum::FBA .'") THEN total_quantity END) as fulfill_items')
            ->selectRaw('count(CASE WHEN fulfill_status = "on_hold" THEN 1 END) as on_hold_orders, sum(CASE WHEN fulfill_status = "on_hold" THEN total_quantity END) as on_hold_items')
            ->selectRaw('count(CASE WHEN fraud_status = "flagged" THEN 1 END) as flagged_orders, sum(CASE WHEN fraud_status = "flagged" THEN total_quantity END) as flagged_items')
            ->selectRaw('count(CASE WHEN support_status <> 0 THEN 1 END) as need_support_orders, sum(CASE WHEN support_status <> 0 THEN total_quantity END) as need_support_items')
            ->filterDateRange($dateRange, $startDate, $endDate, 'fulfilled_at')
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->where('type', '<>', OrderTypeEnum::SERVICE)
            ->first();

        if (is_null($orderStats)) {
            return $this->errorResponse();
        }

        $productStats = OrderProduct::query()
            ->selectRaw('count(DISTINCT(CASE WHEN supplier_id IS NULL THEN order_id END)) as unassigned_orders, sum(CASE WHEN supplier_id IS NULL THEN quantity END) as unassigned_items')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status = "invalid" OR fulfill_status = "reviewing" THEN order_id END)) as need_review_orders, sum(CASE WHEN fulfill_status = "invalid" OR fulfill_status = "reviewing" THEN quantity END) as need_review_items')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status = "processing" OR fulfill_status = "exception" THEN order_id END)) as in_production_orders, sum(CASE WHEN fulfill_status = "processing" OR fulfill_status = "exception" THEN quantity END) as in_production_items')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status in ("processing", "exception") and DATE_SUB(now(), INTERVAL 5 DAY) > fulfilled_at THEN order_id END)) as late_orders, sum(CASE WHEN fulfill_status in ("processing", "exception") and DATE_SUB(now(), INTERVAL 5 DAY) > fulfilled_at THEN quantity END) as late_items')
            ->whereHas('order', function ($query) use ($dateRange, $startDate, $endDate) {
                $query->filterDateRange($dateRange, $startDate, $endDate, 'fulfilled_at')
                    ->where('type', '!=', OrderTypeEnum::SERVICE);
            })
            ->first();

        if (is_null($productStats)) {
            return $this->errorResponse();
        }

        model_map($orderStats, 'intval');
        model_map($productStats, 'intval');

        $stats = [
            'total' => [
                'orders' => $orderStats->total_orders,
                'items' => $orderStats->total_items,
            ],
            'custom' => [
                'orders' => $orderStats->custom_orders,
                'items' => $orderStats->custom_items,
            ],
            'fulfill' => [
                'orders' => $orderStats->fulfill_orders,
                'items' => $orderStats->fulfill_items,
            ],
            'unassigned' => [
                'orders' => $productStats->unassigned_orders,
                'items' => $productStats->unassigned_items,
            ],
            'need_review' => [
                'orders' => $productStats->need_review_orders,
                'items' => $productStats->need_review_items,
            ],
            'on_hold' => [
                'orders' => $orderStats->on_hold_orders,
                'items' => $orderStats->on_hold_items,
            ],
            'in_production' => [
                'orders' => $productStats->in_production_orders,
                'items' => $productStats->in_production_items,
            ],
            'late' => [
                'orders' => $productStats->late_orders,
                'items' => $productStats->late_items,
            ],
            'need_support' => [
                'orders' => $orderStats->need_support_orders,
                'items' => $orderStats->need_support_items,
            ],
            'flagged' => [
                'orders' => $orderStats->flagged_orders,
                'items' => $orderStats->flagged_items,
            ],
        ];

        return $this->successResponse([
            'stats' => $stats
        ]);
    }

    public function statsBySupplier(Request $request): JsonResponse
    {
        $dateRange = $request->query('date_range', DateRangeEnum::LAST_7_DAYS);
        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');

        $supplierStats = OrderProduct::query()
            ->select('supplier_id')
            ->selectRaw('count(DISTINCT(CASE WHEN supplier_id IS NOT NULL and fulfill_status in ("unfulfilled","rejected") THEN order_id END)) as assigned_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status = "rejected" THEN order_id END)) as rejected_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status in ("processing", "exception") THEN order_id END)) as in_production_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status = "exception" THEN order_id END)) as exception_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status = "on_delivery" AND DATE_SUB(now(), INTERVAL 18 DAY) > fulfilled_at THEN order_id END)) as on_delivery_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status = "on_delivery" AND DATE_SUB(now(), INTERVAL 9 DAY) > fulfilled_at AND tracking_status IN ("new", "pending", "pre_shipment") THEN order_id END)) as before_delivery_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status in ("processing", "exception") and DATE_SUB(now(), INTERVAL 5 DAY) > fulfilled_at THEN order_id END)) as late_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status in ("processing", "on_delivery", "exception", "fulfilled") AND tracking_code IS NOT NULL AND tracking_code <> "" AND tracking_status = "notfound" AND DATE_SUB(now(), INTERVAL 8 DAY) > fulfilled_at THEN order_id END)) as track_nf_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN fulfill_status in ("processing", "on_delivery", "exception", "fulfilled") AND tracking_code IS NOT NULL AND tracking_code <> "" AND tracking_status = "pre_shipment" AND DATE_SUB(now(), INTERVAL 8 DAY) > fulfilled_at THEN order_id END)) as track_pre_shipment_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN billed_at IS NOT NULL THEN order_id END)) as billed_orders')
            ->selectRaw('count(DISTINCT(CASE WHEN billed_at IS NULL THEN order_id END)) as unbilled_orders')
            ->selectRaw('sum(base_cost) as total_base_costs, sum(quantity) as total_items, count(DISTINCT order_id) as total_orders')
            ->selectRaw('sum(shipping_cost) as total_shipping_costs')
            ->with('supplier:id,name')
            ->whereHas('order', function ($query) use ($dateRange, $startDate, $endDate) {
                $query->filterDateRange($dateRange, $startDate, $endDate, 'order_product.fulfilled_at')->where('type', '<>', OrderTypeEnum::SERVICE);
            })
            ->whereNotNull('supplier_id')
            ->whereNull('deleted_at')
            ->groupBy('supplier_id')
            ->orderByDesc('assigned_orders')
            ->get();

        if ($supplierStats->isEmpty()) {
            return $this->errorResponse();
        }
        $totalSuppliers = $supplierStats->count();
        $avgLateOrders = round((int) $supplierStats->sum('late_orders') / $totalSuppliers);
        $avgTrackNotFoundOrders = round((int) $supplierStats->sum('track_nf_orders') / $totalSuppliers);
        $avgOnDeliveryOrders = round((int) $supplierStats->sum('on_delivery_orders') / $totalSuppliers);
        $avgTrackPreShipmentOrders = round((int) $supplierStats->sum('track_pre_shipment_orders') / $totalSuppliers);
        $supplierStats = $supplierStats->map(fn($item) => [
            'name' => $item->supplier->name ?? 'Unknown',
            'assigned' => (int) $item->assigned_orders,
            'rejected' => (int) $item->rejected_orders,
            'in_production' => (int) $item->in_production_orders,
            'exception' => (int) $item->exception_orders,
            'late' => (int) $item->late_orders,
            'before_delivery' => (int) $item->before_delivery_orders,
            'on_delivery' => (int) $item->on_delivery_orders,
            'track_nf_orders' => (int) $item->track_nf_orders,
            'track_pre_shipment_orders' => (int) $item->track_pre_shipment_orders,
            'is_warning_late' => $this->isWarningWithPercentage((int) $item->late_orders, $avgLateOrders),
            'is_warning_track_nf' =>$this->isWarningWithPercentage((int) $item->track_nf_orders, $avgTrackNotFoundOrders),
            'is_warning_on_delivery' => $this->isWarningWithPercentage((int) $item->on_delivery_orders, $avgOnDeliveryOrders),
            'is_warning_track_pre_shipment' => $this->isWarningWithPercentage((int) $item->track_pre_shipment_orders, $avgTrackPreShipmentOrders),
            'billed' => (int) $item->billed_orders,
            'unbilled' => (int) $item->unbilled_orders,
            'total_items' => (int) $item->total_items,
            'total_base_costs' => round($item->total_base_costs, 2),
            'total_shipping_costs' => round($item->total_shipping_costs, 2),
            'total_orders' => (int) $item->total_orders,
            'supplier_id' => (int) $item->supplier_id,
        ]);

        return $this->successResponse([
            'stats' => $supplierStats
        ]);
    }

    public function statsByCountry(): JsonResponse
    {
        $data = $this->getStats('country');

        return $this->successResponse($data);
    }

    public function statsBySupplierProcessing(): JsonResponse
    {
        $data = $this->getStats('supplier_name');

        return $this->successResponse($data);
    }

    private function getStats($column): array
    {
        $dateRange = request('date_range', DateRangeEnum::LAST_30_DAYS);
        $startDate = request('start_date');
        $endDate   = request('end_date');

        $orderSub = Order::query()
            ->addSelect([
                'id',
                'shipping_method',
                'country',
            ])
            ->filterDateRange($dateRange, $startDate, $endDate)
            ->where('type', '<>', OrderTypeEnum::SERVICE);

        $shipping = OrderProduct::query()
            ->addSelect([
                $column,
                'supplier_id'
            ])
            ->selectRaw('MIN(shipping_day) as shipping_time_min')
            ->selectRaw('AVG(shipping_day) as shipping_time_avg')
            ->selectRaw('MAX(shipping_day) as shipping_time_max')
            ->joinSub($orderSub, 'order', 'order.id', 'order_product.order_id')
            ->where('fulfill_status', OrderProductFulfillStatus::FULFILLED)
            ->where('shipping_method', ShippingMethodEnum::STANDARD)
            ->where('shipping_day', '>', 0)
            ->groupBy($column)
            ->get();

        $processing = OrderProduct::query()
            ->addSelect([
                $column,
                'supplier_id'
            ])
            ->selectRaw('MIN(processing_day) as printing_time_min')
            ->selectRaw('AVG(processing_day) as printing_time_avg')
            ->selectRaw('MAX(processing_day) as printing_time_max')
            ->joinSub($orderSub, 'order', 'order.id', 'order_product.order_id')
            ->where('fulfill_status', OrderProductFulfillStatus::FULFILLED)
            ->where('processing_day', '>', 0)
            ->groupBy($column)
            ->get();

        $data = OrderProduct::query()
            ->addSelect([
                $column,
                'supplier_id'
            ])
            ->joinSub($orderSub, 'order', 'order.id', 'order_product.order_id')
            ->where('fulfill_status', OrderProductFulfillStatus::FULFILLED)
            ->groupBy($column)
            ->orderByRaw('count(*) desc')
            ->get();
        if ($data->isEmpty()) {
            return [];
        }

        $totalSuppliers = $data->count();
        $totalShippingTimeAvg = (float) $shipping->sum('shipping_time_avg') / $totalSuppliers;
        $totalPrintingTimeAvg = (float) $processing->sum('printing_time_avg') / $totalSuppliers;
        $fields = [
            'shipping_time_min',
            'shipping_time_avg',
            'shipping_time_max',
            'printing_time_min',
            'printing_time_avg',
            'printing_time_max',
        ];
        foreach ($data as $supplier) {
            foreach ($shipping as $index => $each) {
                if ($supplier->$column === $each->$column) {
                    foreach ($each->getAttributes() as $key => $value) {
                        $supplier->$key = $value;
                        if ($key === 'shipping_time_avg') {
                            $supplier->{"is_warning_{$key}"} = $this->isWarningWithPercentage($value, $totalShippingTimeAvg);
                        }
                    }
                    unset($shipping[$index]);
                }
            }
            foreach ($processing as $index => $each) {
                if ($supplier->$column === $each->$column) {
                    foreach ($each->getAttributes() as $key => $value) {
                        $supplier->$key = $value;
                        if ($key === 'printing_time_avg') {
                            $supplier->{"is_warning_{$key}"} = $this->isWarningWithPercentage($value, $totalPrintingTimeAvg);
                        }
                    }
                    unset($processing[$index]);
                }
            }
            foreach ($fields as $field) {
                if (!isset($supplier->$field)) {
                    $supplier->$field = 0;
                }
            }
        }

        return $data->toArray();
    }

    /**
     * @param $value
     * @param $avg
     * @return int
     */
    private function isWarningWithPercentage($value, $avg): int
    {
        return ($value > ($avg * (1 + self::PERCENTAGE_THRESHOLD))) ? 1 : 0;
    }
}
