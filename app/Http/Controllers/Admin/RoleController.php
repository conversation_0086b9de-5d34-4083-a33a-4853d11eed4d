<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdatePermissionsByRole;
use App\Http\Requests\AdminCreateRoleRequest;
use App\Models\Role;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    use ApiResponse;

    public function index(Request $request): JsonResponse
    {
        $request->validate(['guard_name' => 'nullable|string|in:admin,user']);

        $keywords = $request->query('q');
        $guardName = $request->query('guard_name');

        $roles = Role::query()
            ->with('permissions')
            ->when($keywords, function ($query, $keywords) {
                $query->where('name', '%' . $keywords . '%');
            })
            ->when($guardName, function ($query, $guardName) {
                $query->where('guard_name', $guardName);
            })
            ->paginate(15);

        return $this->successResponse($roles);
    }

    public function indexPermissions(): JsonResponse
    {
        $permissions = Permission::query()->get();

        return $this->successResponse($permissions);
    }

    public function updatePermission(UpdatePermissionsByRole $request): JsonResponse
    {
        // get new permission_id array
        $roleId = $request->role_id;
        $permissionIds = $request->permission_ids ?? [];

        // update match guard_name
        $role = Role::query()->find($roleId);
        $guardName = $role->guard_name;
        $permissionIdsValid = Permission::query()
            ->select('id')
            ->where('guard_name', $guardName)
            ->find($permissionIds);

        if (is_null($permissionIdsValid)) {
            return $this->errorResponse();
        }

        $role->permissions()->sync($permissionIdsValid);

        return $this->successResponse();
    }

    public function getPermissionByRole(Request $request): JsonResponse
    {
        $roles = Role::with('permissions')->get();
        $arr = [];
        foreach ($roles as $role) {
            $roleName = $role->name;
            if (empty($arr[$roleName])) {
                $arr[$roleName] = [];
            }
            foreach ($role->permissions as $index => $permission) {
                $arr[$role->name][$index] = $permission->name;
            }
        }

        $role = $request->input('role');
        $roleRequest = $arr[$role];
        return (count($roleRequest) > 0)
            ? $this->successResponse($roleRequest)
            : $this->successResponse('This role has not had any permission!');
    }

    public function getAllPermissions(): JsonResponse
    {
        $permissions = Permission::query()
            ->select('name')
            ->groupBy('name')
            ->pluck('name');

        return $this->successResponse($permissions);
    }

    public function getSystemGuardNames(): JsonResponse
    {
        $guardNames = Role::getSystemGuardNames();
        return $this->successResponse($guardNames);
    }

    /**
     * @param AdminCreateRoleRequest $request
     * @return JsonResponse
     */
    public function createRole(AdminCreateRoleRequest $request): JsonResponse
    {
        $roleName = $request->post('name');
        $guardName = $request->post('guard_name');
        $permissionIds = $request->post('permissions');

        $permissions = Permission::query()->whereIn('id', $permissionIds)
            ->where('guard_name', $guardName)->get();

        if ($permissions->count() !== count($permissionIds)) {
            return $this->errorResponse('Invalid permissions');
        }

        DB::beginTransaction();
        try {
            $newRole = Role::query()->create([
                'name' => $roleName,
                'guard_name' => $guardName
            ]);

            $permissions->map(function ($permission) use (&$newRole) {
                $newRole->givePermissionTo($permission);
            });
            DB::commit();

            return $this->successResponse('Create new role success!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Create new role failed');
        }

    }
}
