<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\ProductController;
use App\Http\Requests\Admin\FulfillMapping\UpdateRequest;
use App\Models\FulfillMapping;
use App\Models\ProductFulfillMapping;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class FulfillMappingController extends Controller
{
    use ApiResponse;

    public function index(Request $request): JsonResponse
    {
        $type = $request->input('type');

        $data = FulfillMapping::query()
            ->where('type', $type)
            ->orderBy('supplier_id')
            ->orderByDesc('location')
            ->get();

        return $this->successResponse($data);
    }

    public function update(UpdateRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            if (!empty($data['id'])) {
                $fulfillMapping = FulfillMapping::query()
                    ->where('id', $data['id'])
                    ->firstOrFail();
            } else {
                $fulfillMapping = new FulfillMapping();
            }
            $fulfillMapping->fill($data);
            $fulfillMapping->save();

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function insertByPrintType(Request $request): JsonResponse
    {
        $validator = validator($request->all(), [
            'supplier_id' => 'nullable|numeric',
            'location' => 'nullable|string',
            'print_type' => 'required|numeric',
            'type' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors()->first());
        }

        $supplierId = $request->input('supplier_id');
        $location = $request->input('location');
        $printType = $request->input('print_type');
        $type = $request->input('type');

        try {
            $request = new Request([
                'has_fulfill' => 'true',
            ]);
            $templateProducts = ProductController::getTemplate($request);
            $templateProducts = array_filter($templateProducts, function ($item) use ($printType) {
                return isset($item['full_printed']) && $item['full_printed'] === $printType;
            });
            $templateProductIds = array_column($templateProducts, 'id');

            //filter existing records on fulfill_mapping
            $existingFulfillMappingIds = FulfillMapping::query()
                ->where('supplier_id', $supplierId)
                ->where('type', $type)
                ->whereIn('object_id', $templateProductIds)
                ->get()
                ->pluck('object_id')
                ->unique()
                ->toArray();

            $templateProductIds = array_diff($templateProductIds, $existingFulfillMappingIds);

            // prepare data to insert
            $data = [];
            foreach ($templateProductIds as $templateProductId) {
                $data[] = [
                    'object_id' => $templateProductId,
                    'supplier_id' => $supplierId,
                    'location' => $location,
                    'type' => $type,
                ];
            }

            if (empty($data)) {
                return $this->successResponse([], 'No data to insert');
            }

            // insert
            try{
                DB::beginTransaction();
                $result = FulfillMapping::query()->insert($data);
                if ($result) {
                    DB::commit();
                    return $this->successResponse([], 'Data inserted');
                } else {
                    DB::rollBack();
                    return $this->errorResponse('Failed to insert data');
                }
            }catch (Throwable $e) {
                DB::rollBack();
                return $this->errorResponse($e->getMessage());
            }
        }catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function delete($id): JsonResponse
    {
        try {
            optional(FulfillMapping::query()->whereId($id)->first())->delete();

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function bulkDelete(Request $request): JsonResponse
    {
        $validator = validator($request->all(), [
            'ids' => 'required|array',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors()->first());
        }
        $ids = $request->input('ids');
        $ids = array_filter($ids, function ($id) {
            return is_numeric($id);
        });
        try {
            FulfillMapping::query()->whereIn('id', $ids)->delete();
            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Fulfill products by template
     *
     * @param     Request     $request
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function fulfillProductsByTemplate(Request $request): JsonResponse
    {
        try {
            if (! $templateId = $request->route('id')) {
                return $this->errorResponse('Template ID is required');
            }

            if ($request->has('cached')) {
                $value = ProductFulfillMapping::findAndCacheByTemplateId($templateId);
            } else {
                $value = ProductFulfillMapping::findByTemplateId($templateId);
            }

            return $this->successResponse($value);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
