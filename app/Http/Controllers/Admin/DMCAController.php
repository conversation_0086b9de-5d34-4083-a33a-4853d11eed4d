<?php

namespace App\Http\Controllers\Admin;

use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\StoreDomainStatusEnum;
use App\Enums\StoreStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Campaign\DMCANotificationRequest;
use App\Http\Requests\Admin\Campaign\DMCARequest;
use App\Models\Campaign;
use App\Models\CampaignDMCANotification;
use App\Models\Product;
use App\Models\SellerNotification;
use App\Models\SendMailLog;
use App\Models\Store;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;


class DMCAController extends Controller
{
    use ApiResponse;

    protected const DMCA_NOTIFICATION_PAGINATE = 15;
    private const PER_PAGE = 25;

    public function index(DMCANotificationRequest $request): JsonResponse
    {
        $searchBar = $request->get('q');
        $sellerId = $request->get('seller_id');
        $startDate = $request->get('from_date') ?? '2023-10-01';
        $endDate = $request->get('to_date') ?? now();
        $perPage = $request->get('per_page', self::PER_PAGE);
        $sortBy = $request->get('sort') ?? 'created_at';
        $sortDirecion = $request->get('direction') ?? 'desc';
        try {
            $dmcaNotifcationsQuery = CampaignDMCANotification::query()
                ->select([
                    'campaign_dmca_notifications.id as notification_id',
                    'campaign_dmca_notifications.seller_name as seller_name',
                    'campaign_dmca_notifications.seller_id as seller_id',
                    'campaign_dmca_notifications.created_at as created_at',
                    'user.email as seller_email',
                    'campaign_dmca_notifications.campaigns_violated_urls as info_group',
                    'campaign_dmca_notifications.dmca_url_info as info',
                    'staff.name as staff_name',
                    'staff.id as staff_id'
                ])->join('user', function ($join) {
                    $join->on('campaign_dmca_notifications.seller_id', '=', 'user.id');
                })->leftjoin('staff', function ($join) {
                    $join->on('campaign_dmca_notifications.staff_id', '=', 'staff.id');
                });
            if (!empty($sellerId)) {
                $dmcaNotifcationsQuery->where('campaign_dmca_notifications.seller_id', $sellerId);
            }
            if (!empty($searchBar)) {
                $dmcaNotifcationsQuery->where('campaign_dmca_notifications.dmca_url_info', 'like', '%' . $searchBar . '%')
                    ->orWhere('campaign_dmca_notifications.seller_name', 'like', '%' . $searchBar . '%');
            }
            $dmcaNotifcationsQuery->whereBetween("campaign_dmca_notifications.created_at", [$startDate, $endDate]);
            $dmcaNotifcationsQuery = $dmcaNotifcationsQuery->orderBy("campaign_dmca_notifications.$sortBy", $sortDirecion);
            $dmcaNotificationIdsArr = $dmcaNotifcationsQuery->pluck('notification_id')->toArray();
            $dmcaNotifcations = $dmcaNotifcationsQuery->paginate($perPage);
            $dmcaNotificationIdsArr = SendMailLog::query()->select(['id', 'dmca_notification_id'])->whereIn('dmca_notification_id', $dmcaNotificationIdsArr)->get()->toArray();
            $dmcaNotificationIdsMappedWithNotificationId = [];
            foreach ($dmcaNotificationIdsArr as $dmcaNotificationId) {
                $dmcaNotificationIdsMappedWithNotificationId[$dmcaNotificationId['dmca_notification_id']] = $dmcaNotificationId['id'];
            }
            foreach ($dmcaNotifcations as &$dmcaNotifcation) {
                $notifiesInfoGroup = json_decode($dmcaNotifcation->info_group);
                try {
                    $dmcaNotifcation->mail_view_id = $dmcaNotificationIdsMappedWithNotificationId[$dmcaNotifcation->notification_id];
                    foreach ($notifiesInfoGroup as &$notifyInfoGroup) {
                        $campInfo = Campaign::query()->select(['status', 'deleted_at'])->where('id', $notifyInfoGroup->id)->first();
                        $notifyInfoGroup->status = $campInfo->status;
                        $notifyInfoGroup->deleted_at = $campInfo->deleted_at;
                    }
                    $dmcaNotifcation->info_group = json_encode($notifiesInfoGroup);
                } catch (\Exception $e) {
                    continue;
                }
            }
            return $this->successResponse($dmcaNotifcations);
        } catch (\Exception $e) {
            graylogError("error in load notifications : " . $e, [
                'category' => 'dmca_err_log',
            ]);
            return $this->errorResponse();
        }
    }

    public function getNotificationsByUser(DMCANotificationRequest $request):JsonResponse
    {
        $sellerId = $request->get('seller_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $perPage = $request->get('per_page', self::PER_PAGE);
        try {
            $dmcaNotifcationsQuery = CampaignDMCANotification::query();
            if (empty($startDate)) {
                $startDate = now()->subRealWeek();
            } else {
                $startDate = Carbon::createFromTimestamp($startDate)->toDateTimeString();
            }
            if (empty($endDate)) {
                $endDate = now();
            } else {
                $endDate = Carbon::createFromTimestamp($endDate)->toDateTimeString();
            }
            if (!empty($sellerId)) {
                $dmcaNotifcationsQuery->where('seller_id', $sellerId);
            }
            $dmcaNotifcationsQuery->whereBetween('created_at', [$startDate, $endDate]);
            $dmcaNotifcations = $dmcaNotifcationsQuery->orderBy('created_at', 'DESC')->paginate($perPage);
            return $this->successResponse($dmcaNotifcations);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    public function batchTakeDown(DMCARequest $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $urls = $request->post('urls');
        $campaignUrls = getSlugByCampaignUrls($urls);
        $sellerDomainNotify = [];
        $domainOrCampaignIsNotSen = [];
        try {
            foreach ($campaignUrls as $domain => $domainInfo) {
                $query = Store::query()
                    ->select([
                        'store.id as id',
                        'store.seller_id as seller_id',
                        'user.name as seller_name',
                        'user.custom_payment as custom_payment',
                        'user.email as seller_email',
                    ])
                    ->leftJoin('user', function($join) {
                        $join->on('store.seller_id', '=', 'user.id');
                    })
                    ->whereIn('store.status', [
                        StoreStatusEnum::ACTIVE,
                        StoreStatusEnum::VERIFIED
                    ]);
                if ($domainInfo['subdomain']) {
                    $query->where('store.sub_domain', $domain);
                } else {
                    $query->where('store.domain', $domain);
                    $query->orWhereHas('storeDomain', function ($query) use ($domain) {
                        $query->where('domain', $domain);
                        $query->where('status', StoreDomainStatusEnum::ACTIVATED);
                    });
                }

                $store = $query->first();
                $originSlugCampaigns = $domainInfo['campaigns'];
                if (!empty($store)) {
                    if ($store->custom_payment) {
                        continue;
                    }
                    $campaignIds = [];
                    $campaignSlugs = [];
                    $campTotal = [];

                    $campaigns = Campaign::query()
                        ->onSellerConnection($seller)
                        ->select(['id', 'slug', 'name'])
                        ->whereIn('slug', $domainInfo['campaigns'])
                        ->with([
                            'stores' => function ($q) use ($store) {
                                $q->select([
                                    'id',
                                ]);
                                $q->where('id', '=', $store->id);
                            }
                        ])
                        ->get();

                    if (!empty($campaigns)) {
                        $campaigns->each(function($campaignInfo) use($domain, $domainInfo, &$campaignIds, &$campaignSlugs, &$originSlugCampaigns, &$sellerDomainNotify, $store, &$campTotal) {
                            if (!isset($sellerDomainNotify[$store->seller_id][$domain]) ||
                                (
                                    isset($sellerDomainNotify[$store->seller_id][$domain]['campaignIds']) &&
                                    !in_array($campaignInfo->id, $sellerDomainNotify[$store->seller_id][$domain]['campaignIds'])
                                )
                            ) {
                                $campaignIds[] = $campaignInfo->id;
                                $campaignSlugs[] = $campaignInfo->slug;
                                $campTotal[] = [
                                    'id' => $campaignInfo->id,
                                    'slug' => $campaignInfo->slug,
                                    'campaign_name' => $campaignInfo->name,
                                ];

                            }

                            $originSlugCampaignKey = array_search($campaignInfo->slug, $originSlugCampaigns);
                            if ($originSlugCampaignKey !== false) {
                                unset($originSlugCampaigns[$originSlugCampaignKey]);
                            }

                        });

                    }

                    if (!empty($originSlugCampaigns)) {
                        $domainOrCampaignIsNotSen[$domain] = [
                            'subdomain' => $domainInfo['subdomain'],
                            'originDomain' => $domainInfo['originDomain'],
                            'campaigns' => $originSlugCampaigns
                        ];
                        unset($originSlugCampaigns);
                    }

                    $sellerDomainNotify[$store->seller_id . '----' . $store->seller_name][$domain] = [
                        'subdomain' => $domainInfo['subdomain'],
                        'seller_name' => $store->seller_name,
                        'originDomain' => $domainInfo['originDomain'],
                        'campaignIds' => $campaignIds,
                        'campaignSlugs' => $campaignSlugs,
                        'campaignTotal' => $campTotal,
                        'seller_email' => $store->seller_email,
                        'seller_id' => $store->seller_id,
                    ];
                } else {
                    $domainOrCampaignIsNotSen[$domain] = [
                        'subdomain' => $domainInfo['subdomain'],
                        'originDomain' => $domainInfo['originDomain'],
                        'campaigns' => $domainInfo['campaigns']
                    ];
                }
                unset($campaignUrls[$domain]);
            }

            $date = Carbon::now();
            $notifyExpiryDate = $date->addDays(2);
            if (!empty($sellerDomainNotify)) {
                foreach ($sellerDomainNotify as $sellerInfo => $storeInfo) {
                    list($sellerId, $sellerName) = explode('----', $sellerInfo);
                    if (empty($sellerId)) {
                        continue;
                    }
                    $campaignUrlsForEmail=[];
                    $campaignsGroup=[];
                    $notifyMessage = '<p>Please remove the following campaigns in violation of the DMCA within 24 hours:<br />';
                    $notifyMessage .= '<ul>';
                    foreach ($storeInfo as $storeCampaignInfo) {
                        if (empty($storeCampaignInfo['campaignIds'])) {
                            continue;
                        }
                        foreach($storeCampaignInfo['campaignIds'] as $campaignId){
                            $startEsTime = round(microtime(true) * 1000);
                            DB::enableQueryLog();
                            Product::query()
                                ->onSellerConnection($seller)
                                ->withTrashed()
                                ->where('id', $campaignId)
                                ->orWhere('campaign_id', $campaignId)
                                ->update([
                                    'tm_status' => CampaignTrademarkStatusEnum::TM,
                                ]);
                            $queryLog = DB::getQueryLog();
                            $endEsTime = round(microtime(true) * 1000);
                            $esTime = $endEsTime - $startEsTime;
                            graylogError("Change tm_status process log: ".$esTime, [
                                'category' => 'dmca_takedown_log',
                                'time' => $esTime,
                                'campaign_id' => $campaignId,
                                'sql' => end($queryLog)['query'],
                            ]);
                        }
                        foreach ($storeCampaignInfo['campaignSlugs'] as $campaignSlug) {
                            $campaignUrl = $storeCampaignInfo['originDomain'] . $campaignSlug;
                            $notifyMessage .= '<li><a href="' . $campaignUrl . '">' . $campaignUrl . '</a>';
                            array_push($campaignUrlsForEmail, $campaignUrl);

                        }
                        foreach($storeCampaignInfo['campaignTotal'] as $campaignTotalInfo){
                            array_push($campaignsGroup, $campaignTotalInfo);
                        }
                    }
                    $notifyMessage .= '</ul></p>';
                    $notify = SellerNotification::query()->create([
                        'seller_id' => $sellerId,
                        'subject' => '[DMCA Notification] Please takedown DMCA violation campaigns in 24h',
                        'message' => $notifyMessage,
                        'admin_id' => $sellerId,
                        'expiry_date' => $notifyExpiryDate,
                        'is_warning' => true
                    ]);
                    foreach($storeInfo as $domain=>$storeInfoSaving){
                        $storeInfo = $storeInfoSaving;
                    }

                    if ($notify->wasRecentlyCreated) {
                        $campaginNotification = CampaignDMCANotification::query()->create([
                            'seller_id' => $sellerId,
                            'seller_name' => $sellerName ?? 0,
                            'notification_id' => $notify->id,
                            'dmca_url_info' => json_encode($storeInfo),
                            'campaigns_violated_urls'=>json_encode($campaignsGroup),
                            'staff_id' => $sellerId ,
                        ]);

                        $config = [
                            'to' => $storeInfo['seller_email'],
                            'template' => 'seller.takedown-trademark-24h-notification',
                            'data' => [
                                'subject' => 'Please takedown DMCA violation campaigns in 24h',
                                'sellerName' => $sellerName,
                                'campaignUrls' => $campaignUrlsForEmail,
                                'base_url' => config('senprints.base_url_seller'),
                            ],
                            'sendMailLog' => [
                                'sellerId' => $storeInfo['seller_id'],
                            ],
                            'dmca_notification_id' =>  $campaginNotification->id,
                        ];
                        $status = sendEmail($config);
                    }
                }
            }else{
                return $this->errorResponse("Campaign is not existed or belongs to seller who's using custom payment, please check again!");
            }
            return $this->successResponse();
        } catch (\Exception $e) {
            graylogError("error in batch takedown : ".$e, [
                'category' => 'dmca_err_log',
            ]);
            return $this->errorResponse();
        }
    }

}
