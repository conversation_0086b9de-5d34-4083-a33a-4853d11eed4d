<?php

namespace App\Http\Controllers;

use App\Http\Requests\Cloudflare\StoreZoneRequest;
use App\Models\StoreDomain;
use App\Services\CloudFlareCustomHostname;
use App\Services\CloudflareService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CloudFlareController extends Controller
{
    use ApiResponse;

    protected $cloudFlareCustomHostname;

    protected $cloudflareService;
    public function __construct(CloudFlareCustomHostname $cloudFlareCustomHostname, CloudflareService $cloudflareService)
    {
        $this->cloudFlareCustomHostname = $cloudFlareCustomHostname;
        $this->cloudflareService = $cloudflareService;
    }

    private function isValidDomain(string $domain): bool
    {
        return (
            // Check if domain is valid using PHP's filter
            filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) &&
            // Ensure domain has at least one dot and no spaces
            str_contains($domain, '.') &&
            !str_contains($domain, ' ') &&
            // Check domain length
            strlen($domain) >= 3 && strlen($domain) <= 253
        );
    }

    public function checkCustomHostname(Request $request): ?JsonResponse
    {
        abort_if($request->query('ref') !== 'senprints', 401, 'Unauthorized');

        $domain = $request->query('domain');

        if (!$domain) {
            return $this->errorResponse('Domain is required', 400);
        }

        if (!$this->isValidDomain($domain)) {
            return $this->errorResponse('Invalid domain format', 400);
        }

        $storeDomain = StoreDomain::where('domain', $domain)->first();

        if (!$storeDomain) {
            return $this->errorResponse('Domain not found', 404);
        }

        if (!$storeDomain->cloudflare_custom_hostname_id) {
            return $this->errorResponse('This domain does not have CloudFlare Custom Hostname ID', 404);
        }

        try {
            $result = $this->cloudFlareCustomHostname->showCustomHostname($storeDomain->cloudflare_custom_hostname_id);
            return $this->successResponse($result);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    public function listZones(Request $request)
    {
        $params['name'] = $request->get('keyword');
        if ($params['name']) {
            $params['name'] = 'contains:' . $params['name'];
        }
        $params['status'] = $request->get('status');
        $params['page'] = $request->get('page', 1);
        $params['per_page'] = $request->get('per_page', 20);
        $params['order'] = $request->get('sort', 'status');
        $params['direction'] = $request->get('direction', 'desc');
        return $this->cloudflareService->listZones($params);
    }

    public function storeZone(StoreZoneRequest $request)
    {
        try {
            $data = $request->validated();
            $this->cloudflareService->storeZone($data);
            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    public function detailZone(Request $request, string $zoneId)
    {
        try {
            $result = $this->cloudflareService->detailZone($zoneId);
            return $this->successResponse($result);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    public function updateDnsRecords(Request $request, string $zoneId)
    {
        try {
            $data = $request->validate([
                'dnsRecords' => [
                    'nullable',
                    'array',
                ],
                'dnsRecords.*.id' => [
                    'nullable',
                    'string',
                ],
                'dnsRecords.*.type' => [
                    'required',
                    'string',
                ],
                'dnsRecords.*.name' => [
                    'required',
                    'string',
                ],
                'dnsRecords.*.ttl' => [
                    'required',
                    'numeric',
                ],
                'dnsRecords.*.content' => [
                    'required',
                    'string',
                ],
                'dnsRecords.*.priority' => [
                    'nullable',
                ],
            ]);
            $this->cloudflareService->updateDnsRecords($zoneId, $data['dnsRecords']);;
            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    public function deleteZone(Request $request, string $zoneId)
    {
        try {
            $this->cloudflareService->deleteZone($zoneId);;
            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
