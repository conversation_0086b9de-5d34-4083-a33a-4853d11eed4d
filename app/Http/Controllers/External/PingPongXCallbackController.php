<?php

namespace App\Http\Controllers\External;

use App\Enums\PingPongX\PingPongXOrderStatusEnum;
use App\Enums\SellerBillingStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\External\PingPongXCallbackRequest;
use App\Library\PingPongX\PingPongX;
use App\Models\SellerBilling;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class PingPongXCallbackController extends Controller
{
    use ApiResponse;

    public function notification(PingPongXCallbackRequest $request): JsonResponse
    {
        $cipherText = $request->post('ciphertext');
        $hookEventName = $request->post('event_type');
        $decryptNotification = PingPongX::instance()->decryptNotification($cipherText, $hookEventName);
        graylogInfo("PingPongXCallbackController: Begin notification \r ciphertext: {$cipherText} \r eventType: {$hookEventName} \r decryptNotification: {$decryptNotification}", [
            'category' => 'process_payout_pingpongx_info',
            'user_type' => 'system',
            'user_id' => null,
            'action' => 'callback'
        ]);

        if (is_null($decryptNotification)) {
            graylogError("PingPongXCallbackController: Cannot decrypt notification \r ciphertext: {$cipherText} \r eventType: {$hookEventName} \r decryptNotification: {$decryptNotification}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'callback'
            ]);
            return $this->errorResponse();
        }

        $notification = \json_decode($decryptNotification, true);

        if (is_null($notification)) {
            graylogError("PingPongXCallbackController: Cannot json decode notification \r ciphertext: {$cipherText} \r eventType: {$hookEventName} \r decryptNotification: {$decryptNotification}", [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'callback'
            ]);
            return $this->errorResponse();
        }

        $status = $notification['status'] ?? null;
        $billId = $notification['partner_order_id'] ?? null;
        $transactionKey = $notification['order_id'] ?? null;

        if (is_null($billId) || is_null($status) || is_null($transactionKey)) {
            graylogError("PingPongXCallbackController: Cannot parse status, billId, transactionKey \r notification: " . json_encode($notification), [
                'category' => 'process_payout_pingpongx_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'callback'
            ]);
            return $this->errorResponse();
        }

        $status = strtolower($status);

        if ($status === PingPongXOrderStatusEnum::SUCCESS) {
            $sellerBilling = SellerBilling::query()
                ->select('status')
                ->where([
                    'id' => $billId,
                    'transaction_key' => $transactionKey,
                    'status' => SellerBillingStatus::PROCESSING
                ])
                ->first();

            if (is_null($sellerBilling)) {
                graylogError("PingPongXCallbackController: Cannot query sellerBilling \r billId: {$billId} \r status: {$status} \r transactionKey: {$transactionKey} \r notification: " . json_encode($notification), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'callback'
                ]);
                return $this->errorResponse();
            }

            if ($sellerBilling->status === SellerBillingStatus::PROCESSING) {
                $sellerBillingUpdated = SellerBilling::query()
                    ->where('id', $billId)
                    ->update([
                        'status' => SellerBillingStatus::COMPLETED
                    ]);

                if ($sellerBillingUpdated) {
                    graylogInfo("PingPongXCallbackController: End notification \r eventType: {$hookEventName} \r decryptNotification: {$decryptNotification}", [
                        'category' => 'process_payout_pingpongx_info',
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => 'callback'
                    ]);
                    return $this->successResponse();
                }

                graylogError("PingPongXCallbackController: Update Seller Billing Error: " . json_encode($notification), [
                    'category' => 'process_payout_pingpongx_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'callback'
                ]);

                return $this->errorResponse();
            }
        }
        graylogError("PingPongXCallbackController: Cannot compare status \r billId: {$billId} \r status: {$status} \r transactionKey: {$transactionKey} \r notification: " . json_encode($notification), [
            'category' => 'process_payout_pingpongx_errors',
            'user_type' => 'system',
            'user_id' => null,
            'action' => 'callback'
        ]);
        return $this->errorResponse();
    }
}
