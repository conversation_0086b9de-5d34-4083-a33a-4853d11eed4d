<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Models\Dev;
use App\Models\DevGitCommit;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BitbucketWebhookController extends Controller
{
    public function __invoke(Request $request): void
    {
        if ($request->header('x-event-key') !== 'repo:push'
            || !Str::startsWith($request->userAgent(), 'Bitbucket-Webhooks')) {
            return;
        }

        $changes = $request->json('push.changes');

        if (empty($changes)) {
            return;
        }

        $jiraIds = Dev::pluck('jira_id')->toArray();
        $commits = [];
        $addedHashes = [];
        $committedJiraIds = [];

        foreach ($changes as $change) {
            if (empty($change['commits'])) {
                continue;
            }

            foreach ($change['commits'] as $commit) {
                $commitHash = $commit['hash'];

                $jiraId = self::getJiraIdByGitAccountId(
                    data_get($commit, 'author.user.account_id', ''),
                    $jiraIds
                );

                if (in_array($jiraId, $jiraIds) && !in_array($commitHash, $addedHashes)) {
                    $now = now();
                    $commits[] = [
                        'jira_id' => $jiraId,
                        'hash' => $commitHash,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];

                    $addedHashes[] = $commitHash;

                    if (!in_array($jiraId, $committedJiraIds)) {
                        $committedJiraIds[] = $jiraId;
                    }
                }
            }
        }

        if (empty($commits)) {
            return;
        }

        try {
            DevGitCommit::query()->insert($commits);
            Dev::query()
                ->whereIn('jira_id', $committedJiraIds)
                ->update(['last_commit_at' => now()]);
        } catch (\Throwable $e) {
            // duplicate key, ignore
        }
    }

    private static function getJiraIdByGitAccountId(string $accountId, array $jiraIds): string
    {
        if (empty($accountId)) {
            return '';
        }

        // if git account id is jira id, don't need to map
        if (in_array($accountId, $jiraIds, true)) {
            return $accountId;
        }

        // mapping between git account id and jira id
        // TODO: add more members
        $mapping = [
            '557058:9609842c-1d51-4104-b86d-3f2c32f10f06' => '5f0bd0b307efc4002803bf77', // Manh Tuan
        ];

        return $mapping[$accountId] ?? '';
    }
}
