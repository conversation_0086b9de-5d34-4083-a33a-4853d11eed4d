<?php

namespace App\Http\Controllers;

use App\Enums\FulfillmentStatusEnum;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Http\Requests\Order\FulfillOrderRequest;
use App\Http\Requests\Order\UpdateFulfillmentTrackingRequest;
use App\Models\Fulfillment;
use App\Models\FulfillmentProduct;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Traits\ApiResponse;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class FulfillmentController extends Controller
{
    use ApiResponse;

    /**
     * @param FulfillOrderRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function store(FulfillOrderRequest $request): JsonResponse
    {
        $orderId = $request->post('order_id');
        $fulfillment = [
            'seller_id' => $request->post('seller_id'),
            'order_id' => $orderId,
            'store_id' => $request->post('store_id'),
            'supplier_id' => $request->post('supplier_id'),
            'supplier_name' => $request->post('supplier_name'),
            'shipping_carrier' => $request->post('shipping_carrier'),
            'tracking_number' => $request->post('tracking_number'),
            'tracking_url' => $request->post('tracking_url'),
        ];

        $products = $request->post('products');

        $order = Order::query()
            ->where('id', $orderId)
            ->firstOrFail();

        // Transaction
        try {
            DB::beginTransaction();

            // Add to 'collection' table
            $fulfillment = Fulfillment::query()->create($fulfillment);
            $fulfillmentId = $fulfillment->id;
            $orderProductIds = [];

            $products = array_map(static function ($product) use ($fulfillmentId, &$orderProductIds) {
                $orderProductIds[] = $product['order_product_id'];
                $product['fulfillment_id'] = $fulfillmentId;
                return $product;
            }, $products);

            $orderProducts = OrderProduct::query()
                ->select(['id', 'fulfilled_quantity', 'quantity', 'fulfill_status'])
                ->whereIn('id', $orderProductIds)
                ->get();

            $orderProducts = $orderProducts->map(static function ($product) use ($products) {
                $productIndex = array_search($product['id'], array_column($products, 'order_product_id'));

                if ($productIndex > -1) {
                    $fulfilledQuantity = (
                        (int)$product['fulfilled_quantity'] + (int)$products[$productIndex]['fulfilled_quantity']
                    );
                    $product['fulfilled_quantity'] = $fulfilledQuantity;

                    if ($fulfilledQuantity < $product['quantity']) {
                        $product['fulfill_status'] = OrderProductFulfillStatus::PARTIALLY_FULFILLED;
                    } else {
                        $product['fulfill_status'] = OrderProductFulfillStatus::FULFILLED;
                    }
                }

                return $product;
            });

            batch()->update(new OrderProduct([], true), $orderProducts->toArray(), 'id');

            FulfillmentProduct::query()->insert($products);
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::FULFILLMENT_CREATED,
                'Fulfillment #' . $fulfillmentId,
            );
            Db::commit();

            return $this->successResponse(['fulfillment_id' => $fulfillmentId]);
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse($ex->getMessage(), 500);
        }
    }

    /**
     * @param int $id
     * @return Builder|Model
     */
    public function detail(int $id)
    {
        // todo: return JSON response
        return Fulfillment::query()
            ->where('id', $id)
            ->with(['fulfillment_products'])
            ->firstOrFail();
    }

    /**
     * @param UpdateFulfillmentTrackingRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function update(UpdateFulfillmentTrackingRequest $request): JsonResponse
    {
        $fulfillmentId = $request->post('fulfillment_id');
        $orderId = $request->post('order_id');
        $fulfillment = [
            'supplier_id' => $request->post('supplier_id'),
            'supplier_name' => $request->post('supplier_name'),
            'shipping_carrier' => $request->post('shipping_carrier'),
            'tracking_number' => $request->post('tracking_number'),
            'tracking_url' => $request->post('tracking_url'),
        ];

        try {
            DB::beginTransaction();
            $order = Order::query()->find($orderId);

            if (!$order) {
                return $this->errorResponse();
            }

            Fulfillment::query()
                ->where('id', $fulfillmentId)
                ->update($fulfillment);

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::FULFILLMENT_UPDATED,
                'Fulfillment #' . $fulfillmentId,
            );

            DB::commit();

            return $this->successResponse(null, 'Update fulfillment tracking success');
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse($ex->getMessage(), 500);
        }
    }

    /**
     * @param UpdateFulfillmentTrackingRequest $request
     * @return JsonResponse
     */
    public function markFulfillmentCompleted(UpdateFulfillmentTrackingRequest $request): JsonResponse
    {
        try {
            $fulfillmentId = $request->post('fulfillment_id');
            $orderId = $request->post('order_id');
            $fulfillment = Fulfillment::query()->find($fulfillmentId);
            $order = Order::query()->find($orderId);

            if ($fulfillment) {
                $fulfillment->supplier_id = $request->post('supplier_id');
                $fulfillment->supplier_name = $request->post('supplier_name');
                $fulfillment->shipping_carrier = $request->post('shipping_carrier');
                $fulfillment->tracking_number = $request->post('tracking_number');
                $fulfillment->tracking_url = $request->post('tracking_url');
                $fulfillment->status = FulfillmentStatusEnum::FULFILLED;
                $fulfillment->save();
            }

            if ($order && $order->isOrderCompleted()) {
                $order->status = OrderStatus::COMPLETED;
                $order->save();
            }

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::FULFILLMENT_COMPLETED,
                'Fulfillment #' . $fulfillmentId,
            );

            if (!$order) {
                // Call event Order Update
                return $this->errorResponse();
            }

            return $this->successResponse(null, 'Update fulfillment tracking success');
        } catch (Exception $ex) {
            return $this->errorResponse($ex->getMessage(), 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function cancelFulfillment(Request $request): JsonResponse
    {
        $fulfillmentId = $request->post('fulfillment_id');
        $fulfillment = Fulfillment::query()
            ->where('id', $fulfillmentId)
            ->with('fulfillment_products')
            ->firstOrFail();

        $order = Order::query()
            ->where('id', $fulfillment->order_id)
            ->firstOrFail();

        $orderProductIds = [];
        $fulfillmentProducts = $fulfillment->fulfillment_products;

        $fulfillmentProducts->map(function ($p) use (&$orderProductIds) {
            $orderProductIds[] = $p['order_product_id'];
        });

        $orderProducts = OrderProduct::query()
            ->select(['id', 'fulfilled_quantity', 'quantity', 'fulfill_status'])
            ->whereIn('id', $orderProductIds)
            ->get();

        $orderProducts->map(function ($product) use ($fulfillmentProducts) {
            $fulfillProductIdx = (
            array_search($product['id'], array_column($fulfillmentProducts->toArray(), 'order_product_id'), true)
            );
            $fulfilledQuantity = $fulfillmentProducts[$fulfillProductIdx]->fulfilled_quantity;
            $product['fulfilled_quantity'] -= $fulfilledQuantity;

            if ($product['quantity'] > $product['fulfilled_quantity'] && $product['fulfilled_quantity'] > 0) {
                $product['fulfill_status'] = OrderProductFulfillStatus::PARTIALLY_FULFILLED;
            } else {
                $product['fulfill_status'] = OrderProductFulfillStatus::UNFULFILLED;
            }

            return $product;
        });

        $fulfillment->status = FulfillmentStatusEnum::CANCELLED;

        try {
            DB::beginTransaction();

            batch()->update(new OrderProduct([], true), $orderProducts->toArray(), 'id');
            $fulfillment->save();

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::FULFILLMENT_CANCELLED,
                'Fulfillment #' . $fulfillmentId,
            );

            Db::commit();

            return $this->successResponse(
                ['fulfillment_id' => $fulfillmentId],
                'Cancel fulfillment success!'
            );
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse($ex->getMessage(), 500);
        }
    }
}
