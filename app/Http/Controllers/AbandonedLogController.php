<?php

namespace App\Http\Controllers;

use App\Models\AbandonedLog;

class AbandonedLogController extends Controller
{
    private string $cartKey = '';

    public function createLog(array $data = null): void
    {
        $this->cartKey = generateUUID();
        AbandonedLog::create($data);
    }

    /**
     * @param int $storeId
     * @return int
     */
    public static function getNotificationKey(int $storeId)
    {
        $total = AbandonedLog::query()
            ->where('store_id', $storeId)
            ->count();

        return $total + 1;
    }
}
