<?php

namespace App\Http\Controllers;

use App\Actions\Storefront\Order\OrderProductBaseCostCalculator;
use App\Enums\CountryEnum;
use App\Enums\DesignByEnum;
use App\Enums\DesignStatusEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\DiscordChannel;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductCategoryEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductType;
use App\Enums\SellerBillingType;
use App\Events\OrderChangeDesign;
use App\Filters\OrderAssignSupplierHistoryFilter;
use App\Http\Controllers\Admin\FulfillController;
use App\Http\Requests\Admin\Order\UpdateOrderProductOptionsRequest;
use App\Http\Requests\CustomOptionDesignUploadRequest;
use App\Imports\Supplier\UpdateTrackingImport;
use App\Jobs\OrderCostStatisticsJob;
use App\Jobs\RenderPrintJob;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\Elastic;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderAssignSupplierHistory;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Slug;
use App\Models\SystemLocation;
use App\Models\Template;
use App\Models\User;
use App\Services\FulfillmentService;
use App\Services\OrderService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Throwable;

class OrderProductController extends Controller
{
    use ApiResponse;

    private const EDITABLE_STATUSES = [
        OrderProductFulfillStatus::UNFULFILLED,
        OrderProductFulfillStatus::REJECTED,
        OrderProductFulfillStatus::INVALID,
        OrderProductFulfillStatus::ON_HOLD,
        OrderProductFulfillStatus::REVIEWING,
        OrderProductFulfillStatus::DESIGNING,
        OrderProductFulfillStatus::FULFILLED,
        OrderProductFulfillStatus::ON_DELIVERY,
    ];

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignSupplierHistories(Request $request): JsonResponse
    {
        try {
            $filter = OrderAssignSupplierHistoryFilter::class;
            $params = $request->all();
            $histories = OrderAssignSupplierHistory::filter($params, $filter)
                ->with([
                    'supplier:id,name',
                    'creator:id,name',
                    'fulfillProduct:id,name'
                ])
                ->orderByDesc('updated_at')
                ->get();

            return $this->successResponse($histories);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(Request $request): JsonResponse
    {
        $orderId = $request->input('order_id');
        $orderProductId = $request->input('order_product_id');
        $order = Order::query()
            ->with('seller')
            ->where('id', $orderId)
            ->first();

        if (!$order) {
            return $this->errorResponse('Data invalid');
        }
        $orderProduct = OrderProduct::query()
            ->select([
                'id',
                'sku',
                'order_id',
                'seller_id',
                'template_id',
                'product_id',
                'product_name',
                'campaign_id',
                'campaign_title',
                'product_url',
                'thumb_url',
                'options',
                'custom_options',
                'quantity',
                'full_printed',
                'personalized',
            ])
            ->with([
                'template:id,print_spaces,options,sku,full_printed,template_id,product_type'
            ])
            ->where([
                'id' => $orderProductId,
                'order_id' => $orderId,
            ])
            ->whereIn('fulfill_status', self::EDITABLE_STATUSES)
            ->first();

        if (!$orderProduct) {
            return $this->errorResponse('Data invalid');
        }

        $seller = $orderProduct->seller;

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select('id', 'slug', 'name', 'product_type', 'template_id')
            ->find($orderProduct->campaign_id);
        $orderProduct->setRelation('campaign', $campaign);

        $product = Product::query()
            ->onSellerConnection($seller)
            ->select('system_type', 'personalized')
            ->find($orderProduct->product_id);
        $orderProduct->setRelation('product', $product);

        if (!empty($orderProduct->campaign_id) && empty($orderProduct->campaign)) {
            return $this->errorResponse('Campaign not found');
        }
        if (empty($orderProduct->campaign_id)) {
            $templateProducts = Product::query()
                ->select([
                    'id',
                    'name',
                    'options',
                    'template_id',
                    'product_type',
                    'thumb_url',
                ])
                ->where('product_type', ProductType::TEMPLATE)
                ->get()->toArray();
        } else if ($orderProduct->campaign->product_type !== ProductType::CAMPAIGN_EXPRESS) {
            $templateProducts = Product::query()
                ->onSellerConnection($seller)
                ->select([
                    'id',
                    'name',
                    'options',
                    'template_id',
                    'product_type',
                    'thumb_url',
                ])
                ->where('campaign_id', $orderProduct->campaign_id)
                ->get()->toArray();
        } else {
            $arrFilterElastic = [
                'campaign_id' => $orderProduct->campaign_id,
                'limit' => 25,
            ];
            $elkProducts = (new Elastic())->getProduct(
                $arrFilterElastic,
                false
            );

            $products = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'product_type' => ProductType::PRODUCT_TEMPLATE,
                    'campaign_id' => $orderProduct->campaign->template_id
                ])
                ->with(['template:id,name,options'])
                ->get();

            if ($products->count() > 0) {
                $products->map(function ($product) use ($elkProducts) {
                    $productIdx = array_search($product->template_id, array_column($elkProducts, 'template_id'));

                    if ($productIdx >= 0 && isset($elkProducts[$productIdx]['thumb_url'])) {
                        $product->thumb_url = $elkProducts[$productIdx]['thumb_url'];
                    }
                });
            }

            $templateProducts = $products;
        }

        return $this->successResponse([
            'order_product' => $orderProduct,
            'template_products' => $templateProducts
        ]);
    }

    public function checkingLocationCode(string $countryCode): string
    {
        if (in_array($countryCode, CountryEnum::EU_COUNTRIES, true)) {
            return '150';
        }

        if ($countryCode === 'US' || $countryCode === 'CA') {
            return $countryCode;
        }

        return '*';
    }

    public function isProductVariantOOS(int $templateId, array $options, string $countryCode): bool
    {
        $variant = ProductVariant::query()
            ->where([
                'product_id' => $templateId,
                'variant_key' => getVariantKey($options),
                'location_code' => [$countryCode, '*'] // Ưu tiên tìm theo $countryCode trước, nếu không có thì tìm theo *
            ])
            ->first();

        return (bool)optional($variant)->out_of_stock;
    }

    public function update(UpdateOrderProductOptionsRequest $request): JsonResponse
    {
        $orderId = $request->post('order_id');
        $orderProductId = $request->post('order_product_id');
        $options = $request->post('options');
        $templateId = $request->post('template_id');
        $productId = $request->post('product_id');
        $quantity = (int)$request->post('quantity');
        $campaignId = $request->post('campaign_id');
        $thumbUrl = $request->post('thumb_url');
        $countryCode = $this->checkingLocationCode(
            $request->post('country')
        );

        // * product_id column of product_variant table is $templateId
        if ($this->isProductVariantOOS($templateId, $options, $countryCode)) {
            return $this->errorResponse('Product is out of stock');
        }

        $variantKey = getVariantKey($options);
        $location = getLocationByCode($countryCode);
        $orderRegionCodes = $location ? $location->getRegionCodes() : ['*'];
        $order = Order::query()->with('seller')->find($orderId);
        if (!$order || $order->isCustomServiceOrder()) {
            return $this->errorResponse('Order invalid');
        }
        $seller = $order->seller;
        if ($seller->custom_payment && ProductVariant::findAndCacheByTemplate($templateId)->contains('variant_key', "{$variantKey}-tm")) {
            return $this->errorResponse('Product is out of stock');
        }
        $variant = ProductVariant::findAndCacheByTemplate($templateId)
            ->filter(function ($each) use ($variantKey, $orderRegionCodes) {
                return $each->variant_key === $variantKey && in_array($each->location_code, $orderRegionCodes);
            })
            ->sortBy(function ($each) use ($orderRegionCodes) {
                return array_search($each['location_code'], $orderRegionCodes);
            })
            ->first();

        if (!$variant) {
            return $this->errorResponse('Product variant not exists');
        }

        $query = OrderProduct::query()
            ->where([
                'id' => $orderProductId,
                'order_id' => $orderId
            ])
            ->whereIn('fulfill_status', self::EDITABLE_STATUSES);

        if (!$query->exists()) {
            return $this->errorResponse('Order product not exists');
        }

        if ($quantity < 1) {
            return $this->errorResponse('Product quantity is invalid');
        }

        $color = null;
        $size = null;
        foreach ($options as $key => $value) {
            if (empty($value)) {
                return $this->errorResponse("Option {$key} is invalid");
            }

            if ($key === 'color') {
                $color = $value;
            }

            if ($key === 'size') {
                $size = $value;
            }
        }

        $encodedOption = json_encode($options);
        $orderProduct = $query->first();

        if (!$orderProduct) {
            return $this->errorResponse('Data invalid');
        }

        $oldProduct = Product::query()
            ->onSellerConnection($seller)
            ->select('system_type', 'personalized')
            ->find($orderProduct->product_id);

        $order = Order::query()->firstWhere('id', $orderId);

        if (!$order) {
            return $this->errorResponse('Data invalid');
        }

        if ($quantity > $orderProduct->quantity) {
            return $this->errorResponse('Product quantity is invalid');
        }

        $orderLocation = SystemLocation::findByCountryCodeThenSetForAssign($order);

        if (!$orderLocation) {
            return $this->errorResponse('Order location not found');
        }

        $orderProductDesignsQuery = Design::query()->where([
            'order_product_id' => $orderProductId,
            'order_id' => $orderId,
        ]);
        $orderProductFilesQuery = File::query()
            ->onSellerConnection($seller)
            ->where([
                'order_product_id' => $orderProductId,
                'order_id' => $orderId,
            ]);

        DB::beginTransaction();
        try {
            $dataFill = [
                'options' => $encodedOption,
            ];
            if (!empty($color)) {
                $dataFill['color'] = $color;
            }
            if (!empty($size)) {
                $dataFill['size'] = $size;
            }
            if ($order->type === OrderTypeEnum::FULFILLMENT) {
                $product = Product::query()->firstWhere('id', $templateId);

                if ($product) {
                    $dataFill['template_id'] = $product->id;
                }
            } else {
                $product = Product::query()
                    ->onSellerConnection($seller)
                    ->firstWhere(['id' => $productId]);
                if (!$product) {
                    return $this->errorResponse('Product not exists');
                }

                $dataFill['product_id'] = $product->id;
                $dataFill['template_id'] = $product->template_id;
                $dataFill['product_name'] = $product->name;
                $dataFill['thumb_url'] = $thumbUrl ?? $this->regexThumbUrlColor($product->thumb_url, $options['color'] ?? 'white');
                $dataFill['base_cost'] = $variant->base_cost;
            }

            if (!empty($campaignId)) {
                $campaign = Campaign::query()
                    ->onSellerConnection($seller)
                    ->select(['id', 'name', 'slug'])
                    ->firstWhere('id', $campaignId);
                if (!$campaign) {
                    return $this->errorResponse('Campaign not exists');
                }
                $dataFill['campaign_id'] = $campaign->id;
                $dataFill['campaign_title'] = $campaign->name;
                $dataFill['product_url'] = '/' . $campaign->slug . '?' . http_build_query($options);
            }

            $dataFill['product_name'] = $product->name;
            $dataFill['sku'] = $product->sku;
            $dataFill['full_printed'] = $product->full_printed;

            $quantitySplit = false;
            if ($quantity < $orderProduct->quantity) {
                $quantitySplit = true;

                $amountPerProduct = $orderProduct->total_amount / $orderProduct->quantity;
                $sellerProfitPerProduct = $orderProduct->seller_profit / $orderProduct->quantity;
                $artistProfitPerProduct = $orderProduct->artist_profit / $orderProduct->quantity;
                $shippingCostPerProduct = $orderProduct->shipping_cost / $orderProduct->quantity;
                $discountAmountPerProduct = $orderProduct->discount_amount / $orderProduct->quantity;
                $senPointsPerProduct = $orderProduct->sen_points / $orderProduct->quantity;

                $dataFill['quantity'] = $quantity;
                $dataFill['total_amount'] = $amountPerProduct * $dataFill['quantity'];
                $dataFill['seller_profit'] = $sellerProfitPerProduct * $dataFill['quantity'];
                $dataFill['artist_profit'] = $artistProfitPerProduct * $dataFill['quantity'];
                $dataFill['shipping_cost'] = $shippingCostPerProduct * $dataFill['quantity'];
                $dataFill['discount_amount'] = $discountAmountPerProduct * $dataFill['quantity'];
                $dataFill['sen_points'] = $senPointsPerProduct * $dataFill['quantity'];

                $splitOrderProductDataFill['quantity'] = $orderProduct->quantity - $quantity;
                $splitOrderProductDataFill['total_amount'] = $amountPerProduct * $splitOrderProductDataFill['quantity'];
                $splitOrderProductDataFill['seller_profit'] = $sellerProfitPerProduct * $splitOrderProductDataFill['quantity'];
                $splitOrderProductDataFill['artist_profit'] = $artistProfitPerProduct * $splitOrderProductDataFill['quantity'];
                $splitOrderProductDataFill['shipping_cost'] = $shippingCostPerProduct * $splitOrderProductDataFill['quantity'];
                $splitOrderProductDataFill['discount_amount'] = $discountAmountPerProduct * $splitOrderProductDataFill['quantity'];
                $splitOrderProductDataFill['sen_points'] = $senPointsPerProduct * $splitOrderProductDataFill['quantity'];

                $splitOrderProduct = $orderProduct->replicate()->fill($splitOrderProductDataFill);
                FulfillController::assignSupplierByLocation($splitOrderProduct, $orderLocation);

                if ($orderProduct->supplier_id && !empty($orderProduct->id) && $orderProduct->wasReAssignSupplier()) {
                    OrderAssignSupplierHistory::createFromOrderProduct($orderProduct);
                }

                // Nếu bất cứ sản phẩm nào trong đơn hàng đã được đánh dấu là cross shipping thì cũng đánh dấu
                // đơn hàng là cross shipping. Việc này để đội vận hành có thể lọc ra để xử lí thủ công
                if ($orderProduct->cross_shipping) {
                    $order->markCrossShipping(true);
                }

                $splitOrderProduct->save();

                $orderProductDesignsQuery->get()
                    ->each(fn($design) => $design->replicate()->fill(['order_product_id' => $splitOrderProduct->id])->save());

                $orderProductFilesQuery->get()
                    ->each(fn($file) => $file->replicate()->fill(['order_product_id' => $splitOrderProduct->id])->save());
            }

            $newOrderProduct = $orderProduct->replicate()->fill($dataFill);
            FulfillController::assignSupplierByLocation($newOrderProduct, $orderLocation);

            if ($orderProduct->supplier_id && !empty($orderProduct->id) && $orderProduct->wasReAssignSupplier()) {
                OrderAssignSupplierHistory::createFromOrderProduct($orderProduct);
            }

            // Nếu bất cứ sản phẩm nào trong đơn hàng đã được đánh dấu là cross shipping thì cũng đánh dấu
            // đơn hàng là cross shipping. Việc này để đội vận hành có thể lọc ra để xử lí thủ công
            if ($orderProduct->cross_shipping) {
                $order->markCrossShipping(true);
            }

            $newOrderProduct->save();
            $orderProductDesignsQuery->update([
                'order_product_id' => $newOrderProduct->id
            ]);
            $orderProductFilesQuery->update([
                'order_product_id' => $newOrderProduct->id
            ]);

            $detail = '#' . $newOrderProduct->id . ': ';

            if ($orderProduct->template_id !== $newOrderProduct->template_id) {
                $detail .= 'Change product: ' . $orderProduct->product_name . ' -> ' . $newOrderProduct->product_name . '; ';
            } else {
                $detail .= $newOrderProduct->product_name . '; ';
            }

            $variantKey = getVariantKey($orderProduct->options);
            $variantKey2 = getVariantKey($newOrderProduct->options);

            if ($variantKey !== $variantKey2) {
                $detail .= 'Change option: ' . $variantKey . ' -> ' . $variantKey2 . '; ';
            }

            if ($orderProduct->quantity !== $newOrderProduct->quantity) {
                $detail .= 'Change quantity: ' . $orderProduct->quantity . ' -> ' . $newOrderProduct->quantity . '; ';
            }

            if ($quantitySplit && !empty($splitOrderProduct)) {
                $detail .= 'Split quantity and create new order product #' . $splitOrderProduct->id . ' with quantity: ' . $splitOrderProduct->quantity . '; ';
            }

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::UPDATE_PRODUCT,
                $detail,
            );

            // cancel and delete old record
            $orderProduct->fulfill_status = OrderHistoryActionEnum::CANCELLED;
            $orderProduct->save();
            $orderProduct->delete();

            // Tính toán lại giá vận chuyển và lợi nhuận của sup cho đơn hàng
            OrderCostStatisticsJob::dispatchAfterResponse($order->id);

            $order->save();
            DB::commit();

            // If update from personalized camp to not personalized camp, inactive designs from design table
            if ($oldProduct->personalized && !$product->personalized && !$product->full_printed) {
                Design::query()->where([
                    'order_id' => $orderId,
                    'order_product_id' => $newOrderProduct->id,
                    'type' => DesignTypeEnum::PRINT
                ])->update([
                    'status' => DesignStatusEnum::INACTIVE
                ]);
            }

            if (!$newOrderProduct->personalized && $newOrderProduct->product_id) {
                $files = File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'product_id' => $newOrderProduct->product_id,
                        'type' => FileTypeEnum::DESIGN,
                        'option' => FileRenderType::PRINT
                    ])
                    ->whereNotNull('design_json')
                    ->whereNull('file_url_2')
                    ->get();

                if ($files->isNotEmpty()) {
                    $files->each(fn($file) => RenderPrintJob::dispatch($file, 'file')->onQueue('render'));
                }
            }
            clearHtmlCacheByTag('order', $orderId);
            return $this->successResponse('Update order product success');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function getOrderProductFiles(Request $request): JsonResponse
    {
        $designId = $request->get('design_id');
        $fileTable = $request->get('file_table');
        $orderId = $request->get('order_id');
        $orderProductId = $request->get('order_product_id');
        $order = Order::query()->with('seller')->find($orderId);
        $seller = $order->seller;
        $orderProduct = OrderProduct::query()
            ->select(['id',
                'sku',
                'order_id',
                'template_id',
                'product_id',
                'product_name',
                'campaign_id',
                'thumb_url',
                'options',
                'custom_options',
                'quantity',
                'full_printed',
                'personalized',
                'custom_print_space'
            ])
            ->with([
                'template:id,print_spaces',
                'product'
            ])
            ->where([
                'id' => $orderProductId,
                'order_id' => $orderId
            ])
            ->first();

        if (!$orderProduct) {
            return $this->errorResponse('Order product not exists');
        }
        if (!$orderProduct->product) {
            $product = Product::query()->onSellerConnection($seller)->first();
            $orderProduct->setRelation('product', $product);
        }

        if (!empty($fileTable) && $fileTable === 'design') {
            $query = Design::query();
        } else {
            $query = File::query()->onSellerConnection($seller);
        }

        $design = $query->firstWhere(['id' => $designId]);

        if ($design !== null) {
            if ($design->design_json) {
                [$designJson, $isChanged] = FulfillmentService::updateUrlOnDesignJson($design->design_json);
                if ($isChanged) {
                    $design->design_json = $designJson;
                    $design->save();
                }
            }
            if (!empty($orderProduct->custom_print_space)) {
                $design->print_space = $orderProduct->custom_print_space;
            }
            $mockups = File::query()
                ->onSellerConnection($seller)
                ->select([
                    'id',
                    'file_url_2',
                    'token',
                    'type',
                    'design_json',
                    'print_space',
                    'option',
                    'product_id',
                    'campaign_id',
                ])
                ->where([
                    'type' => FileTypeEnum::MOCKUP,
                    'render_type' => FileRenderType::BASE,
                    'product_id' => $orderProduct->template_id,
                ])
                ->where(function ($query) use ($orderProduct) {
                    $query->whereNull('type_detail');
                    if (!empty($orderProduct->product)) {
                        $query->orWhere('type_detail', $orderProduct->product->mockup_type);
                    }
                    return $query;
                })
                ->get();

            return $this->successResponse([
                'file_id' => $designId,
                'order_product' => $orderProduct,
                'design' => $design,
                'mockups' => $mockups
            ]);
        }

        return $this->errorResponse('Design do not have JSON data');
    }

    public function saveDesignJSON(Request $request): JsonResponse
    {
        $fileId = $request->get('file_id');
        $fileTable = $request->get('file_table', 'file');
        $designJson = $request->get('design_json');
        $mockupUrl = $request->get('mockup_url');
        $orderProductId = $request->get('order_product_id');
        $orderId = $request->get('order_id');
        $customOptions = $request->get('custom_options');
        $customPrintSpace = $request->get('custom_print_space');
        $order = Order::query()->with('seller')->find($orderId);
        $seller = $order->seller;

        if (!empty($fileTable) && $fileTable === 'design') {
            $query = Design::query();
        } else {
            $query = File::query()->onSellerConnection($seller);
        }

        $design = $query->firstWhere(['id' => $fileId]);

        if ($design === null) {
            return $this->errorResponse('No design found');
        }

        $orderProduct = OrderProduct::query()->with('order')->firstWhere('id', $orderProductId);

        if ($orderProduct === null) {
            return $this->errorResponse('Invalid arguments');
        }

        try {
            if (!empty($designJson)) {
                [$designJson,] = FulfillmentService::updateUrlOnDesignJson($designJson);
            }

            if (!empty($mockupUrl)) {
                $newFilePath = "o/{$orderId}";
                $newFilePath = saveTempFileAws($mockupUrl, $newFilePath);
                $orderProduct->thumb_url = $newFilePath;
            }

            if ($orderProduct->personalized && !empty($customOptions)) {
                $customOptions = json_decode($customOptions, true);
                $currentCustomOptions = json_decode($orderProduct->custom_options, true);
                foreach ($customOptions as $textKey => $textValue) {
                    $currentCustomOptions[$textKey] = $textValue;
                }
                $orderProduct->custom_options = json_encode($currentCustomOptions);
            }
            if (!empty($customPrintSpace)) {
                if ($design->print_space === $customPrintSpace) {
                    $orderProduct->custom_print_space = null;
                    Design::query()->where([
                        'order_id' => $orderProduct->order_id,
                        'product_id' => $orderProduct->product_id,
                        'order_product_id' => $orderProduct->id,
                        'type' => DesignTypeEnum::PRINT,
                        'print_space' => $customPrintSpace,
                    ])->delete();
                } else if (!empty($fileTable) && $fileTable === 'file') {
                    $design = Design::query()->create([
                        'order_id' => $orderProduct->order_id,
                        'product_id' => $orderProduct->product_id,
                        'order_product_id' => $orderProduct->id,
                        'type' => DesignTypeEnum::PRINT,
                        'print_space' => $customPrintSpace,
                        'design_json' => $designJson,
                    ]);
                    $orderProduct->custom_print_space = $customPrintSpace;
                }
            }

            if (empty($orderProduct->custom_print_space) || (!empty($fileTable) && $fileTable === 'design')) {
                if (!empty($designJson)) {
                    $design->design_json = $designJson;
                }

                if (!empty($customPrintSpace) && (!empty($fileTable) && $fileTable === 'design')) {
                    $design->print_space = $customPrintSpace;
                }

                $design->save();
            }
            $cfg = suppliers()->where('supplier_id', $orderProduct->supplier_id)->first();
            if ($cfg && Str::contains('print_space', data_get($cfg, 'on_change'))) {
                $orderLocation = SystemLocation::findByCountryCodeThenSetForAssign($orderProduct->order);
                if ($orderLocation) {
                    FulfillController::assignSupplierByLocation($orderProduct, $orderLocation, $orderProduct->supplier_id, true, true);
                }
            }
            $orderProduct->save();
            RenderPrintJob::dispatch($design, $fileTable)->onQueue('render');
            if (!empty($customOptions)) {
                OrderChangeDesign::dispatch($orderId, json_encode($customOptions));
            }
            return $this->successResponse(['file_id' => $fileId]);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    private function regexThumbUrlColor(string $thumbUrl = '', $color = 'black')
    {
        $hexCode = ltrim(color2hex($color), '#');
        $pattern1 = "/(co_rgb:)\w+/";
        $pattern2 = "/(c=)\w+/";
        if (preg_match($pattern1, $thumbUrl)) {
            return preg_replace($pattern1, "co_rgb:{$hexCode}", $thumbUrl);
        }
        if (preg_match($pattern2, $thumbUrl)) {
            return preg_replace($pattern1, "c={$hexCode}", $thumbUrl);
        }
        return $thumbUrl;
    }

    public function searchCampaign(Request $request): JsonResponse
    {
        $request->validate(['url' => 'required|url']);
        $url = parse_url($request->input('url'), PHP_URL_PATH);
        $arrFilterElastic['slug'] = $this->getSlug($url);
        $sellerId = Slug::query()->where('slug', $arrFilterElastic['slug'])->value('seller_id') ?? '';
        $seller = User::query()->find($sellerId) ?? currentUser()->getInfoAccess();
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select([
                'id', 'name', 'product_type', 'template_id'
            ])
            ->with(['products' => function ($query) use ($seller) {
                $query->onSellerConnection($seller)
                    ->with(['template:id,name,options']);
            }])
            ->firstWhere('slug', $arrFilterElastic['slug']);

        if (!$campaign) {
            return $this->errorResponse();
        }

        if ($campaign->product_type === ProductType::CAMPAIGN_EXPRESS) {
            unset($campaign->products);
            $arrFilterElastic = [
                'campaign_id' => $campaign->id,
                'limit' => 1000,
            ];
            $elkProducts = (new Elastic())->getProduct(
                $arrFilterElastic,
                false
            );

            $products = Product::query()
                ->where([
                    'product_type' => ProductType::PRODUCT_TEMPLATE,
                    'campaign_id' => $campaign->template_id
                ])
                ->with(['template:id,name,options'])
                ->get();

            if ($products->count() > 0) {
                $products->map(function ($product) use ($elkProducts) {
                    $productIdx = array_search($product->template_id, array_column($elkProducts, 'template_id'));
                    if ($productIdx >= 0) {
                        $product->thumb_url = $elkProducts[$productIdx]['thumb_url'];
                    }
                });
            }

            $campaign->setRelation('products', $products);
        }

        return $this->successResponse($campaign);
    }

    private function getSlug($url): string
    {
        $segments = explode('/', trim($url, '/'));
        return $segments[0] ?? '';
    }

    public function resetOrderProductDesign(Request $request): JsonResponse
    {
        $designId = $request->get('design_id');
        $fileTable = $request->get('file_table');
        $orderId = $request->get('order_id');
        $orderProductId = $request->get('order_product_id');
        $order = Order::query()->with('seller')->findOrFail($orderId);
        $seller = $order->seller;
        $orderProduct = OrderProduct::query()
            ->with([
                'template:id,print_spaces',
                'product'
            ])
            ->where([
                'id' => $orderProductId,
                'order_id' => $orderId
            ])
            ->first();

        if (!$orderProduct) {
            return $this->errorResponse('Order product not found');
        }
        if (!$orderProduct->product) {
            $product = Product::query()
                ->onSellerConnection($seller)
                ->first();
            $orderProduct->setRelation('product', $product);
        }

        if (!empty($fileTable) && $fileTable === 'design') {
            $query = Design::query();
        } else {
            $query = File::query()->onSellerConnection($seller);
        }

        $design = $query->where(['id' => $designId])->first();

        if (empty($design)) {
            return $this->errorResponse('Design not found');
        }

        $templateDesign = File::query()
            ->onSellerConnection($seller)
            ->whereIn('option', [FileRenderType::CUSTOM, FileRenderType::PRINT])
            ->orderBy('option')
            ->firstWhere([
                'product_id' => $orderProduct->product_id,
                'print_space' => $design->print_space,
                'type' => FileTypeEnum::DESIGN,
            ]);

        if ($templateDesign === null) {
            return $this->errorResponse('Template not found');
        }

        $result = $design->update([
            'design_json' => $templateDesign->design_json
        ]);

        if ($result) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function customOptionDesignUpload(CustomOptionDesignUploadRequest $request): JsonResponse
    {
        $user = currentUser();

        $orderProductId = $request->input('order_product_id');
        $filePath = $request->input('file_path');
        $printSpace = $request->input('print_space');
        $onlyOnePrintSpace = $request->input('only_one_print_space', false);

        $orderProduct = OrderProduct::query()
            ->with(['order', 'seller'])
            ->when($user->isSeller(), function ($query) use ($user) {
                $query->where('seller_id', $user->getUserId());
            })
            ->findOrFail($orderProductId);
        $order = $orderProduct->order;
        $newFilePath = "o/{$orderProduct->order_id}";
        $newFilePath = saveTempFileAws($filePath, $newFilePath);

        if ($order->isFulfillmentOrder()) {
            $fields = [
                'order_id' => $orderProduct->order_id,
                'seller_id' => $orderProduct->seller_id,
                'type' => FileTypeEnum::DESIGN,
                'option' => FileRenderType::PRINT
            ];
            File::query()->where([
                'order_id' => $orderProduct->order_id,
                'order_product_id' => $orderProduct->id,
                'seller_id' => $orderProduct->seller_id,
            ])->update([
                'status' => FileStatusEnum::INACTIVE
            ]);
            File::query()->insert(array_merge($fields, [
                'order_product_id' => $orderProduct->id,
                'print_space' => $printSpace,
                'file_url' => $newFilePath,
                'file_url_2' => null
            ]));
            $designs = File::query()->where($fields)->get();
        } else {
            $fields = [
                'product_id' => $orderProduct->product_id,
                'order_id' => $orderProduct->order_id,
                'order_product_id' => $orderProduct->id,
                'seller_id' => $orderProduct->seller_id,
            ];

            if (!$onlyOnePrintSpace) {
                $fields['print_space'] = $printSpace;
            }
            Design::query()->where($fields)->update(['status' => DesignStatusEnum::INACTIVE]);
            Design::query()->create([
                'product_id' => $orderProduct->product_id,
                'order_id' => $orderProduct->order_id,
                'order_product_id' => $orderProduct->id,
                'seller_id' => $orderProduct->seller_id,
                'print_space' => $printSpace,
                'file_url' => $newFilePath,
                'type' => DesignTypeEnum::PRINT,
                'status' => DesignStatusEnum::ACTIVE
            ]);
            $designs = Design::query()->where([
                'product_id' => $orderProduct->product_id,
                'order_id' => $orderProduct->order_id,
                'order_product_id' => $orderProduct->id,
                'seller_id' => $orderProduct->seller_id,
                'type' => DesignTypeEnum::PRINT,
                'status' => DesignStatusEnum::ACTIVE,
            ])->get();
        }
        OrderChangeDesign::dispatch($orderProduct->order_id, ($user->getName() ?? $user->getEmail()) . ' has uploaded a new design. Url: ' . imgUrl($filePath));
        return $this->successResponse($designs);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function approveCustomOptionDesign(Request $request): JsonResponse
    {
        $user = currentUser();
        $orderId = $request->input('order_id');
        $order = Order::query()
            ->whereNotIn('fulfill_status', [OrderFulfillStatus::FULFILLED, OrderFulfillStatus::CANCELLED])
            ->findOrFail($orderId);
        $orderProducts = OrderProduct::query()
            ->with(['customOptionDesigns'])
            ->where('order_id', $orderId)
            ->when($user->isSeller(), function ($query) use ($user) {
                $query->where('seller_id', $user->getUserId());
            })
            ->when($order->isFulfillmentOrder(), function ($query) {
                $query->with(['fulfillOrderDesigns']);
            })
            ->get();
        $printDesigns = collect();
        $sellerProducts = collect();
        $orderProducts->groupBy('seller_id')->each(function ($productGroup, $sellerId) use ($user, $order, &$printDesigns, &$sellerProducts) {
            $seller = $user->getUserId() === $sellerId ? $user : User::query()->find($sellerId);
            $productIds = $productGroup->pluck('product_id')->unique()->filter(fn($item) => !empty($item));
            if ($productIds->isEmpty()) {
                return;
            }
            $sellerProducts = $sellerProducts->merge(
                Product::query()
                    ->select(['id', 'system_type', 'seller_id', 'template_id'])
                    ->onSellerConnection($seller)
                    ->with(['template:id,thumb_url,print_spaces,base_costs,base_cost'])
                    ->whereIn('id', $productIds)
                    ->orderBy('updated_at', 'desc')
                    ->get()
            );
            $printDesigns = $printDesigns->merge(
                File::query()
                    ->onSellerConnection($seller)
                    ->where('type', FileTypeEnum::DESIGN)
                    ->where('option', FileRenderType::PRINT)
                    ->whereIn('product_id', $productIds)
                    ->get()
            );
        });
        $orderProducts->each(function (OrderProduct $orderProduct) use ($printDesigns, $sellerProducts, $order) {
            if (!$order->isFulfillmentOrder()) {
                $orderProduct->setRelation('product', $sellerProducts->first(fn($item) => (int)$item->seller_id === (int)$orderProduct->seller_id && (int)$item->id === (int)$orderProduct->product_id));
                $orderProduct->setRelation('printDesigns', $printDesigns->filter(fn($item) => (int)$item->seller_id === (int)$orderProduct->seller_id && (int)$item->product_id === (int)$orderProduct->product_id));
            }
        });

        $invalidOrderProducts = $orderProducts->where('fulfill_status', OrderProductFulfillStatus::INVALID);
        $reviewingOrderProducts = $orderProducts->where('fulfill_status', OrderProductFulfillStatus::REVIEWING);
        $allDesigningOrderProducts = $orderProducts->filter(fn(OrderProduct $orderProduct) => $orderProduct->fulfill_status === OrderProductFulfillStatus::DESIGNING)->values();
        $designingOrderProducts = $orderProducts->filter(fn(OrderProduct $orderProduct) => $orderProduct->fulfill_status === OrderProductFulfillStatus::DESIGNING && ($orderProduct->campaign_type !== ProductSystemTypeEnum::AI_MOCKUP || $orderProduct->design_by === DesignByEnum::SELLER))->values();
        $designingBySenPrintsOrderProducts = $orderProducts->filter(fn(OrderProduct $orderProduct) => $orderProduct->fulfill_status === OrderProductFulfillStatus::DESIGNING && ($orderProduct->campaign_type === ProductSystemTypeEnum::AI_MOCKUP || $orderProduct->design_by === DesignByEnum::SENPRINTS))->values();

        $mugsProductId = Template::getAndCacheProductIdByCategory(ProductCategoryEnum::MUGS);
        if ($allDesigningOrderProducts->isNotEmpty()) {
            $notEnoughDesign = $designingOrderProducts->filter(function (OrderProduct $op) use ($mugsProductId, $order) {
                if ($order->isFulfillmentOrder()) {
                    return $op->isNotEnoughFulfillmentDesigns();
                }
                return $op->isNotEnoughCustomOptionDesigns($mugsProductId);
            })->values()->count();

            if ($notEnoughDesign) {
                return $this->errorResponse('Please upload designs for all custom option products');
            }
            if ($user->isAdmin() && $designingBySenPrintsOrderProducts->isNotEmpty()) {
                $notEnoughDesign = $designingBySenPrintsOrderProducts->filter(function (OrderProduct $product) {
                    return $product->isNotEnoughAiDesigns();
                })->values()->count();
                if ($notEnoughDesign) {
                    return $this->errorResponse('Please upload designs for all print spaces');
                }
            }
            $total_extra_print_cost = 0;
            DB::beginTransaction();
            try {
                foreach ($designingOrderProducts as $product) {
                    /** @var OrderProduct $product */
                    if ($product->customOptionDesigns->isEmpty()) {
                        continue;
                    }
                    foreach ($product->customOptionDesigns as $customOptionDesign) {
                        $newPath = 'o/' . $product->order_id . '/custom_option_designs';
                        $newFileUrl = saveTempFileAws($customOptionDesign->file_url, $newPath);
                        $customOptionDesign->fill([
                            'file_url' => $newFileUrl
                        ])->update();
                    }
                }

                OrderProduct::query()
                    ->whereIn('id', $designingOrderProducts->pluck('id'))
                    ->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);

                if ($user->isAdmin() && $designingBySenPrintsOrderProducts->isNotEmpty()) {
                    foreach ($designingBySenPrintsOrderProducts as $product) {
                        /** @var OrderProduct $product */
                        if ($product->customOptionDesigns->isEmpty()) {
                            continue;
                        }
                        foreach ($product->customOptionDesigns as $customOptionDesign) {
                            $newPath = 'o/' . $product->order_id . '/ai_designs';
                            $newFileUrl = saveTempFileAws($customOptionDesign->file_url, $newPath);
                            $customOptionDesign->fill([
                                'file_url' => $newFileUrl
                            ])->update();
                        }
                    }
                    OrderProduct::query()
                        ->whereIn('id', $designingBySenPrintsOrderProducts->pluck('id'))
                        ->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);
                }
                if ($order->fulfill_status === OrderFulfillStatus::DESIGNING && (($user->isSeller() && $designingBySenPrintsOrderProducts->isEmpty()) || $user->isAdmin())) {
                    $orderFulfillStatus = OrderFulfillStatus::UNFULFILLED;

                    if ($invalidOrderProducts->isNotEmpty()) {
                        $orderFulfillStatus = OrderFulfillStatus::INVALID;
                    } elseif ($reviewingOrderProducts->isNotEmpty()) {
                        $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                    }

                    $order->fill(['fulfill_status' => $orderFulfillStatus])->update();
                }
                /** @var OrderProductBaseCostCalculator $baseCostCalc */
                $baseCostCalc = app(OrderProductBaseCostCalculator::class);
                $orderLocation = getLocationByCode($order->country);
                foreach ($designingOrderProducts as $order_product) {
                    /** @var OrderProduct $order_product */
                    if (in_array((int)$order_product->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY], true) && $order_product->printDesigns->count() > 1) {
                        $extra_print_cost = $order_product->template->calcExtraPrintCost($order_product->printDesigns);
                        $total_extra_print_cost += ($extra_print_cost * $order_product->quantity);
                        $baseCost = $baseCostCalc->handle(
                            $order_product->product->template,
                            getVariantKey($order_product->options),
                            $orderLocation
                        );
                        OrderProduct::query()->where('id', $order_product->id)->update([
                            'extra_print_cost' => $extra_print_cost,
                            'base_cost' => $baseCost + $extra_print_cost
                        ]);
                        if ($order_product->product_id) {
                            Product::query()->where('id', $order_product->product_id)->update(['extra_print_cost' => $extra_print_cost]);
                        }
                    }
                }
                if ($total_extra_print_cost > 0) {
                    if ($order->isRegularOrder()) {
                        $order->seller->updateBalance(-$total_extra_print_cost, SellerBillingType::FEE, 'Extra print cost of order #' . $order->order_number, $orderId);
                    }
                    $order = $order->refresh();
                    $needCharge = $order->calculateAndSaveOrderServiceAndFulfillFee();
                    if ($needCharge) {
                        OrderService::processCustomOrder($order);
                    }
                }
                OrderChangeDesign::dispatch($order->id, ($user->getName() ?? $user->getEmail()) . ' approved custom option designs');
                DB::commit();
            } catch (Throwable $exception) {
                DB::rollBack();
                logToDiscord('Approve design failed - Order Id #' . $orderId . ': ' . $exception->getMessage());
                return $this->errorResponse('Approve design failed');
            }
        }

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function approveSenPrintsDesign(Request $request): JsonResponse
    {
        $user = currentUser();
        $orderProductId = $request->input('order_product_id');
        $orderProduct = OrderProduct::query()
            ->with(['customOptionDesigns', 'order'])
            ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING)
            ->where(function ($query) {
                $query->where('campaign_type', ProductSystemTypeEnum::AI_MOCKUP);
                $query->orWhere('design_by', DesignByEnum::SENPRINTS);
            })
            ->find($orderProductId);
        if (!$orderProduct) {
            return $this->errorResponse("Can't find order product");
        }
        $seller = $orderProduct->seller;
        if ($orderProduct->order->isFulfillmentOrder()) {
            $orderProduct->load(['fulfillOrderDesigns', 'fulfillOrderMockups']);
            if ($orderProduct->isNotEnoughFulfillmentDesigns()) {
                return $this->errorResponse('Please upload designs for all print spaces');
            }
        } else {
            $orderProduct->load([
                'product' => function ($query) use ($seller) {
                    $query->select(['id', 'system_type', 'seller_id', 'template_id'])
                        ->onSellerConnection($seller)
                        ->with(['template:id,thumb_url,print_spaces,base_costs,base_cost'])
                        ->orderBy('updated_at', 'desc');
                },
                'printDesigns' => function ($query) use ($seller) {
                    $query->onSellerConnection($seller)
                        ->where('type', FileTypeEnum::DESIGN)
                        ->where('option', FileRenderType::PRINT);
                }
            ]);
            if ($orderProduct->isNotEnoughAiDesigns()) {
                return $this->errorResponse('Please upload designs for all print spaces');
            }
            if ($orderProduct->customOptionDesigns->isNotEmpty()) {
                foreach ($orderProduct->customOptionDesigns as $customOptionDesign) {
                    $newPath = 'o/' . $orderProduct->order_id . '/ai_designs';
                    $newFileUrl = saveTempFileAws($customOptionDesign->file_url, $newPath);
                    $customOptionDesign->fill([
                        'file_url' => $newFileUrl
                    ])->update();
                }
            }
        }
        $orderProduct->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);
        $hasOtherDesigningOrderProduct = OrderProduct::query()
            ->where('order_id', $orderProduct->order_id)
            ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING)
            ->where('id', '<>', $orderProduct->id)
            ->exists();

        if (!$hasOtherDesigningOrderProduct) {
            $orderProduct->order->update([
                'fulfill_status' => $orderProduct->order->getOrderFullFillStatus()
            ]);
        }
        OrderChangeDesign::dispatch($orderProduct->order_id, ($user->getName() ?? $user->getEmail()) . ' approved design order product: ' . $orderProduct->id);
        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function approveProductDesigns(Request $request): JsonResponse
    {
        $user = currentUser();
        $orderId = $request->input('order_id');
        $order = Order::query()->findOrFail($orderId);
        if (!in_array($order->fulfill_status, [OrderFulfillStatus::FULFILLED, OrderFulfillStatus::CANCELLED], true)) {
            $orderProducts = OrderProduct::query()
                ->where('order_id', $orderId)
                ->when($user->isSeller(), function ($query) use ($user) {
                    $query->where('seller_id', $user->getUserId());
                })
                ->get();
            $printDesigns = collect();
            $sellerProducts = collect();
            $orderProducts->groupBy('seller_id')->each(function ($productGroup, $sellerId) use ($user, &$printDesigns, &$sellerProducts) {
                $seller = $user->getUserId() === $sellerId ? $user : User::query()->find($sellerId);
                $productIds = $productGroup->pluck('product_id')->filter()->unique();
                if ($productIds->isEmpty()) {
                    return;
                }
                $sellerProducts = $sellerProducts->merge(
                    Product::query()
                        ->select(['id', 'system_type', 'seller_id', 'template_id'])
                        ->onSellerConnection($seller)
                        ->with(['template:id,thumb_url,print_spaces,base_costs,base_cost'])
                        ->whereIn('id', $productIds)
                        ->orderByDesc('updated_at')
                        ->get()
                );
                $printDesigns = $printDesigns->merge(
                    File::query()
                        ->onSellerConnection($seller)
                        ->where('type', FileTypeEnum::DESIGN)
                        ->where('option', FileRenderType::PRINT)
                        ->whereIn('product_id', $productIds)
                        ->get()
                );
            });
            $orderProducts->each(function (OrderProduct $orderProduct) use ($printDesigns, $sellerProducts) {
                $orderProduct->setRelation('product', $sellerProducts->first(fn($item) => (int)$item->seller_id === (int)$orderProduct->seller_id && (int)$item->id === (int)$orderProduct->product_id));
                $orderProduct->setRelation('printDesigns', $printDesigns->filter(fn($item) => (int)$item->seller_id === (int)$orderProduct->seller_id && (int)$item->product_id === (int)$orderProduct->product_id));
            });
            $invalidOrderProducts = $orderProducts->where('fulfill_status', OrderProductFulfillStatus::INVALID);
            $reviewingOrderProducts = $orderProducts->where('fulfill_status', OrderProductFulfillStatus::REVIEWING);
            $allDesigningOrderProducts = $orderProducts->filter(fn(OrderProduct $orderProduct) => $orderProduct->fulfill_status === OrderProductFulfillStatus::DESIGNING)->values();
            $designingOrderProducts = $orderProducts->filter(fn(OrderProduct $orderProduct) => $orderProduct->fulfill_status === OrderProductFulfillStatus::DESIGNING && ($orderProduct->campaign_type !== ProductSystemTypeEnum::AI_MOCKUP || $orderProduct->design_by === DesignByEnum::SELLER))->values();
            $designingBySenPrintsOrderProducts = $orderProducts->filter(fn(OrderProduct $orderProduct) => $orderProduct->fulfill_status === OrderProductFulfillStatus::DESIGNING && ($orderProduct->campaign_type === ProductSystemTypeEnum::AI_MOCKUP || $orderProduct->design_by === DesignByEnum::SENPRINTS))->values();

            if ($allDesigningOrderProducts->isNotEmpty()) {
                $notEnoughDesigns = $designingOrderProducts->filter(function (OrderProduct $product) {
                   return $product->isNotEnoughProductDesigns(false);
                })->values()->count();
                if ($notEnoughDesigns) {
                    return $this->errorResponse('Please upload designs for all products');
                }
                if ($user->isAdmin() && $designingBySenPrintsOrderProducts->isNotEmpty()) {
                    $notEnoughDesign = $designingBySenPrintsOrderProducts->filter(function (OrderProduct $product) {
                        return $product->isNotEnoughAiDesigns();
                    })->values()->count();
                    if ($notEnoughDesign) {
                        return $this->errorResponse('Please upload designs for all print spaces');
                    }
                }
                $total_extra_print_cost = 0;
                DB::beginTransaction();
                try {
                    OrderProduct::query()
                        ->whereIn('id', $designingOrderProducts->pluck('id'))
                        ->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);

                    if ($user->isAdmin() && $designingBySenPrintsOrderProducts->isNotEmpty()) {
                        OrderProduct::query()
                            ->whereIn('id', $designingBySenPrintsOrderProducts->pluck('id'))
                            ->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);
                    }

                    if ($order->fulfill_status === OrderFulfillStatus::DESIGNING && (($user->isSeller() && $designingBySenPrintsOrderProducts->isEmpty()) || $user->isAdmin())) {
                        $orderFulfillStatus = OrderFulfillStatus::UNFULFILLED;

                        if ($invalidOrderProducts->isNotEmpty()) {
                            $orderFulfillStatus = OrderFulfillStatus::INVALID;
                        } elseif ($reviewingOrderProducts->isNotEmpty()) {
                            $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                        }
                        $order->fill(['fulfill_status' => $orderFulfillStatus])->update();
                    }
                    /** @var OrderProductBaseCostCalculator $baseCostCalc */
                    $baseCostCalc = app(OrderProductBaseCostCalculator::class);
                    $orderLocation = getLocationByCode($order->country);
                    foreach ($designingOrderProducts as $order_product) {
                        /** @var OrderProduct $order_product */
                        if ($order_product->printDesigns->count() > 1 && in_array((int)$order_product->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY], true)) {
                            $extra_print_cost = $order_product->template->calcExtraPrintCost($order_product->printDesigns);
                            $total_extra_print_cost += ($extra_print_cost * $order_product->quantity);

                            $baseCost = $baseCostCalc->handle(
                                $order_product->product->template,
                                getVariantKey($order_product->options),
                                $orderLocation
                            );

                            OrderProduct::query()->where('id', $order_product->id)->update([
                                'extra_print_cost' => $extra_print_cost,
                                'base_cost' => $baseCost + $extra_print_cost
                            ]);

                            Product::query()->where('id', $order_product->product_id)->update(['extra_print_cost' => $extra_print_cost]);
                        }
                    }
                    if ($total_extra_print_cost > 0) {
                        if ($order->isRegularOrder()) {
                            $order->seller->updateBalance(-$total_extra_print_cost, SellerBillingType::FEE, 'Extra print cost of order #' . $order->order_number, $orderId);
                        }
                        $order = $order->refresh();
                        $needCharge = $order->calculateAndSaveOrderServiceAndFulfillFee();
                        if ($needCharge) {
                            OrderService::processCustomOrder($order);
                        }
                    }
                    OrderChangeDesign::dispatch($order->id, ($user->getName() ?? $user->getEmail()) . ' approved product designs');
                    DB::commit();
                } catch (Throwable $exception) {
                    DB::rollBack();
                    logToDiscord('Approve design failed - Order Id #' . $orderId . ': ' . $exception->getMessage());
                    return $this->errorResponse('Approve design failed');
                }
            }
        }
        return $this->successResponse();
    }

    /**
     * @param int $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function approveOrderProductCustomOptionDesign(int $id): JsonResponse
    {
        $user = currentUser();
        $orderProduct = OrderProduct::query()
            ->with([
                'customOptionDesigns',
                'printDesigns' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                },
                'product' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                }
            ])
            ->whereKey($id)
            ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING)
            ->where('seller_id', $user->getUserId())
            ->first();

        if (empty($orderProduct)) {
            return $this->errorResponse('Order product not found');
        }
        $mugsProductId = Template::getAndCacheProductIdByCategory(ProductCategoryEnum::MUGS);
        if ($orderProduct->isNotEnoughCustomOptionDesigns($mugsProductId)) {
            $this->errorResponse('Please upload enough designs for this order product');
        }

        $order = Order::query()->whereKey($orderProduct->order_id)->whereNotIn('fulfill_status', [OrderFulfillStatus::FULFILLED, OrderFulfillStatus::CANCELLED])->first();
        if (empty($order)) {
            return $this->errorResponse('This order product cannot be approved');
        }
        $hasOtherDesigningOrderProduct = OrderProduct::query()
            ->where('order_id', $orderProduct->order_id)
            ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING)
            ->where('id', '<>', $orderProduct->id)
            ->exists();

        if (!$hasOtherDesigningOrderProduct) {
            $orderFulfillStatus = $order->getOrderFullFillStatus();
        }

        DB::beginTransaction();
        try {
            foreach ($orderProduct->customOptionDesigns as $customOptionDesign) {
                if (str_starts_with($customOptionDesign->file_url, 'tmp/')) {
                    $newPath = 'o/' . $orderProduct->order_id . '/custom_option_designs';
                    $newThumbUrl = saveTempFileAws($customOptionDesign->file_url, $newPath);
                    $customOptionDesign->fill([
                        'file_url' => $newThumbUrl
                    ])->update();
                }
            }

            $orderProduct->fulfill_status = OrderProductFulfillStatus::UNFULFILLED;
            $orderProduct->save();

            if (isset($orderFulfillStatus)) {
                $order->fulfill_status = $orderFulfillStatus;
                $order->save();

                OrderHistory::insertLog(
                    $order->refresh(),
                    OrderHistoryActionEnum::APPROVED_DESIGN,
                    'Approved custom option design'
                );
                /** @var OrderProductBaseCostCalculator $baseCostCalc */
                $baseCostCalc = app(OrderProductBaseCostCalculator::class);
                $orderLocation = getLocationByCode($order->country);
                if (empty($orderProduct->extra_print_cost) && in_array((int)$orderProduct->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY], true) && $orderProduct->printDesigns->count() > 1) {
                    $extraPrintCost = $orderProduct->template->calcExtraPrintCost($orderProduct->printDesigns);
                    if ($extraPrintCost > 0) {
                        $baseCost = $baseCostCalc->handle(
                            $orderProduct->product->template,
                            getVariantKey($orderProduct->options),
                            $orderLocation
                        );
                        OrderProduct::query()->where('id', $orderProduct->id)->update([
                            'extra_print_cost' => $extraPrintCost,
                            'base_cost' => $baseCost + $extraPrintCost
                        ]);
                        Product::query()->where('id', $orderProduct->product_id)->update(['extra_print_cost' => $extraPrintCost]);
                        if ($order->isRegularOrder()) {
                            $order->seller->updateBalance(-$extraPrintCost, SellerBillingType::FEE, 'Extra print cost of order #' . $order->order_number, $order->id);
                        }
                        $needCharge = $order->calculateAndSaveOrderServiceAndFulfillFee();
                        $order = $order->refresh();
                        if ($needCharge) {
                            OrderService::processCustomOrder($order);
                        }
                    }
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            logToDiscord('Approve design failed - Order Id #' . $orderProduct->order_id . ': ' . $exception->getMessage());
            return $this->errorResponse('Approve design failed');
        }

        return $this->successResponse();
    }

    /**
     * @param int $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function approveOrderProductDesign(int $id): JsonResponse
    {
        $user = currentUser();
        $orderProduct = OrderProduct::query()
            ->with([
                'printDesigns' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                },
                'product' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                    $query->with(['template:id,thumb_url,print_spaces'])->orderBy('updated_at', 'desc');
                }])
            ->whereKey($id)
            ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING)
            ->where('seller_id', $user->getUserId())
            ->first();

        if (empty($orderProduct)) {
            return $this->errorResponse('Order product not found');
        }

        if ($orderProduct->isNotEnoughProductDesigns()) {
            $this->errorResponse('Please upload enough designs for this order product');
        }

        $order = Order::query()->whereKey($orderProduct->order_id)->whereNotIn('fulfill_status', [OrderFulfillStatus::FULFILLED, OrderFulfillStatus::CANCELLED])->first();
        if (empty($order)) {
            return $this->errorResponse('This order product cannot be approved');
        }
        $hasOtherDesigningOrderProduct = OrderProduct::query()
            ->where('order_id', $orderProduct->order_id)
            ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING)
            ->where('id', '<>', $orderProduct->id)
            ->exists();

        if (!$hasOtherDesigningOrderProduct) {
            $orderFulfillStatus = $order->getOrderFullFillStatus();
        }

        DB::beginTransaction();

        try {
            if (isset($orderFulfillStatus)) {
                $order->fulfill_status = $orderFulfillStatus;
                $order->save();

                OrderHistory::insertLog(
                    $order->refresh(),
                    OrderHistoryActionEnum::APPROVED_DESIGN,
                    'Approved product design'
                );

                /** @var OrderProductBaseCostCalculator $baseCostCalc */
                $baseCostCalc = app(OrderProductBaseCostCalculator::class);
                $orderLocation = getLocationByCode($order->country);
                if (empty($orderProduct->extra_print_cost) && in_array((int)$orderProduct->full_printed, [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY], true) && $orderProduct->printDesigns->count() > 1) {
                    $extraPrintCost = $orderProduct->template->calcExtraPrintCost($orderProduct->printDesigns);
                    if ($extraPrintCost > 0) {
                        $baseCost = $baseCostCalc->handle(
                            $orderProduct->product->template,
                            getVariantKey($orderProduct->options),
                            $orderLocation
                        );
                        OrderProduct::query()->where('id', $orderProduct->id)->update([
                            'extra_print_cost' => $extraPrintCost,
                            'base_cost' => $baseCost + $extraPrintCost
                        ]);
                        Product::query()->where('id', $orderProduct->product_id)->update(['extra_print_cost' => $extraPrintCost]);
                        if ($order->isRegularOrder()) {
                            $order->seller->updateBalance(-$extraPrintCost, SellerBillingType::FEE, 'Extra print cost of order #' . $order->order_number, $order->id);
                        }
                        $needCharge = $order->calculateAndSaveOrderServiceAndFulfillFee();
                        $order = $order->refresh();
                        if ($needCharge) {
                            OrderService::processCustomOrder($order);
                        }
                    }
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            logToDiscord('Approve design failed - Order Id #' . $orderProduct->order_id . ': ' . $exception->getMessage());
            return $this->errorResponse('Approve design failed');
        }

        return $this->successResponse($orderProduct);
    }

    /**
     * Admin manual request design
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function requestDesign(Request $request): JsonResponse
    {
        $user = currentUser();
        $orderId = $request->input('order_id');
        $orderProductId = $request->input('order_product_id');

        $orderProduct = OrderProduct::query()
            ->where([
                'id' => $orderProductId,
                'order_id' => $orderId,
            ])
            ->whereIn('fulfill_status', [
                OrderProductFulfillStatus::UNFULFILLED,
                OrderProductFulfillStatus::REJECTED,
                OrderProductFulfillStatus::INVALID,
                OrderProductFulfillStatus::REVIEWING,
            ])
            ->with(['order'])
            ->first();

        if (empty($orderProduct)) {
            return $this->errorResponse();
        }

        try {
            DB::beginTransaction();
            $orderProduct->fulfill_status = OrderProductFulfillStatus::DESIGNING;
            $orderProduct->save();

            if (in_array($orderProduct->order->fulfill_status, [
                OrderFulfillStatus::UNFULFILLED,
                OrderFulfillStatus::REVIEWING,
                OrderFulfillStatus::INVALID,
                OrderFulfillStatus::ON_HOLD,
                OrderFulfillStatus::PROCESSING,
            ], true)) {
                Order::query()
                    ->where('id', $orderId)
                    ->update(['fulfill_status' => OrderFulfillStatus::DESIGNING]);
            }
            OrderChangeDesign::dispatch($orderId, ($user->getName() ?? $user->getEmail()) . ' requested design');
            DB::commit();
            return $this->successResponse();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * Admin delete a design
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteDesign(Request $request): JsonResponse
    {
        $request->validate([
            'file_id' => 'required',
            'file_table' => 'required|string|in:file,design',
            'seller_id' => 'nullable',
        ]);
        $fileId = $request->input('file_id');
        $sellerId = $request->input('seller_id');
        $fileTable = $request->input('file_table');
        try {
            if ($fileTable === 'file') {
                $seller = User::query()->where('id', $sellerId)->first();
                $q = File::query()->onSellerConnection($seller)->where(['type' => FileTypeEnum::DESIGN, 'status' => FileStatusEnum::ACTIVE]);
            } else {
                $q = Design::query()->where(['type' => DesignTypeEnum::PRINT, 'status' => DesignStatusEnum::ACTIVE]);
            }
            $design = $q->where('id', $fileId)->firstOrFail();
            $design->status = FileStatusEnum::INACTIVE;
            $design->save();
            if ($design->product_id) {
                $campProduct = Product::query()->with('template:id,extra_print_cost,print_spaces')->where('id', $design->product_id)->first(['id', 'template_id']);
                if ($campProduct) {
                    $extra_print_cost = $campProduct->template->calcExtraPrintCost($design->designSiblings(Product::class));
                    Product::query()->where('id', $design->product_id)->update(['extra_print_cost' => $extra_print_cost]);
                }
            }
            if ($design->order_id && $design->order_id > 0) {
                $user = currentUser();
                OrderChangeDesign::dispatch($design->order_id, ($user->getName() ?? $user->getEmail()) . ' deleted design, ID: ' . $fileId);
            }
            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateTrackingProductsByFiles(Request $request): JsonResponse
    {
        try {
            $updateTrackingImport = new UpdateTrackingImport(currentUser()->getUserId(), currentUser()->getInfo()->name);
            Excel::import($updateTrackingImport, $request->file('file'));
            if ($updateTrackingImport->failCount > 0) {
                logToDiscord(
                    "```" . "\r\n" .
                    "Function: updateTrackingProductsByFiles " . "\r\n" .
                    "Supplier: " . currentUser()->getName() . "\r\n" .
                    "Error: " . "\r\n" . implode($updateTrackingImport->message) . "\r\n" .
                    "```" . "\r\n",
                    'supplier_log'
                );
            }
            return $this->successResponse([
                'success_count' => $updateTrackingImport->successCount,
                'fail_count' => $updateTrackingImport->failCount,
            ]);
        } catch (Throwable $e) {
            logToDiscord(
                __FUNCTION__
                . "\nException:" . $e->getMessage()
                . "\nUser Id:" . currentUser()->getUserId()
                , DiscordChannel::FULFILL_ORDER
                , true
            );
        }
        return $this->errorResponse();
    }
}
