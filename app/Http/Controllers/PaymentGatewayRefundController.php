<?php

namespace App\Http\Controllers;

use App\Enums\DateRangeEnum;
use App\Enums\PaymentGatewayRefundStatusEnums;
use App\Enums\PaymentMethodEnum;
use App\Http\Controllers\Storefront\PaypalController;
use App\Http\Controllers\Storefront\StripeController;
use App\Http\Controllers\Storefront\TazapayController;
use App\Models\PaymentGatewayRefund;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PaymentGatewayRefundController extends Controller
{
    use ApiResponse;

    public function index(Request $request)
    {
        $currentUser = currentUser();

        $status = $request->input('status');
        $time = $request->input('time');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $gatewayType = $request->input('gateway_type');
        $keyword = $request->input('q');
        $perPage = $request->get('per_page', 15);

        $query = PaymentGatewayRefund::query();

        $selectFields = [
            'payment_gateway_refund.id',
            'payment_gateway_refund.order_id',
            'payment_gateway_refund.payment_gateway_id',
            'payment_gateway_refund.store_id',
            'payment_gateway_refund.refund_amount',
            'payment_gateway_refund.reason',
            'payment_gateway_refund.staff_id',
            'payment_gateway_refund.status',
            'payment_gateway_refund.created_at',
            'payment_gateway_refund.seller_id',
            'payment_gateway_refund.log',
        ];

        $query->select($selectFields);

        if (!empty($keyword)) {
            $query->join('order', 'order.id', '=', 'payment_gateway_refund.order_id')
                ->join('user', 'user.id', '=', 'payment_gateway_refund.seller_id')
                ->where('order.customer_name', 'like', '%' . $keyword . '%')
                ->orWhere('order.customer_email', 'like', '%' . $keyword . '%')
                ->orWhere('payment_gateway_refund.order_id', 'like', '%' . $keyword . '%')
                ->orWhere('order.order_number', 'like', '%' . $keyword . '%');
        }
        if (!empty($status)) {
            $query->where('payment_gateway_refund.status', $status);
        }

        if (!empty($time)) {
            $query->filterDateRange($time, $startDate, $endDate);
        }

        if (!empty($gatewayType)) {
            $query->join('payment_gateways', 'payment_gateways.id', '=', 'payment_gateway_refund.payment_gateway_id');

            switch (strtolower($gatewayType)) {
                case 'senprints':
                    $query->whereNull('payment_gateways.seller_id');
                    break;
                case 'custom':
                    $query->whereNotNull('payment_gateways.seller_id');
                    break;
                case 'paypal':
                    $query->where('payment_gateways.gateway', PaymentMethodEnum::PAYPAL);
                    break;
                case 'stripe':
                    $query->where('payment_gateways.gateway', PaymentMethodEnum::STRIPE);
                    break;
            }
        }

        if ($currentUser->isAdmin()) {
            $query->with([
                'seller:id,name,email',
                'staff:id,name',
            ]);
        } else {
            $query->where('payment_gateway_refund.seller_id', $currentUser->getUserId());
        }

        return $query
            ->with([
                'store:id,name,sub_domain',
                'order:id,customer_name,customer_email,store_domain,order_number',
                'payment_gateway:id,name,account_id,seller_id',
            ])
            ->orderBy('payment_gateway_refund.created_at', 'desc')
            ->paginate($perPage);
    }

    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $currentUser = currentUser();
        $ids = $request->input('ids');
        $status = $request->input('status');
        $availableStatus = [PaymentGatewayRefundStatusEnums::PROCESSING, PaymentGatewayRefundStatusEnums::CANCELLED];

        if (!in_array($status, $availableStatus, true)) {
            return $this->errorResponse('Invalid status');
        }
        if (empty($ids)) {
            return $this->errorResponse('Invalid ids');
        }

        try {
            DB::beginTransaction();

            $refund_requests = PaymentGatewayRefund::query()
                ->with(['payment_gateway'])
                ->whereIn('status', [
                    PaymentGatewayRefundStatusEnums::PENDING,
                    PaymentGatewayRefundStatusEnums::PROCESSING
                ])
                ->whereIn('id', $ids)
                ->when(!$currentUser->isAdmin(), function ($query) use ($currentUser) {
                    $query->where('seller_id', $currentUser->getUserId());
                })->get();
            $updated = [];
            $refund_requests->each(function (PaymentGatewayRefund $refund) use ($status, &$updated) {
                if (empty($refund->payment_gateway->seller_id) || $status === PaymentGatewayRefundStatusEnums::CANCELLED) {
                    $refund->update([
                        'status' => $status
                    ]);
                    $updated[] = $refund->id;
                }
            });
            DB::commit();
            if (count($updated) === 0) {
                return $this->errorResponse('You can not update refund status.');
            }
            return $this->successResponse([
                'count_id' => count($ids),
                'count_updated' => count($updated),
                'count_not_update' => $refund_requests->count() - count($updated),
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Bulk update refund status failed');
        }
    }

    public function refundReport(Request $request): array
    {
        $gatewayType = $request->input('gateway_type');
        $time = $request->input('time', DateRangeEnum::THIS_YEAR);
        $startDate = $request->input('from_date');
        $endDate = $request->input('to_date');
        $perPage = $request->input('per_page', 15);

        $query = PaymentGatewayRefund::query()
            ->selectRaw('distinct(reason)')
            ->selectRaw('count(order_id) as orders')
            ->selectRaw('sum(refund_amount) as amount');

        if (!empty($gatewayType)) {
            $query->join('payment_gateways', 'payment_gateways.id', '=', 'payment_gateway_refund.payment_gateway_id');
            switch (strtolower($gatewayType)) {
                case 'senprints':
                    $query->whereNull('payment_gateways.seller_id');
                    break;
                case 'custom':
                    $query->whereNotNull('payment_gateways.seller_id');
                    break;
                case 'paypal':
                    $query->where('payment_gateways.gateway', PaymentMethodEnum::PAYPAL);
                    break;
                case 'stripe':
                    $query->where('payment_gateways.gateway', PaymentMethodEnum::STRIPE);
                    break;
            }
        }

        $result = $query
            ->filterDateRange($time, $startDate, $endDate)
            ->groupBy('reason')
            ->orderByDesc('amount')
            ->paginate($perPage);

        $refunds = $result->items();
        $countAllOrders = 0;
        $totalAmount = 0;

        foreach ($refunds as $item) {
            $countAllOrders += $item['orders'];
            $totalAmount += $item['amount'];
        }

        return array_merge($result->toArray(), [
            'count_all_orders' => $countAllOrders,
            'total_amount' => $totalAmount
        ]);
    }

    public function refundNow($id)
    {
        $refund = PaymentGatewayRefund::query()->where([
            'id' => $id,
        ])->whereIn('status', [
            PaymentGatewayRefundStatusEnums::PENDING,
            PaymentGatewayRefundStatusEnums::PROCESSING,
        ])->first();
        if(!empty($refund)) {
            if(empty($refund->order)) {
                return $this->errorResponse('Order not found');
            }
            if (!empty($refund->payment_gateway->seller_id)) {
                return $this->errorResponse('You can not refund this order.');
            }
            $order = $refund->order;
            if($order->payment_method === PaymentMethodEnum::PAYPAL || Str::contains($order->payment_method, PaymentMethodEnum::PAYPAL)) {
                $refundResponse = (new PaypalController())->refundOrderToGateway($order, $refund->refund_amount, $refund->is_full_refund);
                if(!empty($refundResponse) && $refundResponse['success']) {
                    $refund->update([
                        'status' => PaymentGatewayRefundStatusEnums::COMPLETED
                    ]);
                    return $this->successResponse(null, 'Refund success');
                }
                $refund->status = PaymentGatewayRefundStatusEnums::ERROR;
                if(!empty($refundResponse) && $refundResponse['message']) {
                    $refund->update([
                        'status' => PaymentGatewayRefundStatusEnums::ERROR,
                        'log' => "Error refund #$id: {$refundResponse['message']}"
                    ]);
                    graylogError($refundResponse['message'], ['category' => 'process_refund']);
                    return $this->errorResponse($refundResponse['message']);
                }
                $refund->update([
                    'status' => PaymentGatewayRefundStatusEnums::ERROR,
                    'log' => 'Empty response from PayPal API'
                ]);
                graylogError('Empty response from PayPal API', ['category' => 'process_refund']);
                return $this->errorResponse('Empty response from PayPal API');
            }
            if ($refund->order->payment_method === PaymentMethodEnum::STRIPE || Str::contains($order->payment_method, PaymentMethodEnum::STRIPE)) {
                $refundResponse = (new StripeController())->refundOrderToGateway($order, $refund->refund_amount, $refund->is_full_refund);
                if(!empty($refundResponse) && ($refundResponse['status'] === 'succeeded' || $refundResponse['status'] === 'pending')) {
                    $log = '';
                    if ($refundResponse['status'] === 'pending') {
                        $log = 'Refund is received, but not yet completed, please check the actual status on Stripe dashboard';
                    }
                    $refund->update([
                        'status' => PaymentGatewayRefundStatusEnums::COMPLETED,
                        'log' => $log
                    ]);
                    return $this->successResponse(null, 'Refund success');
                }
                if(!empty($refundResponse) && $refundResponse['reason']) {
                    $refund->update([
                        'status' => PaymentGatewayRefundStatusEnums::ERROR,
                        'log' => "Error refund #$id: {$refundResponse['reason']}"
                    ]);
                    graylogError($refundResponse['reason'], ['category' => 'process_refund']);
                    return $this->errorResponse($refundResponse['reason']);
                }
                $refund->update([
                    'status' => PaymentGatewayRefundStatusEnums::ERROR,
                    'log' => 'Empty response from Stripe API'
                ]);
                graylogError('Empty response from Stripe API', ['category' => 'process_refund']);
                return $this->errorResponse('Empty response from Stripe API');
            }
            if ($refund->order->payment_method === PaymentMethodEnum::TAZAPAY || Str::contains($order->payment_method, PaymentMethodEnum::TAZAPAY)) {
                $refundResponse = (new TazapayController())->initRefund($order->id, $refund->refund_amount, $refund->reason);
                if(!empty($refundResponse) && empty($refundResponse['errors'])) {
                    $refund->update([
                        'status' => PaymentGatewayRefundStatusEnums::COMPLETED
                    ]);
                    return $this->successResponse(null, 'Refund success');
                }
                if(!empty($refundResponse) && !empty($refundResponse['errors'])) {
                    $errors = json_encode($refundResponse['errors']);
                    $refund->update([
                        'status' => PaymentGatewayRefundStatusEnums::ERROR,
                        'log' => "Error refund #$id: {$errors}"
                    ]);
                    graylogError($errors, ['category' => 'process_refund']);
                    return $this->errorResponse($errors);
                }
                $refund->update([
                    'status' => PaymentGatewayRefundStatusEnums::ERROR,
                    'log' => 'Empty response from Tazapay API'
                ]);
                graylogError('Empty response from Tazapay API', ['category' => 'process_refund']);
                return $this->errorResponse('Empty response from Tazapay API');
            }
            $refund->update([
                'status' => PaymentGatewayRefundStatusEnums::ERROR,
                'log' => 'Unknown payment method'
            ]);
            return $this->errorResponse('Unknown payment method');
        }
    }
}
