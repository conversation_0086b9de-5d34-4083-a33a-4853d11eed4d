<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\FeedPlatformEnum;
use App\Enums\QueueName;
use App\Enums\SocialFeedTypeEnum;
use App\Enums\StorageDisksEnum;
use App\Http\Requests\Seller\SocialFeed\UpdateRequest;
use App\Imports\Seller\SocialFeed\ExcludeIdsImport;
use App\Imports\Seller\SocialFeed\RemoveExcludedIdsImport;
use App\Jobs\CreateExportFileSocialFeed;
use App\Models\Elastic;
use App\Models\Product;
use App\Models\SocialFeed;
use App\Traits\ApiResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use RuntimeException;
use Symfony\Component\HttpFoundation\File\Exception\ExtensionFileException;
use Throwable;

class SocialFeedController extends Controller
{
    use ApiResponse;

    public const LIMIT_PRODUCTS = 150000;
    public const LIMIT_MAX_PRODUCT = 1000000;

    public function mappingGoogleProductCategories(Request $request): JsonResponse
    {
        try {
            $productId  = $request->get('product_id');
            $categoryId = $request->get('category_id');

            Product::query()
                ->where('id', $productId)
                ->update([
                    'google_category_id' => $categoryId,
                ]);
            $cacheKeys   = [
                [
                    'tags' => [CacheKeys::SYSTEM_PRODUCT_TEMPLATES],
                    'key'  => CacheKeys::LIST_PRODUCT_TEMPLATES
                ]
            ];
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    public function indexSeller(Request $request, $id = null)
    {
        $user   = currentUser();
        $userId = $user->getUserId();
        $query  = SocialFeed::query()
            ->with('store:id,name,sub_domain,domain,domain_status')
            ->where('seller_id', $userId);

        if (empty($id)) {
            $perPage = $request->input('per_page', 15);

            if ($request->filled('q')) {
                $query->where('name', 'like', '%' . $request->input('q') . '%');
            }

            if ($request->has('store_id')) {
                $query->where('store_id', $request->input('store_id'));
            }

            if ($user->isSeller()) {
                $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());

                if (!empty($store_ids)) {
                    $query->whereIn('store_id', $store_ids);
                }
            }
            $result = $query->latest()->paginate($perPage);
            $social_feeds = $result->getCollection();
            $social_feeds->transform(function ($social_feed) {
                $social_feed->csv_base_download_url = SocialFeedTypeEnum::DAILY ? storageUrl() : cdnUrl();
                $social_feed->xml_base_download_url = SocialFeedTypeEnum::DAILY ? storageUrl() : cdnUrl();
                if ($social_feed->csv_file_size > 300) { // in MB
                    $social_feed->csv_base_download_url = storageUrl();
                }
                if ($social_feed->xml_file_size > 300) {
                    $social_feed->xml_base_download_url = storageUrl();
                }
                return $social_feed;
            });
            $result->setCollection($social_feeds);
            return $result;
        }

        $data = $query->find($id);

        // get
        $filters = $data->filters;

        // collections
        if (!empty($filters['collection_ids'])) {
            $request                = new Request(['ids' => $filters['collection_ids']]);
            $filters['collections'] = (new PromotionController())->ajaxSearch($request, 'collection', true, count($filters['collection_ids']));
        }

        // template products
        if (!empty($filters['template_product_ids'])) {
            $request                      = new Request(
                [
                    'is_feed' => true,
                    'ids'     => $filters['template_product_ids']
                ]
            );
            $filters['template_products'] = ProductController::getTemplate($request);
        }

        // exclude social feeds
        $filters['exclude_social_feeds'] = SocialFeed::query()
            ->select('id', 'name')
            ->whereIn('id', $filters['exclude_social_feed_ids'])
            ->latest()
            ->get();

        // sets
        $data->filters = $filters;

        return $this->successResponse($data);
    }

    public function indexOptionsSizesAndColors()
    {
        return cache()->remember('all_options_colors_sizes_campaigns', CacheTime::CACHE_1H, function () {
            return (new Elastic())->indexOptionsSizesAndColors(1000);
        });
    }

    public function getFilters($request): array
    {
        $filters                                 = [];
        $filters['store_id']                     = $request->get('store_id');
        $filters['collection_ids']               = $request->get('collection_ids');
        $filters['template_product_ids']         = $request->get('template_product_ids');
        $filters['default_product_only']         = $request->get('default_product_only');
        $filters['colors']                       = $request->get('colors');
        $filters['default_color_only']           = $request->boolean('default_color_only');
        $filters['other_colors_only']            = $request->boolean('other_colors_only');
        $filters['sizes']                        = $request->get('sizes');
        $filters['include_keywords']             = is_array($request->get('include_keywords')) ? array_map('strtolower', $request->get('include_keywords')) : strtolower($request->get('include_keywords'));
        $filters['start_date']                   = $request->get('start_date');
        $filters['end_date']                     = $request->get('end_date');
        $filters['keywords']                     = is_array($request->get('keywords')) ? array_map('strtolower', $request->get('keywords')) : strtolower($request->get('keywords'));
        $filters['exclude_social_feed_ids']      = $request->get('exclude_social_feed_ids');
        $filters['custom_label_with_collection'] = $request->get('custom_label_with_collection');
        $filters['custom_gender']                = $request->get('custom_gender');
        $filters['limit_records']                = $request->get('limit_records');
        $filters['id_only']                      = $request->boolean('id_only');
        $filters['include_mpn']                  = $request->boolean('include_mpn');
        $filters['use_image_custom_domain']      = $request->boolean('use_image_custom_domain');
        $filters['pre_download_image']           = $request->boolean('pre_download_image');
        $filters['seller_id']                    = currentUser()->getUserId();
        $filters['default_gender']               = $request->get('default_gender');
        $filters['include_price_currency']       = $request->boolean('include_price_currency');
        $filters['combine_title']                = $request->boolean('combine_title');
        $filters['combine_description']          = $request->boolean('combine_description');
        return $filters;
    }

    public function getCustomLabels($request): array
    {
        $labels                   = [];
        $labels['custom_label_0'] = $request->get('custom_label_0');
        $labels['custom_label_1'] = $request->get('custom_label_1');
        $labels['custom_label_2'] = $request->get('custom_label_2');
        $labels['custom_label_3'] = $request->get('custom_label_3');
        $labels['custom_label_4'] = $request->get('custom_label_4');
        $labels['shipping_label'] = $request->get('shipping_label');
        $labels['url_params'] = $request->get('url_params');

        return array_filter($labels);
    }

    public function updateSeller(UpdateRequest $request): JsonResponse
    {
        try {
            $user     = currentUser();
            $filters  = $this->getFilters($request);
            $labels   = $this->getCustomLabels($request);
            $sellerId = $user->getUserId();
            $id       = $request->get('id');
            $storeId  = $request->get('store_id');
            $name     = $request->get('name');
            $platform = $request->get('platform', FeedPlatformEnum::GOOGLE);
            $type = $request->boolean('type') ? SocialFeedTypeEnum::DAILY : null;
            if ($user->isSeller()) {
                $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
                if (!empty($store_ids) && !in_array((int)$storeId, $store_ids, true)) {
                    return $this->errorResponse('Error: You can not update the data of feed when store is not belong to you.');
                }
            }
            $total = (new Elastic())->getProductWithDefaultFeed($filters, true);

            if ($request->boolean('just_count')) {
                return $this->successResponse($total);
            }

            if (is_null($id)) {
                $feed = new SocialFeed();
            } else {
                $feed = SocialFeed::query()
                    ->where([
                        'id'        => $id,
                        'seller_id' => $sellerId,
                    ])
                    ->firstOrFail();
            }
            $feed->name           = $name;
            $feed->store_id       = $storeId;
            $feed->seller_id      = $sellerId;
            $feed->type      = $type;
            $feed->filters        = $filters;
            $feed->custom_labels  = !empty($labels) ? $labels : null;
            $feed->total_products = $total;
            $feed->path           = null;
            $feed->total_exported = 0;
            $feed->status = 0;
            $feed->platform = $platform;
            $feed->save();
            $large = false;
            if ($total > self::LIMIT_PRODUCTS) {
                $large = true;
            }
            if ($total > self::LIMIT_MAX_PRODUCT) {
                return $this->errorResponse('The number of products is too large, maximum is ' . self::LIMIT_MAX_PRODUCT . '. Please reduce the number of products.');
            }
            $this->pushToQueue($feed, $large);
            return $this->successResponse($total);
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    public function refreshSeller($id): JsonResponse
    {
        try {
            $feed = SocialFeed::query()
                ->where([
                    'id'        => $id,
                    'seller_id' => currentUser()->getUserId()
                ])
                ->firstOrFail();

            $total                = (new Elastic())->getProductWithDefaultFeed($feed->filters, true);
            $feed->total_products = $total;
            $feed->path           = null;
            $feed->total_exported = 0;
            $feed->status = 0;
            $feed->save();
            $large = false;
            if ($total > self::LIMIT_PRODUCTS) {
                $large = true;
            }
            if ($total > self::LIMIT_MAX_PRODUCT) {
                return $this->errorResponse('The number of products is too large, maximum is ' . self::LIMIT_MAX_PRODUCT . '. Please reduce the number of products.');
            }
            $this->pushToQueue($feed, $large);
            return $this->successResponse($total);
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    private function pushToQueue($feed, $large = false): void
    {
        $jobId = 'export_feed_job' . $feed->id;
        if (cache()->has($jobId)) {
            logSocialFeed('Job exists', [
                'id'       => $feed->id,
                'name'     => $feed->name,
                'seller'   => $feed->seller_id,
            ]);
            return;
        }

        logSocialFeed('Push to queue', [
            'id'       => $feed->id,
            'name'     => $feed->name,
            'seller'   => $feed->seller_id,
            'total'    => $feed->total_products,
        ]);
        $queueName = QueueName::SOCIAL_FEED;
        if ($large) {
            $queueName = QueueName::LARGE_SOCIAL_FEED;
        }
        CreateExportFileSocialFeed::dispatch($feed->id, $queueName);
    }

    public function deleteSeller($id): JsonResponse
    {
        try {
            $feed = SocialFeed::query()
                ->where([
                    'id'        => $id,
                    'seller_id' => currentUser()->getUserId()
                ])
                ->firstOrFail();

            $linkCSV = $feed->path . '.csv';
            $linkXML = $feed->path . '.xml';
            $feed->delete();
            Storage::disk(StorageDisksEnum::DEFAULT)->delete([$linkCSV, $linkXML]);
            if (StorageDisksEnum::DEFAULT !== StorageDisksEnum::S3) {
                Storage::disk(StorageDisksEnum::S3)->delete([$linkCSV, $linkXML]);
            }
            clearHtmlCacheByTag('feed', $id);

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    public function restoreSeller(Request $request, $id): JsonResponse
    {
        try {
            $feed = SocialFeed::query()
                ->where([
                    'id'        => $id,
                    'seller_id' => currentUser()->getUserId()
                ])
                ->withTrashed()
                ->firstOrFail();

            $feed->restore();
            $feed = $feed->refresh();
            $large = false;
            if ($feed->total_products > self::LIMIT_PRODUCTS) {
                $large = true;
            }
            if ($feed->total_products > self::LIMIT_MAX_PRODUCT) {
                return $this->errorResponse('The number of products is too large, maximum is ' . self::LIMIT_MAX_PRODUCT . '. Please reduce the number of products.');
            }
            $this->pushToQueue($feed, $large);
            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    public function updateStatusSeller(Request $request, $id): JsonResponse
    {
        try {
            $feed = SocialFeed::query()
                ->where([
                    'id'        => $id,
                    'seller_id' => currentUser()->getUserId()
                ])
                ->firstOrFail();

            $feed->status = $request->boolean('status');
            $feed->save();

            clearHtmlCacheByTag('feed', $id);

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    public function duplicate($id): JsonResponse
    {
        try {
            $feed = SocialFeed::query()
                ->when(currentUser()->isSeller(), function ($query) {
                    $query->where('seller_id', currentUser()->getUserId());
                })
                ->findOrFail($id);

            $feed = $feed->replicate();
            $feed->save();
            $feed->refresh();

            return $this->successResponse($feed->id);
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse();
        }
    }

    public function importExcludeIds(Request $request): JsonResponse
    {
        try {
            Excel::import(new ExcludeIdsImport(currentUser()->getUserId()), $request->input_file);

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse($e->getMessage());
        }
    }

    public function importRemoveExcludedIds(Request $request): JsonResponse
    {
        try {
            Excel::import(new RemoveExcludedIdsImport(currentUser()->getUserId()), $request->input_file);

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed');
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getFile($params)
    {
        $feed = null;
        try {
            // params: 3_116319b9445bc740.43920843_1.csv
            $type = Str::afterLast($params, '.');
            if (!in_array($type, ['xml', 'csv'])) {
                throw new ExtensionFileException("Unsupported format: {$type}");
            }

            $fileName = Str::beforeLast($params, '.');
            $arr      = explode('_', $fileName);

            [$storeId, $id] = $arr;
            $index = Arr::get($arr, 2);

            $feed = SocialFeed::query()
                ->select([
                    'id',
                    'path',
                ])
                ->where([
                    'id'       => $id,
                    'store_id' => $storeId
                ])
                ->whereStatus(1)
                ->firstOrFail();

            $paths = explode('|', $feed->path);

            if (empty($paths)) {
                throw new RuntimeException("Empty paths");
            }

            foreach ($paths as $path) {
                if (isset($index)) {
                    $key = Str::afterLast($path, '/');
                    if ($key === $index) {
                        break;
                    }
                }
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $path .= '.' . $type;
            $body = Http::get(storageUrl($path));

            if ($type === 'csv') {
                $fileName = 'social_feed_' . $id . '.csv';

                return response($body, 200, [
                    'Content-Type'        => 'text/csv',
                    'Content-Disposition' => "attachment; filename=$fileName",
                    'Cache-Tags'          => 'feed=' . $id,
                    'Cache-Expire-Time'   => CacheTime::CACHE_30D
                ]);
            }

            return response($body, 200, [
                'Content-Type'      => 'application/xml',
                'Cache-Tags'        => 'feed=' . $id,
                'Cache-Expire-Time' => CacheTime::CACHE_30D
            ]);
        } catch (ExtensionFileException|ModelNotFoundException $e) {
            return $this->errorResponse($e->getMessage());
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'social_feed', true);
            return $this->errorResponse($e->getMessage());
        }
    }
}
