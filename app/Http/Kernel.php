<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON><PERSON> extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        // \App\Http\Middleware\TrustProxies::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\LogRequestTime::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $middlewareAliases = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'staff_can' => \App\Http\Middleware\CheckStaffPermission::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'auth.custom' => \App\Http\Middleware\AuthCheckGuard::class,
        'auth.access.token' => \App\Http\Middleware\CheckAuthAccess::class,
        'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,
        'role_or_permission' => \Spatie\Permission\Middlewares\RoleOrPermissionMiddleware::class,
        'access_switch_account' => \App\Http\Middleware\CheckAccessRole::class,
        'access.external.api' => \App\Http\Middleware\AccessExternalApi::class,
        'access.pb.api' => \App\Http\Middleware\AccessPBApi::class,
        'auth.ensure_admin' => \App\Http\Middleware\EnsureUserIsAdmin::class,
        'sync' => \App\Http\Middleware\SyncMiddleware::class,
        'user_has_tag' => \App\Http\Middleware\UserHasTagMiddleware::class,
        'seller_has_store' => \App\Http\Middleware\EnsureStoreBelongsToUser::class,
        'staff_log' => \App\Http\Middleware\StaffLog::class,
        'seller_log' => \App\Http\Middleware\SellerLog::class,
        'supplier_log' => \App\Http\Middleware\SupplierLog::class,
        'auth.discord.bot' => \App\Http\Middleware\DiscordBotAuth::class,
        'skip.validate.tld' => \App\Http\Middleware\SetSkipValidateDomainTLD::class,
        'campaign.action' => \App\Http\Middleware\CampaignActionPermission::class,
        'csrf.token' => \App\Http\Middleware\VerifyCsrfToken::class,
        'log_request_time' => \App\Http\Middleware\LogRequestTime::class,
        'cors' => \Illuminate\Http\Middleware\HandleCors::class,
        'check.is_senhub' => \App\Http\Middleware\CheckIsSenhub::class,
    ];
}
