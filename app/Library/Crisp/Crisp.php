<?php

namespace App\Library\Crisp;

use App\Models\Order;
use App\Services\FreshDesk;
use Crisp\CrispClient;
use <PERSON>risp\CrispException;
use GuzzleHttp\Client as GuzzleClient;
use Http\Adapter\Guzzle7\Client as GuzzleAdapter;

class Crisp
{
    // Should we create a backup ticket in FreshDesk?
    private const CREATE_BACKUP_TICKET = false;

    /**
     *
     * @var CrispClient
     */
    public $crispClient;

    /**
     *
     * @var string
     */
    public $websiteId;
    public $name, $email, $subject, $content, $cc, $segments, $data, $ip, $links, $sessionId;
    public $domain;
    public $storeName;
    public $storeId;
    public $sellerEmail;
    public $orderNumber;
    public $orderStatusUrl;
    public $isCustomStore = false;

    /**
     *
     * @param $websiteId
     * @param $identifier
     * @param $key
     */
    public function __construct($websiteId, $identifier, $key)
    {
        $guzzleClient = new GuzzleClient([
            'timeout' => 15,
            'proxy' => null
        ]);
        $client = new GuzzleAdapter($guzzleClient);
        $this->websiteId = $websiteId;
        $this->crispClient = new CrispClient($client);
        $this->crispClient->setTier("plugin");
        $this->crispClient->authenticate($identifier, $key);
    }

    public function startConversation(): array
    {
        $this->initSession();
        if ($this->sessionId) {
            $this->updateConversationMeta();
            $addCCResult = $this->addCcEmail();
            $messageResult = $this->sendMessage();
            $this->sendLinks();
            return ["sessionId" => $this->sessionId, "sendResult" => $messageResult, "addCCResult" => $addCCResult];
        }
        return [];
    }

    /**
     * @return void
     */
    public function initSession(): void
    {
        try {
            $websiteConversation = $this->crispClient->websiteConversations->create($this->websiteId);
            $this->sessionId = $websiteConversation['session_id'];
            $this->crispClient->websiteConversations->initiateOne($this->websiteId, $this->sessionId);
        } catch (\Throwable $e) {
        }
    }

    public function sendMessage()
    {
        $message = [
            "type" => "text",
            "from" => "user",
            "origin" => "chat",
            "content" => self::convertBinaryToString($this->content),
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    public function sendAutoResponse()
    {
        $message = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => [
                "type" => "website",
                "nickname" => "Auto-reply",
                "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
            ],
            "content" => self::convertBinaryToString($this->getAutoResponseMessage()),
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    public function sendNote()
    {
        $isCustomStore = "no";

        if ($this->isCustomStore) {
            $isCustomStore = "yes";
        }

        $note = <<<EOF
        Seller Email: {$this->sellerEmail}
        Order Number: {$this->orderNumber}
        Store Domain: {$this->domain}
        Store Name: {$this->storeName}
        Store ID: {$this->storeId}
        Is Custom Store: {$isCustomStore}
        EOF;

        $message = [
            "type" => "note",
            "from" => "operator",
            "origin" => "chat",
            "user" => [
                "type" => "website",
                "nickname" => "Auto-reply",
                "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
            ],
            "content" => $note,
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    public function sendCustomNote(string $sessionId, string $content)
    {
        $message = [
            "type" => "note",
            "from" => "operator",
            "origin" => "chat",
            "user" => [
                "type" => "website",
                "nickname" => "Auto-reply",
                "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
            ],
            "content" => $content,
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $sessionId, $message);
    }

    public function getSegments(string $sessionId): array
    {
        try {
            $result = $this->crispClient
                ->websiteConversations
                ->getMeta($this->websiteId, $sessionId);

            return data_get($result, 'segments', []);
        } catch (CrispException $e) {
            logException($e);
            return [];
        }
    }

    public function updateSegment(string $sessionId, array $segment): void
    {
        try {
            $this->crispClient
                ->websiteConversations
                ->updateMeta($this->websiteId, $sessionId, [
                    'segments' => $segment
                ]);
        } catch (CrispException $e) {
            logException($e);
        }
    }

    public function addCCEmail(): array
    {
        $addCCResults = [];
        if ($this->cc) {
            $addCCResults[] = $this->updateParticipants($this->cc);
            sleep(1);
            $addCCResults[] = $this->updateParticipants($this->cc);
        }
        return $addCCResults;
    }

    public function updateParticipants($email): ?array
    {
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $client = $this->crispClient->_rest;
        } else {
            $client = $this->crispClient;
        }
        $result = $client->put(
            "website/$this->websiteId/conversation/{$this->sessionId}/participants",
            json_encode([
                "participants" => [[
                    "type" => "email",
                    "target" => $email
                ]]])
        );
        return json_decode($result->getBody(), true);
    }

    public function sendTranscriptConversation($to = null, $sessionId = null): ?array
    {
        if ($to) {
            $this->cc = $to;
        }
        if ($sessionId) {
            $this->sessionId = $sessionId;
        }
        if (empty($this->cc) || empty($this->sessionId)) {
            return [];
        }
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $client = $this->crispClient->_rest;
        } else {
            $client = $this->crispClient;
        }
        $result = $client->post("website/$this->websiteId/conversation/{$this->sessionId}/transcript", json_encode([
            "to" => "operator",
            "email" => $this->cc
        ]));
        return json_decode($result->getBody(), true);
    }

    public function sendLinks(): void
    {
        if (!is_array($this->links) || empty($this->links)) {
            return;
        }

        foreach ($this->links as $link) {
            $imageName = pathinfo($link, PATHINFO_BASENAME);
            $this->sendLink($link, $imageName);
        }
    }

    /**
     * @param $imagePath
     * @return bool|mixed
     */
    public function getImageMimeType($imagePath)
    {
        $ext = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        $mimeList = json_decode(file_get_contents(__DIR__ . DIRECTORY_SEPARATOR . "mime.json"));

        return $mimeList->$ext ?? "application/octet-stream";
    }

    public function sendLink($url, $name)
    {
        $type = $this->getImageMimeType($url);
        $message = [
            "type" => "file",
            "from" => "user",
            "origin" => "urn:backend-api",
            "content" => [
                "url" => $url,
                "type" => $type,
                "name" => $name
            ]
        ];

        return $this->crispClient
            ->websiteConversations
            ->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    public function updateConversationMeta()
    {
        $params = [
            "nickname" => self::convertBinaryToString($this->name),
            "email" => $this->email,
            "segments" => $this->segments,
            "data" => $this->data,
            "ip" => $_SERVER["HTTP_CF_CONNECTING_IP"] ?? "0.0.0.0",
            "subject" => self::convertBinaryToString($this->subject)
        ];

        return $this->crispClient
            ->websiteConversations
            ->updateMeta($this->websiteId, $this->sessionId, $params);
    }

    private static function convertBinaryToString($binary)
    {
        return json_decode(json_encode(mb_convert_encoding($binary, 'UTF-8', 'UTF-8')));
    }

    private static function createSegment($subject): array
    {
        $subject = self::convertBinaryToString($subject);

        $segments = ["contact_form"];
        if ($subject) {
            $newSegment = strtolower($subject);
            $newSegment = str_replace([" ", "?", "/"], ["_", "", "_"], $newSegment);
            $newSegment = trim($newSegment);
            $segments[] = $newSegment;
        }
        return $segments;
    }

    public function sendContactForm(array $data): array
    {
        try {
            // Validate store information based on order number or customer email
            $data = $this->validateAndCorrectStoreInfo($data);
        } catch (\Exception $e) {
            logException($e);
        }

        $this->email = $data['customer_email'];
        $this->name = $data['customer_name'] ?: $this->email;
        $this->content .= $data['message'];
        $this->segments = self::createSegment($data['subject']);

        if (!empty($data['segments'])) {
            $this->segments = array_merge($this->segments, $data['segments']);
        }

        $prefixSubject = $data['subject'] ?: "Contact Form";
        $this->subject = $prefixSubject . " - " . substr($this->content, 0, 64);

        $this->subject = str_replace(PHP_EOL, '', $this->subject);

        if (strlen($this->content) > 64) {
            $this->subject .= "...";
        }

        $this->domain = $data['store_domain'];

        if (isset($data['attached_files']) && is_array($data['attached_files'])) {
            $this->links = array_filter($data['attached_files']);
        }

        $this->cc = $data['cc'];
        $this->storeName = $data['store_name'];
        $this->storeId = $data['store_id'];
        $this->sellerEmail = $data['seller_email'];
        $this->orderNumber = $data['order_number'];
        $this->orderStatusUrl = !empty($data['order_status_url']) ? $data['order_status_url'] : $this->createLink('/order/track');
        $this->isCustomStore = (int)$data['is_custom_store'] === 1;

        $this->content = <<<EOF
        **{$this->subject}**

        {$this->content}

        ===============================
        Name: {$this->name}
        Email: {$this->email}
        Order Number: {$data['order_number']}
        Store Domain: {$data['store_domain']}
        EOF;

        $this->data = array_filter([
            "order_id" => $data['order_number'],
            "seller_id" => $data['seller_id'],
            "domain" => $data['store_domain'],
            "store_id" => $data['store_id'],
            "custom_store" => $data['is_custom_store'],
            "seller_email" => $data['seller_email']
        ]);

        if (self::CREATE_BACKUP_TICKET) {
            // disable cc_emails because Crisp already has cc_emails
            FreshDesk::createTicket([
                "description" => $this->content,
                "subject" => $this->subject,
                "email" => $this->email,
                "priority" => 1,
                "status" => 2,
                "name" => $this->name,
                "tags" => $this->segments,
            ]);
        }

        return $this->startConversation();
    }

    /**
     * Validates and corrects store information based on order number or customer email
     * If the customer or order doesn't belong to the specified store/seller,
     * update the information to the correct store/seller
     *
     * @param array $data
     * @return array
     */
    private function validateAndCorrectStoreInfo(array $data): array
    {
        // Skip validation if order number or customer email is not provided
        if (empty($data['order_number']) && empty($data['customer_email'])) {
            return $data;
        }

        try {
            $orderInfo = null;

            // First try to validate by order number if available
            if (!empty($data['order_number'])) {
                $orderInfo = Order::query()->where('order_number', $data['order_number'])->first();
            }

            // If order not found by order number, try by customer email
            if (!$orderInfo && !empty($data['customer_email'])) {
                $orderInfo = Order::query()->where('customer_email', $data['customer_email'])
                    ->when(!empty($data['store_domain']), function ($query) use ($data) {
                        return $query->where('store_domain', $data['store_domain']);
                    })
                    ->orderBy('created_at', 'desc')
                    ->first();
            }

            // If order found and store info doesn't match, update store data
            if ($orderInfo && (int)$orderInfo->store_id !== (int)$data['store_id']) {
                // Get correct store information using relationship
                $storeInfo = $orderInfo->store;

                if ($storeInfo) {
                    // Get seller information for the correct store using relationship
                    $sellerInfo = $storeInfo->user;

                    if ($sellerInfo) {
                        // Update data with the correct store and seller information
                        $data['store_id'] = $storeInfo->id;
                        $data['store_domain'] = $storeInfo->domain;
                        $data['store_name'] = $storeInfo->name;
                        $data['is_custom_store'] = $storeInfo->is_custom_store;
                        $data['seller_id'] = $sellerInfo->id;
                        $data['seller_email'] = $sellerInfo->email;
                        $data['cc'] = $storeInfo->cc_email ?: $sellerInfo->email;

                        // If order status URL was provided, update it with the correct domain
                        if (!empty($data['order_status_url'])) {
                            $path = parse_url($data['order_status_url'], PHP_URL_PATH);
                            $data['order_status_url'] = "https://{$storeInfo->domain}{$path}";
                        }

                        logToDiscord('Crisp correction: ' . json_encode([
                            'customer_email' => $data['customer_email'],
                            'order_number' => $data['order_number'],
                            'original_store_domain' => $data['store_domain'],
                            'corrected_store_domain' => $storeInfo->domain,
                            'original_seller_email' => $data['seller_email'],
                            'corrected_seller_email' => $sellerInfo->email
                        ], JSON_THROW_ON_ERROR));
                    }
                }
            }
        } catch (\Exception $e) {
            logException($e);
        }

        return $data;
    }

    public function createLink($path): string
    {
        return "https://{$this->domain}$path";
    }

    /**
     * @return string
     */
    public function getAutoResponseMessage()
    {
        $message = <<<EOF
        Hi {$this->name},

        Thanks so much for reaching out!
        You can track your order status via this link below:
        [$this->orderStatusUrl]({$this->orderStatusUrl})

        Please read the articles below if you need any additional information.

        [Return Policy]({$this->createLink("/page/return-policy")})
        [Shipping Policy]({$this->createLink("/page/shipping-policy")})
        [Terms & Conditions]({$this->createLink("/page/terms-of-service")})
        [Privacy Policy]({$this->createLink("/page/privacy")})
        [DMCA]({$this->createLink("/page/dmca")})

        If you have any additional information that you think will help us to assist you, please feel free to reply to this email. We look forward to chatting soon, our response within 24-48 business hours.

        Regards,

        The {$this->storeName} Team
        EOF;
        return $message;
    }

    public function getCC()
    {
        return $this->cc;
    }

    public function getSessionId()
    {
        return $this->sessionId;
    }
}
