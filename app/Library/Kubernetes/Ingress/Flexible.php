<?php

namespace App\Library\Kubernetes\Ingress;

use App\Enums\EnvironmentEnum;
use RenokiCo\LaravelK8s\LaravelK8sFacade;

class Flexible {

    protected $domain = null;

    public function __construct($domain) {
        $this->domain = $domain;
    }

    /**
     * <PERSON>le add self config for domain use ssl cloudflare
     * @return bool
     */
    public function handle(): bool {
        if (is_null($this->domain)) {
            return false;
        }
        $ingress = LaravelK8sFacade::getCluster()->ingress();
        $name = str_replace('.', '-', $this->domain);
        $namespace = nameSpaceK8sByAppEnv();
        if (is_null($namespace)) {
            return false;
        }
        $backendServiceName = 'frontend-storefront-service';
        if (app()->environment(EnvironmentEnum::DEVELOPMENT)) {
            $backendServiceName = 'frontend-storefront-service-build';
        }
        try {
            $ingress->setName("frontend-storefront-{$name}-ingress")
                    ->setNamespace($namespace)
                    ->setAnnotations([
                        'kubernetes.io/ingress.class' => 'nginx',
//                        'nginx.ingress.kubernetes.io/configuration-snippet' => 'if ($http_x_forwarded_proto = "http") { return 301 https://$host$request_uri; }'
                    ])
                    ->setTls([[
                    'hosts' => [$this->domain]
                ]])
                    ->setRules([[
                    'host' => $this->domain,
                    'http' => [
                        'paths' => [[
                        'path' => '/',
                        'pathType' => 'Prefix',
                        'backend' => [
                            'service' => [
                                'name' => $backendServiceName,
                                'port' => [
                                    'number' => 80
                                ]
                            ]
                        ],
                            ]],
                    ],
                ]])->create();
            return true;
        } catch (\Exception $e) {
            logToDiscord("Create ingress for domain: {$this->domain} error! \n SSL Encryption Mode: Flexible - Message: {$e->getMessage()} - Line: {$e->getLine()} - File: {$e->getFile()}");
            return false;
        }
    }

}
