<?php
namespace App\Library\Kubernetes\Ingress;

use App\Enums\EnvironmentEnum;
use RenokiCo\LaravelK8s\LaravelK8sFacade;

class Host
{
    protected $domain = null;
    public function __construct($domain)
    {
        $this->domain = $domain;
    }

    /**
     * Handle ssl for domain with letencrypt
     * @return bool
     */
    public function handle(): bool
    {
        if (is_null($this->domain)) {
            return false;
        }
        $ingress = LaravelK8sFacade::getCluster()->ingress();
        $name = str_replace('.', '-', $this->domain);
        $namespace = nameSpaceK8sByAppEnv();
        if (is_null($namespace)) {
            return false;
        }
        $annotations = [
            'cert-manager.io/cluster-issuer' => 'letsencrypt-prod',
            'kubernetes.io/ingress.class' => 'nginx'
        ];
        $backendServiceName = 'frontend-storefront-service';
        if (app()->environment(EnvironmentEnum::DEVELOPMENT)) {
            $backendServiceName = 'frontend-storefront-service-build';
        }
        try {
            $ingress->setName("frontend-storefront-{$name}-ingress")
                ->setNamespace($namespace)
                ->setAnnotations($annotations)
                ->setTls([[
                    'hosts' => [$this->domain],
                    'secretName'=> "store-{$name}-cert"
                ]])
                ->setRules([[
                    'host' => $this->domain,
                    'http' => [
                        'paths' => [[
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => $backendServiceName,
                                    'port' => [
                                        'number' => 80
                                    ]
                                ]
                            ],
                        ]],
                    ],
                ]])->create();
            return true;
        } catch (\Exception $e) {
            logToDiscord("Create ingress for domain: {$this->domain} error! \n SSL Encryption Mode: Self Letsencrypt - Message: {$e->getMessage()} - Line: {$e->getLine()} - File: {$e->getFile()}");
            return false;
        }
    }
}
