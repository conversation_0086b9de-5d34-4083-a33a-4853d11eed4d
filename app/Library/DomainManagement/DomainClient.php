<?php

namespace App\Library\DomainManagement;

use App\Models\SystemConfig;
use Illuminate\Support\Facades\Http;

class DomainClient
{

    protected $config = null;

    protected static $instance = null;

    public function __construct()
    {
        $this->config = SystemConfig::getDomainManagementApi();
    }

    public static function instance(): ?DomainClient
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function register($domain): bool
    {
        if (is_null(parseDomain($domain)) || is_null($this->config))
        {
            logToDiscordNow("DomainClient: Error register a domain, can not get info api or domain name is wrong");
            return false;
        }
        try {
            if ($this->checkConfig()) {
                $this->config['api_url'] = sprintf("{$this->config['api_url']}/domain/register/%s", $domain);
                $response = Http::withoutVerifying()
                    ->withoutRedirecting()
                    ->withBasicAuth($this->config['user'], $this->config['password'])
                    ->get($this->config['api_url'])
                    ->json();
                if ($response && $response['success'] === true) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            logToDiscordNow("DomainClient: Error register a domain! - File: {$e->getFile()} - Message: {$e->getMessage()} - \n\r Trace info: {$e->getTraceAsString()}");
            return false;
        }
    }

    public function destroy($domain): bool
    {
        if (is_null(parseDomain($domain)) || is_null($this->config))
        {
            logToDiscordNow("DomainClient: Error delete a domain, can not get info api or domain name is wrong");
            return false;
        }
        try {
            if ($this->checkConfig()) {
                $this->config['api_url'] = sprintf("{$this->config['api_url']}/domain/delete/%s", $domain);
                $response = Http::withoutVerifying()
                    ->withoutRedirecting()
                    ->withBasicAuth($this->config['user'], $this->config['password'])
                    ->get($this->config['api_url'])
                    ->json();
                if ($response && $response['success'] === true) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            logToDiscordNow("DomainClient: Error delete a domain! - File: {$e->getFile()} - Message: {$e->getMessage()} - \n\r Trace info: {$e->getTraceAsString()}");
            return false;
        }
    }

    private function checkConfig(): bool
    {
        if (!isset($this->config['api_url'])) {
            logToDiscordNow("DomainClient: Error register a domain, can not get api url");
            return false;
        }
        if (!isset($this->config['user'])) {
            logToDiscordNow("DomainClient: Error register a domain, can not get api user");
            return false;
        }
        if (!isset($this->config['password'])) {
            logToDiscordNow("DomainClient: Error register a domain, can not get api password");
            return false;
        }
        return true;
    }
}
