<?php

namespace App\Library\Payoneer;

use App\Models\SystemConfig;
use Illuminate\Support\Facades\Http;

class Payoneer
{

    protected $isAuth = false;

    private static $config = [
        'program_id' => '',
        'client_id' => '',
        'client_secret' => '',
    ];

    private static $payoneerSessionPrefix = 'senprints-payoneer-sessionid';

    private static $payoneerOAuthUrl = 'https://login.sandbox.payoneer.com/api/v2/oauth2/token';
    private static $payoneerProgramUrl = 'https://api.sandbox.payoneer.com/v4/programs/%d';

    private $accessToken = null;

    protected static $instance = null;

    public const MASSPAYOUT_CLIENT_REF_ID_PREFIX = 'sp_trans_';

    public function __construct()
    {
        $payoneerConfig = config('senprints.payoneer');
        self::$config['program_id'] = $payoneerConfig['program_id'] ?? '';
        self::$config['client_id'] = $payoneerConfig['client_id'] ?? '';
        self::$config['client_secret'] = $payoneerConfig['client_secret'] ?? '';
        self::$config['redirect_url'] = $payoneerConfig['redirect_url'] ?? 'https://senprints.com';
        if (app()->isProduction()) {
            self::$payoneerOAuthUrl = 'https://login.payoneer.com/api/v2/oauth2/token'; // I'll be back
            self::$payoneerProgramUrl = 'https://api.payoneer.com/v4/programs/%d'; // I'll be back
        }
        self::$payoneerProgramUrl = sprintf(self::$payoneerProgramUrl, self::$config['program_id']);
    }

    public static function instance()
    {
        return is_null(self::$instance) ? new self() : self::$instance;
    }

    /**
     * Cancel a payout
     * @param $billId
     * @param $sellerId
     * @return array|null
     */
    public function cancelPayout($billId = null, $sellerId = null): ?array
    {
        if (is_null($billId) || is_null($sellerId)) {
            return null;
        }
        $payoutClientRefId = self::MASSPAYOUT_CLIENT_REF_ID_PREFIX . $billId . '_' . $sellerId;
        $isAccessToken = $this->getAccessToken();
        if (is_null($isAccessToken)) {
            return null;
        }
        try {
            $response = Http::withoutVerifying()
                ->withToken($this->accessToken)
                ->put(self::$payoneerProgramUrl . "/payouts/{$payoutClientRefId}/cancel");

            if ($response->failed()) {
                return null;
            }

            $status = $response->status();
            $response = $response->json();

            if (!is_null($response) && $status === 200) {
                return $response['result'] ?? null;
            }
        } catch (\Exception $e) {
            graylogError("Payoneer: Cancel Payout Error! - Message: {$e->getMessage()} - \n\r BillId: {$billId} \n\r SellerId: {$sellerId}", [
                'category' => 'process_payout_payoneer_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action'  => 'cancel'
            ]);
        }

        return null;
    }

    /**
     * Get a payout status with client_reference_id
     * @param $payoutClientRefId
     * @return array|null
     */
    public function getPayoutStatus($payoutClientRefId = null): ?array
    {
        if (is_null($payoutClientRefId)) {
            return null;
        }
        $isAccessToken = $this->getAccessToken();
        if (is_null($isAccessToken)) {
            return null;
        }
        try {
            $response = Http::withoutVerifying()
                ->withToken($this->accessToken)
                ->get(self::$payoneerProgramUrl . "/payouts/{$payoutClientRefId}/status");

            if ($response->failed()) {
                return null;
            }

            $status = $response->status();
            $response = $response->json();
            graylogInfo("ProcessPOPayout: Payout status", [
                'category' => 'process_payout_payoneer',
                'payoutClientRefId' => $payoutClientRefId,
                'payoutStatus' => json_encode($response),
            ]);
            if (!is_null($response) && $status === 200) {
                return $response['result'] ?? null;
            }
        } catch (\Exception $e) {
            graylogError("Payoneer: Get Payout Status Error! - Message: {$e->getMessage()} - \n\r payoutClientRefId: {$payoutClientRefId}", [
                'category' => 'process_payout_payoneer_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action'  => 'get'
            ]);
        }

        return null;
    }

    /**
     * Payoneer masspayout
     * @param array $paymentParams
     * @return bool|null
     */
    public function massPayout(array $paymentParams = []): ?bool
    {
        /**
         * Params Ex:
         * Array ({ "client_reference_id": "payment_1", "payee_id": "testa5d2f486", "description": "Test_1256g1", "currency": "USD", "amount": "2.0"},....{})
         */
        if (empty($paymentParams)) {
            return null;
        }

        $isAccessToken = $this->getAccessToken();
        if (is_null($isAccessToken)) {
            return null;
        }
        try {
            $response = Http::withoutVerifying()
                ->withToken($this->accessToken)
                ->withHeaders([
                    'Content-Type' => 'application/json'
                ])
                ->post(self::$payoneerProgramUrl . "/masspayouts", [
                    'Payments' => $paymentParams
                ]);

            if ($response->failed()) {
                return null;
            }

            $status = $response->status();
            $response = $response->json();
            graylogInfo("ProcessPOPayout: Payout Payout", [
                'category' => 'process_payout_payoneer',
                'paymentParams' => json_encode($paymentParams),
                'massPayout' => json_encode($response),
            ]);
            if (!is_null($response) && $status === 200 && isset($response['result'])) {
                return ($response['result'] === 'Payments Created');
            }
        } catch (\Exception $e) {
            $paymentParamsString = print_r($paymentParams, true);
            graylogError("Payoneer: Masspayout Error! - Message: {$e->getMessage()} - \n\r paymentParams: {$paymentParamsString}", [
                'category' => 'process_payout_payoneer_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action'  => 'request'
            ]);
        }

        return null;
    }

    /**
     * Get payee status
     * @param $payeeId
     * @return string|null
     */
    public function payeeStatus($payeeId = 0): ?string
    {
        if ($payeeId === 0) {
            return null;
        }

        $isAccessToken = $this->getAccessToken();

        if (is_null($isAccessToken)) {
            return null;
        }

        try {
            $response = Http::withoutVerifying()
                ->withToken($this->accessToken)
                ->get(self::$payoneerProgramUrl . "/payees/{$payeeId}/status");

            if ($response->failed()) {
                return null;
            }

            $status = $response->status();
            $response = $response->json();
            graylogInfo("ProcessPOPayout: Payout Payout", [
                'category' => 'process_payout_payoneer',
                'payeeId' => $payeeId,
                'payeeStatus' => json_encode($response),
            ]);
            if (!is_null($response) && $status === 200 && isset($response['result']['status'])) {
                return strtolower($response['result']['status']['description']);
            }
        } catch (\Exception $e) {
            graylogError("Payoneer: Get PayeeId Status Error! - Message: {$e->getMessage()} - \n\r payeeId: {$payeeId}", [
                'category' => 'process_payout_payoneer_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action'  => 'request'
            ]);
        }

        return null;
    }

    /**
     * Payoneer create registration link
     * @param $payeeId
     * @return string|null
     */
    public function createRegistrationLink($payeeId = 0): ?string
    {
        if ($payeeId === 0) {
            return null;
        }
        $isAccessToken = $this->getAccessToken();
        if (is_null($isAccessToken)) {
            return null;
        }

        try {
            $response = Http::withoutVerifying()
                ->withToken($this->accessToken)
                ->withHeaders([
                    'Content-Type' => 'application/json'
                ])
                ->post(self::$payoneerProgramUrl . '/payees/registration-link', [
                    'payee_id' => $payeeId,
                    'client_session_id' => self::$payoneerSessionPrefix . substr(md5($payeeId), 10, 10),
                    'redirect_url' => self::$config['redirect_url'],
                    'redirect_time' => 3,
                    'already_have_an_account' => false
                ]);

            if ($response->failed()) {
                return null;
            }

            $status = $response->status();
            $response = $response->json();

            if (!is_null($response) && $status === 200 && isset($response['result']['registration_link'])) {
                return $response['result']['registration_link'];
            }
        } catch (\Exception $e) {
            graylogError("Payoneer Create Registration Link Error! - Message: {$e->getMessage()} - \n\r payeeId: {$payeeId}", [
                'category' => 'process_payout_payoneer_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action'  => 'request'
            ]);
        }

        return null;
    }

    /**
     * Get access token from Payoneer OAuth
     * @return string|null
     */
    private function getAccessToken(): ?string
    {
        if (is_null($this->accessToken) && !$this->auth()) {
            return null;
        }

        return $this->accessToken;
    }

    /**
     * Payoneer OAuth API
     * @return bool
     */
    protected function auth(): bool
    {
        $systemConfigModel = SystemConfig::query();
        try {
            $status = 0;
            $systemConfigModel->where([
                'key' => 'payoneer_application_token',
                'status' => 1
            ]);
            $isRecreate = true;
            $systemConfig = $systemConfigModel->first();
            if (!is_null($systemConfig)) {
                $payoneerJsonData = json_decode($systemConfig->json_data, true);
                $payoneerTokenExpired = $payoneerJsonData['expired'];
                $this->accessToken = $systemConfig->value;
                if ($payoneerTokenExpired > now()->timestamp) {
                    $isRecreate = false;
                }
            }
            if (!$isRecreate) {
                return true;
            }
            $response = Http::withoutVerifying()
                ->withBasicAuth(self::$config['client_id'], self::$config['client_secret'])
                ->post(self::$payoneerOAuthUrl, [
                    'grant_type' => 'client_credentials',
                    'scope' => 'read write'
                ]);

            if ($response->failed()) {
                return false;
            }

            $status = $response->status();
            $response = $response->json();
        } catch (\Exception $e) {
            $response = null;
            graylogError("Payoneer OAuth Login Error! - Message: {$e->getMessage()}", [
                'category' => 'process_payout_payoneer_errors',
                'user_type' => 'system',
                'user_id' => null,
                'action'  => 'auth'
            ]);
        }
        if (!is_null($response) && $status === 200) {
            $expires_in = $response['expires_in'];
            $consented_on = $response['consented_on'];
            $tokenExpired = (int)$consented_on + (int)$expires_in;
            try {
                $systemConfigModel->updateOrCreate([
                    'key' => 'payoneer_application_token',
                    'status' => 1
                ], [
                    'value' => $response['access_token'],
                    'json_data' => json_encode([
                        'expired' => $tokenExpired
                    ])
                ]);
            } catch (\Exception $e) {
                graylogError("Payoneer: Update Or Create SystemConfig Error! - Message: {$e->getMessage()}", [
                    'category' => 'process_payout_payoneer_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => 'auth'
                ]);
                return false;
            }
            $this->accessToken = $response['access_token'];
            return true;
        }
        return false;
    }
}
