<?php

namespace App\Services;

use App\Enums\DiscordChannel;
use App\Enums\SendMail\Template;
use App\Jobs\VerifyEmailSellerRegistered;
use App\Models\SendMailLog;
use App\Models\User;
use Modules\Marketing\Enums\EmailLogEnum;
use Throwable;

class ConfirmEmailService
{
    public static function getUrl(string $hash, $sellerId): string
    {

        $seller = null;
        if ($sellerId) {
            $seller = User::query()->find($sellerId);
        }
        if ($seller && $seller->custom_payment) {
            return config('senprints.base_url_seller_center') . "api/seller/email-verify-account/{$hash}";
        }
        return config('senprints.base_url_seller') . "api/seller/email-verify-account/{$hash}";
    }

    /**
     * @throws Throwable
     */
    public static function sellerLookup(string $hash)
    {
        $mail = SendMailLog::query()
            ->where('id', $hash)
            ->where('sent_at', '>=', now()->subDays(2))
            ->where('template', Template::AUTH_VERIFY_EMAIL)
            ->orderBy('created_at', 'desc')
            ->first();
        throw_if(!$mail, new \Exception('The link has expired.'));
        $seller = User::query()->find($mail->seller_id);

        return $seller;
    }

    public static function confirm(string $hash): void
    {
        $seller = self::sellerLookup($hash);
        if (!$seller->email_verified_at) {
            $seller->email_verified_at = now();
            $seller->save();
            dispatch(new VerifyEmailSellerRegistered($seller->refresh()));
        }
    }

    public static function log($message): void
    {
        $formated = "ConfirmEmailService: $message";
        logToDiscord($formated, DiscordChannel::THIENNT_LOG);
    }

    public static function hasReachedLimit(int $sellerId): bool
    {
        $count = SendMailLog::query()
            ->where('seller_id', $sellerId)
            ->where('template', Template::AUTH_VERIFY_EMAIL)
            ->where('status', EmailLogEnum::ACTION_SENT)
            ->where('sent_at', '>=', now()->subDay())
            ->count();
        return $count >= 3;
    }
}
