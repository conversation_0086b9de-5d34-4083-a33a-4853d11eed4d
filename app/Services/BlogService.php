<?php

namespace App\Services;

use App\Models\Collection;
use App\Models\Blog;
use App\Traits\ElasticClient;
use App\Enums\ProductStatus;
use App\Models\Product;
use App\Traits\Product as TraitsProduct;
use App\Services\StoreService;

class BlogService
{

    use ElasticClient, TraitsProduct;
    /**
     * Set collections data for blogs
     *
     * @param $blogs
     * @return void
     */
    public function setCollectionsDataForBlogs(&$blogs)
    {
        $blogItems = $blogs instanceof \Illuminate\Pagination\LengthAwarePaginator
            ? $blogs->getCollection()
            : (is_array($blogs) ? collect($blogs) : collect([$blogs]));

        $allCollectionIds = $blogItems->flatMap(function ($blog) {
            $collections = $blog->collections;

            if (is_string($collections)) {
                $collections = json_decode($collections, true);
            }

            return is_array($collections) ? array_map('intval', $collections) : [];
        })->unique()->filter()->values()->toArray();

        $collections = [];
        if (!empty($allCollectionIds)) {
            $collections = Collection::query()
                ->select(['id', 'name', 'slug'])
                ->whereIn('id', $allCollectionIds)
                ->get()
                ->keyBy('id');
        }

        $blogItems->each(function ($blog) use ($collections) {
            $blogCollectionIds = $blog->collections ?? [];

            if (is_string($blogCollectionIds)) {
                $blogCollectionIds = json_decode($blogCollectionIds, true);
            }

            if (!is_array($blogCollectionIds)) {
                $blogCollectionIds = [];
            }

            $blogCollections = collect($blogCollectionIds)->map(function ($id) use ($collections) {
                return $collections->get($id);
            })->filter()->values();

            $blog->setAttribute('collections', $blogCollections);
        });
    }

    public function parsingShortCode(&$blog) {
        if (empty($blog->html)) {
            return;
        }

        $html = $blog->html;

        // Parse shortcodes with regex pattern - now handles [blog][/blog] and [collection][/collection] directly
        $pattern = '/\[(blog|collection)([^\]]*)\]\[\/\1\]/';

        $html = preg_replace_callback($pattern, function ($matches) {
            $shortcodeType = $matches[1];
            $attributes = $this->parseShortcodeAttributes($matches[2]);

            switch ($shortcodeType) {
                case 'blog':
                    return $this->processBlogShortcode($attributes);
                case 'collection':
                    return $this->processCollectionShortcode($attributes);
                default:
                    return $matches[0]; // Return original if unknown type
            }
        }, $html);

        $blog->html = $html;
    }

    /**
     * Parse shortcode attributes from string
     */
    private function parseShortcodeAttributes($attributeString)
    {
        $attributes = [];
        $pattern = '/(\w+)=["\']([^"\']*)["\']?/';

        preg_match_all($pattern, $attributeString, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $key = $match[1];
            $value = trim($match[2], '"\'');
            $attributes[$key] = $value;
        }

        return $attributes;
    }

    /**
     * Process blog-posts shortcode
     */
    private function processBlogShortcode($attributes)
    {
        $id = $attributes['id'] ?? null;

        if (!$id) {
            return '';
        }

        $blog = Blog::query()
            ->select(['id', 'title', 'slug', 'sub_description', 'main_image', 'status'])
            ->where('status', true)
            ->filterStoreOwner()
            ->findOrFail($id);

        return self::generateBlogCardsHtml($blog);
    }

    /**
     * Process collections shortcode
     */
    private function processCollectionShortcode($attributes)
    {
        $limit = isset($attributes['limit']) ? intval($attributes['limit']) : 4;

        $collectionId = $attributes['id'] ?? null;

        if (!$collectionId) {
            return '';
        }

        try {
            $store = StoreService::getCurrentStore();
            $indexes = StoreService::getElasticSearchIndexes(null, $store, true);
            $queryFilter['bool']['filter'][]['term'] = [
                'status' => ProductStatus::ACTIVE
            ];
            $queryFilter = $this->addFilterToQueryByStore(
                $store,
                $queryFilter,
                true
            );
            $queryFilter['bool']['filter'][]['term'] = [
                'collection_ids' => $collectionId
            ];

            $querySearch    = [
                'index' => $indexes,
                'body'  => [
                    'track_total_hits' => false,
                    'query'            => $queryFilter,
                    'size'             => $limit,
                    '_source'          => [
                        'includes' => Product::getArrListingElastic()
                    ],
                    'sort'             => [
                        '_score' => 'desc'
                    ],
                    'collapse'         => [
                        'field' => 'design_id'
                    ]
                ]
            ];

            $this->response = $this->elastic('search', $querySearch);

            $products = $this->elasticResponse() ?? [];
            self::mappingCorrectPricing($products);
            StoreService::getVariantOfProducts($products);

            return self::generateProductsHtml($products);
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Generate HTML for blog cards
     */
    private static function generateBlogCardsHtml(Blog $blog)
    {

        $title = htmlspecialchars($blog->title ?? '');
        $description = htmlspecialchars($blog->sub_description ?? '');
        $slug = htmlspecialchars($blog->slug ?? '');
        $image = htmlspecialchars($blog->main_image ?? '');
        $altText = htmlspecialchars($blog->title ?? 'Blog Image');

        $html = '
        <div class="blog-card">
            <div class="blog-content">
                <h2>' . $title . '</h2>
                <p>' . $description . '</p>
                <button class="blog-button" data-slug="' . $slug . '">
                    Read More
                    <svg width="10" height="9" viewBox="0 0 10 9" fill="none">
                        <path d="M5.5 1L8.5 4.5L5.5 8M8.5 4.5H1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
            </div>
            <div class="blog-image">
                <img src="' . $image . '" alt="' . $altText . '">
            </div>
        </div>';

        return $html;
    }

    /**
     * Generate HTML for product cards
     */
    private static function generateProductsHtml($products)
    {
        if (empty($products) || !is_array($products)) {
            return '';
        }

        $html = '<div class="products-section">';
        $html .= '<div class="product-grid">';

        foreach ($products as $product) {
            // Get product data with safe defaults
            $productId = $product['id'] ?? '';
            $productName = htmlspecialchars($product['campaign_name'] ?? $product['name'] ?? '');
            $productSlug = htmlspecialchars($product['slug'] ?? '');
            $productImage = htmlspecialchars($product['thumb_url'] ?? '');

            // Get price with currency
            $price = $product['price'] ?? 0;
            $currencyCode = $product['currency_code'] ?? 'USD';

            // Format price based on currency
            $formattedPrice = self::formatPrice($price, $currencyCode);

            // Generate product card HTML
            $html .= '
            <div class="product-card" data-product-id="' . $productId . '">
                <a href="/' . $productSlug . '" class="product-link">
                    <div class="product-image-placeholder">
                        ' . (!empty($productImage) ? '<img src="' . imgUrl($productImage, 'list') . '" alt="' . $productName . '" loading="lazy" />' : '') . '
                    </div>
                    <div class="product-info">
                        <div class="product-price">
                            ' . $formattedPrice . '
                        </div>
                        <div class="product-name">
                            ' . $productName . '
                        </div>
                    </div>
                </a>
            </div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Format price with currency symbol
     */
    private static function formatPrice($price, $currencyCode)
    {
        $currencySymbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'CHF' => 'Fr.',
            'CNY' => '¥',
            'SEK' => 'kr',
            'NZD' => 'NZ$',
        ];

        $symbol = $currencySymbols[$currencyCode] ?? $currencyCode . ' ';

        // Format price to 2 decimal places (except for JPY which typically doesn't use decimals)
        if ($currencyCode === 'JPY') {
            return $symbol . number_format($price, 0);
        } else {
            return $symbol . number_format($price, 2);
        }
    }
}
