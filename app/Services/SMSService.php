<?php
namespace App\Services;

use App\Enums\PhoneNumberStatusEnum;
use App\Enums\SMSLogStatus;
use App\Enums\TwilioCallbackStatus;
use App\Models\PhoneNumbers;
use App\Models\SmsLog;
use App\Models\SystemConfig;
use App\Models\TwillioSms;
use Illuminate\Support\Str;
use libphonenumber\PhoneNumberUtil;
use Twilio\Rest\Client;

class SMSService
{
    public $smsId, $message, $phoneNumber;
    public $sendBy = 'sms', $externalId = null, $externalKey = null, $logs = null;
    public $orderId = null, $sellerId = null;
    public $created_at, $updated_at;
    public $hash, $from, $to;
    protected $phoneSender = '+***********';

    public function __construct(string $smsId, $message, $phoneNumber, $sendBy = 'sms', $externalId = null, $externalKey = null, $orderId = null, $sellerId = null, $logs = null)
    {
        $this->smsId = $smsId;
        $this->message = $message;
        $this->phoneNumber = $phoneNumber;
        $this->sendBy = $sendBy;
        $this->orderId = $orderId;
        $this->sellerId = $sellerId;
        $this->externalId = $externalId;
        $this->externalKey = $externalKey;
        $this->logs = $logs;
        $this->created_at = currentTime();
        $this->updated_at = currentTime();

        $this->hash = $this->createSMSHash();
        $this->phoneSender = $this->sendBy === 'sms' ? self::getFromPhone($phoneNumber) : self::getDefaultFromPhone();
        $this->from = $this->sendBy . ':' . $this->phoneSender;
        $this->to = $this->phoneNumber;
    }

    /**
     * @param bool $skip
     * @return array|string[]
     */
    public function sendNow(bool $skip = false): array
    {
        try {
            if ($skip) {
                $this->hash = md5($this->smsId . time());
                $this->createSmsLog();
                return $this->send();
            }
            if ($this->shouldCreateSmsLog()) {
                $this->createSmsLog();
                return $this->send();
            }
            $this->logReportToGraylog('Duplicate SMS - hash already exists');
            return ['duplicate', 'Duplicate SMS - hash already exists'];
        }catch (\Exception $e) {
            $this->logReportToGraylog($e->getMessage());
            return ['error', $e->getMessage()];
        }
    }

    /*
     * Create a hash based on order id, external id, phone number, message
     */
    public function createSMSHash() :string
    {
        $message = $this->message;
        $message = str_replace(array("\r", "\n"), '', $message);
        $message = preg_replace('/\s+/', '', $message);

        //base on order id, external id, phone number, message
        $data = [
            'orderId' => $this->orderId,
            'sendBy' => $this->sendBy,
            'externalId' => $this->externalId,
            'phoneNumber' => $this->phoneNumber,
            'message' => $message,
        ];

        return md5_array($data);
    }

    /**
     * @param $hash
     * @return bool
     */
    public function isDuplicate($hash): bool
    {
        return SmsLog::query()->where('hash', $hash)->exists();
    }

    /**
     * @return bool
     */
    public function shouldCreateSmsLog(): bool
    {
        $isHashDuplicate = $this->isDuplicate($this->hash);
        if ($isHashDuplicate) {
            $this->logReportToGraylog('Duplicate SMS - hash already exists');
            return false;
        }
        return true;
    }

    /**
     * @param string $to
     * @param array $data
     * @param string $smsId
     * @param bool $useWhatsapp
     * @return array
     */
    public static function sendSms(string $to, array $data, $smsId, $useWhatsapp = false)
    {
        $accountSid = '**********************************';
        $authToken = '69c5ab77f98e6bb2aca61b0ed82cff50';
        $sid = SystemConfig::getConfig('twilio_account_sid', $accountSid);
        $token = SystemConfig::getConfig('twilio_auth_token', $authToken);
        if (!isset($data['from'])) {
            $data['from'] = self::getFromPhone($to);
        }
        $sms_log = SmsLog::query()->where('send_by', '=', 'whatsapp')
            ->where('created_at', '<=', app()->isProduction() ? now()->subMinutes(10) : now()->subDay())
            ->where('status', '=', SMSLogStatus::SENT)
            ->where('phone_number', '=', $to)
            ->orderByDesc('created_at')
            ->first();
        $driver = 'sms';
        if ($useWhatsapp && empty($sms_log)) {
            $driver = 'whatsapp';
            $to = Str::startsWith($to, $driver . ':') ? $to : $driver . ':' . $to;
            $data['from'] = $driver . ':+***********';
        }
        $baseUrl = config('app.url', 'https://api.senprints.com');
        $data['statusCallback'] = rtrim($baseUrl, '/') . '/public/twilio/callback?key=' . $smsId . '&driver=' . $driver;
        $data['to'] = $to;
        try {
            $client = new Client($sid, $token);
            $message = $client->messages->create($to, $data);
            SmsLog::query()->whereKey($smsId)->update([
                'send_by' => $driver,
                'external_id' => $message->sid,
                'updated_at' => currentTime()
            ]);
            $data['category'] = 'abandoned_success';
            graylogInfo("[Send message via $driver] success", $data);
            return array(TwilioCallbackStatus::QUEUED, $message->toArray());
        } catch (\Throwable $e) {
            $code = $e->getCode();
            $data['category'] = 'abandoned_error';
            if ($code === 21610) {
                graylogInfo("[Send message via $driver] customer unsubscribe", $data);
                return array(TwilioCallbackStatus::UNSUBSCRIBED, $e->getMessage());
            }
            graylogInfo("[Send message via $driver] failed", $data);
            return array(TwilioCallbackStatus::FAILED, $e->getMessage());
        }
    }

    /**
     * @return array
     */
    public function send(): array
    {
        $phoneNumber = $this->phoneNumber;
        if ($this->sendBy === 'whatsapp') {
            $phoneNumber = $this->sendBy . ':' . $phoneNumber;
        }
        if (!PhoneNumbers::canSend($phoneNumber)) {
            return ['error', 'Phone number is invalid'];
        }
        $data = [
            'from' => $this->phoneSender,
            'body' => $this->message
        ];
        [$status, $twilioMsg] = self::sendSms($this->phoneNumber, $data, $this->smsId, $this->sendBy === 'whatsapp');
        return [$status, $twilioMsg];
    }

    /**
     * @throws \Exception
     * @deprecated Because already run
     */
    public static function crawlPhoneCode(): void {}

    /**
     * @param $to
     * @return string
     */
    public static function getFromPhone($to) {
        $to = preg_replace('/\D/', '', $to);
        $to = '+' . $to;
        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            $swissNumberProto = $phoneUtil->parse($to);
            $code = $swissNumberProto->getCountryCode();
            $record = TwillioSms::query()
                ->where('status', 1)
                ->where('code', 'LIKE', '%"'.$code.'"%')
                ->first();
            return $record->phone ?? self::getDefaultFromPhone();
        } catch (\Exception $e) {
            logToDiscord('Error getFromPhone', $e->getMessage());
            return self::getDefaultFromPhone();
        }
    }

    /**
     * @param $to
     * @return string
     */
    public static function getDriver($to) {
        $to = preg_replace('/\D/', '', $to);
        $to = '+' . $to;
        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            $swissNumberProto = $phoneUtil->parse($to);
            $code = $swissNumberProto->getCountryCode();
            if ($code === 1) { // US
                return 'whatsapp';
            }
            return 'sms';
        } catch (\Exception $e) {
            return 'sms';
        }
    }

    /**
     * @return string
     */
    public static function getDefaultFromPhone() {
        $record = TwillioSms::query()
            ->where('status', 1)
            ->whereNull('code')
            ->first();
        return $record->phone ?? '+***********';
    }

    /**
     * @return void
     */
    public function createSmsLog()
    {
        SmsLog::query()->insert([
            'id' => $this->smsId,
            'message' => $this->message,
            'phone_number' => $this->phoneNumber,
            'send_by' => $this->sendBy,
            'order_id' => $this->orderId,
            'seller_id' => $this->sellerId,
            'external_key' => $this->externalKey,
            'external_id' => $this->externalId,
            'created_at' => currentTime(),
            'updated_at' => currentTime(),
            'logs' => $this->logs,
            'hash' => $this->hash,
        ]);
    }

    /**
     * @param $phoneNumber
     * @param $twilioStatus
     * @return void
     */
    public static function createOrUpdatePhoneNumberStatus($phoneNumber, $twilioStatus): void
    {
        try {
            if ($twilioStatus === TwilioCallbackStatus::QUEUED || empty($phoneNumber)) {
                return;
            }
            if (in_array($twilioStatus, [TwilioCallbackStatus::UNSUBSCRIBED, TwilioCallbackStatus::FAILED, TwilioCallbackStatus::UNDELIVERED], true)) {
                $phoneStatus = PhoneNumberStatusEnum::INVALID;
                if (!PhoneNumbers::isExists($phoneNumber)) {
                    PhoneNumbers::query()->insertOrIgnore([
                        'phone_number' => $phoneNumber,
                        'status' => $phoneStatus,
                        'created_at' => currentTime(),
                        'updated_at' => currentTime()
                    ]);
                } else {
                    PhoneNumbers::query()->where('phone_number', $phoneNumber)->updateOrCreate([
                        'status' => $phoneStatus,
                        'updated_at' => currentTime(),
                    ]);
                }
            }
        } catch (\Exception $e) {
            logException($e, 'createOrUpdatePhoneNumberStatus');
        }
    }

    /**
     * @param $messages
     * @param array|null $context
     * @return void
     */
    public function logReportToGraylog($messages, array $context = null): void
    {
        $context ??= [
            'category' => 'send_'.$this->sendBy,
            'from' => $this->from,
            'to' => $this->phoneNumber,
            'content' => $this->message,
            'hash' => $this->hash,
            'externalId' => $this->externalId,
        ];

        $messages = '[Report]-'. $messages;
        graylogInfo($messages, $context);
    }
}
