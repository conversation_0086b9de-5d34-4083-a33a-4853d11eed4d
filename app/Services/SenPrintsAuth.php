<?php

namespace App\Services;

use App\Enums\SellerTeamRoleEnum;
use App\Enums\SystemRole;
use App\Enums\UserStatusEnum;
use App\Models\Supplier;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

/**
 * Class SenPrintsAuth
 * Get current user
 */
class SenPrintsAuth
{

    private $ADMIN_SERVICE = 'admin';
    private $SELLER_SERVICE = 'seller';
    private $SUPPLIER_SERVICE = 'supplier';
    private $DESIGNER_SERVICE = 'designer';
    protected $auth;
    protected $auth_access;
    protected $access_role;
    protected $request;
    protected static $currentInstance;

    public function __construct($userId = null, $request = null)
    {
        $this->request = $request ?? request();
        $service = $this->request->service_name;
        if ($userId !== null) {
            if ($service === $this->SUPPLIER_SERVICE) {
                $user = Supplier::query()->find($userId);
            } else {
                $user = User::query()->find($userId);
            }

            if ($user) {
                Auth::setUser($user);
            }
        }
        if (!empty($this->request->access_account_id)) {
            if ($service === $this->SUPPLIER_SERVICE) {
                $user = Supplier::query()->find($this->request->access_account_id);
            } else {
                $user = User::query()->find($this->request->access_account_id);
            }
            if ($user) {
                $this->auth_access = $user;
                $this->access_role = $this->request->access_role;
            }
        }
        if ($service === $this->SUPPLIER_SERVICE) {
            $this->auth = Auth::guard('supplier')->user();
        } else {
            $this->auth = Auth::user();
        }
    }

    public static function instance($userId = null): SenPrintsAuth
    {
        if (!self::$currentInstance instanceof self
            || (!is_null($userId) && (self::$currentInstance)->getUserId() !== $userId)

            // Chạy trên môi trường test, thì laravel nó không khởi tạo lại instance ngay cả khi chạy các http test
            // Vì thế sẽ bị lỗi nếu chạy các test case trên các account khác nhau
            // Cho nên nếu chạy trong môi trường test thì sẽ cần khởi tạo lại đối tượng
            || isEnvTesting()
        ) {
            return self::$currentInstance = new self($userId);
        }

        return self::$currentInstance;
    }

    public static function setRequestData($payload, Request $request): void
    {
        $request->access_account_id = $payload['access_account_id'] ?? null;
        $request->hasAccessRole = false;

        if ($request->access_account_id) {
            $request->hasAccessRole = true;
        }

        $request->access_role = $payload['access_role'] ?? null;
        $request->service_name = $payload['service_name'];
        $request->guard_name = $payload['guard_name'] ?? null;
        $request->tfa_confirm = $payload['tfa_confirm'] ?? false;
        $request->session_token = $payload['session_token'] ?? null;
    }

    public function roles()
    {
        if ($this->auth === null) {
            return false;
        }
        return $this->auth->roles->pluck('name');
    }

    public function hasRole($roleName): bool
    {
        if ($this->auth === null) {
            return false;
        }

        return ($this->auth->hasRole($roleName) || $this->request->hasAccessRole);
    }

    public function hasExactlyRole($roleName): bool
    {
        return $this->hasRole($roleName) && in_array($roleName, $this->roles()->toArray(), true);
    }

    public function isLoggedAsByAdmin(): bool
    {
        return $this->isAuthorizedAccount() && $this->request->service_name === $this->ADMIN_SERVICE;
    }

    public function hasPermission($permissionName): bool
    {
        if ($this->hasRole(SystemRole::ADMIN)) {
            return true;
        }

        return $this->auth->hasPermissionTo($permissionName);
    }

    public function isAdmin(): bool
    {
        if (!$this->isAuthorizedAccount()) {
            return $this->request->service_name === $this->ADMIN_SERVICE;
        }

        return false;
    }

    public function isSeller(): bool
    {
        if ($this->isAuthorizedAccount() && $this->request->service_name === $this->ADMIN_SERVICE) {
            return true;
        }

        return $this->request->service_name === $this->SELLER_SERVICE;
    }

    public function isSupplier(): bool
    {
        if ($this->isAuthorizedAccount() && $this->request->service_name === $this->ADMIN_SERVICE) {
            return true;
        }

        return $this->request->service_name === $this->SUPPLIER_SERVICE;
    }

    public function getUserBalance(): float
    {
        return round(optional($this->getInfoAccess())->balance, 2);
    }

    public function getUserId()
    {
        return $this->isAuthorizedAccount() ? $this->request->access_account_id : optional($this->auth)->id;
    }

    public function getRefId(): ?int
    {
        if (!$this->isSeller()) {
            return null;
        }

        return $this->getInfoAccess()->ref_id;
    }

    public function getAuthorizedAccountId()
    {
        return $this->isAuthorizedAccount() ? $this->auth->id : null;
    }

    public function isAuthorizedAccount(): ?bool
    {
        return $this->request->hasAccessRole;
    }

    public function getInfo()
    {
        return $this->auth;
    }

    public function getInfoAccess()
    {
        return $this->isAuthorizedAccount() ? $this->auth_access : $this->auth;
    }

    public function getUTCOffset()
    {
        return optional($this->getInfoAccess())->utc_offset;
    }

    public function getEmail()
    {
        return optional($this->auth)->email;
    }

    public function getEmailAccess()
    {
        return optional($this->getInfoAccess())->email;
    }

    public function getName(): string
    {
        return $this->auth ? $this->auth->name : 'Unknown';
    }

    public function getTags(): string
    {
        return optional($this->auth)?->tags ?? '';
    }

    public function getNumberCampaignCanCreate(): int
    {
        return (int)optional($this->getInfoAccess())->number_campaign_can_create;
    }

    public function isBlocked(): bool
    {
        return UserStatusEnum::isBlocked($this->auth->status);
    }

    public function can($permission): bool
    {
        try {
            $role = $this->auth->roles()->first();

            if (!$role) {
                return false;
            }

            return $this->auth->hasAnyPermission($permission) || $role->hasAnyPermission($permission);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * If user is admin or staff but does not have
     * permission to access, then abort
     *
     * @param $permission
     * @return void
     */
    public function hasPermissionOrAbort($permission): void
    {
        $hasPermission = false;
        $permissions = is_array($permission)
            ? $permission
            : explode('|', $permission);
        foreach ($permissions as $per) {
            if ($this->can($per)) {
                $hasPermission = true;
            }
        }
        if ($this->isAdmin() && !$hasPermission) {
            abort(403);
        }
    }

    public function hasPermissionOrFalse($permission): bool
    {
        if ($this->hasRole(SystemRole::ADMIN)) {
            return true;
        }
        return $this->can($permission);
    }

    public function getTfaEnable()
    {
        return $this->auth ? $this->auth->tfa_enable : null;
    }

    public function getTfaSecret()
    {
        return $this->auth ? $this->auth->tfa_secret : null;
    }

    public function getTfaQrCode()
    {
        return $this->auth && $this->getTfaSecret() ? UserService::generateGoogle2FaQrCode([
            'name' => $this->auth->name,
            'google2fa_secret' => $this->getTfaSecret(),
        ]) : null;
    }

    public function hasPrivateConnection(): bool
    {
        return UserShardingStatusEnum::hasPrivateConnection($this->auth?->sharding_status);
    }

    public function shardingCompleted(): bool
    {
        return UserShardingStatusEnum::shardingCompleted($this->auth?->sharding_status);
    }
    public function getPrivateConnection($shardingCompleted = true): ?string
    {
        if ($shardingCompleted) {
            return $this->shardingCompleted() ? 'mysql_'.$this->auth->id : ($this->auth?->db_connection ?? 'mysql');
        }
        return $this->hasPrivateConnection() ? 'mysql_'.$this->auth->id : ($this->auth?->db_connection ?? 'mysql');
    }

    public function hasCustomPayment(): bool
    {
        return optional($this->getInfoAccess())->custom_payment ?? false;
    }

    public function getAccessRole(): ?string
    {
        return $this->access_role;
    }

    public function canMemberManageStore(): bool
    {
        return $this->getAuthorizedAccountId() === null
            || $this->getAccessRole() === SellerTeamRoleEnum::MANAGER
            || $this->getAccessRole() === SellerTeamRoleEnum::MANAGER_LITE;
    }
}
