<?php
namespace App\Services;

use App\Enums\SendMail\LogStatus;

class ResendMailService
{
    public static function shouldCancel($failedMail): bool
    {
        return empty($failedMail->content) || empty($failedMail->subject);
    }

    public static function replicateAndPrepareForResend($failedMail)
    {
        $resendMailLog = $failedMail->replicate();
        $resendMailLog->status = LogStatus::RESEND;
        $resendMailLog->logs = 'resend from mail id: ' . $failedMail->id;
        $resendMailLog->sent_at = now();
        return $resendMailLog;
    }

}
