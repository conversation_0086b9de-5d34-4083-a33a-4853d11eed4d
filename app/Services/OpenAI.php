<?php

namespace App\Services;

use App\Enums\OpenAiModel;
use Illuminate\Support\Facades\Http;

class OpenAI
{
    public const BASE_URL = 'https://api.openai.com/v1';

    public static function completions(array $messages, string $model = null, bool $jsonMode = false, array $jsonSchema = []): string
    {
        $apiKey = config('services.openai.api_key');

        if (!$apiKey) {
            throw new \RuntimeException('OpenAI API key not set.');
        }

        if (!$model) {
            $model = OpenAiModel::default();
        }

        $endpoint = self::BASE_URL . '/chat/completions';
        $postData = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => 300
        ];

        // https://platform.openai.com/docs/guides/text-generation/json-mode
        if ($jsonMode) {
            $postData['response_format'] = [
                'type' => 'json_object',
            ];
        }

        if ($jsonSchema) {
            $postData['response_format']['type'] = 'json_schema';
            $postData['response_format']['json_schema'] = $jsonSchema;
        }

        $res = Http::withToken($apiKey)
            ->asJson()
            ->timeout(30)
            ->post($endpoint, $postData);

        if ($res->successful()) {
            return $res->json('choices.0.message.content', 'No response.');
        }

        // If request failed, throw exception with error details
        if ($res->failed()) {
            throw new \RuntimeException(
                'OpenAI API error: ' .
                $res->status() . ' - ' .
                ($res->json('error.message') ?? 'Unknown error')
            );
        }

        self::checkApiKey($res);

        return 'No response.';
    }

    public static function createThreadAndRun(string $assistantId, array $messages, bool $jsonMode = true)
    {
        $apiKey = config('services.openai.api_key');

        if (!$apiKey) {
            throw new \RuntimeException('OpenAI API key not set.');
        }

        $endpoint = self::BASE_URL . '/threads/runs';

        $postData = [
            'assistant_id' => $assistantId,
            'thread' => [
                'messages' => $messages,
            ],
        ];

        if ($jsonMode) {
            $postData['response_format'] = [
                'type' => 'json_object',
            ];
        }

        $res = Http::withToken($apiKey)
            ->asJson()
            ->timeout(30)
            ->withHeaders([
                'OpenAI-Beta' => 'assistants=v2',
            ])
            ->post($endpoint, $postData);

        if ($res->successful()) {
            return $res->json();
        }

        self::checkApiKey($res);

        return null;
    }

    public static function listThreadMessages(string $threadId, string $runId = null)
    {
        $apiKey = config('services.openai.api_key');

        if (!$apiKey) {
            throw new \RuntimeException('OpenAI API key not set.');
        }

        $endpoint = self::BASE_URL . '/threads/' . $threadId . '/messages';

        $res = Http::withToken($apiKey)
            ->asJson()
            ->timeout(30)
            ->withHeaders([
                'OpenAI-Beta' => 'assistants=v2',
            ])
            ->get($endpoint, [
                'run_id' => $runId,
            ]);

        if ($res->successful()) {
            return $res->json('data', []);
        }

        self::checkApiKey($res);

        return null;
    }

    private static function checkApiKey($res): void
    {
        if ($res->json('error.code') === 'invalid_api_key') {
            throw new \RuntimeException('OpenAI API key is invalid.');
        }
    }

    /**
     * @param $labelContent
     * @return array
     */
    public static function extractTrackingInfo ($labelContent): array {
        $prompt = [
            [
                'role' => 'system',
                'content' => 'You are a helpful assistant designed to output JSON. You are helping an user come up with a label content in text type provided by shipping carrier services like USPS, FEXED on user orders.',
            ],
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        "text" => 'Extract tracking code and shipping carrier from this label in shipping label content. Answer in JSON format `{"tracking_code":  "...", "shipping_carrier": "..."}`. Label content is ' . $labelContent
                    ],
                ]
            ],
        ];
        $response = self::completions($prompt, OpenAiModel::GPT_4o, true);
        if (empty($response)) {
            return [];
        }
        $responseArr = json_decode($response);
        graylogError('openAI extract tracking : ', [
            'category' => 'openai_extract_tracking',
            'response' => $response
        ]);
        return (array)$responseArr;
    }

    /**
     * Send image to OpenAI for analysis
     *
     * @param string $imageUrl URL of the image to analyze
     * @param string $prompt Text prompt to guide the analysis
     * @param string|null $model Model to use (defaults to gpt-4o-mini)
     * @return string Response from OpenAI
     */
    public static function analyzeImage(string $imageUrl, string $prompt, ?string $model = null): string
    {
        $messages = [
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt
                    ],
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => $imageUrl
                        ]
                    ]
                ]
            ]
        ];

        return self::completions($messages, $model ?? 'gpt-4o-mini');
    }

    /**
     * Send image to OpenAI for analysis using base64 data
     *
     * @param string $base64Image Base64 encoded image data
     * @param string $prompt Text prompt to guide the analysis
     * @param string|null $model Model to use (defaults to gpt-4o-mini)
     * @return string Response from OpenAI
     */
    public static function analyzeImageBase64(string $base64Image, string $prompt, ?string $model = null): string
    {
        try {
            $messages = [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => 'data:image/jpeg;base64,' . $base64Image
                            ]
                        ]
                    ]
                ]
            ];

            return self::completions($messages, $model ?? 'gpt-4o-mini');
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to analyze image. Please try again later.');
        }
    }
}
