<?php
namespace App\Services;

use App\Models\SellerTeam;
use App\Models\Staff;
use App\Models\User;
use App\Models\Supplier;
use App\Models\UserSessions;
use Illuminate\Support\Str;

class SessionService
{
    public const DEBUG = false;

    public function createSession(User|Staff|Supplier $user, User|Staff|Supplier $userAccess = null): string
    {
        $type = match (true) {
            $user instanceof User => 'seller',
            $user instanceof Supplier => 'supplier',
            $user instanceof Staff => 'staff'
        };
        $token = Str::random(15);
        [$ipAddress, $userAgent, $location, $longitude, $latitude, $os, $device, $browser] = $this->getPayloadInfo();
        self::log('Create session for user: ' . $user->id . ' with token: ' . $token . ' and type: ' . $type);
        $user_access_type = match (true) {
            $userAccess instanceof User => 'seller',
            $userAccess instanceof Supplier => 'supplier',
            $userAccess instanceof Staff => 'staff',
            default => null,
        };
        UserSessions::query()
            ->create([
                'user_id' => $user->id,
                'token' => $token,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'location' => $location,
                'last_activity' => now(),
                'user_type' => $type,
                'user_access_id' => $userAccess?->id,
                'user_access_type' => $user_access_type,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'os' => $os,
                'device' => $device,
                'browser' => $browser,
            ]);

        return $token;
    }

    public function checkSession(User|Staff|Supplier $user ,string $token): bool
    {
        $type = match (true) {
            $user instanceof User => 'seller',
            $user instanceof Supplier => 'supplier',
            $user instanceof Staff => 'staff',
            default => null,
        };
        $session = UserSessions::query()
            ->where('token', $token)
            ->first();

        if (!$session) {
            self::log(
                'Session not found for user: ' . $user->id . ' with token: ' . $token . ' and type: ' . $type,
                true,
            );
            return false;
        }

        $isPass = false;
        // Access by another user case
        if (
            $session->user_access_id
            && $session->user_access_id === $user->id
            && $session->user_access_type === $type
        ) {
            $isPass = true;
        }

        // Access by same user case
        if (
            $session->user_id === $user->id
            && $session->user_type === $type
        ) {
            $isPass = true;
        }

        if ($isPass) {
            $this->setLastActivity($token);
        }

        return $isPass;
    }

    public function setLastActivity(string $token): void
    {
        self::log('Set last activity for token: ' . $token);
        UserSessions::query()
            ->where('token', $token)
            ->update([
                'last_activity' => now()
            ]);
    }

    public function getPayloadInfo(): array
    {
        $request = request();
        $ipAddress = $request->cookie('ip_address');
        $userAgent = $request->cookie('user_agent') ?? $request->userAgent();
        $location = $request->cookie('location');
        $longitude = $request->cookie('longitude');
        $latitude = $request->cookie('latitude');
        $os = $request->cookie('os');
        $device = $request->cookie('device');
        $browser = $request->cookie('browser');

        return [
            $ipAddress,
            $userAgent,
            $location,
            $longitude,
            $latitude,
            $os,
            $device,
            $browser,
        ];
    }

    public function getLoggedBy(UserSessions $session): ?string
    {
        if (!$session->user_access_type || !$session->user_access_id) {
            return null;
        }
        $model = match ($session->user_type) {
            'seller' => User::class,
            'supplier' => Supplier::class,
            'staff' => Staff::class,
            default => null,
        };
        if (!$model) {
            return null;
        }
        $record = $model::query()
            ->select(['id', 'name'])
            ->find($session->user_access_id);

        if ($session->user_access_type === 'seller') {
            $type = SellerTeam::query()
                ->select('role')
                ->where('seller_id', $session->user_id)
                ->where('member_id', $session->user_access_id)
                ->first()
                ?->role ?? 'seller';
        } else {
            $type = 'admin';
        }
        return ucfirst($type) . ' - ' . $record->name;
    }

    public static function log(string $message, $importance = false): void
    {
        if (self::DEBUG || $importance) {
            logToDiscord($message, 'user_session');
        }
    }

    public function destroySession(string $token): void
    {
        self::log('Destroy session for token: ' . $token);
        UserSessions::query()
            ->where('token', $token)
            ->delete();
    }

    public function flushSessions(User|Staff|Supplier $user): void
    {
        $type = match (true) {
            $user instanceof User => 'seller',
            $user instanceof Supplier => 'supplier',
            $user instanceof Staff => 'staff',
            default => null,
        };
        self::log('Flush sessions for user: ' . $user->id . ' and type: ' . $type);
        UserSessions::query()
            ->where('user_id', $user->id)
            ->where('user_type', $type)
            ->delete();
    }
}
