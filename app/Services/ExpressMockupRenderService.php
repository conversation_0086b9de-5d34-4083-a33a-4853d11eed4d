<?php

namespace App\Services;

use App\Enums\CampaignRenderModeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\StorageDisksEnum;
use App\Models\Campaign;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\Product;
use Cloudinary\Cloudinary;
use Cloudinary\Transformation\Argument\Color;
use Cloudinary\Transformation\Effect;
use Cloudinary\Transformation\Format;
use Cloudinary\Transformation\ImageTransformation;
use Cloudinary\Transformation\Overlay;
use Cloudinary\Transformation\Reshape;
use Cloudinary\Transformation\Resize;
use Cloudinary\Transformation\Source;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

class ExpressMockupRenderService
{
    /**
     * @param array $designs
     * @param $userId
     * @param string $storage
     * @return false|int
     */
    public function saveDesigns(array $designs, $userId, string $storage = 's3') : bool|int
    {
        $count = 0;
        $newDesigns = [];
        foreach ($designs as $design) {
            $tempDesignPath = $design['file_url'];
            $productId = $design['product_id'];
            $campaignId = $design['campaign_id'];
            $mockupId = $design['mockup_id'];
            $printSpace = $design['print_space'];
            $isDefaultMockup = $design['is_default_mockup'];
            $count++;

            // Remove old designs
            $this->removeOldDesigns($productId, $mockupId, $printSpace);

            if (!empty($tempDesignPath)) {
                $filePath1 = null;
                $filePath2 = null;

                if ($storage === StorageDisksEnum::S3) {
                    $filePath1 = $this->saveTempDesign($tempDesignPath, $campaignId);
                } else {
                    $filePath2 = $tempDesignPath;
                }

                try {
                    if ($isDefaultMockup) {
                        $this->updateDefaultMockup($campaignId, $productId, $filePath1);
                    }

                    $newDesigns[] = [
                        'file_url' => $filePath1,
                        'file_url_2' => $filePath2,
                        'campaign_id' => $campaignId,
                        'mockup_id' => $mockupId,
                        'product_id' => $productId,
                        'type' => FileTypeEnum::DESIGN,
                        'option' => FileRenderType::EXPRESS,
                        'print_space' => $printSpace,
                        'seller_id' => $userId,
                    ];
                } catch (Throwable $ex) {
                    logException($ex, 'saveDesigns (ExpressMockupRenderService)');
                    return false;
                }
            }
        }
        if (!empty($newDesigns)) {
            File::query()->insert($newDesigns);
        }
        return $count;
    }

    /**
     * Remove old designs
     * @param $productId
     * @param $mockupId
     * @param string $printSpace
     * @return ExpressMockupRenderService
     */
    public function removeOldDesigns($productId, $mockupId, string $printSpace = 'front'): static
    {
        File::query()
            ->where([
                'product_id' => $productId,
                'type' => FileTypeEnum::DESIGN,
                'option' => FileRenderType::EXPRESS,
                'mockup_id' => $mockupId,
                'print_space' => $printSpace,
            ])
            ->delete();
        return $this;
    }

    /**
     * @param $campaignId
     * @param $productId
     * @return void
     */
    public function removeOldImages($campaignId, $productId): void
    {
        File::query()
            ->where([
                'campaign_id' => $campaignId,
                'product_id' => $productId,
                'type' => FileTypeEnum::IMAGE,
            ])
            ->whereNull('type_detail')
            ->delete();
    }

    /**
     * Save temp design
     * @param $tempPath
     * @param $campaignId
     * @return string
     */
    public function saveTempDesign($tempPath, $campaignId): string
    {
        // default is 'p' for product, but for design it's 'd'
        if (!$tempPath || !$campaignId) {
            return '';
        }
        if (str_starts_with($tempPath, 'p/' . $campaignId)) {
            return $tempPath;
        }
        $filePaths = explode('/', $tempPath);
        $fileName = end($filePaths);
        $newPath = 'p/' . $campaignId . '/' . $fileName;
        saveTempFileAws($tempPath, $newPath);
        return $newPath;
    }

    /**
     * Update default mockup
     * @param $campaignId
     * @param $productId
     * @param string $thumbFilePath
     * @return void
     */
    public function updateDefaultMockup($campaignId, $productId, string $thumbFilePath): void
    {
        $campaign = Campaign::query()->find($campaignId);
        $product = Product::query()->find($productId);
        $product->update(['thumb_url' => $thumbFilePath]);
        if ($campaign->default_product_id === $productId) {
            $campaign->update(['thumb_url' => $thumbFilePath]);
        }
    }

    /**
     * @param $templateId
     * @param string $mockupType
     * @return Collection|null
     */
    public function listMockup($templateId, string $mockupType = FileRenderType::EXPRESS): ?Collection
    {
        $mockups = File::query()
            ->where([
                'product_id' => $templateId,
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => $mockupType
            ])
            ->orderBy('position')
            ->get();
        if ($mockups->count() === 0) {
            return null;
        }
        return $mockups;
    }

    /**
     * Find design
     * @param ExpressCampaign $campaign
     * @return File|null
     */
    public function findDesign(ExpressCampaign $campaign)
    {
        if ($campaign->designs->count() === 0) {
            return null;
        }
        return $campaign->designs->first();
    }

    /**
     * @param ExpressCampaign $campaign
     * @return File|null
     */
    public function findDefaultMockup(ExpressCampaign $campaign)
    {
        $printSpace = $campaign->default_option;
        $design = $campaign->designs->first();
        if (!$design) {
            return null;
        }
        $defaultProduct = $campaign->defaultProduct;
        if (!$defaultProduct) {
            return null;
        }
        $mockupType = $defaultProduct->mockup_type;
        return File::whereProductId($defaultProduct->template_id)
            ->whereType("mockup")
            ->whereTypeDetail($mockupType)
            ->whereRenderType(FileRenderType::EXPRESS)
            ->wherePrintSpace($printSpace)
            ->first();
    }

    /**
     * @param Product $product
     * @param Campaign $campaign
     * @return void
     */
    public function generateImages(Product $product, Campaign $campaign): void
    {
        $productMockupType = $product->mockup_type;

        $mockups = $this->listMockup($product->template_id);
        if (is_null($mockups)) {
            // no mockup found
            return;
        }


        // drop old imageFiles
        $this->removeOldImages($campaign->id, $product->id);

        $defaultImages = null;
        $customMockup = $product->images;

        if ($customMockup->isNotEmpty()) {
            // if custom mockup found, use it as default
            if (!in_array($product->thumb_url, $customMockup->pluck('file_url')->toArray())) {
                $product->fill([
                    'thumb_url' => $customMockup->first()->file_url,
                    'sync_status' => 0
                ])->update();
            }

            // set default campaign/product image
            if ($campaign->default_product_id === $product->id) {
                $campaign->fill([
                    'thumb_url' => $product->thumb_url,
                    'sync_status' => 0
                ])->update();
            }

            $defaultImages = $product->thumb_url;
        }

        $renderMode = ($campaign->render_mode === $product->template->render_mode) ? $campaign->render_mode : CampaignRenderModeEnum::NATURE;
        $mockups->map(function ($mockup) use ($product, $campaign, &$defaultImages, $productMockupType, $renderMode) {
            if ($productMockupType === $mockup['type_detail'] || empty($mockup['type_detail'])) {
                $designFile = File::query()->firstWhere([
                    'product_id' => $product->id,
                    'type' => FileTypeEnum::DESIGN,
                    'mockup_id' => $mockup->id,
                    'option' => FileRenderType::EXPRESS,
                ]);

                // put image without design to bottom
                $position = is_null($designFile) ? $mockup->position * 10 : $mockup->position;
                $defaultColor = $product->default_option ?? 'white';
                if (is_null($designFile)) {
                    // skip if no design file
                    return;
                }
                $fileUrl2 = $this->prepareOptionsForGenerateImageUrl($mockup, $designFile, $defaultColor, $renderMode);

                // set default campaign/product image
                if ((is_null($defaultImages) && !is_null($designFile) && empty($product->default_mockup_id))
                    || $product->default_mockup_id === $mockup->id
                ) {
                    $defaultImages = $fileUrl2;

                    Product::query()
                        ->where(['id' => $product->id])
                        ->update([
                            'thumb_url' => $fileUrl2,
                            'sync_status' => 0
                        ]);

                    if ($campaign->default_product_id === $product->id) {
                        Campaign::query()
                            ->where(['id' => $campaign->id])
                            ->update([
                                'thumb_url' => $fileUrl2,
                                'sync_status' => 0
                            ]);
                    }
                }

                File::query()->insertOrIgnore([
                    'campaign_id' => $product->campaign_id,
                    'product_id' => $product->id,
                    'seller_id' => $campaign->seller_id,
                    'type' => FileTypeEnum::IMAGE,
                    'design_id' => optional($designFile)->id,
                    'mockup_id' => $mockup->id,
                    'print_space' => $mockup->print_space,
                    'position' => $position,
                    'file_url_2' => $fileUrl2,
                ]);
            }
        });
    }

    /**
     * @param File $mockup
     * @param File|null $design
     * @param string $colorName
     * @param string $renderMode
     * @param bool $noDesign
     * @return string
     */
    public function prepareOptionsForGenerateImageUrl(File $mockup, ?File $design, string $colorName, string $renderMode = CampaignRenderModeEnum::NATURE, $noDesign = false): string
    {
        try {
            $designJson = json_decode($mockup->design_json, true); // to array
            if (is_null($designJson)) {
                return '';
            }
            $layoutColor = $designJson['color'] ?? '';
            $layoutColor = str_replace('.png', '', $layoutColor);
            $layoutCrop = $designJson['crop'] ?? '';
            $layoutCrop = str_replace('.png', '', $layoutCrop);
            $layoutShadow = $designJson['shadow'] ?? '';
            $layoutShadow = str_replace('.png', '', $layoutShadow);
            $background = str_replace('.png', '', $mockup->file_url);
            $pathPrefix = ($design && (is_null($design->file_url) || empty($design->file_url))) ? '/s3' : '/s4';
            if ($noDesign === true || !$design) {
                $layoutDesign = 'blank';
            } else {
                $layoutDesign = $design->file_url ?? $design->file_url_2;
            }
            $layoutDesign = str_replace('.png', '', $layoutDesign);
            $hexColor = color2hex($colorName);
            $options = [
                'background' => $background,
                'color' => $layoutColor,
                'crop' => $layoutCrop,
                'shadow' => $layoutShadow,
                'design' => $layoutDesign,
                'hexColor' => $hexColor,
                'renderMode' => $renderMode,
                'pathPrefix' => $pathPrefix,
            ];
            // if expressOptions in designJson is not empty, use it
            if (!empty($designJson['expressOptions'])) {
                $expressOptions = $designJson['expressOptions'];
                $expressWidth = $expressOptions['width'] ?? 0;
                $expressHeight = $expressOptions['height'] ?? 0;
                $expressX = $expressOptions['x'] ?? 0;
                $expressY = $expressOptions['y'] ?? 0;
                $expressString = "/v1/po:{$expressY}:{$expressX}:{$expressWidth}:{$expressHeight}/";
                if ($noDesign === false) {
                    $options['expressOptions'] = $expressString;
                }
            }
            return $this->getMockupUrlFromOptions($options);
        } catch (Exception $ex) {
            logException($ex, 'generateOptionsForMockupUrl (ExpressMockupRenderService)');
            return '';
        }
    }

    /**
     * @param array $options
     * @return string
     */
    public function getMockupUrlFromOptions(array $options): string
    {
        $background = $options['background'];
        $color = $options['color'];
        $crop = $options['crop'];
        $shadow = $options['shadow'];
        $design = $options['design'];
        $hexColor = $options['hexColor'];
        $renderMode = $options['renderMode'];
        $pathPrefix = $options['pathPrefix'];
        $expressOptions = $options['expressOptions'] ?? null;
        $config = config('senprints.cloudinary_config');
        $cloudinary = new Cloudinary($config);

        if (!empty($design)) {
            $image = $cloudinary->image($design);
            if (!empty($crop)) {
                $image->reshape(Reshape::cutByImage(Source::image($crop)));
            }
        } else {
            $image = $cloudinary->image($background);
        }

        if (!empty($design) && !empty($shadow) && $renderMode === CampaignRenderModeEnum::DYNAMIC) {
            $image->underlay(Overlay::source(Source::image($shadow)));
        }

        if (!empty($color)) {
            $transformationColor = Source::image($color)
                ->transformation((new ImageTransformation())
                    ->effect(Effect::colorize()
                        ->level(100)
                        ->color(Color::rgb($hexColor))));
            if (!empty($design)) {
                $image->underlay(Overlay::source($transformationColor));
            } else {
                $image->overlay(Overlay::source($transformationColor));
            }
        }

        if ((!empty($shadow) && $renderMode !== CampaignRenderModeEnum::DYNAMIC) || empty($design)) {
            $image->overlay(Overlay::source(Source::image($shadow)));
        }

        if (!empty($background) && !empty($design)) {
            $image->underlay(Overlay::source(Source::image($background)));
        }

        $image->resize(Resize::thumbnail()->width(1280))->format(Format::jpg());

        $url = preg_replace('/https:\/\/res\.cloudinary\.com\/\w+\/image\/upload/', $pathPrefix, $image->toUrl());
        // if /v1/blank is not found in url, replace /blank with /v1/blank
        if (strpos($url, '/blank') !== false && strpos($url, '/v1/blank') === false) {
            $url = str_replace('/blank', '/v1/blank', $url);
        }
        $hashName = substr(md5($url), 0, 16);
        // remove version param and add file name

        $url = preg_replace('/\?_a=.+$/', '/t/' . $hashName . '.jpg', $url);

        if (!empty($expressOptions)) {
            // $expressOptions = /v1/po:0:0:100:100/
            $url = str_replace('/v1/', $expressOptions, $url);
        }

        return $url;
    }

    /**
     * @param Campaign $campaign
     * @return void
     */
    public function renderMockupForCampaign(Campaign $campaign): void
    {
        $products = Product::query()
            ->with('template')
            ->with('images', function ($images) {
                $images->where('type_detail', FileRenderType::CUSTOM);
            })
            ->where('campaign_id', $campaign->id)
            ->get();

        if ($products->count() > 0) {
            $products->each(fn (Product $product) => $this->generateImages($product, $campaign));
        }
    }
}
