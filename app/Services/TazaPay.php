<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class TazaPay
{
    private $httpHandler;

    public function __construct($apiKey, $apiSecret)
    {
        if (app()->environment('local', 'development')) {
            $endpoint = 'https://api-sandbox.tazapay.com/v1';
        } else {
            $endpoint = 'https://api.tazapay.com/v1';
        }

        $this->httpHandler = Http::withBasicAuth($apiKey, $apiSecret)
            ->baseUrl($endpoint)
            ->acceptJson()
            ->asJson();
    }

    /**
     * @url https://docs.tazapay.com/reference/create-checkout
     *
     * @param array $buyer ['email', 'country', 'ind_bus_type', 'first_name', 'last_name']
     * @param string $txnDescription
     * @param float $invoiceAmount
     * @param string $invoiceCurrency
     * @return array|false|mixed
     */
    public function createCheckoutSession(array $buyer, string $txnDescription, float $invoiceAmount, string $invoiceCurrency = 'USD')
    {
        $buyer['ind_bus_type'] = 'Individual';
        $postData = [
            'buyer' => $buyer,
            'invoice_currency' => $invoiceCurrency,
            'invoice_amount' => $invoiceAmount,
            'txn_description' => $txnDescription,
        ];

        try {
            $paymentMethodsReq = $this->getCollectionMethods($invoiceAmount, $buyer['country'], $invoiceCurrency);

            if (($paymentMethodsReq !== false) && data_get($paymentMethodsReq, 'status') === 'success') {
                $methods = data_get($paymentMethodsReq, 'data.payment_methods');

                // only get name field
                $paymentMethods = collect($methods)
                    ->map(fn($method) => data_get($method, 'name'))
                    ->toArray();

                // 'Card' always first
                if (in_array('Card', $paymentMethods)) {
                    array_unshift($paymentMethods, 'Card');
                    $paymentMethods = array_unique($methods);
                }

                $postData['payment_methods'] = $paymentMethods;
            }
        } catch (\Throwable $e) {
            // do nothing
        }

        return $this->httpHandler
            ->post('/checkout', $postData)
            ->json();
    }

    /**
     * @url https://docs.tazapay.com/reference/get-checkout-session
     *
     * @param string $txnNo
     * @return array|false|mixed
     */
    public function getCheckoutSession(string $txnNo)
    {
        $res = $this->httpHandler->get('/checkout/' . urlencode($txnNo));

        if ($res->successful()) {
            return $res->json();
        }

        return false;
    }

    /**
     * @url https://docs.tazapay.com/reference/create-user-api
     *
     * @param string $email
     * @param string $country
     * @param string $firstName
     * @param string $lastName
     * @param string $businessName
     * @param string $contactCode
     * @param string $contactNumber
     * @param int $partnersCustomerId
     * @return array|false|mixed
     */
    public function createUser(string $email, string $country, string $firstName, string $lastName, string $businessName, string $contactCode, string $contactNumber, int $partnersCustomerId)
    {
        $res = $this->httpHandler
            ->post('/user', [
                'email' => $email,
                'country' => $country,
                'first_name' => $firstName,
                'business_name' => $businessName,
                'last_name' => $lastName,
                'contact_code' => $contactCode,
                'contact_number' => $contactNumber,
                'partners_customer_id' => $partnersCustomerId,
            ]);

        if ($res->successful()) {
            return $res->json();
        }

        return false;
    }

    /**
     * @url https://docs.tazapay.com/reference/get-user-by-email-api
     *
     * @param string $email
     * @return array|false|mixed
     */
    public function getUserByEmail(string $email)
    {
        if (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
            throw new \InvalidArgumentException('Invalid email address.');
        }

        $res = $this->httpHandler->get('/user/' . urlencode($email));

        if ($res->successful()) {
            return $res->json();
        }

        return false;
    }

    /**
     * @url https://docs.tazapay.com/reference/collection-methods-api
     *
     * @param float $amount
     * @param string $buyerCountry
     * @param string $invoiceCurrency
     * @param string $sellerCountry
     * @return array|false|mixed
     */
    public function getCollectionMethods(float $amount, string $buyerCountry, string $invoiceCurrency = 'USD', string $sellerCountry = 'US')
    {
        $res = $this->httpHandler
            ->get('/metadata/collect', [
                'amount' => $amount,
                'buyer_country' => $buyerCountry,
                'invoice_currency' => $invoiceCurrency,
                'seller_country' => $sellerCountry,
            ]);

        if ($res->successful()) {
            return $res->json();
        }

        return false;
    }

    /**
     * @url https://docs.tazapay.com/reference/refund-api
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $detail
     * @param int $fullRefund
     * @return array|false|mixed
     */
    public function refundTransaction(string $transactionId, float $amount, string $detail, int $fullRefund = 0)
    {
        $refundData = [
            'txn_no' => $transactionId,
            'remarks' => $detail,
        ];
        if (empty($fullRefund)) {
            $refundData['amount'] = $amount;
        }
        return $this->httpHandler->post('/payment/refund/request', $refundData)->json();
    }
}
