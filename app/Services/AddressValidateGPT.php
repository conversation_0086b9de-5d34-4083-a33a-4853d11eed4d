<?php

namespace App\Services;

use App\Enums\OpenAiModel;

class AddressValidateGPT
{
    public static function validate(string $address): bool
    {
        $systemPrompt = file_get_contents(resource_path('ai-prompts/validate-address.txt'));
        $jsonSchema = file_get_contents(resource_path('ai-prompts/validate-address-schema.json'));

        $prompt = [
            [
                'role' => 'system',
                'content' => $systemPrompt,
            ],
            [
                'role' => 'user',
                'content' => $address,
            ]
        ];

        $jsonString = OpenAI::completions(
            $prompt,
            OpenAiModel::GPT_4o_2024_08_06,
            true,
            json_decode($jsonSchema, true)
        );
        $json = json_decode($jsonString);

        return $json->verdict === 'valid';
    }
}
