<?php

namespace App\Services;

use App\Enums\CacheKeys;
use App\Enums\ColorSpaceEnum;
use App\Enums\DesignStatusEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\NeedDesignTagsEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductCategoryEnum;
use App\Enums\ProductOptionEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductType;
use App\Enums\ShippingMethodEnum;
use App\Enums\SupplierEnum;
use App\Enums\TradeMarkStatusEnum;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductDesignMapping;
use App\Models\SystemConfig;
use App\Models\Template;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;
use Throwable;

class FulfillmentService
{
    public const PRODUCT_ALLOW_REFACTOR_OPTION = [
        SupplierEnum::PRINTWAY_V2 => [
            'Round Wooden Sign - 9mm',
            'Round Wooden Sign - 9mm - original',
            'Round Wooden Sign - 9mm - black'
        ]
    ];

    private const SUPPLIERS_FOR_GROUP = [
        SupplierEnum::DREAMSHIP,
    ];

    public const FLS_SUPPLIER_GROUP = [
        SupplierEnum::FLASH_SHIP,
        SupplierEnum::FLASH_SHIP_EXPEDITE,
        SupplierEnum::FLASH_SHIP_LABEL
    ];

    /**
     * @param Collection $orderProducts
     * @param $fulfillOrderIdsOnSuppliers
     * @return \Illuminate\Support\Collection
     */
    public static function groupOrderProductsBySuppliers(Collection $orderProducts, &$fulfillOrderIdsOnSuppliers)
    {
        $orderProductsOnSups = [];
        foreach ($orderProducts as $key => $op) {
            if (in_array($op->supplier_id, self::SUPPLIERS_FOR_GROUP)) {
                $supplier = $op->supplier_id;
                $orderProductsOnSups[$supplier][] = $op;
                $fulfillOrderIdsOnSuppliers[$supplier][] = $op->fulfill_order_id;
                $orderProducts->forget($key);
            }
        }
        return collect($orderProductsOnSups);
    }

    /**
     * @param $productId
     * @param array $defaultPrintSpace
     * @return bool
     * @throws Exception
     */
    public static function productIncludeAdditionalPrintSpace($productId, array $defaultPrintSpace = []): bool
    {
        if (empty($productId)) {
            return false;
        }
        $tag = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
        $product = cacheAlt()->tags([$tag])->remember(
            md5('product_print_spaces_full_printed_' . $productId),
            CacheKeys::CACHE_1H,
            function () use ($productId) {
                return Product::query()
                    ->select(['print_spaces', 'full_printed'])
                    ->where('id', $productId)
                    // Only check for 2D print type, all other types are not have additional print space
                    // or has but not need to check, such as AOP, Embroidery, Handmade, 2D Full, 3D Full
                    ->where('full_printed', ProductPrintType::PRINT_2D)
                    ->first();
            }
        );
        if (!$product) {
            return false;
        }
        $designFiles = cacheAlt()->tags([$tag])->remember(
            md5('product_design_files_' . $productId),
            CacheKeys::CACHE_1H,
            function () use ($productId) {
                return File::query()
                    ->select([
                        'id',
                        'campaign_id',
                        'file_url',
                        'file_url_2',
                        'product_id',
                        'option',
                        'token',
                        'print_space'
                    ])
                    ->where('product_id', $productId)
                    ->where('type', FileTypeEnum::DESIGN)
                    ->where('option', FileRenderType::PRINT)
                    ->get();
            }
        );
        if (!$designFiles || $designFiles->isEmpty()) {
            return false;
        }
        $collect = $designFiles->filter(function ($design) {
            return in_array($design->print_space, PrintSpaceEnum::additionalPrintSpaces(), true);
        });
        if ($collect->isEmpty()) {
            return false;
        }
        if (isset($product, $product->print_spaces)) {
            $productPrintSpaces = json_decode($product->print_spaces, false, 512, JSON_THROW_ON_ERROR);
            foreach ($productPrintSpaces as $printSpace) {
                $printSpace = (array)$printSpace;
                if (isset($printSpace['name']) && in_array($printSpace['name'], $defaultPrintSpace, true)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param bool $limit
     * @return array
     */
    public static function getPendingFulfillmentOrderIds(bool $limit = true): array
    {
        $limitation_setting = SystemConfig::getCustomConfig('fulfillment_limitation_post_orders');
        $limitRegular = 10;
        $limitExpress = 5;
        $limitFulfillment = 10;
        if ($limitation_setting) {
            $value = $limitation_setting->json_data;
            $limitation = json_decode($value, true);
            $limitRegular = data_get($limitation, 'regular', 10);
            $limitExpress = data_get($limitation, 'express', 5);
            $limitFulfillment = data_get($limitation, 'fulfillment', 10);
            $limitRegular = $limitRegular % 2 === 0 ? $limitRegular : $limitRegular + 1;
        }
        // Get list of suppliers that are not using API
        $supplier_ids = suppliers()->filter(fn($item) => !is_supplier_support_api($item))->pluck('supplier_id')->toArray();
        $query = Order::query()
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->where('fraud_status', OrderFraudStatus::TRUSTED)
            ->whereIn('tm_status', [TradeMarkStatusEnum::UNVERIFIED, TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::FLAGGED])
            ->where('updated_at', '<=', now()->subHour())
            ->where('address_verified', '!=', OrderAddressVerifiedEnum::INVALID)
            ->orderBy('paid_at')
            ->whereHas('order_products', function ($q) use ($supplier_ids) {
                return $q->where('fulfill_status', OrderProductFulfillStatus::UNFULFILLED)
                    ->where('supplier_id', '>', 0)
                    ->whereNotNull('supplier_id')
                    ->when(!empty($supplier_ids), function ($q) use ($supplier_ids) {
                        return $q->whereNotIn('supplier_id', $supplier_ids);
                    })
                    ->whereIn('tm_status', [TradeMarkStatusEnum::UNVERIFIED, TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::FLAGGED]);
            })
            ->filterFulfill([
                OrderStatus::PROCESSING,
            ], [
                OrderFulfillStatus::PROCESSING,
                OrderFulfillStatus::UNFULFILLED,
            ]);

        $regular_order_ids = $query->clone()
            ->where('paid_at', '<=', now()->subHours(24))
            ->where('type', OrderTypeEnum::REGULAR)
            ->when($limit, function ($q) use ($limitRegular) {
                return $q->limit($limitRegular / 2);
            })
            ->pluck('id')
            ->toArray();

        $custom_order_ids = $query->clone()
            ->where('paid_at', '<=', now()->subHours(24))
            ->where('type', OrderTypeEnum::CUSTOM)
            ->where('fulfill_fee_paid', '>', 0)
            ->when($limit, function ($q) use ($limitRegular) {
                return $q->limit($limitRegular / 2);
            })
            ->pluck('id')
            ->toArray();

        $express_order_ids = $query->clone()
            ->where('paid_at', '<=', now()->subHour())
            ->where('shipping_method', ShippingMethodEnum::EXPRESS)
            ->whereIn('type', [
                OrderTypeEnum::REGULAR,
                OrderTypeEnum::CUSTOM,
            ])
            ->when($limit, function ($q) use ($limitExpress) {
                return $q->limit($limitExpress);
            })
            ->pluck('id')
            ->toArray();

        $fulfillment_order_ids = $query->clone()
            ->whereIn('type', [
                OrderTypeEnum::FULFILLMENT,
                OrderTypeEnum::FBA
            ])
            ->when($limit, function ($q) use ($limitFulfillment) {
                return $q->limit($limitFulfillment);
            })
            ->pluck('id')
            ->toArray();

        return array_unique(array_merge($regular_order_ids, $custom_order_ids, $express_order_ids, $fulfillment_order_ids));
    }

    /**
     * @param array $rows
     * @return array
     */
    public static function handleSupplierOptionMapping(array $rows): array
    {
        $productOptionMapping = [];
        foreach ($rows as $row) {
            if (!isset($row['product_sku']) ||
                !isset($row['optionssize']) ||
                !isset($row['size_of_supplier'])
            ) {
                continue;
            }
            $productOptionMapping[$row['product_sku']] [ProductOptionEnum::SIZE] [] = [
                $row['optionssize'] => $row['size_of_supplier']
            ];
        }

        return $productOptionMapping;
    }

    /**
     * @param array $supplierOptionMapping
     * @param $supplierId
     * @return void
     * @throws \JsonException
     */
    public static function addOptionMappingToAttributes(array $supplierOptionMapping, $supplierId): void
    {
        foreach ($supplierOptionMapping as $productId => $data) {
            $product = Product::query()->where('supplier_id', $supplierId)->where('id', $productId)->first();
            if (!isset($product)) {
                continue;
            }
            $product->attributes = json_encode([
                'supplier_option_mapping' => $data
            ], JSON_THROW_ON_ERROR);
            $product->save();
        }
    }

    public static function getNeedDesignTags(&$orders, $isPaginate = true)
    {
        $sellerIds = array_unique($orders->pluck('seller_id')->toArray());
        $productIds = array_unique($orders->pluck('order_products')->flatten()->pluck('product_id')->toArray());
        $orderProductIds = array_unique($orders->pluck('order_products')->flatten()->pluck('id')->toArray());

        $sellersQuery = User::query()->whereIn('id', $sellerIds);
        $files = collect();
        $sellersQuery->get()
            ->groupBy('db_connection')
            ->each(function ($sellerGroupBy, $dbConnection) use (&$files, $productIds) {
                $files = $files->merge(File::query()
                    ->on($dbConnection)
                    ->whereIn('seller_id', $sellerGroupBy->pluck('id'))
                    ->where('product_id', $productIds)
                    ->where('type', FileTypeEnum::DESIGN)
                    ->where('status', FileStatusEnum::ACTIVE)
                    ->groupBy('seller_id')
                    ->get());
            });
        $designs = Design::query()
            ->whereIn('order_product_id', $orderProductIds)
            ->where('status', DesignStatusEnum::ACTIVE)
            ->get();
        if ($isPaginate) {
            $orders->getCollection()->transform(
                function ($order) use ($files, $designs) {
                    $order->order_products->transform(
                        function ($orderProduct) use ($files, $designs) {
                            $orderProduct->queryDesignButNotUploadedDesign($orderProduct, NeedDesignTagsEnum::UPLOADED_DESIGN_BUT_NOT_APPROVED, $files, $designs);
                            $orderProduct->queryDesignButNotUploadedDesign($orderProduct, NeedDesignTagsEnum::DESIGNING_BUT_NOT_UPLOADED_DESIGN, $files, $designs);
                            return $orderProduct;
                        }
                    );
                    return $order;
                }
            );
        } else {
            foreach ($orders as &$order) {
                $order->order_products->transform(
                    function ($orderProduct) use ($files, $designs) {
                        $orderProduct->queryDesignButNotUploadedDesign($orderProduct, NeedDesignTagsEnum::UPLOADED_DESIGN_BUT_NOT_APPROVED, $files, $designs);
                        $orderProduct->queryDesignButNotUploadedDesign($orderProduct, NeedDesignTagsEnum::DESIGNING_BUT_NOT_UPLOADED_DESIGN, $files, $designs);
                        return $orderProduct;
                    }
                );
            }
        }

    }

    public static function getNoRenderedOrder(&$orderQuery)
    {
        $orderData = (clone $orderQuery)->get();
        $sellerIdsOnOrders = array_unique($orderData->pluck('seller_id')->toArray());
        $productIds = [];
        $orderData->map(function ($order) use (&$productIds) {
            $productIds = array_merge($productIds, $order->order_products->pluck('product_id')->toArray());
        });
        $productIdsWithoutFileUrl2 = collect();
        User::query()
            ->whereIn('id', $sellerIdsOnOrders)
            ->where('sharding_status', UserShardingStatusEnum::COMPLETED)
            ->where('db_connection', 'like', '%mysql_%')
            ->get()
            ->groupBy('db_connection')
            ->each(function ($sellerGroupBy, $dbConnection) use (&$productIdsWithoutFileUrl2, $productIds) {
                $sellerIds = $sellerGroupBy->pluck('id')->toArray();
                $validProductIds = Product::query()
                    ->on($dbConnection)
                    ->from('product')
                    ->select('id')
                    ->whereIn('seller_id', $sellerIds)
                    ->whereIn('id', $productIds)
                    ->where('status', FileStatusEnum::ACTIVE)
                    ->whereNotExists(function ($query) use ($sellerIds, $productIds) {
                        $query->select(DB::raw(1))
                            ->from('file as f')
                            ->whereIn('f.seller_id', $sellerIds)
                            ->whereIn('f.product_id', $productIds)
                            ->where('f.type', FileTypeEnum::DESIGN)
                            ->where('f.status', FileStatusEnum::ACTIVE)
                            ->whereNotNull('f.file_url_2')
                            ->whereRaw('f.product_id = product.id');
                    })
                    ->groupBy('id')
                    ->pluck('id');
                $productIdsWithoutFileUrl2 = $productIdsWithoutFileUrl2->merge($validProductIds);
            });

        if ($productIdsWithoutFileUrl2->isNotEmpty()) {
            $orderQuery
                ->with(['order_products' => function ($q) use ($productIdsWithoutFileUrl2) {
                    $q->whereIn('product_id', $productIdsWithoutFileUrl2);
                }])
                ->whereHas('order_products', function ($q) use ($productIdsWithoutFileUrl2) {
                    $q->whereIn('product_id', $productIdsWithoutFileUrl2);
                });
        }
        $orderQuery
            ->whereBetween('paid_at', [now()->subHours(24)->toDateTimeString(), now()->subHours(1)->toDateTimeString()])
            ->where('type', '!=', OrderTypeEnum::FULFILLMENT);
    }

    /**
     * @param $designJson
     * @param bool $toJson
     * @return array
     */
    public static function updateUrlOnDesignJson($designJson, bool $toJson = true): array
    {
        $isChanged = false;
        try {
            if (Str::isJson($designJson)) {
                $designJson = json_decode($designJson, true, 512, JSON_THROW_ON_ERROR);
            } else if (is_object($designJson)) {
                $designJson = json_decode(json_encode($designJson, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR);
            }
            $objects = data_get($designJson, 'objects', []);
            if (count($objects) > 0) {
                foreach ($objects as &$object) {
                    if (isset($object['fontUrl']) && str_starts_with($object['fontUrl'], 'http') && str_contains($object['fontUrl'], '.amazonaws.com')) {
                        $object['fontUrl'] = preg_replace('/^https?:\/\/.*?amazonaws\.com\//', cdnUrl() . '/', $object['fontUrl']);
                        $isChanged = true;
                    }
                }
                unset($object);
                data_set($designJson, 'objects', $objects);
            }
            return [
                $toJson ? json_encode($designJson, JSON_THROW_ON_ERROR) : $designJson,
                $isChanged
            ];
        } catch (\Throwable $e) {
            logException($e);
            return [
                $designJson,
                $isChanged
            ];
        }
    }

    /**
     * @param $orderProductId
     * @param $orderId
     * @param $productId
     * @param $size
     * @param $color
     * @param bool $fullPath
     * @param bool $isSupplier
     * @param OrderProduct|null $orderProduct
     * @param bool $isSeller
     * @return array
     * @throws Throwable
     */
    public static function getOrderProductFiles($orderProductId, $orderId, $productId, $size, $color, bool $fullPath = false, bool $isSupplier = false, ?OrderProduct $orderProduct = null, bool $isSeller = false): array
    {
        $orderProduct ??= OrderProduct::query()
            ->select(['id', 'seller_id', 'thumb_url', 'supplier_id', 'personalized', 'template_id', 'campaign_id', 'options', 'full_printed', 'custom_print_space', 'campaign_type', 'product_id'])
            ->with('seller:tags')
            ->find($orderProductId);
        if (is_null($orderProduct)) {
            return [collect(), collect()];
        }
        $full_printed = $orderProduct->full_printed;
        $isSupplierHasApi = is_supplier_support_api($orderProduct->supplier_id);
        if (!$isSupplier && !$isSupplierHasApi) {
            $isSupplier = true;
        }
        $order = Order::query()->with('seller')->find($orderId);
        if (is_null($order)) {
            return [collect(), collect()];
        }
        $isDesignBySenPrints = $orderProduct->isDesignBySenPrints();
        $designs = Design::query()
            ->select(['id', 'file_url', 'type', 'print_space', 'design_json'])
            ->where(['order_id' => $orderId, 'type' => DesignTypeEnum::PRINT])
            ->when($orderProductId, function ($q) use ($orderProductId, $isSeller, $isDesignBySenPrints) {
                $q->when($isSeller && $isDesignBySenPrints, function ($q) use ($orderProductId) {
                    $q->where('order_product_id', '!=', $orderProductId);
                }, function ($q) use ($orderProductId) {
                    $q->where('order_product_id', $orderProductId);
                });
            })
            ->orderBy('print_space')
            ->get();
        $files = collect();
        $isFulfillOrder = OrderTypeEnum::isFulfillOrder($order->type);
        $seller = $order->seller;
        if (empty($orderProduct->custom_print_space) || $designs->isEmpty()) {
            $files = File::query()
                ->when(!$isFulfillOrder, fn($q) => $q->onSellerConnection($seller))
                ->select(['id', 'file_url', 'file_url_2', 'type', 'print_space', 'design_json', 'seller_id'])
                ->where(function ($q) use ($orderProductId, $productId) {
                    if (!is_null($orderProductId)) {
                        $q->orWhere('order_product_id', $orderProductId);
                    }
                    if (!is_null($productId)) {
                        $q->orWhere('product_id', $productId);
                    }
                })
                ->when($isSeller && $isDesignBySenPrints, function ($q) use ($orderProductId, $productId) {
                    if (!is_null($orderProductId)) {
                        $q->orWhere('order_product_id', '!=', $orderProductId);
                    }
                    if (!is_null($productId)) {
                        $q->orWhere('product_id', '!=', $productId);
                    }
                })
                ->where('type', FileTypeEnum::DESIGN)
                ->where('option', FileRenderType::PRINT)
                ->when(!empty($size) && $orderProduct->isFullPrintedType(), function ($q) use ($size) {
                    $q->where(fn($q) => $q->whereRaw("INSTR('$size', `print_space`)")->orWhere('print_space', 'default'));
                })
                ->orderBy('print_space')
                ->get();
        }

        $campaign = Campaign::query()->onSellerConnection($seller)->firstWhere('id', $orderProduct->campaign_id);
        $expressCampDesign = null;
        if ($campaign && $campaign->product_type === ProductType::CAMPAIGN_EXPRESS) {
            $expressCampDesign = File::query()->onSellerConnection($orderProduct->seller)
                ->select(['id', 'file_url', 'file_url_2', 'type', 'print_space'])
                ->where(['campaign_id' => $orderProduct->campaign_id, 'option' => FileRenderType::PRINT, 'type' => FileTypeEnum::DESIGN])
                ->first();
        }
        // remove all duplicate file same print space with design
        if ($files->isNotEmpty()) {
            $files = $files->filter(fn($file) => is_null($designs->firstWhere('print_space', $file->print_space)));
        }
        $files = $designs->concat($files);
        if (!empty($expressCampDesign)) {
            $files->push($expressCampDesign);
        }
        $designMappings = null;
        $mugsProductId = Template::getAndCacheProductIdByCategory(ProductCategoryEnum::MUGS);
        if ($fullPath || $isSupplier) {
            $designMappings = ProductDesignMapping::query()
                ->where(function ($q) use ($orderProduct) {
                    $q->orWhere('product_id', $orderProduct->template_id);
                    $q->orWhere('product_id', 0);
                })
                ->where('supplier_id', $orderProduct->supplier_id)
                ->orderByDesc('product_id')
                ->orderByDesc('print_space')
                ->get();
        }
        if (!empty($mugsProductId) && !empty($orderProduct->options) && $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION && in_array((int)$orderProduct->template_id, $mugsProductId, true)) {
            $productOptions = json_decode($orderProduct->options, true, 512, JSON_THROW_ON_ERROR);
            if (isset($productOptions['size'])) {
                $files = $files->filter(fn($file) => $file->print_space === $productOptions['size'])->values();
            }
        }
        foreach ($files as $file) {
            $file->type = FileTypeEnum::DESIGN;
            $file->table = ($file instanceof Design) ? 'design' : 'file';
            if (self::shouldFallbackToFileUrl2($file, $orderProduct)) {
                $file->file_url_2 = $file->file_url;
            }
            try {
                if (($fullPath || $isSupplier) && isset($designMappings)) {
                    $designMapping = $designMappings->first(fn($designMapping) => $designMapping->print_space === $file->print_space);
                    if (!is_null($designMapping) && $full_printed === ProductPrintType::PRINT_2D) {
                        if ($orderProduct->supplier_id === SupplierEnum::GOOTEN && !empty($mugsProductId) && in_array((int)$orderProduct->template_id, $mugsProductId, true)) {
                            $designMapping->color_space = ColorSpaceEnum::RGB;
                        }
                        if ($orderProduct->supplier_id === SupplierEnum::QTCO) {
                            $designMapping->dpi = 200;
                            $designMapping->noxmp = true;
                        }
                        $file->design_url = scaleDesignUrl($designMapping, self::cleanUrlPath($file->design_to_print_url));
                        $file->is_design_mapping = true;
                    } else if ($orderProduct->supplier_id === SupplierEnum::MWW) {
                        $file->design_url = jpgUrl($file->design_to_print_url);
                    } else {
                        $file->design_url = s3Url($file->design_to_print_url);
                    }
                } else {
                    $file->design_url = $file->design_to_print_url;
                }
            } catch (Throwable $e) {
                if ($fullPath || $isSupplier) {
                    throw $e;
                }
            }
        }
        $mockups = File::query()
            ->when(!$isFulfillOrder, fn($q) => $q->onSellerConnection($seller))
            ->select(['id', 'file_url', 'file_url_2', 'print_space'])
            ->where(function ($q) use ($orderProductId, $productId) {
                return $q->orWhere('order_product_id', $orderProductId)->when(!is_null($productId), function ($q) use ($productId) {
                    return $q->orWhere('product_id', $productId);
                });
            })
            ->where('type', FileTypeEnum::IMAGE)
            ->where(function ($q) use ($color) {
                return $q->where('option', $color)->orWhereNull('option');
            })
            ->groupBy('print_space')
            ->orderBy('print_space')
            ->orderBy('position')
            ->get();

        if (!$fullPath) {
            $templateId = $orderProduct->template_id;
            $printSpace = cacheAlt()->remember(CacheKeys::getTemplateProductByTemplateId($templateId), CacheKeys::CACHE_30D, function () use ($templateId) {
                return Template::query()->whereKey($templateId)->first();
            });
            $printSpacesTemplate = json_decode($printSpace->print_spaces ?? '[]', true, 512, JSON_THROW_ON_ERROR);
        }

        $sellerTags = $orderProduct->seller?->tags;
        foreach ($files as $index => $file) {
            if (isset($sellerTags) && !str_contains($sellerTags, 'sleeve printspace') && in_array($file?->print_space, PrintSpaceEnum::additionalPrintSpaces(), true)) {
                unset($files[$index]);
                continue;
            }

            $mockup = null;
            foreach ($mockups as $idx => $each) {
                if ($each->print_space === $file->print_space) {
                    $mockup = $each;
                    unset($mockups[$idx]);
                    break;
                }
            }
            if (!empty($orderProduct->custom_print_space)) {
                $file->print_space = $orderProduct->custom_print_space;
            }
            // get print space info to validate design size
            if (!$fullPath) {
                $printSpace = !empty($printSpacesTemplate) ? $printSpacesTemplate[0] : null;
                if (isset($printSpacesTemplate)) {
                    foreach ($printSpacesTemplate as $each) {
                        if ($each['name'] === $file->print_space) {
                            $printSpace = $each;
                            break;
                        }
                    }
                }
                $file->printSpace = adjustPrintSpace($printSpace);
            }

            if (is_null($mockup) || ($orderProduct->personalized && $file->table === 'design') || !empty($orderProduct->custom_print_space)) {
                if (!is_null($mockup) && $orderProduct->personalized && $orderProduct->full_printed === ProductPrintType::EMBROIDERY) {
                    $mockupUrl = $mockup->file_url;
                    if (!$mockupUrl) {
                        $mockupUrl = File::query()
                            ->when(!$isFulfillOrder, fn($q) => $q->onSellerConnection($seller))
                            ->select('file_url')
                            ->when($orderProduct->campaign_id, fn($q) => $q->where('campaign_id', $orderProduct->campaign_id))
                            ->where('seller_id', $orderProduct->seller_id)
                            ->where('type', FileTypeEnum::IMAGE)
                            ->where('print_space', $file->print_space)
                            ->where('type_detail', 'custom')
                            ->where(function ($q) use ($color) {
                                return $q->where('option', $color)->orWhereNull('option');
                            })
                            ->groupBy('print_space')
                            ->orderBy('position')
                            ->value('file_url');
                        if (!$mockupUrl) {
                            $mockupUrl = File::query()
                                ->when(!$isFulfillOrder, fn($q) => $q->onSellerConnection($seller))
                                ->select('file_url')
                                ->when($orderProduct->campaign_id, fn($q) => $q->where('campaign_id', $orderProduct->campaign_id))
                                ->where('seller_id', $orderProduct->seller_id)
                                ->where('type', FileTypeEnum::IMAGE)
                                ->where('type_detail', 'custom')
                                ->where(function ($q) use ($color) {
                                    return $q->where('option', $color)->orWhereNull('option');
                                })
                                ->groupBy('print_space')
                                ->orderBy('position')
                                ->value('file_url');
                        }
                    }
                } else {
                    $mockupUrl = $orderProduct->thumb_url;
                    if (empty($mockupUrl)) {
                        $mockupUrl = $file->design_to_print_url;
                    }
                }
            } else {
                $mockupUrl = imgColorUrl($mockup->mockup_to_print_url, $color);
            }
            $file->mockup_url = ($fullPath || $isSupplier) ? imgUrl($mockupUrl) : $mockupUrl;
            $file->has_design_json = false;
            if (!empty($file->design_json)) {
                $file->has_design_json = true;
            }
            unset($file->design_json);
        }

        foreach ($mockups as $index => &$mockup) {
            if (isset($sellerTags) && !str_contains($sellerTags, 'sleeve printspace') && in_array($mockup?->print_space, PrintSpaceEnum::additionalPrintSpaces(), true)) {
                unset($mockups[$index]);
                continue;
            }
            $mockupUrl = imgColorUrl($mockup->mockup_to_print_url, $color);
            $mockup->mockup_url = ($fullPath || $isSupplier) ? imgUrl($mockupUrl) : $mockupUrl;
        }

        return [$files->values(), $mockups->values()];
    }

    /**
     * @param $file
     * @param $orderProduct
     * @return bool
     */
    private static function shouldFallbackToFileUrl2($file, $orderProduct)
    {
        $campaign = $orderProduct?->campaign;
        $full_printed = $orderProduct->full_printed;
        return empty($file->file_url_2) && (
            $full_printed === ProductPrintType::PRINT_3D_FULL ||
            ($full_printed === ProductPrintType::PRINT_2D && $orderProduct->personalized === PersonalizedType::NONE) ||
            (!is_null($campaign) && in_array($campaign->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true)) ||
            (!is_null($campaign) && $campaign->system_type === ProductSystemTypeEnum::AOP && $full_printed === ProductPrintType::AOP) ||
            ($full_printed === ProductPrintType::EMBROIDERY && $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION)
        );
    }

    /**
     * @param string $filePath
     * @return string
     */
    private static function cleanUrlPath(string $filePath): string
    {
        if (!str_starts_with($filePath, 'http')) {
            return $filePath;
        }
        $patterns = [
            '.amazonaws.com' => '/^https?:\/\/.*?amazonaws\.com\//',
            '.cloudimgs.net' => '/^https?:\/\/.*?cloudimgs\.net\//',
            '.senprints.xyz' => '/^https?:\/\/.*?senprints\.xyz\//',
        ];
        foreach ($patterns as $domain => $pattern) {
            if (str_contains($filePath, $domain)) {
                return preg_replace($pattern, '', $filePath);
            }
        }
        return preg_replace('/^https?:\/\/(www\.)?/', 'url/', $filePath);
    }
}
