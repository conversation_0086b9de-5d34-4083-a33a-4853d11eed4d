<?php

namespace App\Services;

use App\Enums\DiscordChannel;
use App\Models\ProcessLocks;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;

class ProcessLockService
{
    /**
     * @param $key
     * @return bool
     */
    public function has($key): bool
    {
        return ProcessLocks::query()->where('key', $key)->exists();
    }

    /**
     * @param $key
     * @return ProcessLocks|null
     */
    public function get($key): ?ProcessLocks
    {
        $lock = ProcessLocks::query()->where('key', $key)->first();
        if (is_null($lock)) {
            return null;
        }
        if (!is_null($lock->released_at) && $lock->released_at->lte(now())) {
            $this->forgetIfExpired($key);
            return null;
        }
        return $lock;
    }

    /**
     * @param $key
     * @param int $max_process
     * @param null $ttl
     * @return bool
     */
    public function put($key, int $max_process = 1, $ttl = null): bool
    {
        $released_at = null;
        if ($ttl) {
            $released_at = now()->addSeconds($ttl);
        }
        $upsert = [
            'key' => $key,
            'current_process' => 0,
            'max_process' => $max_process,
            'released_at' => $released_at,
        ];
        try {
            return ProcessLocks::query()->upsert($upsert, 'key') > 0;
        } catch (QueryException) {
            return false;
        }
    }

    /**
     * @param $key
     * @param int $max_process
     * @param null $ttl
     * @return ProcessLocks|null
     */
    public function add($key, int $max_process = 1, $ttl = null): ?ProcessLocks
    {
        $lock = $this->get($key);
        if (!is_null($lock)) {
            return $lock;
        }
        $released_at = null;
        if ($ttl) {
            $released_at = now()->addSeconds($ttl);
        }
        try {
            return ProcessLocks::query()->create([
                'key' => $key,
                'current_process' => 0,
                'max_process' => $max_process,
                'released_at' => $released_at,
            ]);
        } catch (QueryException) {}
        return null;
    }

    /**
     * @param $key
     * @param $callback
     * @param int $ttl
     * @param int $max_process
     * @return void
     */
    public function handle($key, $callback, int $max_process = 1, int $ttl = 300): void
    {
        try {
            $allowed = $this->lock($key, $max_process, $ttl);
            if (!$allowed) {
                logToDiscord('Process is locked, key: ' . $key, app()->isProduction() ? DiscordChannel::LOCK_PROCESS_CHANNEL : DiscordChannel::DEV_LOGS);
                graylogInfo('Process is locked, key: ' . $key, [
                    'category' => 'lock_process',
                    'key' => $key,
                ]);
                return;
            }
            if ($callback && is_callable($callback)) {
                $callback();
            }
            $this->unlock($key);
        } catch (\Throwable $e) {
            logException($e, 'ProcessLockService@handle -> ' . $key, app()->isProduction() ? DiscordChannel::LOCK_PROCESS_CHANNEL : DiscordChannel::DEV_LOGS, true);
        }
    }

    /**
     * @param $key
     * @param int $max_process
     * @param int $ttl
     * @return bool
     */
    private function lock($key, int $max_process = 1, int $ttl = 300): bool
    {
        DB::beginTransaction();
        $lock = true;
        $process = ProcessLocks::query()->where('key', $key)->lockForUpdate()->first();
        if (is_null($process)) {
            $process = $this->add($key, $max_process, $ttl);
            if (is_null($process)) {
                DB::commit();
                return false;
            }
        }
        if (!is_null($process->released_at) && $process->released_at->lte(now())) {
            $process->current_process = 0;
            if ($ttl) {
                $process->released_at = now()->addSeconds($ttl);
            }
        }
        if ($process->current_process >= $max_process) {
            $lock = false;
        } else {
            $process->current_process++;
            if ($ttl) {
                $process->released_at = now()->addSeconds($ttl);
            }
        }
        $process->save();
        DB::commit();
        return $lock;
    }

    /**
     * @param $key
     * @return void
     */
    private function unlock($key): void
    {
        DB::beginTransaction();
        $process = ProcessLocks::query()->where('key', $key)->lockForUpdate()->first();
        if (is_null($process)) {
            DB::commit();
            return;
        }
        $process->current_process--;
        if ($process->current_process < 0) {
            $process->current_process = 0;
        }
        $process->last_run_at = now();
        $process->save();
        DB::commit();
    }

    /**
     * @param $key
     * @param int $max_process
     * @return bool
     */
    public function forever($key, int $max_process = 1): bool
    {
        return $this->put($key, 1, $max_process);
    }

    /**
     * @param $key
     * @return bool
     */
    public function release($key): bool
    {
        return ProcessLocks::query()->where('key', $key)->delete();
    }

    /**
     * @param $key
     * @return bool
     */
    public function forgetIfExpired($key): bool
    {
        return ProcessLocks::query()->where('key', $key)->where('released_at', '<', now())->delete();
    }

    /**
     * @return bool
     */
    public function flushExpired(): bool
    {
        return ProcessLocks::query()->whereNotNull('released_at')->where('released_at', '<', now()->subMinutes(5))->delete();
    }
}
