<?php

namespace App\Services;

use App\Contracts\ProductTemplateServiceContract;
use App\Data\Product\PendingFileData;
use App\Enums\ProductStatus;
use App\Helpers\DesignJsonHelper;
use App\Helpers\ProductAssetsHelper;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductDesignMapping;
use App\Models\ProductFulfillMapping;
use App\Models\ProductPoint;
use App\Models\ProductPromotion;
use App\Models\ProductSizeGuide;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use Closure;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\InvalidCastException;
use Illuminate\Database\Eloquent\MassAssignmentException;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use InvalidArgumentException;
use League\Flysystem\FilesystemException;
use League\Flysystem\UnableToCheckExistence;
use Spatie\LaravelData\Exceptions\CannotCreateData;
use Spatie\LaravelData\Exceptions\CannotSetComputedValue;
use TypeError;
use ValueError;
use function DeepCopy\deep_copy;

class ProductTemplateService implements ProductTemplateServiceContract
{
    public ?Product $productTemplate;
    public ?Product $newProductTemplate;
    public ?ProductCategory $category;
    /** @var \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductVariant> $variants */
    public $variants;
    /** @var \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $mockups */
    public $mockups;
    /** @var \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $videos */
    public $videos;
    /** @var \Illuminate\Support\Collection<int, PendingFileData> $pendingFiles */
    public Collection $pendingFiles;
    public string $sku;

    public function __construct()
    {
        $this->pendingFiles = new Collection();
    }

    public function getProduct() {
        return $this->productTemplate;
    }

    public function getNewProduct() {
        return $this->newProductTemplate;
    }

    public function getPendingFiles() {
        return $this->pendingFiles;
    }

    public function getNewProductTemplate() {
        return $this->newProductTemplate;
    }

    public function getVideos() {
        return $this->videos;
    }

    public function getMockups() {
        return $this->mockups;
    }

    public function getVariants() {
        return $this->variants;
    }

    public function getThumbnails() {
        return $this->productTemplate->thumbnails;
    }

    /**
     * Get product template
     *
     * @param mixed $productId
     * @return $this
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function getProductTemplate($productId) {
        $this->productTemplate = Product::where('id', $productId)
            ->with([
                'mockups' => function ($query) {
                    return $query->withoutGlobalScope('getActive');
                },
                'category',
                'videos' => function ($query) {
                    return $query->withoutGlobalScope('getActive');
                },
            ])
            ->first();
        return $this;
    }

    /**
     * Get variants
     *
     * @return $this
     */
    public function withVariants() {
        $this->variants = $this->productTemplate->variants;
        return $this;
    }

    /**
     * Get mockups
     *
     * @return $this
     */
    public function withMockups() {
        $this->mockups = $this->productTemplate->mockups;
        return $this;
    }

    /**
     * Get videos
     *
     * @return $this
     */
    public function withVideos() {
        $this->videos = $this->productTemplate->videos;
        return $this;
    }

    public function withSku(string $sku)
    {
        $this->sku = $sku;
        return $this;
    }

    public function cloneProduct() {
        if (empty($this->sku)) {
            throw new \Exception('SKU is empty');
        }
        $this->newProductTemplate = $this->productTemplate->replicate()->fill([
            'name' => $this->productTemplate->name . ' (Copy)',
            'sku' => $this->sku,
            'status' => ProductStatus::DRAFT,
            'sync_status' => Product::SYNC_DATA_STATS_ENABLED
        ]);
        $this->newProductTemplate->save();
        return $this;
    }

    public function deleteNewProduct()
    {
        $this->newProductTemplate->forceDelete();
    }

    /**
     * Clone model
     *
     * @param mixed $name
     * @param array $conditions
     * @param array $fills
     * @param Closure|null $closure
     * @return bool
     * @throws BindingResolutionException
     */
    public function cloneModel($name, array $conditions = [], array $fills = [], Closure $closure = null)
    {
        $model = app($name);
        $query = $model::query();
        if (is_array($conditions)) {
            foreach ($conditions as $field => $value) {
                $query->where($field, $value);
            }
        }
        $records = $query->get();
        if ($records->isEmpty()) {
            graylogInfo('No records to clone', [
                'category' => 'product_template_service',
                'model' => $name,
                'conditions' => $conditions,
            ]);
            return true;
        }
        $newRecords = [];
        foreach ($records as $record) {
            $newRecord = deep_copy($record);
            if ($newRecord->id) {
                $newRecord->id = null;
            }
            foreach ($fills as $field => $value) {
                $newRecord->$field = $value;
            }
            if ($closure) {
                $newRecord = $closure($newRecord);
            }
            $newRecords[] = $newRecord->toArray();
        }
        if (empty($newRecords)) {
            graylogError('Failed to clone records', [
                'category' => 'product_template_service',
                'model' => $name,
                'conditions' => $conditions,
            ]);
            return true;
        }
        return $model::insert($newRecords);
    }

    public function cloneShippingRules()
    {
        if (!$this->newProductTemplate->id || !$this->productTemplate->id) {
            graylogInfo('New product template or product template is not created yet (shipping rules)', [
                'category' => 'product_template_service',
            ]);
            return false;
        }

        $result = $this->cloneModel(ShippingRule::class, [
            'product_id' => $this->productTemplate->id,
        ], [
            'product_id' => $this->newProductTemplate->id,
        ]);
        if (!$result) {
            graylogError('Failed to clone shipping rules', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    public function cloneCategory()
    {
        if (!$this->newProductTemplate->id || !$this->productTemplate->id) {
            graylogInfo('New product template or product template is not created yet (category)', [
                'category' => 'product_template_service',
            ]);
            return false;
        }
        $result = $this->cloneModel(ProductCategory::class, [
            'product_id' => $this->productTemplate->id,
        ], [
            'product_id' => $this->newProductTemplate->id,
        ]);
        if (!$result) {
            graylogError('Failed to clone product category', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    /**
     * Clone variants
     *
     * @return bool
     * @throws Exception
     */
    public function cloneVariants() {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (variants)');
        }
        $result = $this->cloneModel(ProductVariant::class, [
            'product_id' => $this->productTemplate->id,
            'campaign_id' => null,
        ], [
            'product_id' => $this->newProductTemplate->id,
        ], function ($newVariant) {
            $newVariant->sku = $this->sku . '-' . $newVariant->variant_key;
            $newVariant->sku_bak = $this->sku . '-' . $newVariant->variant_key;
            return $newVariant;
        });
        if (!$result) {
            graylogError('Failed to clone variants', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    public function cloneAttributes()
    {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (attributes)');
        }
        $attributes = $this->productTemplate->attributes;
        if (empty($attributes) || !ProductAssetsHelper::isJson($attributes)) {
            return $this;
        }
        $data = json_decode($attributes, true);
        if (empty($data)) {
            return $this;
        }
        $filePaths = DesignJsonHelper::extractFilePaths($data);
        if (empty($filePaths)) {
            return $this;
        }
        foreach ($filePaths as $filePath) {
            $this->enqueuePendingFile(PendingFileData::from([
                'old_path' => $filePath,
                'new_path' => ProductAssetsHelper::replaceIdFilePath($filePath, $this->productTemplate->id, $this->newProductTemplate->id),
                'product_id' => $this->productTemplate->id,
                'file_id' => null,
            ]));
        }
        $newAttributes = DesignJsonHelper::replaceId($data, $this->productTemplate->id, $this->newProductTemplate->id);
        $this->newProductTemplate->attributes = json_encode($newAttributes, JSON_UNESCAPED_SLASHES);
        if (!$this->newProductTemplate->save()) {
            throw new \Exception('Failed to save new product template');
        }
        return $this;
    }

    /**
     * Clone video
     *
     * @param File $file
     * @return array
     * @throws Exception
     * @throws ValueError
     * @throws TypeError
     * @throws CannotCreateData
     * @throws CannotSetComputedValue
     */
    public function cloneVideo(File $file) {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (videos)');
        }
        $productId = $this->productTemplate->id;
        $newProductId = $this->newProductTemplate->id;
        $fillData = [
            'product_id' => $newProductId
        ];
        $fields = ['file_url', 'file_url_2', 'option'];
        foreach ($fields as $field) {
            $newValue = $this->processField($file, $field, $productId, $newProductId);
            if ($newValue) {
                $fillData[$field] = $newValue;
            }
        }
        return $fillData;
    }

    /**
     * Clone mockup
     *
     * @param File $file
     * @return array
     * @throws Exception
     * @throws ValueError
     * @throws TypeError
     * @throws CannotCreateData
     * @throws CannotSetComputedValue
     * @throws InvalidArgumentException
     * @throws FilesystemException
     * @throws UnableToCheckExistence
     */
    public function cloneMockup(File $file) {
        if ($this->productTemplate->id === $this->newProductTemplate->id) {
            throw new \Exception('Product template and new product template have the same id');
        }
        $productId = $this->productTemplate->id;
        $newProductId = $this->newProductTemplate->id;
        $fillData = [
            'product_id' => $newProductId,
        ];
        $fields = ['file_url', 'file_url_2', 'design_json'];
        foreach ($fields as $field) {
            $newValue = $this->processField($file, $field, $productId, $newProductId);
            if ($newValue && is_string($newValue)) {
                $fillData[$field] = $newValue;
            }
        }
        return $fillData;
    }

    /**
     * Clone thumbnail
     *
     * @param File $file
     * @return array
     * @throws Exception
     * @throws ValueError
     * @throws TypeError
     * @throws CannotCreateData
     * @throws CannotSetComputedValue
     */
    public function cloneThumbnail(File $file)
    {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (thumbnail)');
        }
        $productId = $this->productTemplate->id;
        $newProductId = $this->newProductTemplate->id;
        $fillData = [
            'product_id' => $newProductId,
        ];
        $fields = ['file_url'];
        foreach ($fields as $field) {
            $newValue = $this->processField($file, $field, $productId, $newProductId);
            if ($newValue) {
                $fillData[$field] = $newValue;
            }
        }
        return $fillData;
    }

    /**
     * Clone design mapping
     *
     * @return bool
     * @throws Exception
     * @throws InvalidCastException
     */
    public function cloneDesignMapping() {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (design mapping)');
        }
        $result = $this->cloneModel(ProductDesignMapping::class, [
            'product_id' => $this->productTemplate->id
        ], [
            'product_id' => $this->newProductTemplate->id
        ]);
        if (!$result) {
            graylogError('Failed to clone design mapping', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    /**
     * Clone fulfill mapping
     *
     * @return bool
     * @throws Exception
     * @throws MassAssignmentException
     */
    public function cloneFulfillMapping()
    {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (fulfill mapping)');
        }
        $result = $this->cloneModel(ProductFulfillMapping::class, [
            'product_id' => $this->productTemplate->id
        ], [
            'product_id' => $this->newProductTemplate->id
        ]);

        if (!$result) {
            graylogError('Failed to clone fulfill mapping', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    /**
     * Clone product points
     *
     * @return bool
     * @throws Exception
     * @throws MassAssignmentException
     */
    public function cloneProductPoints()
    {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (product points)');
        }
        $result = $this->cloneModel(ProductPoint::class, [
            'product_id' => $this->productTemplate->id
        ], [
            'product_id' => $this->newProductTemplate->id
        ]);
        if (!$result) {
            graylogError('Failed to clone product points', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    /**
     * Clone product promotion
     *
     * @return mixed
     * @throws Exception
     * @throws MassAssignmentException
     */
    public function cloneProductPromotion()
    {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (product promotion)');
        }
        $result = $this->cloneModel(ProductPromotion::class, [
            'product_id' => $this->productTemplate->id
        ], [
            'id' => Str::uuid(),
            'product_id' => $this->newProductTemplate->id
        ]);
        if (!$result) {
            graylogError('Failed to clone product promotion', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    /**
     * Clone product size guide
     *
     * @return bool
     * @throws Exception
     * @throws MassAssignmentException
     */
    public function cloneProductSizeGuide()
    {
        if (!$this->newProductTemplate->id) {
            throw new \Exception('New product template is not created yet (product size guide)');
        }
        $result = $this->cloneModel(ProductSizeGuide::class, [
            'product_id' => $this->productTemplate->id
        ], [
            'product_id' => $this->newProductTemplate->id
        ]);
        if (!$result) {
            graylogError('Failed to clone product size guide', [
                'category' => 'product_template_service',
                'product_id' => $this->productTemplate->id,
                'new_product_id' => $this->newProductTemplate->id,
            ]);
            return false;
        }
        return true;
    }

    /**
     * Enqueue pending file
     *
     * @param PendingFileData $pendingFile
     * @return void
     */
    public function enqueuePendingFile(PendingFileData $pendingFile) {
        $this->pendingFiles->push($pendingFile);
    }

    /**
     * Delete folder
     *
     * @return bool
     * @throws FilesystemException
     */
    public function deleteFolder()
    {
        if (!$this->productTemplate->id || !$this->newProductTemplate->id) {
            graylogInfo('Product template or new product template is not created yet (delete folder)', [
                'category' => 'product_template_service',
            ]);
            return false;
        }
        $productId = $this->productTemplate->id;
        $newProductId = $this->newProductTemplate->id;
        try {
            // path format: p/{product_id}/
            $path = 'p/' . $newProductId;
            delete_directory_on_storage($path);
            return true;
        } catch (Exception $e) {
            graylogError('Failed to delete folder', [
                'category' => 'product_template_service',
                'product_id' => $productId,
                'new_product_id' => $newProductId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Copy pending files
     *
     * @return bool
     * @throws InvalidArgumentException
     * @throws FilesystemException
     * @throws UnableToCheckExistence
     * @throws Exception
     */
    public function copyPendingFiles() {
        $success = 0;
        $totalFiles = $this->pendingFiles->count();
        if ($totalFiles === 0) {
            graylogError('No pending files to copy', [
                'category' => 'product_template_service',
            ]);
            return false;
        }
        foreach ($this->pendingFiles as $pendingFile) {
            $oldPath = $pendingFile->old_path;
            $newPath = $pendingFile->new_path;
            if (is_null($oldPath) || is_null($newPath)) {
                graylogError('Old path or new path is null', [
                    'old_path' => $oldPath,
                    'new_path' => $newPath,
                    'category' => 'product_template_service',
                    'product_id' => $pendingFile->product_id,
                    'file_id' => $pendingFile->file_id,
                ]);
                continue;
            }
            if (!is_file_exists_on_storage($oldPath)) {
                graylogError('File is not on default storage', [
                    'old_path' => $oldPath,
                    'new_path' => $newPath,
                    'category' => 'product_template_service',
                    'product_id' => $pendingFile->product_id,
                    'file_id' => $pendingFile->file_id,
                ]);
                continue;
            }
            copy_file_on_storage($oldPath, $newPath);
            $success++;
        }
        return true;
    }

    /**
     * Process field
     *
     * @param File $file
     * @param mixed $field
     * @param mixed $productId
     * @param mixed $newProductId
     * @return null|string|false
     * @throws ValueError
     * @throws TypeError
     * @throws CannotCreateData
     * @throws CannotSetComputedValue
     */
    public function processField(File $file, $field, $productId, $newProductId)
    {
        $value = $file->$field;
        if (empty($value)) {
            return null;
        }
        if (ProductAssetsHelper::isJson($value)) {
            $jsonData = json_decode($value, true);
            if (empty($jsonData)) {
                return null;
            }
            $filePaths = DesignJsonHelper::extractFilePaths($jsonData);
            foreach ($filePaths as $filePath) {
                $this->enqueuePendingFile(PendingFileData::from([
                    'old_path' => $filePath,
                    'new_path' => ProductAssetsHelper::replaceIdFilePath($filePath, $productId, $newProductId),
                    'product_id' => $productId,
                    'file_id' => $file->id,
                ]));
            }
            $jsonData = DesignJsonHelper::replaceId($jsonData, $productId, $newProductId);
            $jsonData = json_encode($jsonData, JSON_UNESCAPED_SLASHES);
            return $jsonData;
        }
        if (ProductAssetsHelper::isAssetUrl($value)) {
            $fileUrl = trim($value);
            $filePath = DesignJsonHelper::extractFilePath($fileUrl);
            if (!$filePath) {
                return null;
            }
            $newFilePath = ProductAssetsHelper::replaceIdFilePath($filePath, $productId, $newProductId);
            $newFileUrl = ProductAssetsHelper::replaceIdFileUrl($fileUrl, $productId, $newProductId);
            if (!$newFileUrl) {
                return null;
            }
            $this->enqueuePendingFile(PendingFileData::from([
                'old_path' => $filePath,
                'new_path' => $newFilePath,
                'product_id' => $productId,
                'file_id' => $file->id,
            ]));
            return $newFileUrl;
        }
        if (ProductAssetsHelper::isAssetPath($value)) {
            $filePath = trim($value);
            $newFileUrl = ProductAssetsHelper::replaceIdFilePath($filePath, $productId, $newProductId);
            if (!$newFileUrl) {
                return null;
            }
            $this->enqueuePendingFile(PendingFileData::from([
                'old_path' => $value,
                'new_path' => $newFileUrl,
                'product_id' => $productId,
                'file_id' => $file->id,
            ]));
            return $newFileUrl;
        }
        return null;
    }
}
