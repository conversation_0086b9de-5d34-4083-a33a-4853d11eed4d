<?php

namespace App\Services;

use App\Enums\CustomEmailLogEnum;
use App\Enums\CustomEmailTemplateEnum;
use App\Mail\DefaultCustomTemplate;
use App\Models\CustomEmailLog;
use App\Models\CustomEmailTemplate;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class CustomEmailTemplateService
{
    public static function getListProcess($templateId) :array
    {
        return CustomEmailTemplate::query()
            ->select('id', 'process_id', 'start_sent_email_at', 'end_sent_email_at', 'sent_by')
            ->with('logs.order:id,order_number')
            ->where('default_template_id', $templateId)
            ->where('status', CustomEmailTemplateEnum::SAVED)
            ->where('process_id', '!=', null)
            ->get()
            ->toArray();
    }

    public static function send($id, $emails, $sellerIds) :void
    {
        $templateObj = CustomEmailTemplate::query()
            ->where('id', $id)
            ->firstOrFail()
            ->toArray();

        $variables = json_decode($templateObj['variables'], true);
        $templateObj['content'] = self::translateVariable($variables, $templateObj['content']);

        $users = User::query()
            ->select('id', 'email', 'name')
            ->whereIn('id', $sellerIds)
            ->orWhereIn('email', $emails)
            ->get()
            ->toArray();
        foreach ($users as $user) {
            $config = self::prepareConfig($user, $templateObj);
            sendEmail($config);
        }
    }

    public static function send2(string $templateId, array $orderIds) :bool
    {
        $chunk = false;
        if (empty($orderIds) && count($orderIds) === 0) {
            return false;
        }

        if (count($orderIds) > 100) {
            $chunk = true;
            $orderIds = array_chunk($orderIds, 30);
        }

        $processId = gen_unique_hash();
        $newTemplateId = self::updateTemplateForSend($templateId, $processId);
        if (!$newTemplateId) {
            return false;
        }

        if ($chunk) {
            $insertLogs = self::insertLogsByChunk($newTemplateId, $orderIds, $processId);
        } else {
            $insertLogs = self::insertLogs($newTemplateId, $orderIds, $processId);
        }

        if ($insertLogs) {
            return true;
        }

        return false;
    }

    private static function insertLogsByChunk(string $templateId, array $orderIds, $processId) :bool
    {
        try {
            //get email
            $data = [];

            foreach ($orderIds as $chunk) {
                //get email
                $data[] = Order::query()
                    ->select('id', 'customer_id', 'customer_email')
                    ->whereIn('id', $chunk)
                    ->get()
                    ->toArray();
            }

            //insert into custom_email_logs
            foreach ($data as $item1) {
                $insertData = [];
                if (!empty($item1)) {
                    foreach($item1 as $item2) {
                        $insertData[] = [
                            'id' => generateUUID(),
                            'process_id' => $processId,
                            'template_id' => $templateId,
                            'order_id' => Arr::get($item2, 'id'),
                            'email' => Arr::get($item2, 'customer_email'),
                            'user_id' => Arr::get($item2, 'customer_id'),
                            'status' => CustomEmailLogEnum::SCHEDULED,
                            'sent_at' => null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                    CustomEmailLog::query()->insert($insertData);
                }
            }

            return true;
        }catch (\Exception $e) {
            return false;
        }
    }

    private static function insertLogs(string $templateId, array $orderIds, $processId): bool
    {
        try {
            $data = Order::query()
                ->select('id', 'customer_id', 'customer_email')
                ->whereIn('id', $orderIds)
                ->get()
                ->toArray();

            //insert into custom_email_logs
            $insertData = [];
            if (!empty($data)) {
                foreach ($data as $item) {
                    $insertData[] = [
                        'id' => generateUUID(),
                        'process_id' => $processId,
                        'template_id' => $templateId,
                        'order_id' => Arr::get($item, 'id'),
                        'email' => Arr::get($item, 'customer_email'),
                        'user_id' => Arr::get($item, 'customer_id'),
                        'status' => CustomEmailLogEnum::SCHEDULED,
                        'sent_at' => null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
                CustomEmailLog::query()->insert($insertData);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    private static function updateTemplateForSend($templateId, $processId) :?string
    {
        try {
            DB::BeginTransaction();
            $template = CustomEmailTemplate::query()
                ->where('id', $templateId)
                ->where('status', CustomEmailTemplateEnum::IDLE)
                ->firstOrFail();
            $template->status = CustomEmailTemplateEnum::PROCESSING;

            if ($template->save()) {
                $templateNew = $template->replicate();
                $templateNew->status = CustomEmailTemplateEnum::SAVED;
                $templateNew->default_template_id = $template->id;
                $templateNew->process_id = $processId;
                $templateNew->start_sent_email_at = now();
                $templateNew->sent_by = currentUser()->getInfo()->name ?? 'unknown';
                $templateNew->save();
                DB::commit();
                return $templateNew->id;
            } else {
                DB::rollBack();
                return null;
            }
        }catch (\Exception $e) {
            DB::rollBack();
            return null;
        }
    }

    public static function prepareConfig($user, $templateObj)
    {
        $dataSendMailLog = [
            'sellerId' => $user['id'],
        ];

        return [
            'to' => $user['email'],
            'template' => 'default_custom_template',
            'data' => [
                'subject' => $templateObj['subject'],
                'greeting' => $templateObj['greeting'],
                'content' => $templateObj['content'],
                'closing' => $templateObj['closing'],
            ],
            'sendMailLog' => $dataSendMailLog,
            'hash' => gen_unique_hash()
        ];
    }
    public static function getListTemplate()
    {
        return CustomEmailTemplate::query()
            ->where('is_default', 0)
            ->whereNotIn('status', [CustomEmailTemplateEnum::SAVED])
            ->get()
            ->toArray();
    }
    public static function getPreview($id)
    {
        $templateObj = CustomEmailTemplate::query()
            ->where('id', $id)
            ->firstOrFail()
            ->toArray();

        $variables = $templateObj['variables'] !== null ? json_decode($templateObj['variables'], true) : [];
        $templateObj['content'] = self::translateVariable($variables, $templateObj['content']);

        $template = new DefaultCustomTemplate($templateObj);

        return $template->render();
    }

    public static function translateVariable(array $variables, string $content): string
    {
        foreach ($variables as $item) {
            $key = $item['key'];
            $value = $item['value'];

            $regex = "{{\\s*${key}\\s*}}";
            $content = preg_replace("/$regex/", $value, $content);
        }
        return $content;
    }

    public static function translateDefaultVariables(array $variables, string $content): string
    {
        foreach ($variables as $key => $value) {
            if (is_null($value) || $value === '') {
                // Nếu giá trị là null hoặc rỗng, xoá key khỏi content
                $content = preg_replace('/{{\s*' . $key . '\s*}}/', '', $content);
            } else {
                // Thay thế giá trị của biến vào content
                $content = str_replace('{{ ' . $key . ' }}', $value, $content);
            }
        }

        return $content;
    }

    public static function getDefaultTemplate() :array
    {
        $templateObj = CustomEmailTemplate::query()
            ->firstOrCreate(
                [
                    'template' => 'default_custom_template',
                    'is_default' => 1
                ],
                CustomEmailTemplateEnum::prepareDefaultTemplate()
            )
            ->toArray();


        $template = new DefaultCustomTemplate($templateObj);
        return [
            'template' => $template->render(),
            'info' => [
                'id' => $templateObj["id"],
                'template' => $templateObj["template"],
                'subject' => $templateObj["subject"],
                'greeting' => $templateObj["greeting"],
                'content' => $templateObj["content"],
                'closing' => $templateObj["closing"],
                'variables' => $templateObj["variables"],
                'isDefault' => $templateObj["is_default"],
            ]
        ];
    }

    public static function getTemplate($id)
    {
        $templateObj = CustomEmailTemplate::query()
            ->where('id', $id)
            ->firstOrFail()
            ->toArray();

        return [
            'id' => $templateObj["id"],
            'subject' => $templateObj["subject"],
            'greeting' => $templateObj["greeting"],
            'content' => $templateObj["content"],
            'closing' => $templateObj["closing"],
            'variables' => $templateObj["variables"],
            'isDefault' => $templateObj["is_default"],
        ];
    }

    public static function saveNew($customEmailTemplate)
    {
        CustomEmailTemplate::query()->create([
            'default_template_id' => $customEmailTemplate['id'],
            'template' => $customEmailTemplate['template'],
            'is_default' => 0,
            'subject' => $customEmailTemplate['subject'],
            'greeting' => $customEmailTemplate['greeting'],
            'content' => $customEmailTemplate['content'],
            'closing' => $customEmailTemplate['closing'],
            'variables' => empty($customEmailTemplate['variables']) ? null : json_encode($customEmailTemplate['variables']),
            'status' => CustomEmailTemplateEnum::IDLE,
        ]);
    }

    public static function update($customEmailTemplate, $data)
    {
        $customEmailTemplate->fill([
            'template' => $data['template'],
            'subject' => $data['subject'],
            'greeting' => $data['greeting'],
            'content' => $data['content'],
            'closing' => $data['closing'],
            'variables' => !empty($data['variables']) ? json_encode($data['variables']) : null,
            'status' => $data['status']
        ]);
        return $customEmailTemplate;
    }

    public static function delete($id) :void
    {
        CustomEmailTemplate::query()
            ->where('id', $id)
            ->delete();
    }

    public static function translateContentTemplate(string $templateId): array
    {
        $template = CustomEmailTemplate::query()
            ->where('id', $templateId)
            ->first();

        if (!$template) {
            return []; // or handle the case when template is not found
        }

        $templateArray = $template->toArray();

        // Decode the variables JSON
        $variables = json_decode($templateArray['variables'], true);
        $templateArray['variables'] = $variables;

        // Translate the variables in the content
        $templateArray['content'] = self::translateVariable($variables, $templateArray['content']);

        return $templateArray;
    }

    public static function prepareConfigToSendMail($userId, $userEmail, $templateObj): array
    {
        $dataSendMailLog = [
            'sellerId' => $userId,
        ];

        return [
            'to' => $userEmail,
            'template' => 'default_custom_template',
            'data' => [
                'subject' => $templateObj['subject'],
                'greeting' => $templateObj['greeting'],
                'content' => $templateObj['content'],
                'closing' => $templateObj['closing'],
                'logo_url' => Arr::get($templateObj, 'store_info.logo_url'),
                'store_name' => Arr::get($templateObj, 'store_info.name'),
                'store_info' => $templateObj['store_info'],
            ],
            'sendMailLog' => $dataSendMailLog,
            'hash' => gen_unique_hash()
        ];
    }
}
