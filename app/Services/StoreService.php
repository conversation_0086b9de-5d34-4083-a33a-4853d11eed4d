<?php
namespace App\Services;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\DomainStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\PaymentGatewayStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\ProductType;
use App\Enums\StoreDomainStatusEnum;
use App\Enums\StorePaymentGatewayTypeEnum;
use App\Enums\StoreStatusEnum;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserStatusEnum;
use App\Enums\SystemConfigCrispKeyEnum;
use App\Jobs\Storefront\UpdateStoreDomainExpirationDate;
use App\Models\File;
use App\Models\PaymentGateway;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Store;
use App\Models\StoreCollection;
use App\Models\StoreDomain;
use App\Models\StoreNavigation;
use App\Models\User;
use App\Models\UserInfo;
use App\Models\SystemConfig;
use App\Traits\GetStoreDomain;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
use Illuminate\Database\Eloquent\Model;
use Modules\OrderService\Services\RegionOrderService;

class StoreService
{
    use GetStoreDomain;
    /**
     *
     * @var Store
     */
    private static $store = null;

    /**
     * @param $store
     * @return array
     */
    public static function getPaymentGatewayStore($store) {
        $sellerId = $store->seller_id;
        $response = ['type' => '', 'enable_card' => false];
        try {
            $paymentGateways = PaymentGateway::query()->select([
                'store_id',
                'seller_id',
                'gateway',
                'active',
                'enable_card',
            ])
            ->where('seller_id', $sellerId)
            ->where('active', PaymentGatewayStatusEnum::ACTIVE)
            ->get();
            $paypalExisted = false;
            $stripeExisted = false;

            foreach ($paymentGateways as $paymentGateway) {
                if ($paymentGateway->store_id == null || $paymentGateway->store_id == $store->id) {
                    if ($paymentGateway->gateway === PaymentMethodEnum::PAYPAL) {
                        $paypalExisted = true;
                        if ($paymentGateway->enable_card) {
                            $response['enable_card'] = true;
                        }
                    }
                    if ($paymentGateway->gateway === PaymentMethodEnum::STRIPE) {
                        $stripeExisted = true;
                    }
                }
            }

            if ($paypalExisted && $stripeExisted) {
                $response['type'] = StorePaymentGatewayTypeEnum::BOTH;

            } else {
                if ($paypalExisted) {
                    $response['type'] = StorePaymentGatewayTypeEnum::PAYPAL;
                }
                if ($stripeExisted) {
                    $response['type'] = StorePaymentGatewayTypeEnum::STRIPE;
                }
            }
            return $response;
        } catch (\Exception $e) {
            return ['type' => null, 'enable_card' => false];
        }
    }

    public static function getVariantOfProducts (&$products) {
        try {
            if (empty($products)) {
                return ;
            }
            $productsArr = is_array($products) ? $products : $products->getCollection()->toArray();
            $sellerIds = array_column($productsArr, 'seller_id');
            $seller = User::query()->find($sellerIds[0]);
            $productTemplateIds = array_column($productsArr, 'template_id');
            $variants = ProductVariant::query()
                ->onSellerConnection($seller)
                ->select([
                    'product_id',
                    'variant_key',
                    'out_of_stock',
                    'adjust_price',
                    'price',
                    'old_price',
                    'location_code',
                    'base_cost'
                ])
                ->whereIn('product_id', $productTemplateIds)
                ->get();

            $productDefaultIds = [];
            foreach ($products as $product) {
                if ($product['product_type'] == ProductType::CAMPAIGN && !in_array($product['default_product_id'], $productDefaultIds)) {
                    $productDefaultIds [] = $product['default_product_id'];
                }
            }
            $productDefaults = [];
            if (!empty($productDefaultIds)) {
                $productDefaults = Product::query()
                ->select(['campaign_id',
                    'options',
                    'default_option',
                    'id',
                    'name'
                ])
                ->whereIn('id',$productDefaultIds)
                ->where('product_type', ProductType::PRODUCT)
                ->get()->keyBy('id')->toArray();
            }
            $defaultOptions = [];
            foreach ($products as &$product) {
                $product['product_options'] = [];
                $product['product_default_option_color'] = '';
                if ($product['product_type'] == ProductType::CAMPAIGN && !empty($productDefaults)) {
                    $productOptions = [];
                    $productDefaultOptionColor = '';
                    $productName = '';
                    if (isset($productDefaults[$product['default_product_id']])) {
                        if (is_array($productDefaults[$product['default_product_id']]['options'])) {
                            $productOptions = $productDefaults[$product['default_product_id']]['options'];
                        } else {
                            $productOptions = (array)json_decode($productDefaults[$product['default_product_id']]['options']);
                        }
                        $productDefaultOptionColor = $productDefaults[$product['default_product_id']]['default_option'];
                        $productName = $productDefaults[$product['default_product_id']]['name'];
                    }
                    $product['product_options'] = $productOptions;
                    $product['product_default_option_color'] = $productDefaultOptionColor;
                    $product['product_name'] = $productName;
                } else {
                    $product['product_options'] = is_array($product['options']) ? $product['options'] : (array)json_decode($product['options']);
                    $product['product_default_option_color'] = $product['default_option'];
                    $product['product_name'] = $product['name'];
                }

                $product['product_default_option_color'] = str_replace(' ', '_',  $product['product_default_option_color']);

                if (!empty($product['product_options']) && !empty($product['product_options']['size'])) {
                    $defaultOptions[$product['template_id']]['variant_key'][$product['id']] = $product['product_default_option_color'].'-'.$product['product_options']['size'][0];
                    $defaultOptions[$product['template_id']]['product_options'][$product['id']] = [
                        'color' => $product['product_default_option_color'],
                        'size' => $product['product_options']['size'][0]
                    ];

                }
            }
            $variantList = [];
            $variantsArr = $variants->toArray();
            foreach ($variants as $variant) {
                if ($variant->base_cost == 0 ) {
                    $variantQuery = array_filter($variantsArr, function ($item) use ($variant) {
                        return $item['product_id'] == $variant->product_id;
                    });
                    $lowestBaseCostVariant = CampaignService::getLowestBaseCostVariant($variant->variant_key, $variant->location_code, $variantQuery);
                    if (isset($lowestBaseCostVariant) && isset($lowestBaseCostVariant['base_cost'])) {
                        $variant->base_cost = $lowestBaseCostVariant['base_cost'];
                    }
                }
                $variantList[$variant->product_id][$variant->variant_key][] = $variant;
            }
            $collect = is_array($products) ? collect($products) : $products->getCollection();
            $collect->transform(function ($product) use ($variantList, $defaultOptions) {
                $productId = $product['id'];
                $variantKey = $defaultOptions[$product['template_id']]['variant_key'][$productId] ?? null;
                $variantOptions = $defaultOptions[$product['template_id']]['product_options'][$productId] ?? null;
                $product['default_variants'] = isset ($variantKey) ? ($variantList[$product['template_id']][$variantKey] ?? []) : [];
                $product['variant_options'] = isset($variantOptions) ? $variantOptions : [];
                return $product;
            });
            $products = $collect->toArray();
        }catch (\Exception $e) {
        }
    }

    public static function getBaseVariantOnOrderAndCampLocation ($templateId, $variantKey, $orderLocationCodes, $campLocationCodes) {
        $response = [
            'order_location' => null,
            'campaign_location' => null,
        ];
        try {
            $templateVariantOnOrderLocation = self::getVariantWithLocation($templateId, $variantKey, $orderLocationCodes);
            $templateVariantOnCampLocation = self::getVariantWithLocation($templateId, $variantKey, $campLocationCodes);
            $variants = ProductVariant::findAndCacheByTemplate($templateId)->all();

            if ($templateVariantOnOrderLocation->base_cost == 0) {
                $lowestBaseCostVariant = CampaignService::getLowestBaseCostVariant($templateVariantOnOrderLocation->variant_key, $templateVariantOnOrderLocation->location_code, $variants);
                if (isset($lowestBaseCostVariant) && isset($lowestBaseCostVariant['base_cost'])) {
                    $templateVariantOnOrderLocation->base_cost = $lowestBaseCostVariant['base_cost'];
                }
            }

            if ($templateVariantOnCampLocation->base_cost == 0) {
                $lowestBaseCostVariant = CampaignService::getLowestBaseCostVariant($templateVariantOnCampLocation->variant_key, $templateVariantOnCampLocation->location_code, $variants);
                if (isset($lowestBaseCostVariant) && isset($lowestBaseCostVariant['base_cost'])) {
                    $templateVariantOnCampLocation->base_cost = $lowestBaseCostVariant['base_cost'];
                }
            }

            $response = [
                'order_location' => $templateVariantOnOrderLocation ?? null,
                'campaign_location' => $templateVariantOnCampLocation  ?? null,
            ];

        } catch (\Exception $e) {
        }
        return $response;
    }

    public static function getVariantWithLocation($templateId, $variantKey, $locationCodes) {
        return ProductVariant::findAndCacheByTemplate($templateId)
            ->filter(function ($each) use ($variantKey, $locationCodes) {
                return $each->variant_key === $variantKey && in_array($each->location_code, $locationCodes);
            })->sortBy(function ($each) use ($locationCodes) {
                return array_search($each['location_code'], $locationCodes);
            })
            ->first();
    }

    /**
     * @return Store|null
     */
    public static function getCurrentStoreInfo($domain = null)
    {
        if (!is_null(self::$store) && is_null($domain)) {
            RegionOrderService::forceDistributedCheckout(self::$store);
            return self::$store;
        }
        try {
            $domain = $domain ?? self::getDomain();
            if (empty($domain)) {
                self::$store = null;
                return null;
            }
            $cache = request()?->header('fcache');
            $store = cache()->get(CacheKeys::getStoreDomainKey($domain));
            if ($store) {
                if ($cache === '2') {
                    clearStoreCache($store->id);
                } else {
                    RegionOrderService::forceDistributedCheckout($store);
                    self::overrideStoreEnableCrispSupport($store);
                    self::$store = $store;
                    return $store;
                }
            }
            $store = self::getStore($domain);
            if (is_null($store)) {
                cache()->forget(CacheKeys::getStoreDomainKey($domain));
                self::$store = null;
                return null;
            }

            $store['currentDomain'] = self::currentDomain();
            $hasContractInfoTag = data_get($store, 'tags') && in_array('custom contact info', data_get($store, 'tags'), true);
            $canHasCustomInfo = !empty(data_get($store, 'custom_payment')) || $hasContractInfoTag;
            $store['storeContact'] = [
                'address' => $store['address'] ?? null,
                'phone' => $store['phone'] ?? null,
                'email' => $canHasCustomInfo ? ($store['email'] ?? null) : ($store['support_mail'] ?? null),
                'email_info' => $canHasCustomInfo ? ($store['email'] ?? null) : null,
            ];
            $storeId = data_get($store, 'id');
            $store['banners'] = self::getBannersByType($storeId, 'store_banner');
            $store['collection_banners'] = self::getBannersByType($storeId, 'collection_banner');
            $store->makeHidden(['address', 'phone', 'email']);

            // get all navigations for this store
            $navs = StoreNavigation::query()
                ->where('store_id', data_get($store, 'id'))
                ->orderBy('position')
                ->orderBy('id')
                ->get();

            $menus = [];
            $menus2 = [];
            foreach ($navs as $nav) {
                if ($nav->parent_id !== null) { // if parent id !== null: child
                    $parentId = $nav->parent_id;
                    foreach ($navs as $nav2) { // find parent
                        // nav2 = parent
                        if ($nav2->id === $parentId) {
                            $nav2->addChildMenu($nav);
                            break;
                        }
                    }
                } else {
                    $menus[] = $nav; // parent
                }
            }

            foreach ($menus as $menu) {
                $menus2[$menu->place][] = $menu->getDisplayMenu();
            }

            // build navigation JSON struct
            $store['headerMenu'] = count($menus2) > 0 && array_key_exists('header', $menus2) ? $menus2['header'] : [];

            $store['footerMenu'] = count($menus2) > 0 && array_key_exists('footer', $menus2) ? $menus2['footer'] : [];

            $store['socialsLink'] = json_decode(data_get($store, 'social_accounts'), true);

            $store->makeHidden(['social_accounts']);

            $collections = self::getCollectionIdsByStoreId(data_get($store, 'id'));

            $store['collections'] = $collections->toArray();

            $store['languages'] = [
                [
                    'code' => 'en',
                    'name' => 'English'
                ],
                [
                    'code' => 'fr',
                    'name' => 'France'
                ],
                [
                    'code' => 'vi',
                    'name' => 'Vietnamese'
                ]
            ];

            $store['primaryColor'] = '#f4478e';

            $store['trackingCode'] = self::getSellerTrackingCode(data_get($store, 'seller_id'));

            $store['test_price_templates'] = UserService::getPricingBySellerId(data_get($store, 'seller_id'))->filter(function ($pricing) {
                return $pricing->test_price_percent != 0 && $pricing->test_price != 0;
            })->values();

            //Don't cache if load default store on non-exist domains
            $needCache = true;

            $paymentGateway =  self::getPaymentGatewayStore($store);
            $store['payment_gateway_type'] = $paymentGateway['type'];
            $store['paypal_enable_card'] = $paymentGateway['enable_card'];
            if (!app()->isProduction()) {
                $needCache = Store::query()
                    ->where('domain', $domain)
                    ->orWhere('sub_domain', $domain)
                    ->exists();
            }

            if ($needCache) {
                cache()->put(CacheKeys::getStoreDomainKey($domain), $store, CacheTime::CACHE_24H);
            }
            RegionOrderService::forceDistributedCheckout($store);

            self::overrideStoreEnableCrispSupport($store);
            self::$store = $store;
            return $store;
        } catch (\Throwable $e) {
            logException($e);
            return null;
        }
    }

    /**
     * @param int $storeId
     * @param string $type
     * @return array
     */
    private static function getBannersByType(int $storeId, string $type): array
    {
        $banners = File::query()
            ->where([
                'store_id' => $storeId,
                'type' => FileTypeEnum::BANNER,
                'type_detail' => $type,
            ])
            ->get();

        if ($banners->isEmpty()) {
            return [];
        }

        $results = [];

        foreach ($banners as $banner) {
            $option = json_decode($banner->option, true);
            $bannerLink = $option && !empty($option['url']) ? $option['url'] : '#';
            $bannerText = $option && !empty($option['text']) ? $option['text'] : '';
            $bannerUrl = $banner->file_url;
            $results[] = [
                'banner_url' => $bannerUrl,
                'banner_link' => $bannerLink,
                'banner_text' => $bannerText,
            ];
        }

        return $results;
    }

    /**
     * @param $storeId
     * @return StoreCollection[]|Builder[]|Collection
     */
    public static function getCollectionIdsByStoreId($storeId)
    {
        return StoreCollection::query()
            ->where('store_collection.store_id', $storeId)
            ->get(['collection_id']);
    }

    /**
     * @param $sellerId
     * @return HigherOrderBuilderProxy|mixed|string|null
     */
    private static function getSellerTrackingCode($sellerId)
    {
        $result = UserInfo::query()
            ->select('value')
            ->firstWhere([
                'user_id' => $sellerId,
                'key' => UserInfoKeyEnum::TRACKING_CODE
            ]);
        return $result->value ?? null;
    }

    /**
     * @param $domain
     * @param array $fields
     * @param bool $withBaseUrl
     * @return Store|Builder|Model|object|null
     */
    public static function getStore($domain, array $fields = [], bool $withBaseUrl = false)
    {
        $isSubDomain = false;
        if (!strpos($domain, '.')) {
            $isSubDomain = true;
        }

        if ($fields && count($fields) > 0) {
            $selectFields = $fields;

            $selectFields[] = 'domain';
            $selectFields[] = 'sub_domain';
            $selectFields[] = 'domain_status';

            $selectFields = array_unique($selectFields);
        } else {
            $selectFields = [
                'id',
                'name',
                'sub_domain',
                'domain',
                'logo_url',
                'email',
                'phone',
                'cc_email',
                'address',
                'theme',
                'theme_options',
                'foot_line',
                'promotion_title',
                'discount_code',
                'auto_apply_coupon',
                'seller_id',
                'company_id',
                'checkout_phone',
                'tracking_code',
                'favicon',
                'default_currency',
                'default_language',
                'default_color',
                'feature_collection_id',
                'featured_collection_ids',
                'feature_text',
                'list_all_my_campaigns',
                'random_popular',
                'market_place_listing',
                'market_place_upsell',
                'status',
                'store_type',
                'show_payment_button',
                'show_tipping',
                'default_tipping',
                'product_select_type',
                'show_checkout_shipping_info',
                'sitewide_banner',
                'sitewide_banner_enable',
                'option_label_enable',
                'is_proxy',
                'social_accounts',
                'product_review_display',
                'product_review_coupon',
                'product_review_thank_you_message',
                'order_prefix',
                'tags',
                'style',
                'enable_search',
                'checkout_store_id',
                'enable_add_to_cart',
                'enable_custom_phone',
                'enable_contract_form',
                'enable_product_name_after',
                'enable_dynamic_base_cost',
                'disable_promotion',
                'disable_related_product',
                'disable_related_collection',
                'disable_pre_discount',
                'paypal_gateway_id',
                'stripe_gateway_id',
                'new_arrival_collection_id',
                'best_seller_collection_id',
                'always_show_order_summary',
                'search_return_x_page',
                'order_summary_position',
                'enable_product_name_export_feed',
                'enable_distributed_checkout',
                'enable_payment_ssl_norton',
                'enable_deliver_to',
                'enable_insurance_fee',
                'mail_support',
                'show_countdown',
                'countdown_end_time',
                'enable_crisp_support',
            ];
        }

        $query = Store::query()
            ->select($selectFields)
            ->with('seller:id,custom_payment,smart_remarketing,status')
            ->whereIn('status', [
                StoreStatusEnum::ACTIVE,
                StoreStatusEnum::VERIFIED,
                StoreStatusEnum::BLOCKED
            ]);

        if ($isSubDomain) {
            $query->where('sub_domain', $domain);
        } else {
            $query->where(function ($query) use ($domain) {
                $query->where('domain', $domain);
                $query->orWhereHas('storeDomain', function ($query) use ($domain) {
                    $query->where('domain', $domain);
                    $query->where('status', StoreDomainStatusEnum::ACTIVATED);
                });
            });
        }

        // get custom tags to inject into <head> or <body>
        $query->with('headTags:store_id,tag,code,position,priority,additional_properties,path');

        // get checkout store
        $query->with('checkoutStore:id,domain,sub_domain,status');

        $store = $query->first();

        if (is_null($store) && !app()->isProduction()) {
            // if store not found and environment is production then get default store = 1
            $store = Store::query()
                ->select($selectFields)
                ->isSenStore()
                ->with('seller:id,custom_payment,smart_remarketing,status')
                ->with('headTags:store_id,tag,code,position,priority,path')
                ->first();
        }

        if (!is_null($store)) {
            if ($store->relationLoaded('seller')) {
                $store->custom_payment = optional($store->seller)->custom_payment;
                $store->seller_status = optional($store->seller)->status;
                $store->smart_remarketing = optional($store->seller)->smart_remarketing ?? false;
                $store->unsetRelation('seller');
            }

            if ($store->checkout_domain) {
                $store->checkout_domain = self::getDomainByStoreId($store->checkout_domain);
                $store->unsetRelation('checkoutStore');
            }

            // enable store proxy if use custom payment
            if ($store->custom_payment && $store->seller_status !== UserStatusEnum::TRUSTED) {
                $store->is_proxy = $store->custom_payment;
            }

            if ($store->seller_status === UserStatusEnum::SOFT_BLOCKED || $store->seller_status === UserStatusEnum::HARD_BLOCKED) {
                $store->status = StoreStatusEnum::BLOCKED;
            }

            $store->tracking_code = $store->tracking_code ? json_decode($store->tracking_code) : new \stdClass();
            if (empty($store->tracking_code?->klaviyo_public_key) && $store->seller?->klaviyo_public_key) {
                $store->tracking_code->klaviyo_public_key = $store->seller?->klaviyo_public_key;
            }

            $store->append('support_mail');

            if (!$withBaseUrl) {
                $store->makeHidden(['base_url']);
            }

            if (!empty($store->tags)) {
                $store->tags = explode(',', $store->tags);
            }
        }
        return $store;
    }

    public static function getCurrentStore(): ?Store
    {
        $domain = self::getDomain();

        $isSubDomain = false;
        if (!strpos($domain, '.')) {
            $isSubDomain = true;
        }

        $query = Store::query();

        if ($isSubDomain) {
            $query->where('sub_domain', $domain);
        } else {
            $query->where(function ($query) use ($domain) {
                $query->where('domain', $domain);
                $query->orWhereHas('storeDomain', function ($query) use ($domain) {
                    $query->where('domain', $domain);
                    $query->where('status', StoreDomainStatusEnum::ACTIVATED);
                });
            });
        }

        return $query->first();
    }

    private static function overrideStoreEnableCrispSupport(&$store)
    {
        $systemConfigCrisp = SystemConfig::getCustomConfig(SystemConfigCrispKeyEnum::KEY, false);
        if (!$systemConfigCrisp || $systemConfigCrisp->status === 0 || (int)data_get($systemConfigCrisp, 'value') === 0) {
            $store['enable_crisp_support'] = 0;
        }
    }

    /**
     * @param $storeId
     * @param array $additionFields
     * @return Store|null
     */
    public static function getStoreInfo($storeId, array $additionFields = []): ?Store
    {
        $fields = [
            'id',
            'name',
            'email',
            'sub_domain',
            'domain',
            'logo_url',
            'address',
            'email',
            'mail_support',
            'cc_email',
            'phone',
            'social_accounts',
            'discount_code',
            'promotion_title',
            'seller_id',
            'notification_settings',
        ];

        if (!empty($additionFields)) {
            $fields = array_merge($fields, $additionFields);
        }

        $store = Store::query()
            ->select($fields)
            ->firstWhere('id', $storeId);

        if (is_null($store)) {
            return null;
        }

        $store->makeHidden(['banners', 'base_url']);

        if (empty($store->domain)) {
            $domain = $store->sub_domain . '.' . getStoreBaseDomain();
            $store->domain = $domain;
        }

        $store->append('support_mail');

        return $store;
    }

    /**
     * @return string
     */
    public static function currentDomain(): string
    {
        $domain = request()?->header('X-Domain');
        $domain = trim($domain);
        return preg_replace('/:\d+$/', '', $domain);
    }

    /**
     * @return string
     */
    public static function getDomain(): string
    {
        $domain = self::currentDomain();
        $domainPattern = '.' . getStoreBaseDomain();
        if (strpos($domain, $domainPattern) > 0) {
            preg_match("/^([a-zA-Z0-9-]+)\./", $domain, $matches);

            if (count($matches) > 0) {
                return strtolower($matches[1]);
            }
        }
        return $domain;
    }

    /**
     * @param $id
     * @return Store|null
     */
    public static function findById($id): ?Store
    {
        return cache()->tags(CacheKeys::getStoreTag($id))->remember(
            CacheKeys::getStoreIdKey($id),
            CacheKeys::CACHE_24H,
            function () use ($id) {
                return Store::query()->find($id);
            }
        );
    }

    /**
     * @param $storeDomainId
     * @param $storeId
     * @return bool
     */
    public static function setDefaultStoreDomain($storeDomainId, $storeId)
    {
        $storeDomainStatus = StoreDomain::getCurrentStoreDomainByIdAndStoreId($storeDomainId, $storeId);
        if (is_null($storeDomainStatus)) {
            return false;
        }
        if ($storeDomainStatus->status === StoreDomainStatusEnum::ACTIVATED) {
            StoreDomain::resetDefaultByStoreId($storeId);
            $storeDomainUpdated = StoreDomain::updateStoreDomainById($storeDomainId, [
                'is_default' => 1
            ]);
            if (!$storeDomainUpdated) {
                return false;
            }
            $storeUpdated = Store::query()->where('id', $storeId)->update([
                'domain' => $storeDomainStatus->domain,
                'domain_status' => DomainStatusEnum::COMPLETE
            ]);
            if ($storeUpdated) {
                UpdateStoreDomainExpirationDate::dispatch($storeId, $storeDomainStatus->domain);
                return true;
            }
        }
        return false;
    }

    /**
     * @param User|null $seller
     * @param Store|null $store
     * @param bool $hasKeyword
     * @return string
     */
    public static function getElasticSearchIndexes(?User $seller = null, ?Store $store = null, bool $hasKeyword = false): string
    {
        $indexes = [];
        if ($store && $store->seller && !$seller) {
            $seller = $store->seller;
        }
        if (!$seller) {
            graylogError('getElasticSearchIndexes: seller not found, Store Id: ' . data_get($store, 'id'), [
                'category' => 'elastic_search_index_not_found',
                'store_id' => data_get($store, 'id'),
                'store_data' => $store,
                'seller_data' => $seller,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            ]);
            return '';
        }
        $elasticSearchSellerIndex = $seller->getElasticSearchIndex();
        $elasticSearchDefaultIndex = get_env('ELATICSEARCH_INDEX', 'products');
        $indexes[] = $elasticSearchSellerIndex;
        $shouldAddIndexes = !$seller->shardingCompleted() && ($hasKeyword || ($store && ($store->market_place_listing || $store->id === Store::SENPRINTS_STORE_ID)));
        if ($shouldAddIndexes) {
            $indexes[] = $elasticSearchDefaultIndex;
            $indexes[] = $elasticSearchDefaultIndex . '_sub';
        }
        return implode(',', array_unique($indexes));
    }
}
