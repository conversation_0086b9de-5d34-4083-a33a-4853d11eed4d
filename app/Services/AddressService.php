<?php

namespace App\Services;

use App\Models\Order;
use App\Traits\AddressValidation;
use Illuminate\Support\Facades\Http;
use Modules\OrderService\Models\RegionOrders;

class AddressService
{
    use AddressValidation;

    private const ENDPOINT_EASYPOST = 'https://api.easypost.com/v2/addresses';
    private const ENDPOINT_GEOAPIFY = 'https://api.geoapify.com/v1/geocode/search';
    private const RECONFIRM = false; // true: need to confirm with another service
    private const ONLY_RECONFIRM_INVALID_ADDRESS = false;
    private const DEBUG = false;
    private static string $raw = '';
    private static string $text = '';
    private static string $response = '';
    private static string $order_number = '';

    public static function verify(Order|RegionOrders $order, string &$log = null)
    {
        // check required fields, don't need customer phone
        if (!isset($order->address, $order->city, $order->postcode, $order->country)) {
            self::addLog($log, 'Missing required fields');
            return false;
        }

        if (!isset($order->state) && in_array('required', self::getStateRule($order->country))) {
            self::addLog($log, 'State is required');
            return false;
        }

        $geoApifyResult = self::geoapifyVerify($order, $log);

        // note: we're testing to see if ChatGPT can validate the address
        // maybe remove this in the future
//        try {
//            $address = 'Address: ' . $order->address . "\n";
//            $address .= 'City: ' . $order->city . "\n";
//            $address .= 'State: ' . $order->state . "\n";
//            $address .= 'Postcode: ' . $order->postcode . "\n";
//            $address .= 'Country: ' . $order->country . "\n";
//            $address .= 'Phone: ' . $order->customer_phone . "\n";
//            $gptResult = AddressValidateGPT::validate($address);
//
//            if ($geoApifyResult !== $gptResult && $gptResult === false) {
//                self::logValidateResult($geoApifyResult, $gptResult, $address);
//            }
//        } catch (\Throwable $e) {
//            telegramDebug('[Validate customer address] Error: ' . $e->getMessage());
//        }

        if (self::DEBUG) {
            echo 'Result GeoApify: ' . ($geoApifyResult ? 'true' : 'false') . PHP_EOL;
        }

        if (!self::RECONFIRM) {
            return $geoApifyResult;
        }

        if (self::ONLY_RECONFIRM_INVALID_ADDRESS && !$geoApifyResult) {
            return self::easypostVerify($order);
        }

        $easyPostResult = self::easypostVerify($order, false, $log);

        if (self::DEBUG) {
            echo 'Result EasyPost: ' . ($easyPostResult ? 'true' : 'false') . PHP_EOL;
        }

        return $geoApifyResult || $easyPostResult;
    }

    private static function logValidateResult(bool $geoApifyResult, bool $gptResult, string $address): void
    {
        $debugMessage = '[Validate customer address]' . PHP_EOL;
        $debugMessage .= '+ Order number: ' . self::$order_number . PHP_EOL;
        $debugMessage .= '+ GeoApify Result: ' . ($geoApifyResult ? 'true' : 'false') . PHP_EOL;
        $debugMessage .= '+ ChatGPT Result: ' . ($gptResult ? 'true' : 'false') . PHP_EOL;
        $debugMessage .= '+ Address detail: ' . PHP_EOL;
        $debugMessage .= '===' . PHP_EOL;
        $debugMessage .= $address . PHP_EOL;
        $debugMessage .= '===' . PHP_EOL;
        $debugMessage .= '+ Link: https://admin.senprints.com/order/detail/' . self::$order_number;
        telegramDebug($debugMessage);
    }

    private static function addLog(&$log, $message): void
    {
        if (!is_null($log)) {
            $log = $message;
            graylogInfo('Address validate: ' . $message, [
                'order_number' => self::$order_number,
                'address_raw' => self::$raw,
                'address_text' => self::$text,
                'address_response' => self::$response,
                'category' => 'address_validate',
            ]);
        }
    }

    public static function easyPostVerify(Order|RegionOrders $order, bool $withErrorResponse = false, string &$log = null)
    {
        $apiKey = config('senprints.easypost_api_key');

        if (!$apiKey) {
            throw new \RuntimeException('Easypost API key is not set');
        }
        try {
            self::correctAddress($order);
            $res = Http::asForm()
                ->timeout(10)
                ->retry(3)
                ->withBasicAuth($apiKey, '')
                ->post(self::ENDPOINT_EASYPOST, [
                    'verify_strict[]' => 'delivery',
                    'address[street1]' => $order->address,
                    'address[city]' => $order->city,
                    'address[state]' => $order->state,
                    'address[zip]' => $order->postcode,
                    'address[country]' => $order->country,
                    'address[phone]' => $order->customer_phone
                ]);
        } catch (\Throwable $e) {
            if ($withErrorResponse) {
                return self::getErrorResponse($e->getMessage());
            }
            return false;
        }

        if ($res->failed()) {
            if ($withErrorResponse) {
                $json = $res->json();

                if (isset($json['error'])) {
                    return $json['error'];
                }
            }

            return false;
        }

        if ($res->json('verifications.delivery.success', false)) {
            return true;
        }

        return false;
    }

    public static function geoapifyVerify(Order|RegionOrders $order, string &$log = null): ?bool
    {
        self::correctAddress($order);
        $apiKey = config('senprints.geoapify_api_key');
        self::$order_number = empty($order->order_number) ? 'null' : $order->order_number;
        self::$raw = $order->address . ' ' . $order->city . ' ' . $order->state . ' ' . $order->postcode . ' ' . $order->country . ' ' . $order->customer_phone;
        if (!$apiKey) {
            throw new \RuntimeException('Geoapify API key is not set');
        }
        try {
            $res = Http::asForm()
                ->timeout(10)
                ->retry(3)
                ->get(self::ENDPOINT_GEOAPIFY, [
                    'text' => self::$raw,
                    'apiKey' => $apiKey
                ]);
        } catch (\Throwable $e) {
            self::addLog($log, 'The address is entirely inaccurate.');
            return null;
        }

        if ($res->failed()) {
            self::addLog($log, 'The address is entirely inaccurate.');
            return null;
        }

        $json = $res->json();
        // https://www.geoapify.com/validate-addresses-with-api
        $acceptLevel = 0.9;
        $declineLevel = 0.2;
        $acceptStreetLevel = 0.5;


        if (!isset($json['features'])) {
            self::addLog($log, 'The address is entirely inaccurate.');
            return false;
        }
        $features = $json['features'];
        $query = $json['query'];

        // if there is no elements in features, then the address is invalid
        if (count($features) === 0) {
            self::addLog($log, 'The address is entirely inaccurate.');
            return false;
        }

        if (isset($features[0]['properties']['rank']['confidence'])) {
            $properties = $features[0]['properties'];
            $confidence = $properties['rank']['confidence'];
            self::$text = data_get($query, 'text', '');
            self::$response = data_get($properties, 'formatted', '');

            if ($confidence >= $acceptLevel) {
                $queryCountry = data_get($query, 'parsed.country', $order->country);
                $country = data_get($properties, 'country', null);
                $countryCode = data_get($properties, 'country_code', null);
                if ($country !== null && $countryCode !== null) {
                    $queryCountry = strtolower($queryCountry);
                    $country = strtolower($country);
                    if (strlen($queryCountry) === 2 || strlen($queryCountry) === 3) { // check is country code
                        $country = strtolower($countryCode);
                    }
                    if (!str_contains($queryCountry, $country)) {
                        self::addLog($log, 'Country is invalid');
                    }
                }

                $queryPostcode = data_get($query, 'parsed.postcode', $order->postcode);
                $postcode = data_get($properties, 'postcode', null);
                if ($postcode !== null) {
                    $queryPostcode = strtolower($queryPostcode);
                    $queryPostcode = preg_replace('/\s+/', '', $queryPostcode);
                    if (is_array($postcode)) {
                        $postcode = implode(' ', $postcode);
                    }
                    $postcode = strtolower($postcode);
                    $postcode = preg_replace('/\s+/', '', $postcode);

                    if ($queryPostcode !== $postcode) {
                        self::addLog($log, 'Postcode is invalid');
                    }
                }

                $queryState = data_get($query, 'parsed.state', $order->state);
                $state = data_get($properties, 'state', null);
                $stateCode = data_get($properties, 'state_code', null);

                if ($state !== null) {
                    $queryState = strtolower($queryState);
                    $state = strtolower($state);
                    if ((strlen($queryState) === 2 || strlen($queryState) === 3) && $stateCode !== null) { // check is state code
                        $state = strtolower($stateCode);
                    }
                    if ($queryState !== $state) {
                        self::addLog($log, 'State is invalid');
                    }
                }

                $city = data_get($properties, 'city', null);
                $queryCity = data_get($query, 'parsed.city', $order->city);
                if ($city !== null) {
                    $queryCity = strtoupper($queryCity);
                    $city = strtoupper($city);

                    if ($queryCity === $city) {
                        return true;
                    }

                    if (strlen($queryCity) > 3 && str_contains($city, $queryCity)) {
                        return true;
                    }

                    // handle short version city input: 'New York' => 'NY'
                    $shortCity = '';
                    $words = explode(" ", $city);
                    if (count($words) === 1) {
                        // If city has only 1 world => get the first 2 letters, Texas => TX
                        // but skip 'E' and find the next letter
                        $shortCity .= $city[0];
                        $shortCity .= ($city[0] === 'E') ? $city[1] : $city[0];
                    } else {
                        foreach ($words as $word) {
                            $letter = $word[0];
                            $shortCity .= $letter;
                        }
                    }

                    if ($queryCity !== $shortCity) {
                        self::addLog($log, 'City is invalid');
                    }
                }

                $street = data_get($properties, 'street', '');
                $houseNumber = data_get($properties, 'housenumber', '');
                $queryHouseNumber = data_get($query, 'parsed.housenumber', '');
                $queryStreet = data_get($query, 'parsed.street', '');
                $fullStreet = $houseNumber . ' ' . $street;
                $fullQueryStreet = $queryHouseNumber . ' ' . $queryStreet;
                $fullQueryStreet = trim($fullQueryStreet);
                $fullStreet = trim($fullStreet);
                if ($fullStreet !== '' && $fullQueryStreet !== '') {
                    $fullQueryStreet = strtolower($fullQueryStreet);
                    $fullStreet = strtolower($fullStreet);
                    $maxLength = max(strlen($fullQueryStreet), strlen($fullStreet));
                    $matchingChars = similar_text($fullQueryStreet, $fullStreet);
                    $matchingPercent = $matchingChars / $maxLength;
                    if ($matchingPercent < $acceptStreetLevel) {
                        self::addLog($log, 'Street is invalid');
                    }
                }

                return true;
            }
            $confidenceCity = data_get($properties, 'rank.confidence_city_level', 0);
            $confidenceStreet = data_get($properties, 'rank.confidence_street_level', 0);
            $messages = [];

            if ($confidenceCity > 0 && $confidenceCity < $acceptLevel) {
                $messages[] = 'City or/and state is invalid';
            }

            if ($confidenceStreet > 0 && $confidenceStreet < $acceptLevel) {
                $messages[] = 'Street or/and building is invalid';
            }

            $message = implode('. ', $messages);
            if (!empty($message)) {
                $message .= '.';
            }
            self::addLog($log, $message);
        }

        return false;
    }

    /**
     * @param string $message
     * @return array[]
     */
    private static function getErrorResponse(string $message): array
    {
        // generate same error response as easypost
        return [
            'error' => [
                'code' => 'ADDRESS.VERIFY.FAILURE',
                'message' => 'Unable to verify address.',
                'errors' => [
                    [
                        'code' => 'E.ADDRESS.CAN_NOT_VERIFY',
                        'field' => 'address',
                        'message' => $message,
                        'suggestion' => null
                    ]
                ]
            ]
        ];
    }

    /**
     * @param Order|RegionOrders $order
     * @return void
     */
    private static function correctAddress(Order|RegionOrders $order): void
    {
        if ($order->country === 'BE' && $order->house_number && !str_ends_with($order->address, $order->house_number)) {
            $order->address .= ' ' . $order->house_number;
        }
        if ($order->country === 'UK' && $order->house_number && !str_starts_with($order->address, $order->house_number)) {
            $order->address = $order->house_number . ' ' . $order->address;
        }
    }
}
