<?php
namespace App\Responses;

use Illuminate\Pagination\LengthAwarePaginator;

class CustomPaginator extends LengthAwarePaginator
{
    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        foreach ($this->options as $key => $value) {
            $data[$key] = $value;
        }
        return $data;
    }
}
