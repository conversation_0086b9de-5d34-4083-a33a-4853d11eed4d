<?php

namespace App\Imports\Supplier;

use App\Models\ProductDesignMapping;
use App\Models\Template;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ProductDesignMappingImport implements ToArray, WithHeadingRow
{
    private $supplierId;

    public function __construct($supplierId)
    {
        $this->supplierId = $supplierId;
    }

    public function array($array): void
    {
        $data       = [];
        $productIds = [];

        foreach ($array as $each) {
            $sku = $each['product_sku'];

            // if sku empty => product id = 0
            if (empty($sku)) {
                $productIds[$sku] = 0;
            } elseif (!array_key_exists($sku, $productIds)) {
                $product = Template::query()
                    ->where('sku', $sku)
                    ->firstOrFail('id');

                $productIds[$sku] = $product->id;
            }

            $data[] = [
                'supplier_id' => $this->supplierId,
                'print_space' => $each['print_space'],
                'product_id'  => $productIds[$sku],
                'width'       => (int) $each['width'],
                'height'      => (int) $each['height'],
                'crop_transparent' => !empty($each['crop_transparent']) ? (int) $each['crop_transparent'] : 0,
            ];
        }

        ProductDesignMapping::query()->upsert($data, [
            'supplier_id',
            'print_space',
            'product_id',
        ]);
    }
}
