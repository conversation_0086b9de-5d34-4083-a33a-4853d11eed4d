<?php

namespace App\Imports\Supplier\Duplium;

use App\Enums\SupplierEnum;
use App\Imports\Supplier\ImportTrait;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class Import implements WithMultipleSheets
{
    protected int $supplierId;

    public function __construct()
    {
        $this->supplierId = SupplierEnum::DUPLIUM;
    }

    public function sheets(): array
    {
        ImportTrait::updateInactiveProduct($this->supplierId);
        return [
            'Product Catalog' => new ProductImport($this->supplierId),
            'Product List'    => new VariantImport($this->supplierId),
        ];
    }
}
