<?php

namespace App\Imports\Supplier\PrintLogistic;

use App\Enums\OrderProductFulfillStatus;
use App\Enums\SupplierEnum;
use App\Imports\Supplier\BaseValidateBillingImporter;
use Illuminate\Support\Collection;
use RuntimeException;

/**
 * Class ValidateBillingImporter
 */
class ValidateBillingImporter  extends BaseValidateBillingImporter
{
    private static ?bool $isItemFile = null;

    /**
     * @param     string     $supplierId
     */
    public function __construct(string $supplierId)
    {
        parent::__construct([SupplierEnum::PRINTLOGISTIC, SupplierEnum::PRINTLOGISTIC_V2, SupplierEnum::PRINTLOGISTIC_UK, SupplierEnum::PRINTLOGISTIC_US]);
    }

    /**
     * @param $row
     *
     * @return string
     */
    public function preValidateKey($row): string
    {
        $this->isItemFile($row); // để caching $isItemFile

        return parent::preValidateKey($row); // TODO: Change the autogenerated stub
    }

    /**
     * @param     array     $row
     *
     * @return bool
     *@see self::mapFulfillShippingCost()
     *
     * @override BaseValidateBillingImporter::shouldUnifyQuantity()
     *
     */
    public function shouldUnifyQuantity(array $row): bool
    {
        return $this->isItemFile($row);
    }

    /**
     * Map fulfill order id
     *
     * @override BaseValidateBillingImporter::mapFulfillOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapFulfillOrderId(array $row): ?string
    {
        return $row['order_id'];
    }

    /**
     * Để phân biệt đang import file nào thì dùng cột sku
     * File excel shipping có không có sku
     * File excel item có sku
     *
     * @override BaseValidateBillingImporter::mapOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapOrderId(array $row): ?string
    {
        return preg_replace('#-\d+$#', '', $row['order_id']);
    }

    /**
     * Get quantity
     *
     * @override BaseValidateBillingImporter::mapQuantity()
     *
     * @param     array     $row
     *
     * @return int|null
     */
    public function mapQuantity(array $row): ?int
    {
        return $row['quantity'] ?? null;
    }

    /**
     * Get fulfill sku
     *
     * @override BaseValidateBillingImporter::mapFulfillSku()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapFulfillSku(array $row): ?string
    {
        return $row['pl_sku'] ?? '';
    }

    /**
     * Cả 2 file excel shipping và item đều có không có tracking code
     *
     * @override BaseValidateBillingImporter::mapTrackingCode()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapTrackingCode(array $row): ?string
    {
        return $row['tracking_number'] ?? null;
    }

    /**
     * Map fulfill base cost
     *
     * @override BaseValidateBillingImporter::mapFulfillBaseCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillBaseCost(array $row): ?float
    {
        return (float) ($row['summary'] ?? null);
    }

    /**
     * Map fulfill shipping cost
     *
     * @override BaseValidateBillingImporter::mapFulfillShippingCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillShippingCost(array $row): ?float
    {
        return (float) ($row['summary'] ?? null);
    }

    /**
     * @param     array|null     $row
     *
     * @return bool
     */
    private function isItemFile(?array $row = null): bool
    {
        if (is_null(self::$isItemFile) && is_null($row)) {
            throw new RuntimeException('isItemFile() must be called with a row');
        }

        return self::$isItemFile ??= (bool) $this->mapFulfillSku($row);
    }

    /**
     * @override BaseValidateBillingImporter::orderNotFound()
     *
     * @param $rows
     *
     * @return void
     */
    public function orderNotFound($rows): void
    {
        if (! $this->isItemFile()) {
            $rows = $rows->all();
        }

        parent::orderNotFound($rows);
    }

    /**
     * @override BaseValidateBillingImporter::stopAtFirstError()
     *
     * @return bool
     */
    public function stopAtFirstError(): bool
    {
        return false;
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRows(Collection $orderProducts, Collection $rows): void
    {
        if ($this->isItemFile()) {
            parent::validateRows($orderProducts, $rows);
        } else {
            $this->validateRowsFileShipping($orderProducts, $rows);
        }
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRowsFileShipping(Collection $orderProducts, Collection $rows): void
    {
        $order = $rows->first();
        $errors = [];

        // Cảnh báo nếu có 1 sản phẩm nào đó chưa được giao
        $statuses = [OrderProductFulfillStatus::FULFILLED, OrderProductFulfillStatus::ON_DELIVERY];
        $every = $orderProducts->every(static fn($op) => in_array($op->fulfill_status, $statuses, true));
        if (! $every) {
            $errors[] = static::ERR_UNFULFILLED;
        }

        // Cảnh báo nếu phí ship của supplier lớn hơn phí ship của sen charge seller
        $shippingCost = $orderProducts->sum('fulfill_shipping_cost');
        if ($shippingCost < $order['fulfill_shipping_cost']) {
            $errors[] = static::ERR_SHIPPING_COST;
        }

        // Cảnh báo nếu có 1 sản phẩm nào đó có tracking code khác với tracking code của order
        if (! $orderProducts->every(fn($op) => $op->tracking_code === $order['tracking_code'])) {
            $errors[] = static::ERR_TRACKING_CODE;
        }

        if ($errors) {
            $order['errors'] = implode('; ', $errors);
            $this->errors->push($order);
        }
    }

    /**
     * @return string[]
     */
    public function validators(): array
    {
        return [
            'billedValidator',
            'quantityValidator',
            'fulfillStatusValidator',
            'fulfillBaseCostValidator',
        ];
    }


    /**
     * @return string
     */
    protected function orderKey(): string
    {
        return 'fulfill_order_id';
    }

    /**
     * $row['fulfill_base_cost'] là giá tổng rồi nên không nhân quantity
     *
     * @param               $op
     * @param     array     $row
     *
     * @return array
     */
    public function fulfillBaseCostValidator($op, array $row): array
    {
        return [
            $valid = $op->fulfill_base_cost * $op->quantity > $row['fulfill_base_cost'],
            $valid ? '' : static::ERR_FULFILL_BASE_COST
        ];
    }
}
