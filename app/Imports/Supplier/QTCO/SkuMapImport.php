<?php /** @noinspection DuplicatedCode */

namespace App\Imports\Supplier\QTCO;

use App\Enums\ProductOptionEnum;
use App\Enums\ProductStatus;
use App\Imports\Supplier\ImportTrait;
use App\Models\FulfillProduct;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SkuMapImport implements ToArray, WithHeadingRow
{
    use ImportTrait;

    public function array(array $array): void
    {
        $arr = [];
        $lastProductSku = '';

        foreach ($array as $index => $row) {
            if (
                empty($lastProductSku)
                || $lastProductSku !== $row['client_style']
            ) {
                $lastProductSku = $row['client_style'];
                $productName = $row['client_description'] . ' - ' . $row['client_style'] . ' - ' . $row['client_brand'];
                $arr[$lastProductSku]['product'] = [
                    'name' => $productName,
                ];
            }

            $color = $this->mappingOptions(
                $row['client_color'],
                ProductOptionEnum::COLOR,
            );
            $size = $this->mappingOptions(
                $row['client_size'],
                ProductOptionEnum::SIZE,
            );
            $options = [
                ProductOptionEnum::COLOR => $color,
                ProductOptionEnum::SIZE  => $size,
            ];
            sortArrayOptions($options);

            if (empty($arr[$lastProductSku]['product']['options'])) {
                $arr[$lastProductSku]['product']['options'] = [];
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::COLOR] = [];
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::SIZE] = [];
            }
            if (
                !in_array(
                    $color,
                    $arr[$lastProductSku]['product']['options'][ProductOptionEnum::COLOR]
                )
            ) {
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::COLOR][] = $color;
            }
            if (
                !in_array(
                    $size,
                    $arr[$lastProductSku]['product']['options'][ProductOptionEnum::SIZE]
                )
            ) {
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::SIZE][] = $size;
            }

            $arr[$lastProductSku]['variants'][] = [
                'sku'         => $row['sku'],
                'variant_key' => getVariantKey($options),
            ];
        }

        $arrVariants = [];
        foreach ($arr as $productSku => $each) {
            sortArrayOptions($each['product']['options']);
            $productOption = json_encode($each['product']['options']);
            $product = FulfillProduct::query()
                ->updateOrCreate([
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ], [
                    'name'    => $each['product']['name'],
                    'options' => $productOption,
                    'status'  => ProductStatus::ACTIVE,
                ]);

            $productId = $product->id;
            foreach ($each['variants'] as $variant) {
                $arrVariants[] = [
                    'sku'         => $variant['sku'],
                    'product_id'  => $productId,
                    'variant_key' => $variant['variant_key'],
                ];
            }
        }

        $this->updateToDB($arrVariants);
    }
}
