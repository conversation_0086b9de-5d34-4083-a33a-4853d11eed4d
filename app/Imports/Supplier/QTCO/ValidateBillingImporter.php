<?php

namespace App\Imports\Supplier\QTCO;

use App\Enums\OrderProductFulfillStatus;
use App\Imports\Supplier\BaseValidateBillingImporter;
use Illuminate\Support\Collection;
use RuntimeException;

/**
 * Class ValidateBillingImporter
 *
 * Raw data shipping sample:
 * "article_orderpkidserial" => "Ord284149"
 * "article_order_organisationname" => "SenPrints"
 * "article_orderexternalref" => "21887446"
 * "article_orderctotalitems" => 4
 * "article_ordershippingmethod" => "STANDARD"
 * "article_ordershippingaddresscountry" => "Australia"
 * "article_ordershippingaddresscountrycode" => "AU"
 * "article_ordercweightitems" => 0.704
 * "surcharge_rate" => null
 * "surcharge_total" => null
 * "article_ordershippingaddressstate" => "NSW"
 * "article_ordershippingaddresspostcode" => "2145"
 * "article_packagename" => "Plain Large Bag"
 * "article_itemcategory" => "Garment"
 * "shippingcost_exgst" => 10.7
 * "shippingcost" => 11.77
 * "auspostchargecodeused" => "7D85"
 * "article_package_forsubpackagingname" => null
 * "article_articlesubpackages_forsubpackagingcountscount" => null
 * "timestampshipped" => 45282.*********
 *
 * Raw data item sample:
 */
class ValidateBillingImporter  extends BaseValidateBillingImporter
{
    private static ?bool $isItemFile = null;

    /**
     * Với file excel shipping thì không có sku, chỉ có id đơn hàng và phí ship.
     * Nên không cần phải collapse quantity mà chỉ cần tính phí ship theo đơn
     * hàng bằng cách nhân số lượng sản phẩm với phí ship
     *
     * @param     array     $row
     *
     * @return bool
     *
     * @see self::mapFulfillShippingCost()
     *
     * @override BaseValidateBillingImporter::shouldUnifyQuantity()
     *
     */
    public function shouldUnifyQuantity(array $row): bool
    {
        return $this->isItemFile($row);
    }

    /**
     * Map fulfill order id
     *
     * @override BaseValidateBillingImporter::mapFulfillOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapFulfillOrderId(array $row): ?string
    {
        if ($this->isItemFile($row)) {
            return $row['item_orderexternalref'];
        }

        return $row['article_orderexternalref'];
    }

    /**
     * Để phân biệt đang import file nào thì dùng cột sku
     * File excel shipping có không có sku
     * File excel item có sku
     *
     * @override BaseValidateBillingImporter::mapOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapOrderId(array $row): ?string
    {
        return null;
    }

    /**
     * Get quantity
     *
     * @override BaseValidateBillingImporter::mapQuantity()
     *
     * @param     array     $row
     *
     * @return int|null
     */
    public function mapQuantity(array $row): ?int
    {
        return 1;
    }

    /**
     * Get fulfill sku
     *
     * @override BaseValidateBillingImporter::mapFulfillSku()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapFulfillSku(array $row): ?string
    {
        return (string) ($row['sku'] ?? '');
    }

    /**
     * Cả 2 file excel shipping và item đều có không có tracking code
     *
     * @override BaseValidateBillingImporter::mapTrackingCode()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapTrackingCode(array $row): ?string
    {
        return '';
    }

    /**
     * Map fulfill base cost
     *
     * @override BaseValidateBillingImporter::mapFulfillBaseCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillBaseCost(array $row): ?float
    {
        if ($this->isItemFile($row)) {
            return (float) $row['zcalc_totalitemcost'];
        }

        return null;
    }

    /**
     * Map fulfill shipping cost
     *
     * @override BaseValidateBillingImporter::mapFulfillShippingCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillShippingCost(array $row): ?float
    {
        if ($this->isItemFile($row)) {
            return null;
        }

        return $row['article_orderctotalitems'] * (float) $row['shippingcost'];
    }

    /**
     * @param     array|null     $row
     *
     * @return bool
     */
    private function isItemFile(?array $row = null): bool
    {
        if (is_null($row) && is_null(self::$isItemFile)) {
            throw new RuntimeException('isItemFile() must be called with a row');
        }

        return self::$isItemFile ??= (bool) $this->mapFulfillSku($row);
    }

    /**
     * @override BaseValidateBillingImporter::orderNotFound()
     *
     * @param $rows
     *
     * @return void
     */
    public function orderNotFound($rows): void
    {
        if (! $this->isItemFile()) {
            $rows = $rows->all();
        }

        parent::orderNotFound($rows);
    }

    /**
     * @override BaseValidateBillingImporter::stopAtFirstError()
     *
     * @return bool
     */
    public function stopAtFirstError(): bool
    {
        return false;
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRows(Collection $orderProducts, Collection $rows): void
    {
        if ($this->isItemFile()) {
            parent::validateRows($orderProducts, $rows);
        } else {
            $this->validateRowsFileShipping($orderProducts, $rows);
        }
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRowsFileShipping(Collection $orderProducts, Collection $rows): void
    {
        $order = $rows->first();
        $errors = [];

        // Cảnh báo nếu có 1 sản phẩm nào đó chưa được giao
        $statuses = [OrderProductFulfillStatus::FULFILLED, OrderProductFulfillStatus::ON_DELIVERY];
        $every = $orderProducts->every(static fn($op) => in_array($op->fulfill_status, $statuses, true));
        if (! $every) {
            $errors[] = static::ERR_UNFULFILLED;
        }

        // Cảnh báo nếu phí ship của supplier lớn hơn phí ship của sen charge seller
        $shippingCost = $orderProducts->sum('fulfill_shipping_cost');
        if ($shippingCost < $order['fulfill_shipping_cost']) {
            $errors[] = static::ERR_SHIPPING_COST;
        }

        if ($errors) {
            $order['errors'] = implode('; ', $errors);
            $this->errors->push($order);
        }
    }

    /**
     * @return string[]
     */
    public function validators(): array
    {
        return [
            'billedValidator',
            'quantityValidator',
            'fulfillStatusValidator',
            'fulfillBaseCostValidator',
        ];
    }


    /**
     * @return string
     */
    protected function orderKey(): string
    {
        return 'fulfill_order_id';
    }
}
