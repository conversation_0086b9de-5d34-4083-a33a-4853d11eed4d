<?php

namespace App\Imports\Supplier\LuxuryPro;

use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\FulfillProduct;
use Illuminate\Support\Arr;

trait TemplateTrait
{
    protected function getDataProduct($row, array $mapping): array
    {
        $this->parseBeforeMapping($row);

        foreach ($mapping as $key => $value) {
            if (is_array($value)) {
                $row[$key] = '';
                foreach ($value as $v) {
                    $row[$key] .= ($row[$v] ?? '') . '|';
                }
            } else {
                $row[$key] = $row[$value] ?? null;
            }
        }

        return [
            'sku'          => Arr::get($row, 'spu'),
            'name'         => Arr::get($row, 'name'),
            'description'  => Arr::get($row, 'description'),
            'base_cost'    => $this->getBaseCost($row['base_cost']),
            'product_type' => ProductType::FULFILL_PRODUCT,
            'supplier_id'  => $this->supplier_id,
            'thumb_url'    => Arr::get($row, 'thumb_url'),
        ];
    }

    protected function insert($arr): void
    {
        $variants = [];

        foreach ($arr as $productSku => $each) {
            $each['options'] = json_encode($each['options']);
            $each['status'] = ProductStatus::ACTIVE;
            $product = FulfillProduct::query()
                ->updateOrCreate([
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ], $each);

            foreach ($each['variants'] as $variant) {
                $variants[] = [
                    'product_id'  => $product->id,
                    'sku'         => $variant['sku'],
                    'variant_key' => $variant['variant_key'],
                    'base_cost'   => $variant['base_cost'],
                ];
            }
        }

        $this->updateToDB($variants);
    }
}
