<?php /** @noinspection DuplicatedCode */

namespace App\Imports\Supplier\LuxuryPro;

use App\Enums\ProductOptionEnum;
use App\Imports\Supplier\ImportTrait;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MugsImport implements ToArray, WithHeadingRow
{
    use ImportTrait;
    use TemplateTrait;

    public function array(array $array): void
    {
        // dd($array);
        $arr = [];
        $lastProductSku = '';

        foreach ($array as $index => $row) {
            if ($index === 0) {
                continue;
            }

            $dataProduct = $this->getDataProduct(
                $row,
                [
                    'name'        => 'product_name',
                    'spu'         => 'spu',
                    'description' => 'material',
                    'base_cost'   => 'base_cost_usd',
                    'thumb_url'   => 'picture',
                ]
            );
            $dataVariant = $this->getDataVariant($row);

            if (!empty($dataProduct['sku'])) {
                $lastProductSku = $dataProduct['sku'];
            }

            if (empty($arr[$lastProductSku])) {
                $arr[$lastProductSku] = $dataProduct;
            }

            if (!empty($dataProduct['base_cost'])) {
                $baseCost = $dataProduct['base_cost'];
            } else {
                $baseCost = $arr[$lastProductSku]['base_cost'];
            }

            $variant = [
                'sku'         => $dataVariant['variant_sku'],
                'variant_key' => getVariantKey([$dataVariant['size']]),
                'base_cost'   => $baseCost,
            ];

            $arr[$lastProductSku]['options']['size'][] = $dataVariant['size'];
            $arr[$lastProductSku]['variants'][] = $variant;
            // dd($arr);
        }

        $this->insert($arr);
    }

    private function getDataVariant($row): array
    {
        return [
            'variant_sku' => $row['sku'],
            'size'        => $this->getSize($row),
        ];
    }

    private function getSize($row): string
    {
        $size = Str::afterLast($row['sku'], '-');

        return $this->mappingOptions(
            $size,
            ProductOptionEnum::SIZE,
        );
    }
}
