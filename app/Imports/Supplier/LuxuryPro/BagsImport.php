<?php /** @noinspection DuplicatedCode */

namespace App\Imports\Supplier\LuxuryPro;

use App\Enums\ProductOptionEnum;
use App\Imports\Supplier\ImportTrait;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class BagsImport implements ToArray, WithHeadingRow
{
    use ImportTrait;
    use TemplateTrait;

    public function array(array $array): void
    {
        // dd($array[0]);
        $arr = [];
        $lastProductSku = '';
        $lastProductName = '';

        foreach ($array as $index => $row) {
            if ($index < 2) {
                continue;
            }
            $size = '';

            $dataProduct = $this->getDataProduct(
                $row,
                [
                    'name'      => 'product',
                    'base_cost' => 'base_cost_usd',
                ]
            );

            if (!empty($dataProduct['name'])) {
                $lastProductName = $dataProduct['name'];
            } else {
                $dataProduct['name'] = $lastProductName;
            }

            $data = explode('-', $row['sku']);
            if (count($data) > 1) {
                [$dataProduct['sku'], $size] = $data;
            } else {
                $dataProduct['sku'] = $data[0];
            }

            $size = $this->mappingOptions(
                $size,
                ProductOptionEnum::SIZE,
                $row['product'],
            );
            $dataVariant = [
                'variant_sku' => $row['sku'],
                'size'        => $size,
            ];

            if (!empty($dataProduct['sku'])) {
                $lastProductSku = $dataProduct['sku'];
            }

            if (empty($arr[$lastProductSku])) {
                $arr[$lastProductSku] = $dataProduct;
            }

            if (!empty($dataProduct['base_cost'])) {
                $baseCost = $dataProduct['base_cost'];
            } else {
                $baseCost = $arr[$lastProductSku]['base_cost'];
            }

            $variant = [
                'sku'         => $dataVariant['variant_sku'],
                'variant_key' => getVariantKey([$dataVariant['size']]),
                'base_cost'   => $baseCost,
            ];

            if (!empty($dataVariant['size'])) {
                $arr[$lastProductSku]['options']['size'][] = $dataVariant['size'];
            } else {
                $arr[$lastProductSku]['options'] = [];
            }
            $arr[$lastProductSku]['variants'][] = $variant;

            // dd($arr);
        }
        // dd($arr);

        $this->insert($arr);
    }
}
