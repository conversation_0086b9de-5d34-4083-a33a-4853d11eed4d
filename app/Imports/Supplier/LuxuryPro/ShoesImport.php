<?php /** @noinspection DuplicatedCode */

namespace App\Imports\Supplier\LuxuryPro;

use App\Enums\ProductOptionEnum;
use App\Imports\Supplier\ImportTrait;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ShoesImport implements ToArray, WithHeadingRow
{
    use ImportTrait;
    use TemplateTrait;

    public function array(array $array): void
    {
        // dd($array);
        $arr = [];
        $lastProductSku = '';
        $lastProductName = '';

        foreach ($array as $index => $row) {
            if ($index === 0) {
                continue;
            }

            $dataProduct = $this->getDataProduct(
                $row,
                [
                    'name'        => 'name',
                    'spu'         => 'spu',
                    'description' => 'material',
                    'base_cost'   => 'base_cost_usd',
                    'thumb_url'   => 'picture',
                ]
            );

            if (!empty($dataProduct['name'])) {
                $lastProductName = $dataProduct['name'];
            } else {
                $dataProduct['name'] = $lastProductName;
            }
            $dataProduct['name'] = preg_replace('/\s+/', ' ', $dataProduct['name']);

            if (!empty($dataProduct['sku'])) {
                $lastProductSku = $dataProduct['sku'];
            }

            if (empty($arr[$lastProductSku])) {
                $arr[$lastProductSku] = $dataProduct;
            }

            if (!empty($dataProduct['base_cost'])) {
                $baseCost = $dataProduct['base_cost'];
            } else {
                $baseCost = $arr[$lastProductSku]['base_cost'];
            }

            $sizes = $this->getSizes($row);
            $variants = [];
            foreach ($sizes as $size) {
                $variants[] = [
                    'sku'         => $lastProductSku . '-' . $size,
                    'variant_key' => getVariantKey([$size]),
                    'base_cost'   => $baseCost,
                ];
            }

            if (empty($arr[$lastProductSku]['options'])) {
                $arr[$lastProductSku]['options']['size'] = [];
            }
            $arr[$lastProductSku]['options']['size'] = array_merge(
                $arr[$lastProductSku]['options']['size'],
                $sizes
            );
            if (empty($arr[$lastProductSku]['variants'])) {
                $arr[$lastProductSku]['variants'] = $variants;
            } else {
                $arr[$lastProductSku]['variants'] = array_merge(
                    $arr[$lastProductSku]['variants'],
                    $variants
                );
            }

            // dd($arr);
        }
        // dd($arr);

        $this->insert($arr);
    }

    private function getSizes($row): array
    {
        $sizes = [];
        $column = $row['sizecm'];

        if (Str::contains($column, '-')) {
            [$min, $max] = explode('-', $column);
            $min = filter_var($min, FILTER_SANITIZE_NUMBER_INT);
            $max = filter_var($max, FILTER_SANITIZE_NUMBER_INT);
            for ($i = $min; $i <= $max; $i++) {
                $sizes[] = $i;
            }
        } else if (Str::contains($column, ' ')) {
            $sizes = explode(' ', $column);
        } else {
            $sizes[] = $column;
        }

        foreach ($sizes as $key => $size) {
            $sizes[$key] = $this->mappingOptions(
                $size,
                ProductOptionEnum::SIZE,
            );
        }

        return $sizes;
    }
}
