<?php

namespace App\Imports\Supplier;

use App\Enums\OrderProductFulfillStatus;
use App\Models\OrderProduct;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

abstract class BaseValidateBillingImporter  implements ToArray, WithHeadingRow
{
    public const ERR_UNFULFILLED = 'unfulfilled';
    public const ERR_BILLED = 'billed';
    public const ERR_SKU_NOT_FOUND = 'sku not found';
    public const ERR_QUANTITY = 'quantity invalid';
    public const ERR_SHIPPING_COST = 'shipping cost invalid';
    public const ERR_FULFILL_BASE_COST = 'fulfill base cost invalid';
    public const ERR_ORDER_NOT_FOUND = 'order id not found';
    public const ERR_ORDER_COST = 'total order cost invalid';
    public const ERR_TRACKING_CODE = 'tracking code invalid';

    protected Collection $errors;

    protected Collection $valid;

    /**
     * @var string|string[]|int|int[]
     */
    private $supplierId;

    /**
     * @param     string|string[]|int|int[]     $supplierId
     */
    public function __construct($supplierId)
    {
        $this->supplierId = $supplierId;
        $this->valid = collect();
        $this->errors = collect();
    }

    /**
     * @return string
     */
    public function supplierId()
    {
        return $this->supplierId;
    }

    /**
     * Map fulfill order id
     *
     * @param     array     $row
     *
     * @return string
     */
    abstract public function mapFulfillOrderId(array $row): ?string;

    /**
     * Map order id
     *
     * @param     array     $row
     *
     * @return string|null
     */
    abstract public function mapOrderId(array $row): ?string;

    /**
     * Map quantity
     *
     * @param     array     $row
     *
     * @return int
     */
    abstract public function mapQuantity(array $row): ?int;

    /**
     * Map fulfill sku
     *
     * @param     array     $row
     *
     * @return string
     */
    abstract public function mapFulfillSku(array $row): ?string;

    /**
     * Map tracking code
     *
     * @param     array     $row
     *
     * @return string
     */
    abstract public function mapTrackingCode(array $row): ?string;

    /**
     * Map fulfill base cost
     *
     * @param     array     $row
     *
     * @return float
     */
    abstract public function mapFulfillBaseCost(array $row): ?float;

    /**
     * Map fulfill shipping cost
     *
     * @param     array     $row
     *
     * @return float
     */
    abstract public function mapFulfillShippingCost(array $row): ?float;

    /**
     * @return string[]
     * @example  [
     *    'skuValidator',
     *    'billedValidator',
     *    'fulfillStatusValidator',
     *    'trackingCodeValidator',
     *  ]
     */
    abstract public function validators(): array;

    /**
     * Sau khi import thành công file, tuỳ thuộc có triển khai multisheet
     * hay không thì dữ liệu các sheet sẽ vào đây.
     * Lúc này follow chuẩn để thực hiện là
     * - Chuẩn hóa dữ liệu
     * - Tiền xử lí dữ liệu trước khi chunk
     * - Chunk dữ liệu
     * - Validate dữ liệu
     *
     * @param     array     $rows
     */
    public function array(array $rows): void
    {
        $this->standardize($rows)
//            ->dd()
            ->pipe(fn(Collection $rows) => $this->preChunk($rows))
//            ->dd()
            ->chunk(50)
//            ->dd()
            ->each(fn($rows) => $this->validate($rows));
    }

    /**
     * Với data thô có được từ file excel, ta cần chuẩn hóa lại dữ liệu.
     * Trong quá trình chuẩn hoá có thể sẽ cần hợp nhất một số thông tin cho
     * phù hợp với tình huống.
     * - Chuẩn hóa các trường dữ liệu
     * - Tính lại quantity nếu cần thiết vì có thể có
     *   trường hợp 1 order có nhiều dòng cùng sku
     *
     * @param     array     $rows
     *
     * @return \Illuminate\Support\Collection
     */
    public function standardize(array $rows): Collection
    {
        return array_reduce($rows, function(Collection $res, array $row): Collection {
            if ($this->validRow($row)) {
                [$key, $validRow] = $this->unifyQuantity($res, $row);

                $res->put($key, $validRow);
            }

            return $res;
        }, collect([]));
    }

    /**
     * Điều kiện 1 dòng dữ liệu hợp lệ giúp chúng ta có thể chủ động bỏ qua
     * những dòng không hợp lệ để tối ưu tốc độ xử lí
     *
     * @param     array     $row
     *
     * @return bool
     */
    public function validRow(array $row): bool
    {
        return (bool) $this->mapFulfillOrderId($row);
    }

    /**
     * Method này sẽ được gọi khi ta cần tính lại quantity của row
     * Mặc định là true
     * Nếu ta không cần tính lại quantity thì ta sẽ trả về false
     *
     * @param     array     $row
     *
     * @return bool
     */
    public function shouldUnifyQuantity(array $row): bool
    {
        return true;
    }

    /**
     * Trường hợp lý tưởng là fulfill_order_id chính là id của order_product thì
     * ta sẽ không cần phải tính lại quantity. Ta chỉ cần so sánh từng row trong
     * file và dữ liệu đã lưu trong db.
     * - Tuy nhiên hiếm gặp trường họp lý tưởng như vậy nên ta cần tuỳ cơ ứng biến
     * theo từng case cụ thể. Để có thể phù hợp với đa số trường hợp đã gặp thì
     * ta sẽ nhóm các row theo một quy tắc nào đó để có thể dùng để so sánh.
     * - Để giúp code có thể dễ dàng mở rộng thì ta sẽ triển khai template
     * design pattern để có thể override lại các phương thức cần thiết.
     * - Khi vòng lặp chạy qua mỗi raw row thì ta cần chuẩn bị một key dùng để nhóm các row
     * lại với nhau. Mặc định là fulfill_order_id - fulfill_sku
     * (Vì đa phần fulfill_order_id = order_id sau khi ta làm sạch dữ liệu)
     * - Sau khi có được key thì ta sẽ chuẩn hóa dữ liệu của row đó vì đây
     * mới là dữ liệu mình sẽ dùng trong bước validate
     * - Sau khi chuẩn hóa xong thì ta sẽ kiểm tra xem row đó đã tồn tại trong
     * dữ liệu đã chuẩn bị trước đó hay chưa. Nếu đã tồn tại thì ta sẽ tính lại
     * quantity của row đó và cộng vào quantity của row đã tồn tại. Nếu chưa tồn
     * tại thì ta sẽ lấy row đó làm row chuẩn bị cho bước validate.
     *
     * @param     \Illuminate\Support\Collection     $results
     * @param     array                              $row
     *
     * @return array
     */
    public function unifyQuantity(Collection $results, array $row): array
    {
        $key = $this->preValidateKey($row);
        $standardizedRow = $this->standardizeRow($row);

        $existsRow = $results->get($key);

        if ($existsRow && $this->shouldUnifyQuantity($standardizedRow)) {
            $existsRow['quantity'] += $standardizedRow['quantity'];
        } else {
            $existsRow = $standardizedRow;
        }

        return [$key, $existsRow];
    }

    /**
     * Thực hiện các thao tác chuẩn bị trước khi chunk
     * Mặc định là group theo order key
     *
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return \Illuminate\Support\Collection
     */
    protected function preChunk(Collection $rows): Collection
    {
        return $rows->groupBy($this->orderKey());
    }

    /**
     * Chuẩn hóa 1 dòng dữ liệu
     *
     * @param     array     $row
     *
     * @return array
     */
    public function standardizeRow(array $row): array
    {
        return [
            'fulfill_order_id' => $this->mapFulfillOrderId($row),
            'order_id' => $this->mapOrderId($row),
            'quantity' => $this->mapQuantity($row),
            'fulfill_sku' => $this->mapFulfillSku($row),
            'tracking_code' => $this->mapTrackingCode($row),
            'fulfill_base_cost' => $this->mapFulfillBaseCost($row),
            'fulfill_shipping_cost' => $this->mapFulfillShippingCost($row),
        ];
    }

    /**
     * Validate rows
     *
     * @param     \Illuminate\Support\Collection     $chunk
     *
     * @return void
     */
    public function validate(Collection $chunk): void
    {
        $groupedOrderProducts = $this->loadOrderWithProducts($chunk)->groupBy(
            $this->orderKey()
        );

        foreach ($chunk as $key => $rows) {
            if (! $oderProducts = $groupedOrderProducts->get($key)) {
                $this->orderNotFound($rows);
            } else {
                $this->validateRows($oderProducts, $rows);
            }
        }
    }

    /**
     * @param     \Illuminate\Support\Collection     $orderProducts
     * @param     \Illuminate\Support\Collection     $rows
     *
     * @return void
     */
    public function validateRows(Collection $orderProducts, Collection $rows): void
    {
        $orderProducts = $this->collapseOrderProducts($orderProducts);

        foreach ($rows as $row) {
            $key = $this->collapseOrderProductKey($row);

            if (! $op = $orderProducts->get($key)) {
                $this->rowError($row, [static::ERR_SKU_NOT_FOUND]);
                continue;
            }

            $errors = [];
            foreach ($this->validators() as $validator) {
                [$status, $message] = $this->$validator($op, $row);

                if ($status !== true) {
                    $errors[] = $message;
                }

                if ($status !== true && $this->stopAtFirstError()) {
                    break;
                }
            }

            if ($errors) {
                $this->rowError($row, $errors);
            }
        }
    }

    /**
     * @param     array     $row
     * @param     array     $errors
     *
     * @return void
     */
    protected function rowError(array $row, array $errors): void
    {
        $this->errors->push(
            array_merge($row, ['note' => implode('; ', $errors)])
        );
    }

    /**
     * Nhóm lại các order product theo tiêu chí nào đó
     * Mặc định là theo fulfill_sku
     *
     * @param     \Illuminate\Support\Collection     $ops
     *
     * @return mixed
     */
    public function collapseOrderProducts(Collection $ops)
    {
        return $ops->reduce(function (Collection $res, $op) {
            $key = $this->collapseOrderProductKey($op);
            $exists = $res->get($key);

            if ($exists) {
                $exists->quantity += $op->quantity;
                $exists->shipping_cost += $op->shipping_cost;
                $exists->fulfill_shipping_cost += $op->fulfill_shipping_cost;

                $statuses = [OrderProductFulfillStatus::FULFILLED, OrderProductFulfillStatus::ON_DELIVERY];
                if (! in_array($op->fulfill_status, $statuses, true)) {
                    $exists->fulfill_status = $op->fulfill_status;
                }

                if ($op->billed_at) {
                    $exists->billed_at = $op->billed_at;
                }

                $res[$key] = $exists;
            } else {
                $res[$key] = $op;
            }

            return $res;
        }, collect());
    }

    /**
     * @param $data
     *
     * @return string
     */
    public function collapseOrderProductKey($data): string
    {
        return data_get($data, 'fulfill_sku');
    }

    /**
     * @return bool
     */
    public function stopAtFirstError(): bool
    {
        return true;
    }

    /**
     * @param               $op
     * @param     array     $row
     *
     * @return array
     */
    public function totalOrderCostValidator($op, array $row): array
    {
        $savedCost = $op->fulfill_base_cost + $op->fulfill_shipping_cost;
        $importedCost = $row['fulfill_base_cost'] + $row['fulfill_shipping_cost'];
        $senCost = $op->base_cost + $op->shipping_cost;
        $valid = $senCost >= $savedCost && $savedCost >= $importedCost;

        return [! $valid, ! $valid ? '' : static::ERR_ORDER_COST];
    }

    /**
     * @param               $op
     * @param     array     $row
     *
     * @return array
     */
    public function billedValidator($op, array $row): array
    {
        return [! $op->billed_at, ! $op->billed_at ? '' : static::ERR_BILLED];
    }

    /**
     * @param               $op
     * @param     array     $row
     *
     * @return array
     */
    public function fulfillStatusValidator($op, array $row): array
    {
        $fulfilled = in_array($op->fulfill_status, [
            OrderProductFulfillStatus::FULFILLED,
            OrderProductFulfillStatus::ON_DELIVERY
        ], true);

        return [
            $fulfilled,
            $fulfilled ? '' : static::ERR_UNFULFILLED
        ];
    }

    /**
     * @param               $op
     * @param     array     $row
     *
     * @return array
     */
    public function quantityValidator($op, array $row): array
    {
        return [
            $equal = (int) $op->quantity === (int) $row['quantity'],
            $equal ? '' : static::ERR_QUANTITY
        ];
    }

    /**
     * @param               $op
     * @param     array     $row
     *
     * @return array
     */
    public function fulfillBaseCostValidator($op, array $row): array
    {
        return [
            $valid = $op->fulfill_base_cost > $row['fulfill_base_cost'],
            $valid ? '' : static::ERR_FULFILL_BASE_COST
        ];
    }

    /**
     * @override BaseValidateBillingImporter::preValidateKey()
     *
     * @param $row
     *
     * @return string
     */
    public function preValidateKey($row): string
    {
        return $this->mapFulfillOrderId($row) . '-' . $this->mapFulfillSku($row);
    }

    /**
     * @param     \Illuminate\Support\Collection     $chunk
     *
     * @return \Illuminate\Support\Collection
     */
    protected function loadOrderWithProducts(Collection $chunk): Collection
    {
        return $this->getOrderProducts(
            $chunk->keys()->toArray()
        );
    }

    /**
     * @param     array     $orderIDs
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getOrderProducts(array $orderIDs, string $orderKey = 'order_id'): Collection
    {
        return DB::table('order_product')
            ->select([
                'order_id',
                'fulfill_order_id',
                'fulfill_sku',
                'quantity',
                'fulfill_status',
                'tracking_code',
                'billed_at',
                'base_cost', // giá sản phẩm của sen
                DB::raw('fulfill_cost as fulfill_base_cost'), // 'fulfill_base_cost',
                'shipping_cost', // phi ship của sen
                DB::raw('base_shipping_cost as fulfill_shipping_cost'), // 'fulfill_shipping_cost'
            ])
            ->whereIn('supplier_id', is_array($this->supplierId) ? $this->supplierId : [$this->supplierId])
            ->whereIn($orderKey, $orderIDs)
            ->whereNull('deleted_at')
            ->get();
    }

    /**
     * Chúng ta cần một cách để nhóm các row giống nhau lại với nhau
     * và nó phải đồng nhất ở cả dữ liệu excel và dữ liệu và db. Việc này
     * để chúng ta có thể lặp qua từng nhóm có khoá giống nhau để xử lí
     *
     * @return string|Closure
     */
    protected function orderKey()
    {
        return 'fulfill_order_id';
    }

    /**
     * @param $rows
     *
     * @return void
     */
    public function orderNotFound($rows): void
    {
        foreach ($rows as $row) {
            $this->errors->push(
                array_merge($row, ['note' => static::ERR_ORDER_NOT_FOUND])
            );
        }
    }

    /**
     * @return void
     */
    public function markBilled(): void
    {
        $this->valid()
            ->each(function($row) {
                OrderProduct::query()
                    ->where('fulfill_order_id', $row['fulfill_order_id'])
                    ->where('fulfill_sku', $row['fulfill_sku'])
                    ->update(['billed_at' => now()]);
            });
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function errors(): Collection
    {
        return $this->errors;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function valid(): Collection
    {
        return $this->valid;
    }
}
