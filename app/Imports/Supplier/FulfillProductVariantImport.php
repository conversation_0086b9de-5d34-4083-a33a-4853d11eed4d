<?php

namespace App\Imports\Supplier;

use App\Enums\CacheKeys;
use App\Enums\ProductPrintType;
use App\Enums\SupplierEnum;
use App\Models\FulfillProduct;
use App\Models\ProductFulfillMapping;
use App\Models\ProductVariant;
use App\Models\Template;
use App\Services\FulfillmentService;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use RuntimeException;
use Illuminate\Support\Arr;
use App\Data\Product\ProductVariantData;
class FulfillProductVariantImport implements ToArray, WithHeadingRow
{
    use ImportTrait;
    // fields: Product SKU,SKU,Base Cost,Out Of Stock,Options:Color,Options:Size,...

    public function __construct($supplierId)
    {
        $this->supplier_id = $supplierId;
    }

    public function array($array): void
    {
        $arr                 = [];
        $arrUpsert           = [];
        $arrFulfillProductId = [];
        $productSkus = array_column($array, 'product_sku');
        $productSkus = array_unique($productSkus);
        $productTemplates = Template::query()->select([
            'sku',
            'full_printed'
        ])->whereIn('sku', $productSkus)
            ->get()
            ->keyBy('sku')
            ->toArray();
        $supplierOptionMapping = FulfillmentService::handleSupplierOptionMapping($array);

        foreach ($array as $row) {
            $this->parseBeforeMapping($row);
            $productSku = $row['product_sku'];
            if (empty($arr[$productSku])) {
                $arr[$productSku] = FulfillProduct::query()
                    ->select([
                        'id',
                        'name',
                    ])
                    ->firstWhere(
                        [
                            'supplier_id' => $this->supplier_id,
                            'sku'         => $productSku
                        ]
                    );
                if (is_null($arr[$productSku])) {
                    throw new RuntimeException('Product have SKU: ' . $productSku . ' not found');
                }

                //drop all variant belong to product to insert new
                ProductVariant::query()
                    ->where('product_id', $arr[$productSku]->id)
                    ->delete();
                $arrFulfillProductId[] = $arr[$productSku]->id;
            }
            // set null to insert match number columns
            // prevent row have, row don't

            $row['base_cost']    ??= null;
            $row['out_of_stock'] ??= empty($row['sku']);
            $row['weight']       ??= null;
            if (empty($row['out_of_stock'])) {
                $row['out_of_stock'] = 0;
            }
            $row['out_of_stock'] = intval($row['out_of_stock']);
            $row['sku'] = str_replace(' ', '_', $row['sku']);
            $row['product_id']  = $arr[$productSku]->id;
            if (!empty(Arr::get($supplierOptionMapping, $productSku))) {
                $newKey = $supplierOptionMapping[$productSku];
                $supplierOptionMapping[$row['product_id']] = $newKey;
                unset($supplierOptionMapping[$productSku]);
            }
            $isHandmade = false;
            if (isset($productTemplates[$productSku]) && $productTemplates[$productSku]['full_printed'] == ProductPrintType::HANDMADE) {
                $isHandmade = true;
            }
            $row['variant_key'] = $this->getVariantKey($row, $arr[$productSku]->name, $isHandmade);
            unset($row['product_sku']); // remove unused field to insert to table
            unset($row['size_of_supplier']);
            $arrUpsert[] = ProductVariantData::removeNullValues(ProductVariantData::from($row)->toArray());
        }
        if (!empty($supplierOptionMapping)) {
            FulfillmentService::addOptionMappingToAttributes($supplierOptionMapping, $this->supplier_id);
        }
        $templateIds = ProductFulfillMapping::query()
            ->whereIn('fulfill_product_id', $arrFulfillProductId)
            ->where('supplier_id', $this->supplier_id)
            ->pluck('product_id');

        $cacheKeys = [];
        foreach ($templateIds as $templateId) {
            $cacheKeys['tags'][] = CacheKeys::getTemplateFulfillProduct($templateId);
        }
        syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
        ProductVariant::insert($arrUpsert);
    }

    private function getVariantKey(&$row, $productName, $isHandmade = false): string
    {
        return getVariantKey($this->getOptions($row, $productName, $isHandmade));
    }

    private function getOptions(&$row, $productName, $isHandmade = false): array
    {
        $column       = 'options';
        $stringLength = strlen($column);
        $options      = [];
        foreach ($row as $key => $value) {
            if (strpos($key, $column) !== false) {
                // check if option empty
                if (!empty($value)) {
                    $optionCode = substr($key, $stringLength);
                    $mappingType = !$isHandmade ? $optionCode : 'all';
                    $value      = $this->mappingOptions(
                        $value,
                        $mappingType,
                        $productName,
                        $this->supplier_id
                    );

                    if (!empty($value)) {
                        $options[$optionCode] = $value;
                    }
                }

                unset($row[$key]);
            }
        }
        sortArrayOptions($options);
        return $options;
    }

    private array $OVERPASS_MAPPING_OPTIONS_FOR_SUPPLIERS = [
        SupplierEnum::SENPRINT_VN
    ];

    private array $ARRAY_MAPPING = [
        SupplierEnum::SENPRINT_VN     => [
            'Printway-Custom Shape Acrylic Ornament' => [
                '3-93x3-93 inches' => '3-93x3-93 inches',
                '6x6 inches' => '6x6 inches',
                '8x8 inches' => '8x8 inches'
            ],
        ],
    ];
}
