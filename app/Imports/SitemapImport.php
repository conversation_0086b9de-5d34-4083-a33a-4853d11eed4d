<?php

namespace App\Imports;

use App\Models\SitemapUrls;
use Maatwebsite\Excel\Concerns\ToModel;

class SitemapImport implements ToModel
{
    protected $sitemapId;

    public function __construct($sitemapId)
    {
        $this->sitemapId = $sitemapId;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new SitemapUrls([
            'sitemap_id' => $this->sitemapId,
            'url' => $row[0],
        ]);
    }
}
