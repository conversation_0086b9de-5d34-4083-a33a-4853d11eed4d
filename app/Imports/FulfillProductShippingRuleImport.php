<?php

namespace App\Imports;

use App\Enums\ProductType;
use App\Enums\ShippingMethodEnum;
use App\Models\Product;
use App\Models\ShippingRule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

// no format heading
HeadingRowFormatter::default('none');

class FulfillProductShippingRuleImport implements ToCollection, WithHeadingRow
{

    public const COST_BASE = 'base';

    public const COST_EXTRA = 'extra';

    public const SHIP_MIN_DAYS = 'min_days';

    public const SHIP_MAX_DAYS = 'max_days';

    private string $separateRegex;

    public function __construct()
    {
        $this->separateRegex = $this->separateRegex();
    }

    // fields: Product SKU;Variant Key;Supplier Id;standard-base-150,*,US;standard-extra-150,*,US;express-base-US,*;express-extra-US,*...
    public function collection(Collection $rows): void
    {
        $rules = $this->transform($rows);

        // chuyển key của products thành sku-supplier_id) để dễ dàng lấy id sản phẩm
        $products = $this->getAvailableProducts($rules)->keyBy(
            fn($p) => $this->productToKey($p)
        );

        // Để giữ cho logic nghiệp vụ đơn giản ta sẽ lọc ra những rule có sản phẩm trong db và thêm product_id vào rule
        $rules = array_reduce($rules, function(array $result, array $item) use($products) {
            $key = $this->ruleToKey($item);

            unset($item['sku']);

            if ($products->has($key)) {
                $item['product_id'] = $products->get($key)->id;
                $result[] = $item;
            }

            return $result;
        }, []);

        // tạo mới shipping rule
        // xoá các rule cũ
        ShippingRule::whereIn('product_id', $products->pluck('id'))->delete();

        // tạo mới các rule
        foreach (array_chunk($rules, 1000) as $chunk) {
            ShippingRule::insert($chunk);
        }

        // cập nhật shipping cost cho sản phẩm bằng cách lấy phí ship lớn nhất
        // của các rule có cùng product_id và shipping_method là standard
        // việc này sẽ có lợi cho sen khi tính phí ship cho sản phẩm
        $upsert = array_reduce($rules, static function(array $result, array $item) {
            if ($item['shipping_method'] === ShippingMethodEnum::STANDARD) {
                $pId = $item['product_id'];

                $result[$pId] ??= [
                    'id'            => $pId,
                    'shipping_cost' => 0,
                ];

                if ($result[$pId]['shipping_cost'] < $item['shipping_cost']) {
                    $result[$pId]['shipping_cost'] = $item['shipping_cost'];
                }
            }

            return $result;
        }, []);

        Product::upsert($upsert, 'id');
    }

    /**
     * @param     array     $rules
     *
     * @return Collection
     */
    private function getAvailableProducts(array $rules): Collection
    {
        return $this->productQuery()
            ->select('sku', 'id', 'supplier_id')
            ->tap(fn($q) => $this->applyGetProductCondition($q, $rules))
            ->get();
    }

    /**
     * @return Builder
     */
    private function productQuery(): Builder
    {
        return Product::query()->where('product_type', ProductType::FULFILL_PRODUCT);
    }

    /**
     * @param                                        $q
     * @param     array                              $rules
     *
     * @return void
     */
    private function applyGetProductCondition($q, array $rules): void
    {
        collect($rules)
            ->groupBy('supplier_id')
            ->each(function($rules, $supplierId) use($q) {
                $q->orWhere(
                    fn($q) => $q->where('supplier_id', $supplierId)->whereIn('sku', $rules->pluck('sku')->unique())
                );
            });
    }

    /**
     * @param    Product    $p
     *
     * @return string|null
     */
    private function productToKey(Product $p): ?string
    {
        return $p->sku . '-' . $p->supplier_id;
    }

    /**
     * @param     array     $rule
     *
     * @return string
     */
    private function ruleToKey(array $rule): string
    {
        return $rule['sku'] . '-' . $rule['supplier_id'];
    }

    /**
     * @return string
     */
    public function separateRegex(): string
    {
        return sprintf(
            "#(%s)-(%s)-([^-]+)#",
            implode('|', [ShippingMethodEnum::STANDARD, ShippingMethodEnum::EXPRESS]),
            implode('|', [static::COST_BASE, static::COST_EXTRA, static::SHIP_MIN_DAYS, static::SHIP_MAX_DAYS])
        );
    }

    /**
     * @param    Collection    $rows
     *
     * @return array
     */
    private function transform(Collection $rows): array
    {
        $rules = [];
        /**
         * @var Collection $row
         */
        foreach ($rows as $row) {
            if (! $this->validRow($row)) {
                continue;
            }

            $seed = $this->seed($row);

            foreach ($this->standardized($row) as $method => $options) {
                foreach ($options as $locationCode => $values) {
                    $locationCode = $locationCode === 'WW' ? '*' : $locationCode;

                    // Bỏ qua option nếu thiếu phí ship cơ sở
                    if (empty($values[static::COST_BASE])) {
                        continue;
                    }

                    $rules[] = array_merge($seed, [
                        'shipping_method' => $method,
                        'location_code'   => strtoupper($locationCode),
                        'shipping_cost'   => $values['base'],
                        'extra_cost'      => $values['extra'] ?? $values['base'],
                        'min_days'        => $values['min_days'] ?? null,
                        'max_days'        => $values['max_days'] ?? null,
                    ]);
                }
            }
        }

        return $rules;
    }

    /**
     *  - Trường sku là bắt buộc phải có. Mục đích trường này để lấy id sản phẩm sẽ
     *    được dùng khi lưu shipping rule
     *  - Fulfill product thì required supplier id vì sản phẩm fulfill từ các
     *    supplier khác nhau có thể có sku giống nhau nên cần phải có supplier id để
     *    lấy đúng sản phẩm
     *
     * @param $row
     *
     * @return bool
     */
    protected function validRow($row): bool
    {
        return ! empty($row->first()) && ! empty($row['Supplier Id']);
    }

    /**
     * @param    Collection    $row
     *
     * @return array
     */
    protected function seed(Collection $row): array
    {
        return [
            'sku'         => $row->first(),
            'supplier_id' => $row['Supplier Id'] ?? '',
            'variant_key' => (string) Str::of($row['Variant Key'] ?? '')->replace(' ', '')->lower(),
        ];
    }

    /**
     * @param    Collection    $row
     *
     * @return array
     */
    protected function standardized(Collection $row): array
    {
        $optionsMatrix = [];

        // ex: standard-base-US,150,*;standard-extra-US,150,*,FR
        foreach ($row as $columnName => $value) {
            if (! preg_match($this->separateRegex, $columnName, $matches)) {
                continue;
            }

            [, $method, $costType, $rawLocationCode] = $matches;
            foreach (explode(',', $rawLocationCode) as $locationCode) {
                $locationCode = $locationCode === '*' ? 'WW' : $locationCode;
                data_set($optionsMatrix, "$method.$locationCode.$costType", $value);
            }
        }

        return $optionsMatrix;
    }
}
