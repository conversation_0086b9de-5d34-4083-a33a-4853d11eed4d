<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class LuxuryProWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING    => [
            'unmatched',
            'unaudited',
            'unpaid',
        ],
        FulfillmentStatusEnum::PROCESSING => [
            'paid',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'shipped',
            'received',
        ],
    ];
    protected array $statusesCancelled = [
        'cancel',
        'refund',
    ];
    protected array $statusesOnHold = [
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response['data'];
    }

    public function crawlHandle(): self
    {
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $this->data['order_status']);
        $this->setFulfillmentStatus($fulfillStatus);

        $orderId = Arr::get($this->data, 'order_no');
        $this->setSenPrintsOrderId($orderId);

        $arrShippingCarrier = [];
        $senFulfills = [];
        $key = -1;
        $lineItems = Arr::get($this->data, 'items', []);
        foreach ($lineItems as $lineItem) {
            $trackings = Arr::get($lineItem, 'trackings', []);

            if (count($trackings) > 1) {
                $this->setExceptionMessage("Orders have multiple fulfillments");
            }
            $tracking = last($trackings);

            $trackingNumber = Arr::get($tracking, 'TrackingNumber');
            $shippingCarrier = Arr::get($tracking, 'ShipCarrierName');
            $trackingUrl = Arr::get($tracking, 'TrackingUrl');

            $stringCarrier = $trackingNumber . $shippingCarrier;
            if (!in_array($stringCarrier, $arrShippingCarrier)) {
                $arrShippingCarrier[] = $stringCarrier;
                $key++;
            }

            $senFulfills[$key]['items'][] = [
                'sku'      => $lineItem['sku'],
                'id'       => $lineItem['variant_id'],
                'tracking' => [
                    'tracking_code'    => $trackingNumber,
                    'shipping_carrier' => $shippingCarrier,
                    'tracking_url'     => $trackingUrl,
                ],
            ];
        }

        $this->setFulfillments($senFulfills);

        return $this;
    }
}
