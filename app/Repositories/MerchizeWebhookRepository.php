<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Enums\SupplierEnum;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\Merchize\Model\ResponseModel;
use App\Providers\FulfillAPI\Merchize\Provider;
use App\Providers\FulfillAPI\ObjectFulfill;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class MerchizeWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    // statues must lower key
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING => [
            ResponseModel::ORDER_IMPORTED,
            ResponseModel::BUYER_PAID,
            ResponseModel::PUSHED_ORDER,
            ResponseModel::FULFILLMENT_COST_PAID,
        ],
        FulfillmentStatusEnum::PROCESSING => [
            ResponseModel::IN_PRODUCTION,
        ],
        FulfillmentStatusEnum::FULFILLED => [
            ResponseModel::SHIPMENT_STARTED,
            ResponseModel::DELIVERED
        ],
        FulfillmentStatusEnum::REJECTED => [
            ResponseModel::CANCELED,
        ],
    ];

    protected array $statusesCancelled = [
        ResponseModel::CANCELED,
    ];

    protected array $statusesOnHold = [
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

    /**
     * @return $this
     */
    public function webhookHandle(): self
    {
        $webhookType = Arr::get($this->data, 'event_type');
        if (empty($webhookType)) {
            $webhookType = Arr::get($this->data, 'event');
        }
        $data = [];
        $source = Arr::get($this->data, 'resource');
        $supplierOrderId = Arr::get($source, 'code', Arr::get($source, 'order_code'));
        $this->setSupplierOrderId($supplierOrderId);
        $this->setSenPrintsOrderId(
            Arr::get($source, 'external_number')
        );
        $senOrderData = $this->getSenprintOrder();
        $items = $this->getOrderItems();
        if ($webhookType === 'ORDER.CREATED') {
            $items = $this->getOrderItems(false);
            $this->setFulfillmentStatus(
                FulfillmentStatusEnum::PENDING
            );
            OrderProduct::query()
                ->where('supplier_id', SupplierEnum::MERCHIZE)
                ->where('order_id', $this->getSenPrintsOrderId())
                ->update([
                    'fulfill_order_id' => $this->getSupplierOrderId()
                ]);

        } else if ($webhookType === 'ORDER.IMPORTER.ERROR') {
            $items = $this->getOrderItems(false);
            $webhookStatus = Arr::get($source, 'status');
            if ($webhookStatus === 'failed') {
                $this->setFulfillmentStatus(
                    FulfillmentStatusEnum::EXCEPTION
                );
                $this->setExceptionMessage(
                    Arr::get($source, 'error')
                );
            }
        } else if ($webhookType === 'ORDER.CHANGED.TRACKING') {
            $trackingNumber = Arr::get($source, 'tracking_number');
            $carrierName = Arr::get($source, 'tracking_company');
            $trackingUrl = Arr::get($source, 'tracking_url');
            $items = Arr::get($source, 'items');

            if (!empty($trackingNumber)) {
                $this->setFulfillmentStatus(FulfillmentStatusEnum::PROCESSING);
                $data = [
                    'tracking' => [
                        'tracking_code' => $trackingNumber,
                        'shipping_carrier' => $carrierName ?? '',
                        'tracking_url' => $trackingUrl ?? '',
                    ]
                ];
            } else if ($senOrderData) {
                $this->setFulfillmentStatus($senOrderData->fulfill_status);
            }
        } else if ($webhookType === 'ORDER.INVALID.ADDRESS') {
            $this->setFulfillmentStatus(FulfillmentStatusEnum::EXCEPTION);
            $this->setExceptionMessage($webhookType . ' - ' . Arr::get($source, 'type_invalid') . ' - ' . Arr::get($source, 'message_invalid'));
        } else if ($webhookType === 'ORDER.CHANGED.PROGRESS') {
            $orderProgresses = Arr::get($source, 'order_progress');
            $orderStatus = $this->getOrderProgressStatus($orderProgresses);
            $this->setFulfillmentStatus(
                $this->fulfillStatusMapping($this->arrStatusMapping, $orderStatus)
            );
            $tracking = [];
            $this->crawlMoreOrderTracking($tracking);
            $data = [
                'tracking' => $tracking
            ];
        }

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }
        $this->setFulfillments([$data]);

        return $this;
    }


    //{
    //"event": "ORDER.CHANGED.PROGRESS",
    //"resource": {
    //"code": "RX-XXXX-XXXX",
    //"external_number": "xxxx-xxxx",
    //"identifier": "hello.com",
    //"order_progress": [
    //{
    //"event": "pushed_order",
    //"status": "done",
    //"expected": "YYYY-MM-DDThh:mm:ss.000Z",
    //"actual": "YYYY-MM-DDThh:mm:ss.000Z"
    //},
    //{
    //    "event": "buyer_paid",
    //                "status": "done",
    //                "expected": "YYYY-MM-DDThh:mm:ss.000Z",
    //                "actual": "YYYY-MM-DDThh:mm:ss.000Z"
    //            },
    //{
    //    "event": "fulfillment_cost_paid",
    //                "status": "done",
    //                "expected": "YYYY-MM-DDThh:mm:ss.000Z",
    //                "actual": "YYYY-MM-DDThh:mm:ss.000Z"
    //            },
    //{
    //    "event": "in_production",
    //                "status": "pending",
    //                "expected": "YYYY-MM-DDThh:mm:ss.000Z"
    //            },
    //{
    //    "event": "shipment_started",
    //                "status": "pending"
    //            },
    //{
    //    "event": "delivered",
    //                "status": "pending"
    //            }
    //],
    //"package_progresses": [
    //            {
    //                "name": "RX-XXXX-XXXX-FX",
    //                "supplier": "US",
    //                "progress": [
    //                    {
    //                        "event": "buyer_paid",
    //                        "status": "done",
    //                        "actual": "YYYY-MM-DDThh:mm:ss.000Z"
    //                    },
    //                    {
    //                        "event": "fulfillment_cost_paid",
    //                        "status": "done",
    //                        "actual": "YYYY-MM-DDThh:mm:ss.000Z"
    //                    },
    //                    {
    //                        "event": "in_production",
    //                        "status": "pending"
    //                    },
    //                    {
    //                        "event": "shipment_started",
    //                        "status": "pending",
    //                        "expected_days": 1
    //                    },
    //                    {
    //                        "event": "in_transit",
    //                        "status": "pending"
    //                    },
    //                    {
    //                        "event": "delivered",
    //                        "status": "pending",
    //                        "expected_days": 1
    //                    }
    //                ]
    //            }
    //        ]
    //    },
    //    "event_id": "123",
    //    "event_time": "YYYY-MM-DDThh:mm:ss.000Z"
    //}

    /**
     * @return $this
     * @throws \Throwable
     */
    public function crawlHandle(): self
    {
        if (count($this->data) === 0) {
            return $this;
        }
        $data = $this->data[0];

        $this->setSenPrintsOrderId(Arr::get($data, 'external_number'));
        $this->setSupplierOrderId(Arr::get($data, 'code'));

        $orderProgresses = Arr::get($data, 'order_progress');
        $orderStatus = $this->getOrderProgressStatus($orderProgresses);
        // TODO: Tạm thời bỏ qua trạng thái exception khi order đã pushed nhưng bị invalid address, cần xử lý phía sup để triệt để vấn đề
        $skipException = false;
        if ($orderStatus === ResponseModel::PUSHED_ORDER) {
            $skipException = true;
        }
        $this->setFulfillmentStatus($this->fulfillStatusMapping($this->arrStatusMapping, $orderStatus));
        $items = $this->getOrderItems(skipException: $skipException);
        $tracking = [];
        $this->crawlMoreOrderTracking($tracking);

        $data = [
            'tracking' => $tracking
        ];

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }

        $this->setFulfillments([$data]);
        return $this;
    }

    /**
     * @return Order|null
     */
    private function getSenprintOrder()
    {
        return Order::query()->select([
            'status',
            'fulfill_status'
        ])->find($this->getSenPrintsOrderId());
    }

    /**
     * @param bool $withFulfillOrderId
     * @param bool $skipException
     * @return OrderProduct[]|Collection[]
     */
    private function getOrderItems($withFulfillOrderId = true, $skipException = false)
    {
        $supplierOrderId = $this->getSupplierOrderId();
        $senPrintsOrderId = $this->getSenPrintsOrderId();
        return OrderProduct::query()
            ->where('supplier_id', SupplierEnum::MERCHIZE)
            ->where('order_id', $senPrintsOrderId)
            ->when($withFulfillOrderId, function ($q) use ($supplierOrderId) {
                $q->where('fulfill_order_id', $supplierOrderId);
            })
            ->when($skipException, function ($q) {
                $q->where('fulfill_status', '!=', FulfillmentStatusEnum::EXCEPTION);
            })
            ->get();
    }

    /**
     * @param $tracking
     * @return void
     * @throws \Throwable
     */
    protected function crawlMoreOrderTracking(&$tracking)
    {
        if (!in_array($this->getFulfillmentStatus(), [
            FulfillmentStatusEnum::FULFILLED,
            FulfillmentStatusEnum::ON_DELIVERY,
        ], true)) {
            return;
        }
        $object = new ObjectFulfill();
        $objectProvider = $object->getProviderBySupplierId(SupplierEnum::MERCHIZE);
        if (!$objectProvider) {
            return;
        }
        /** @var Provider $objectProvider */
        $objectProvider->setMethod($objectProvider::METHOD_CRAWL_ORDER_TRACKING);
        $objectProvider->setParams($this->getSupplierOrderId());
        $objectProvider->setOrderId($this->getSenPrintsOrderId());
        $response = $objectProvider->sendData();
        if (!Arr::get($response, 'success')) {
            return;
        }
        $trackingResponseData = Arr::get($response, 'data');
        if (count($trackingResponseData) === 0) {
            return;
        }
        $trackingData = $trackingResponseData[0];
        if (!Arr::get($trackingData, 'has_tracking')) {
            return;
        }
        $tracking = [
            'tracking_code' => Arr::get($trackingData, 'tracking_number') ?? '',
            'shipping_carrier' => Arr::get($trackingData, 'tracking_company') ?? '',
            'tracking_url' => Arr::get($trackingData, 'tracking_url') ?? '',
        ];
    }

    /**
     * @param $orderProgresses
     * @return string
     */
    private function getOrderProgressStatus($orderProgresses): string
    {
        $status = ResponseModel::ORDER_IMPORTED;
        foreach ($orderProgresses as $orderProgress) {
            if ($orderProgress['status'] === 'pending') {
                return $status;
            }
            if ($orderProgress['status'] === 'done') {
                $status = $orderProgress['event'];
            }
        }
        return $status;
    }
}
