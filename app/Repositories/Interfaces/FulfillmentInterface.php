<?php

namespace App\Repositories\Interfaces;

interface FulfillmentInterface
{
    /**
     * Get fulfillment status
     * @return self
     */
    public function getFulfillmentStatus();

    /**
     * Get reference id (order id)
     * @return self
     */
    public function getSenPrintsOrderId();

    /**
     * Get fulfillment order id
     * @return string
     */
    public function getSupplierOrderId();

    /**
     * Get fulfillments.
     * @return [type]
     */
    public function getFulfillments();

    /**
     * @return [type]
     */
    public function get();
}
