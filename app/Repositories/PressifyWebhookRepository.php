<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Providers\FulfillAPI\Pressify\Model\ResponseModel;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class PressifyWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    // statues must lower key
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING => [
            ResponseModel::DRAFT,
            ResponseModel::DOWNLOADED,
            ResponseModel::ORDERED,
        ],
        FulfillmentStatusEnum::PROCESSING => [
            ResponseModel::IN_PRODUCTION,
            ResponseModel::PRINTED,
            ResponseModel::REPRINT,
            ResponseModel::LABEL_PRINTED,
            ResponseModel::PRESSED,
            ResponseModel::ON_HOLD,
            ResponseModel::NEW,
            ResponseModel::TEST
        ],
        FulfillmentStatusEnum::FULFILLED => [
            ResponseModel::DELIVERED,
            ResponseModel::TRANSIT,
            ResponseModel::SHIPPED,
        ],
        FulfillmentStatusEnum::REJECTED => [
            ResponseModel::CANCELED,
            ResponseModel::REFUNDED,
        ],
    ];

    protected array $statusesCancelled = [
        ResponseModel::CANCELED,
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

    /*
     * {
              "id": 360757,
              "ref_id": "test_22",
              "store_id": 116,
              "shipping_label": null,
              "shipping_service": "USPS",
              "shipping_method": "standard",
              "tracking_id": null,
              "fulfill_status": "test_order",
              "total_cost": "41.05",
              "paid_cost": null,
              "print_cost": "35.76",
              "shipping_cost": "5.29",
              "first_name": null,
              "last_name": null,
              "phone": null,
              "address_1": null,
              "address_2": null,
              "city": null,
              "state": null,
              "postcode": null,
              "country": null,
              "payment_status": "pending",
              "items": [
                {
                  "id": 391633,
                  "price": "23.07",
                  "sku": null,
                  "status": null,
                  "quantity": 3,
                  "product_name": "The Black Dog sweatShirt, New Album Era sweatShirt, Ts New Album sweatShirt, TTPD Merch, Trend Shirt, Trendy Concert Shirt, Gift for her",
                  "mockup": "https://supoverdesign.nyc3.digitaloceanspaces.com/In%20Draco%20We%20Trust%20Double%20Sided%20Tee%20White%20Color%20and_409.png",
                  "mockup_back": null,
                  "variant_id": "104"
                },
                {
                  "id": 391634,
                  "price": "12.69",
                  "sku": null,
                  "status": null,
                  "quantity": 1,
                  "product_name": "The Black Dog sweatShirt, New Album Era sweatShirt, Ts New Album sweatShirt, TTPD Merch, Trend Shirt, Trendy Concert Shirt, Gift for her",
                  "mockup": "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/0c6e47ea7e51444fa44b04d6ddd925f6~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=645250395",
                  "mockup_back": null,
                  "variant_id": "289"
                }
              ],
              "tracking": []
            }
     * */
    public function crawlHandle(): self
    {
        $fulfillStatus = Arr::get($this->data, 'fulfill_status');
        $this->setFulfillmentStatus(
            $this->fulfillStatusMapping($this->arrStatusMapping, $fulfillStatus)
        );

//        if ($this->getFulfillmentStatus() === FulfillmentStatusEnum::EXCEPTION) {
//            $this->setExceptionMessage(
//                implode(', ', data_get($this->data, 'error', []))
//            );
//        }

        $this->setSenPrintsOrderId(
            Arr::get($this->data, 'ref_id')
        );
        $this->setSupplierOrderId(
            Arr::get($this->data, 'id')
        );

        $items = Arr::get($this->data, 'items');
        $items = array_map(
            function ($item) {
                $skuItem = Arr::get($item, 'variant_id');
                $item['sku'] = $skuItem;
                unset($item['id']);
                return $item;
            },
            $items
        );

        $data = [
            'tracking' => [
                'tracking_code' => Arr::get($this->data, 'tracking_id') ?? '',
                'shipping_carrier' => Arr::get($this->data, 'shipping_service') ?? '',
            ]
        ];

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }
        $this->setFulfillments([$data]);
        return $this;

    }
}
