<?php

namespace App\Repositories;

use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

// for response crawl order don't have fulfillments
class GeneralWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public $data; // string or array
    protected array $arrStatusMapping = [];
    protected ?string $trackingCode = null;
    protected ?string $shippingCarrier = null;
    protected ?string $trackingUrl = null;

    public function __construct($data, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $data;
    }

    public function webhookHandle(): self
    {
        // Fulfillment Status
        $status = Arr::get($this->data, 'status', '');
        if ($this->handleStatusException($status)) {
            $status = lookupArrayMapping($this->arrStatusMapping, $status) ?? '';
            $this->setFulfillmentStatus($status);
        }
        if ($status === '') {
            $this->setExceptionMessage('Status not matching SenPrints fulfillment status');
        }
        // SenPrints Order Id
        $senOrderId = Arr::get($this->data, 'reference_id', '');
        $this->setSenPrintsOrderId($senOrderId);
        // Supplier Order Id
        $supOrderId = Arr::get($this->data, 'order_id', '');
        $this->setSupplierOrderId($supOrderId);
        // Parse fulfillments
        if (Arr::exists($this->data, 'fulfillments.items')) {
            $supFulfills[] = Arr::get($this->data, 'fulfillments', []);
        } else {
            $supFulfills = Arr::get($this->data, 'fulfillments', []);
        }
        $this->setFulfillments($supFulfills);

        return $this;
    }

    public function crawlHandle(): self
    {
        if (!is_array($this->data)) {
            $this->setExceptionMessage('Data response not valid');
            $fulfillStatus = self::DEFAULT_FULFILL_STATUS_FAILED;
            $this->setFulfillmentStatus($fulfillStatus);
        } else {
            // Status
            $fulfillStatus = Arr::get($this->data, 'status');
            if ($this->handleStatusException($fulfillStatus)) {
                $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $fulfillStatus);
                $this->setFulfillmentStatus($fulfillStatus);
            }
        }

        $fulfillments = [
            [
                'no_items' => true,
                'tracking' => [
                    'tracking_code'    => $this->trackingCode,
                    'shipping_carrier' => $this->shippingCarrier,
                    'tracking_url'     => $this->trackingUrl,
                ]
            ]
        ];
        $this->setFulfillments($fulfillments);

        return $this;
    }
}
