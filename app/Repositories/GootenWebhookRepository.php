<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class GootenWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PROCESSING => [
            'New',
            'InProduction',
            'NeedsManualApproval',
            'ReadyForPrint',
            'Pending',
            'VendorAPIIssue',
            'ReadyForAccounting',
            'ReadyForImageDl',
            'OutForDelivery',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'Delivered',
            'Shipped',
            'OutForDelivery',
        ],
        FulfillmentStatusEnum::REJECTED   => [
            'AddressIssue',
            'OutOfStockItem',
            'ImageIssue',
            'Cancelled',
            'CancelledRefunded',
            'BilledRefunded',
        ],
    ];

    protected array $statusesOnHold = [
        'Hold',
        'On Hold',
    ];
    protected array $statusesCancelled = [
        'Cancelled',
        'CancelledRefunded',
        'BilledRefunded',
    ];

    public function __construct($data, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $data;
    }

    // public function webhookHandle(): self
    // {
    //     return $this;
    // }

    public function crawlHandle(): self
    {
        $supplierOrderId = Arr::get($this->data, 'Id');
        $this->setSupplierOrderId($supplierOrderId);

        $senOrderId = Arr::get($this->data, 'SourceId');
        $this->setSenPrintsOrderId($senOrderId);

        // default
        $fulfillStatus = FulfillmentStatusEnum::PROCESSING;

        $arrShippingCarrier = [];
        $senFulfills        = [];
        $key                = -1;

        $lineItems = Arr::get($this->data, 'Items', []);
        foreach ($lineItems as $lineItem) {
            $status        = $lineItem['Status'];
            $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $status);
            // ignore if failed
            if ($this->isException()) {
                $errorLog = Arr::get($this->data, 'Meta.data_issues');
                $this->setExceptionMessage('Exception status: ' . $status);
                $this->setExceptionMessage($errorLog);
            }

            $fulfillmentItems = Arr::get($lineItem, 'Shipments', []);
            if (count($fulfillmentItems) > 1) {
                $this->setExceptionMessage("Orders have multiple fulfillments");
            }
            $fulfillmentItem = last($fulfillmentItems);

            $trackingNumber  = Arr::get($fulfillmentItem, 'TrackingNumber');
            $shippingCarrier = Arr::get($fulfillmentItem, 'ShipCarrierName');
            $trackingUrl     = Arr::get($fulfillmentItem, 'TrackingUrl');
            $stringCarrier   = $trackingNumber . $shippingCarrier;
            if (!in_array($stringCarrier, $arrShippingCarrier)) {
                $arrShippingCarrier[] = $stringCarrier;
                $key++;
            }

            $senFulfills[$key]['items'][]['sku'] = $lineItem['Sku'];
            $senFulfills[$key]['fulfill_status'] = $fulfillStatus;
            $senFulfills[$key]['tracking'] = [
                'tracking_code'    => $trackingNumber,
                'shipping_carrier' => $shippingCarrier,
                'tracking_url'     => $trackingUrl,
            ];
        }
        $this->setFulfillmentStatus($fulfillStatus);

        $this->setFulfillments($senFulfills);

        return $this;
    }
}
