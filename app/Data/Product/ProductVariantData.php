<?php

namespace App\Data\Product;

use App\Data\DataObject;


class ProductVariantData extends DataObject
{
    public function __construct(
        public string $product_id,
        public ?string $campaign_id,
        public string $variant_key,
        public null|float|string $base_cost,
        public ?string $location_code,
        public int|string $out_of_stock,
        public null|float|string $adjust_price,
        public null|float|string  $price,
        public null|float|string  $old_price,
        public null|float|string  $weight,
        public null|int|string $quantity,
        public null|int|string $check_quantity,
        public string $sku,
        public ?string $sku_bak,
    ) {}

}
