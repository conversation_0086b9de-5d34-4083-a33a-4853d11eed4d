<?php

namespace App\Data\Order;

use App\Data\OrderItem\OrderItemThirdPartyData;
use App\Models\User;
use <PERSON>tie\LaravelData\Data;

class OrderThirdPartyData extends Data
{
    public function __construct(
        /* @var OrderItemThirdPartyData[] */
        public array $items,
        public int $external_order_id,
        public int $seller_id,
        public ?string $store_id,
        public ?int $ref_id, // auto generated
        public ?string $store_name = '',
        public ?string $store_domain = '',
    ) {}

    public static function from(...$payloads): static
    {
        $payload = $payloads[0] ?? [];

        $sellerId = $payload['seller_id'];
        $payload['ref_id'] = User::query()->whereKey($sellerId)->value('ref_id') ?? null;

        return parent::from($payload);
    }

    public function setSellerId(int $seller_id): OrderThirdPartyData
    {
        $this->seller_id = $seller_id;
        return $this;
    }
}
