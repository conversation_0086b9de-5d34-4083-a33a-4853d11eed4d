<?php

namespace App\Data\Order;

use <PERSON><PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Optional;
use App\Models\Staff;
use App\Models\Order;
use Illuminate\Support\Collection;

class OrderDisputeData extends Data
{
    public function __construct(
        public ?string $id,
        public int $order_id,
        public int $staff_id,
        public ?string $dispute_type,
        public ?string $dispute_status,
        public ?string $dispute_opened_reason,
        public ?string $dispute_solution,
        public ?bool $klarna,
        public ?string $dispute_due_date,
        public ?string $dispute_created_at,
        public ?bool $allow_submit_case,
        public ?bool $allow_reachout,
        public ?string $supplier_names,
        public ?bool $is_fraud,
        public ?string $payment_gateway_type,
        public ?string $payment_gate,
        public ?string $dispute_id,
        public ?bool $is_reopened,
        public ?bool $inquiry,
        public null|Optional|Staff $staff,
        public null|Optional|Order $order,
        public null|Optional|Collection $submit_cases,
        public null|Optional|Collection $reachouts,
        public null|Optional|Collection $create_actions
    ) {}

    public static function getFillableAttributes(): array
    {
        return [
            'order_id',
            'staff_id',
            'dispute_type',
            'dispute_status',
            'dispute_opened_reason',
            'dispute_solution',
            'klarna',
            'dispute_due_date',
            'dispute_created_at',
            'payment_gate',
            'dispute_id',
            'is_reopened',
            'inquiry'
        ];
    }
}
