<?php
namespace App\Console\Commands;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\QueueName;
use App\Jobs\ProcessOrderAbandonedJob;
use App\Models\AbandonedLog;
use Illuminate\Console\Command;

class AbandonedRunCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'abandoned:run';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Abandoned Run';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $abandonedLogs = AbandonedLog::query()
            ->with('order')
            ->where('status', AbandonedLogStatusEnum::SCHEDULED)
            ->where('start_at', '<=', now())
            ->where('start_at', '>', now()->subHours(24))
            ->get();
        if ($abandonedLogs->count() === 0) {
            return self::SUCCESS;
        }
        foreach ($abandonedLogs as $abandonedLog) {
            dispatch(new ProcessOrderAbandonedJob($abandonedLog))->onQueue(QueueName::ORDER_EVENTS);
        }
        return self::SUCCESS;
    }
}
