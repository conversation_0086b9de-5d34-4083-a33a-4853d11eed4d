<?php

namespace App\Console\Commands;

use App\ActivityLogIndexConfigurator;
use App\Models\ActivityLog;
use App\ProductIndexConfigurator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ElasticFresh extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'elastic:fresh';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Elastic fresh data index';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Drop & Create new Activity index
        try {
            Artisan::call('elastic:drop-index', [
                'index-configurator' => ActivityLogIndexConfigurator::class
            ]);
        } catch (\Exception $e) {
        }
        Artisan::call('elastic:create-index', [
            'index-configurator' => ActivityLogIndexConfigurator::class
        ]);
        //Update mapping index for elasticsearch
        Artisan::call('elastic:update-mapping', [
            'model' => ActivityLog::class
        ]);

        // Drop & Create new Product index
        try {
            Artisan::call('elastic:drop-index', [
                'index-configurator' => ProductIndexConfigurator::class
            ]);
        } catch (\Exception $e) {
        }

        Artisan::call('elastic:create-index', [
            'index-configurator' => ProductIndexConfigurator::class
        ]);
        return 0;
    }
}
