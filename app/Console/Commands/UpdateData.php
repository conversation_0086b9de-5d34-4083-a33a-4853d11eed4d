<?php

namespace App\Console\Commands;

use App\Actions\Commons\SendEmailConfirmToBuyer;
use App\Enums\CacheKeys;
use App\Enums\CampaignStatusEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\SendMail\LogStatus;
use App\Events\OrderProductFulfilled;
use App\Http\Controllers\CampaignController;
use App\Models\Campaign;
use App\Models\DataUpdateLog;
use App\Models\Elastic;
use App\Models\File;
use App\Models\IndexCampaign;
use App\Models\IndexProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Page;
use App\Models\PaymentGateway;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\ProductReview;
use App\Models\PromotionRule;
use App\Models\SendMailLog;
use App\Models\Slug;
use App\Models\SocialFeed;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Models\StoreProduct;
use App\Models\SystemConfig;
use App\Models\Temp;
use App\Models\User;
use App\Scopes\SoftDeletingScope;
use App\Services\CampaignService;
use App\Services\MailService;
use App\Services\StoreService;
use App\Traits\ElasticClient;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Jobs\SyncSlugJob;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Jobs\InsertCampaignJob;
use Modules\ShardingTable\Jobs\SyncRemainingCampaignJob;

class UpdateData extends Command
{
    use ElasticClient;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system-data:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Temporary Data Update';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle(): void
    {
        // $this->blockCampaigns();
    }

    private function deleteCamp() {
        $limit = 500;
        $ids = IndexProduct::query()->where('seller_id', 123955)->where('is_deleted', '=', 0)->whereNotNull('slug')->where('created_at', '<=', '2024-03-14 23:59:59')->limit($limit)->pluck('id')->toArray();
        try {
            if (!empty($ids)) {
                Product::query()->filterByProductOrCampaignIds($ids)->withTrashed()->update([
                    'slug' => null,
                    'sync_status' => 0,
                    'is_deleted' => 1,
                    'deleted_at' => now()
                ]);
                IndexProduct::query()->filterByProductOrCampaignIds($ids)->update([
                    'slug' => null,
                    'is_deleted' => 1,
                    'deleted_at' => now()
                ]);
            }
        } catch (\Throwable $e) {
            logToDiscord(date('d/m/Y H:i A') . ' - Delete products error: ' . $e->getMessage(), 'dev_logs');
        }
    }

    private function reSyncCamp() {
        $limit = 5000;
        $settings = SystemConfig::getCustomConfig('resync_seller_campaigns_config');
        if (empty($settings)) {
            return false;
        }
        $enabled = optional($settings)->value ?? 'disabled';
        if ($enabled !== 'enabled') {
            return false;
        }
        $value = optional($settings)->json_data;
        $configs = json_decode($value, true);
        $id = !empty($configs['id']) ? (int) $configs['id'] : 0;
        $temp_status = !empty($configs['temp_status']) ? $configs['temp_status'] : 11;
        $seller_ids = !empty($configs['seller_ids']) ? array_map('trim', explode(',', $configs['seller_ids'])) : [];
        $created_at = !empty($configs['created_at']) ? $configs['created_at'] : null;
        $ids = IndexProduct::query()->where('temp_status', '!=', $temp_status)
            ->when(!empty($id), function ($query) use ($id) {
                $query->where('id', '<=', $id);
            })
            ->when(!empty($seller_ids), function ($query) use ($seller_ids) {
                $query->whereIn('seller_id', $seller_ids);
            })
            ->when(!empty($created_at), function ($query) use ($created_at) {
                $query->where('created_at', '>=', $created_at);
            })
            ->orderBy('created_at', 'DESC')
            ->limit($limit)
            ->pluck('id');
        if (!empty($ids)) {
            Product::query()->whereIn('id', $ids)->withTrashed()->update([
                'sync_status' => 0,
                'temp_status' => $temp_status
            ]);
            IndexProduct::query()->whereIn('id', $ids)->update(['temp_status' => $temp_status]);
        }
        return true;
    }

    private function reSyncEsCampaigns() {
        $limit = 5000;
        $settings = SystemConfig::getCustomConfig('resync_seller_campaigns_config');
        if (empty($settings)) {
            return false;
        }
        $enabled = optional($settings)->value ?? 'disabled';
        if ($enabled !== 'enabled') {
            return false;
        }
        $value = optional($settings)->json_data;
        $configs = json_decode($value, true);
        $id = !empty($configs['id']) ? (int) $configs['id'] : 0;
        $temp_status = !empty($configs['temp_status']) ? $configs['temp_status'] : 11;
        $ids = IndexProduct::query()->where('temp_status', '=', $temp_status)
            ->when(!empty($id), function ($query) use ($id) {
                $query->where('id', '<=', $id);
            })
            ->orderBy('created_at', 'DESC')
            ->limit($limit)
            ->pluck('id');
        if (!empty($ids)) {
            Product::query()->whereIn('id', $ids)->withTrashed()->update([
                'sync_status' => 0,
                'temp_status' => 0
            ]);
            IndexProduct::query()->whereIn('id', $ids)->update(['temp_status' => 0]);
        }
        return true;
    }

    private function updateSyncCamp() {
        $limit = 3000;
        $temp_status = 19;
        $ids = IndexProduct::query()->where('temp_status', '!=', $temp_status)
            ->where('id', '<=', 143101381)
            ->where('system_type', '=', 'mockup')
            ->where('status', ProductStatus::ACTIVE)
            ->orderBy('created_at', 'DESC')
            ->limit($limit)
            ->pluck('id');
        if (!empty($ids)) {
            Product::query()->whereIn('id', $ids)->withTrashed()->update([
                'sync_status' => 0,
                'temp_status' => $temp_status
            ]);
            IndexProduct::query()->whereIn('id', $ids)->update(['temp_status' => $temp_status]);
        }
        return true;
    }

    private function updateCamp() {
        $limit = 500;
        $ids = IndexProduct::query()
            ->select('id')
            ->where('seller_id', '=', 101454)
            ->where('pricing_mode', '=', 'fixed_price')
            ->where('created_at', '<=', '2024-04-22 23:59:59')
            ->whereNotNull('slug')
            ->withTrashed()
            ->limit($limit)
            ->get()
            ->pluck('id')
            ->toArray();
        if (!empty($ids)) {
            foreach ($ids as $id) {
                Product::query()->filterByProductOrCampaignIds([$id])->withTrashed()->update([
                    'sync_status' => 0,
                    'pricing_mode' => 'adjust_price',
                ]);
                IndexProduct::query()->filterByProductOrCampaignIds([$id])->withTrashed()->update([
                    'sync_status' => 0,
                    'pricing_mode' => 'adjust_price',
                ]);
            }
        }
    }

    private function updateColorOption() {
        // remove light pink color for Trung
        $ids = Temp::where('status',0)->limit(1000)->pluck('id1');
        $products = Product::select(['id','options'])->whereIn('id', $ids)->get();
        foreach ($products as $product) {
            $options = json_decode($product->options, true);
            if (($key = array_search('light pink', $options['color'])) !== false) {
                unset($options['color'][$key]);
                $options['color'] = array_values($options['color']);
            }
            $product->update([
                'options' => $options,
                'sync_status' => 0
            ]);
        }
        Temp::whereIn('id1', $ids)->update(['status' => 1]);
    }

    /**
     * @return void
     */
    private function updateOrderTracking() {
        $ids = Temp::query()->where('status',0)->whereNotNull('id2')->orderByDesc('id2')->limit(100)->pluck('id2');
        $orders = Order::query()->with(['products:id,order_id,tracking_code,shipping_carrier,tracking_url'])->whereIn('id', $ids)->get();
        foreach ($orders as $order) {
            $order_product = $order->products->last();
            if (!$order_product) {
                continue;
            }
            /** @var OrderProduct $order_product */
            $tracking = [
                'tracking_code' => $order_product->tracking_code,
                'shipping_carrier' => $order_product->shipping_carrier,
                'tracking_url' => $order_product->tracking_url,
            ];
            OrderProductFulfilled::dispatch($order->id, $tracking);
        }
        Temp::query()->whereIn('id2', $ids)->update(['status' => 1, 'sku1' => 'Ok']);
    }

    private function udpateOrderSenPoints() {
        $orders = Order::query()->where([
            'type' => OrderTypeEnum::CUSTOM,
            'payment_status' => OrderPaymentStatus::PAID,
            'sen_fulfill_status' => OrderSenFulfillStatus::YES,
            'total_sen_points' => 0
        ])->where('paid_at', '>=', '2022-09-01')
        ->limit(500)->get();
        foreach($orders as $order) {
            $totalSenPoints = 0;
            $order->products->map(function ($orderProduct) use ($order, &$totalSenPoints) {
                $orderProduct->sen_points = $orderProduct->getSenPoints($order, $totalSenPoints);
            });
            $order->total_sen_points = $totalSenPoints;
            $order->push();
        }
    }

    private function moveStore($storeId, $fromSellerId, $toSellerId) {
        try {
            $store = Store::find($storeId);
            if (empty($store)) {
                throw new \RuntimeException('Store invalid: ' . $storeId);
            }
            $toSeller = User::find($toSellerId);
            if (empty($toSeller)) {
                throw new \RuntimeException('Seller invalid: ' . $storeId);
            }

            if ($store->seller_id === $fromSellerId) {
                // Update seller_id bảng store_domain
                $this->moveStoreRelation(StoreDomain::query(), $store, $toSellerId);

                // Update seller_id bảng page
                $this->moveStoreRelation(Page::query(), $store, $toSellerId);

                // Update seller_id bảng file
                $this->moveStoreRelation(File::query(), $store, $toSellerId);

                // Update seller_id bảng payment_gateways
                $this->moveStoreRelation(PaymentGateway::query(), $store, $toSellerId);

                // Update seller_id bảng promotion_rule
                $this->moveStoreRelation(PromotionRule::query(), $store, $toSellerId);

                // Update seller_id bảng social_feed
                $this->moveStoreRelation(SocialFeed::query(), $store, $toSellerId);

                // Update seller_id bảng store_head_tag
                // $this->moveStoreRelation(StoreHeadTag::query(), $store, $toSellerId);

                // Update seller_id bảng store
                $store->update(['seller_id' => $toSellerId]);

                logToDiscord('Move store info successfully: ' . $storeId);
            }

            if ($store->seller_id === $toSellerId) {
                $limit = 500;
                // Move Campaign
                $campIds = StoreProduct::query()->where('store_id', $store->id)
                    ->join('product', 'product.id', '=', 'store_product.product_id')
                    ->where('product.seller_id', '!=', $toSellerId)
                    ->limit($limit)
                    ->pluck('store_product.product_id');

                // Update seller_id bảng promotion có campaign_id trong danh sách
                $this->moveCampRelation(PromotionRule::query(), $campIds, $fromSellerId, $toSellerId, 'campaign_id');

                // Update seller_id bảng file có campaign_id trong danh sách
                $this->moveCampRelation(File::query(), $campIds, $fromSellerId, $toSellerId);

                // Update seller_id bảng product_collection
                $this->moveCampRelation(ProductCollection::query(), $campIds, $fromSellerId, $toSellerId);

                // Update seller_id bảng product_review
                $this->moveCampRelation(ProductReview::query(), $campIds, $fromSellerId, $toSellerId);

                // Update seller_id bảng product
                Product::query()->whereIn('id', $campIds)->where('seller_id', $fromSellerId)
                    ->update(['seller_id' => $toSellerId, 'sync_status' => 0]);
                Product::query()->whereIn('campaign_id', $campIds)->where('seller_id', $fromSellerId)
                    ->update(['seller_id' => $toSellerId, 'sync_status' => 0]);

                if (count($campIds) > 0) {
                    logToDiscord('Move store campaigns successfully: ' . count($campIds));
                    if (count($campIds) < $limit) {
                        logToDiscord('Move store completed: ' . $storeId);
                    }
                }
            }
        } catch (\Throwable $e) {
            logToDiscord('Move store error: ' . $e->getMessage());
        }
    }

    private function moveStoreRelation($query, $store, $toSellerId) {
        $query->where(['seller_id' => $store->seller_id, 'store_id' => $store->id]);
        $query->update(['seller_id' => $toSellerId]);
    }

    private function moveCampRelation($query, $campIds, $fromSellerId, $toSellerId, $whereInField = 'product_id') {

        $query->whereIn($whereInField, $campIds);
        $query->where('seller_id', $fromSellerId);
        $query->update(['seller_id' => $toSellerId]);
    }

    private function updateProductImages()
    {
        $products = Product::query()->where([
            'seller_id' => 101974,
            'template_id' => 572
        ])
            ->with('campaign')
            ->with('images', function ($images) {
                $images->where('type_detail', FileRenderType::CUSTOM);
            })
            ->leftJoin('data_update_logs', 'data_id', 'product.id')
            ->whereNull('data_id')
            ->select('product.*')
            ->limit(10)->get();

        $updateData = [];
        $products->map(function ($product) use (&$updateData) {
            CampaignController::generateImageFilesProduct2($product, $product->campaign);
            $updateData[] = [
                'action' => 'update_product_image',
                'data_type' => 'product',
                'data_id' => $product->id,
                'detail' => $product->thumb_url,
            ];
        });

        DataUpdateLog::insert($updateData);
    }

    private function resendOrderConfirmation()
    {
        try {
            $mail_logs = SendMailLog::query()->select(['id', 'order_id'])
                ->where('template', 'buyer.order_confirmation')
                ->where('status', '=', LogStatus::SENT)
                ->whereBetween('created_at', ['2023-12-11', '2023-12-12'])
                ->whereNull('content')
                ->orderBy('created_at')
                ->limit(100)
                ->get();
            foreach ($mail_logs as $mail_log) {
                $order = Order::query()->whereKey($mail_log->order_id)->first();
                if ($order) {
                    (new SendEmailConfirmToBuyer())->handle($order);
                }
                SendMailLog::query()->where('id', $mail_log->id)->update(['status' => LogStatus::CANCELLED]);
            }
        } catch (\Throwable $e) {
            logToDiscord(date('d/m/Y H:i A') . ' - Resend order confirmation error: ' . $e->getMessage(), 'dev_logs');
        }
    }

    /**
     * @throws \Throwable
     */
    private function reSyncCampaignsFromEs() {
        $arrListing = [
            'id',
            'name',
            'slug',
            'status',
            'seller_id',
            'created_at',
            'product_type',
            'system_product_type',
            'system_type',
        ];
        $user_id = 131658;
        $arrFilterElastic = [];
        $arrFilterElastic['seller_id'] = $user_id;
        $arrFilterElastic['status'] = 'draft';
        $arrFilterElastic['product_type'] = ProductType::CAMPAIGN;
        try {
            [$campaigns, $total] = (new Elastic())->getCampaign($arrListing, $arrFilterElastic, 100);
            if (empty($campaigns) || $total <= 0) {
                return;
            }
            $campaignIds = Arr::pluck($campaigns, 'id');
            foreach ($campaignIds as $campaignId) {
                $this->elasticDeleteProductsByProductIds([$campaignId]);
                $campaign = Product::query()->where('id', $campaignId)->first();
                if ($campaign && $campaign->status === ProductStatus::DRAFT) {
                    continue;
                }
                Product::query()->filterByProductOrCampaignIds([$campaignId])->withTrashed()->update([
                    'sync_status' => 0
                ]);
                graylogInfo('[Schedule] Re-sync campaign for Campaign Id: '.  $campaignId , [
                    'category' => 'bulk_campaigns',
                    'campaign_id' => $campaignId
                ]);
            }
        } catch (\Throwable $e) {
            logException($e, 'reSyncCampaignsFromEs', 'bulk_campaign');
        }
    }

    private function renderCampaignProductImages() {
        try {
            $limit = 100;
            $temp_status = 1;
            $template_id = 3133964;
            $campaigns = IndexCampaign::query()
                ->selectRaw('id, slug, product_type, temp_status, created_at')
                ->withoutGlobalScope(new SoftDeletingScope())
                ->fromRaw('product as campaign')
                ->whereExists(function ($q) use ($template_id) {
                    $q->selectRaw(1)->from('product')->whereColumn('product.campaign_id', '=', 'campaign.id')->where('product.template_id', $template_id);
                })
                ->whereBetween('campaign.created_at', ['2024-02-20', '2024-03-17'])
                ->where('campaign.temp_status', '!=', $temp_status)
                ->where('campaign.status', ProductStatus::ACTIVE)
                ->where('campaign.is_deleted', 0)
                ->limit($limit)
                ->get();
            if ($campaigns->isEmpty()) {
                graylogInfo('[Schedule] Done re-rendered', [
                    'category' => 'render_image_campaigns',
                    'template_id' => $template_id,
                ]);
                return;
            }

            $campaigns->map(function ($campaign) use ($template_id) {
                $product = Product::query()->where('campaign_id', $campaign->id)->where('template_id', $template_id)->first();
                if ($product) {
                    graylogInfo('[Schedule] Re-render images for campaign id: ' . $campaign->id , [
                        'category' => 'render_image_campaigns',
                        'template_id' => $template_id,
                        'product_id' => $product->id,
                        'campaign_id' => $campaign->id
                    ]);
                    dispatch(static function () use ($product, $campaign){
                        try {
                            CampaignController::generateImageFilesProduct2($product, $campaign);
                            $cacheKey = CacheKeys::PRODUCT_PREFIX . $campaign->slug;
                            syncClearCache([$cacheKey], CacheKeys::CACHE_TYPE_ALTERNATIVE);
                        } catch (\Throwable $e) {
                            logException($e, 'renderCampaignProductImages', 'bulk_campaign');
                        }
                    })->onQueue(config('campaign.config.general.queue'));
                }
            });
            $ids = $campaigns->pluck('id')->toArray();
            IndexCampaign::query()->whereIn('id', $ids)->update(['temp_status' => $temp_status]);
            Campaign::query()->whereIn('id', $ids)->update(['sync_status' => 0, 'temp_status' => $temp_status]);
            graylogInfo('[Schedule] Re-rendered total records: ' . count($ids) , [
                'category' => 'render_image_campaigns',
                'template_id' => $template_id,
                'campaign_ids' => $ids
            ]);
        } catch (\Throwable $e) {
            logException($e, 'renderCampaignProductImages', 'bulk_campaign');
        }
    }

    private function updateProductThumbUrl() {
        $limit = 500;
        $temp_status = 1;
        $campaigns = IndexProduct::query()
            ->with(['products' => function ($q) {
                $q->select('id', 'campaign_id')->whereIn('template_id', [3108552, 3146072, 3112080, 3134787]);
            }])
            ->select('id')
            ->where('seller_id', 1163957)
            ->where('product_type', '!=',ProductType::PRODUCT)
            ->where('temp_status', '!=', $temp_status)
            ->where('status', ProductStatus::ACTIVE)
            ->where('slug', 'LIKE', '%-d12')
            ->limit($limit)
            ->get();
        if ($campaigns->isEmpty()) {
            return false;
        }
        $this->info('Total campaigns: ' . $campaigns->count());
        foreach ($campaigns as $campaign) {
            $product_ids = $campaign->products->pluck('id')->toArray();
            if (empty($product_ids)) {
                IndexProduct::query()->where('id', $campaign->id)->withTrashed()->update(['temp_status' => $temp_status]);
                continue;
            }
            foreach ($product_ids as $product_id) {
                $file = File::query()
                    ->where([
                        'campaign_id' => $campaign->id,
                        'product_id' => $product_id,
                        'type' => FileTypeEnum::IMAGE,
                    ])
                    ->whereNull('type_detail')
                    ->orderBy('position')
                    ->first();
                if (!$file || empty($file->file_url_2)) {
                    continue;
                }
                $this->info('Updated thumb for product: ' . $product_id . ' - Thumb url: ' . $file->file_url_2);
                Product::query()->whereKey($product_id)->withTrashed()->update([
                    'thumb_url' => $file->file_url_2
                ]);
            }
            Product::query()->filterByProductOrCampaignIds($campaign->id)->withTrashed()->update(['sync_status' => 0, 'temp_status' => $temp_status]);
            IndexProduct::query()->filterByProductOrCampaignIds($campaign->id)->withTrashed()->update(['temp_status' => $temp_status]);
            $this->info('Updated thumb url for campaign: ' . $campaign->id);
        }
        return true;
    }

    private function sendTempOrderEmail() {
        $limit = 1000;
        $status = 2;
        $rows = Temp::query()
            ->where('status', '!=', $status)
            ->limit($limit)
            ->get();
        if ($rows->isEmpty()) {
            return false;
        }
        $order_shipping_notification_setting = SystemConfig::getCustomConfig('order_shipping_notification_setting');
        $enabled = optional($order_shipping_notification_setting)->value ?? 0;
        if (empty($enabled)) {
            return true;
        }
        $this->info('Total rows: ' . $rows->count());
        foreach ($rows as $row) {
            $order = Order::query()->whereKey($row->id1)->first();
            if (!$order) {
                $this->info('Order not found: ' . $row->id1);
                graylogInfo('[Schedule] Order not found: ' . $row->id1, [
                    'category' => 'email_logs',
                ]);
                continue;
            }
            $config = [
                'to'          => $order->customer_email,
                'template'    => 'buyer.order_ship_late_notification',
                'data'        => [
                    'subject'          => 'Shipping Notification',
                    'name'             => $order->customer_name,
                    'email'            => $order->customer_email,
                    'order'            => [
                        'id'                    => $order->id,
                        'status_url'            => $order->status_url,
                        'order_number'          => $order->order_number
                    ],
                    'store_info' => StoreService::getStoreInfo($order->store_id)
                ],
                'sendMailLog' => [
                    'sellerId' => $order->seller_id ?? null,
                    'storeId' => $order->store_id ?? null,
                    'orderId' => $order->id ?? null
                ]
            ];
            sendEmail($config);
            $this->info('Sent email for order: ' . $order->id);
            graylogInfo('[Schedule] Sent email for order: ' . $order->id, [
                'category' => 'email_logs',
                'order' => $order
            ]);
            Temp::query()->where('id1', $row->id1)->update(['status' => $status]);
        }
        return true;
    }

    private function updateCampaignDeletedSlug($limit = 500)
    {
        $ids = IndexCampaign::query()
            ->withTrashed()
            ->where('is_deleted', 1)
            ->whereNotNull('slug')
            ->limit($limit)
            ->pluck('id');

        if ($ids->isEmpty()) {
            return false;
        }

        IndexCampaign::query()
            ->withTrashed()
            ->whereIn('id', $ids)
            ->update(['slug' => null]);
        Campaign::query()
            ->withTrashed()
            ->whereIn('id', $ids)->update(['sync_status' => 0, 'slug' => null]);

        return true;
    }

    /**
     * @throws \JsonException
     */
    private function updateDisableCountdown()
    {
        $limit = 500;
        $temp_status = 26;
        $seller_id = 261;
        $campaigns = IndexCampaign::query()
            ->select('id')
            ->where('seller_id', $seller_id)
            ->where('temp_status', '!=', $temp_status)
            ->where('status', ProductStatus::ACTIVE)
            ->where('show_countdown', '>', 0)
            ->limit($limit)
            ->get();
        if ($campaigns->isEmpty()) {
            return false;
        }
        $campaignIds = $campaigns->pluck('id')->toArray();
        Product::query()->filterByProductOrCampaignIds($campaignIds)->withTrashed()->update(['show_countdown' => 0, 'sync_status' => 0, 'temp_status' => $temp_status]);
        IndexProduct::query()->filterByProductOrCampaignIds($campaignIds)->withTrashed()->update(['show_countdown' => 0, 'temp_status' => $temp_status]);
        $this->info('Updated disabled countdown for campaigns: ' . json_encode($campaignIds, JSON_THROW_ON_ERROR));
        return true;
    }

    private function syncRemainingCampaign(): void
    {
        IndexCampaign::query()->withTrashed()
            ->where('seller_id', 103001)
            ->where('updated_at', '>', '2024-11-20')
            ->update([
                'temp_status' => TempStatusEnum::DEFAULT
            ]);
        IndexCampaign::query()->withTrashed()
            ->where('updated_at', '>', '2024-11-26')
            ->update([
                'temp_status' => TempStatusEnum::DEFAULT
            ]);
        $seller = User::query()->find(103001);
        SyncRemainingCampaignJob::dispatch($seller);
    }

    /**
     * @return false|void
     * @throws \Throwable
     */
    private function correctCampaignData() {
        $temps = Temp::query()->where('status',0)->whereNotNull('id1')->whereNotNull('id2')->orderBy('id1')->limit(1000)->get();
        if ($temps->isEmpty()) {
            return false;
        }
        $import_datas = ImportCampaignsData::query()->select(['id', 'campaign_id', 'seller_id', 'suffix', 'name_suffix', 'campaign_slug', 'campaign_name'])->whereIn('id', $temps->pluck('id1')->unique()->toArray())->get();
        foreach ($temps as $temp) {
            $import_data = $import_datas->where('id', $temp->id1)->first();
            if (!$import_data) {
                continue;
            }
            $process_id = $temp->id1;
            dispatch(static function () use ($import_data, $process_id) {
                $suffix = $import_data->suffix;
                $campaign_slug = $import_data->campaign_slug;
                $campaign_id = $import_data->campaign_id;
                $seller_id = $import_data->seller_id;
                $count_slug = substr_count($campaign_slug, '-' . $suffix);
                $seller = User::query()->where('id', $seller_id)->first();
                if ($count_slug <= 1 || !$seller) {
                    Temp::query()->where('id1', $process_id)->update(['status' => 1]);
                    return;
                }
                $campaign_slug = preg_replace("/($suffix)(?:-$suffix)+$/", $suffix, $campaign_slug);
                if (Slug::isInvalid($campaign_slug)) {
                    $campaign = Product::query()->where('slug', $campaign_slug)->first();
                    if (!$campaign || $campaign->status !== CampaignStatusEnum::DRAFT) {
                        Temp::query()->where('id1', $process_id)->update(['status' => 2]); // can not change slug
                        return;
                    }
                    CampaignService::clearCampaignById($campaign->id, $seller, false);
                }
                $name_suffix = $import_data->name_suffix;
                $campaign_name = $import_data->campaign_name;
                $count_name = substr_count($campaign_name, ' ' . $name_suffix);
                if ($count_name > 1) {
                    $campaign_name = preg_replace("/($name_suffix)(?:\s$name_suffix)+$/", $name_suffix, $campaign_name);
                }
                Product::query()->where('id', $campaign_id)->update([
                    'slug' => $campaign_slug,
                    'name' => $campaign_name,
                    'sync_status' => 0
                ]);
                ImportCampaignsData::query()->where([
                    'id' => $process_id,
                    'campaign_id' => $campaign_id
                ])->update([
                    'campaign_slug' => $campaign_slug,
                    'campaign_name' => $campaign_name
                ]);
                SyncSlugJob::dispatchSync(ids: [$campaign_id], seller: $seller, isUpsert: true);
                Temp::query()->where('id1', $process_id)->update(['status' => 1]); // corrected
            });
        }
        $this->info('---------------> Done <-----------------');
    }

    /**
     * @return int
     */
    private function syncDraftCampaigns()
    {
        $temps = Temp::query()->where('status',0)->whereNotNull('id1')->orderBy('id1')->limit(500)->get();
        if ($temps->isEmpty()) {
            return 0;
        }
        $sellerId = 103001;
        try {
            $campaignIds = $temps->pluck('id1')->unique()->toArray();
            $campaigns = IndexCampaign::query()
                ->withTrashed()
                ->select(['id'])
                ->whereIn('id', $campaignIds)
                ->where('seller_id', $sellerId)
                ->orderBy('id')
                ->limit(500)
                ->get();
            $user = User::query()->whereKey($sellerId)->first();
            $campaigns->each(function (IndexCampaign $campaign) use ($user) {
                InsertCampaignJob::dispatch($campaign, $user)->onQueue('sharding-table-insert');
                Temp::query()->where('id1', $campaign->id)->update(['status' => 1]);
                $this->info('Sync campaign: ' . $campaign->id);
            });
        } catch (\Throwable $e) {
            var_dump($e->getMessage());
        }
        return 1;
    }

    /**
     * @param $start
     * @param $end
     * @param $limit
     * @return void
     */
    private function correctBulkCampaigns($start, $end, $limit = 500) {
        $temp_status = 2;
        $bulks = ImportCampaignsData::query()
            ->select('id', 'seller_id')
            ->where('type', 'regular')
            ->where('status', ImportCampaignStatusEnum::COMPLETED)
            ->where('temp_status', '!=', $temp_status)
            ->whereIn('seller_id', [261, 258])
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)
            ->whereNotNull('campaign_id')
            ->orderBy('id')
            ->limit($limit)
            ->get();
        if ($bulks->isEmpty()) {
            return;
        }
        $seller_ids = $bulks->pluck('seller_id')->unique()->toArray();
        graylogInfo('[Schedule] Start correct bulk campaigns data: Total' . $bulks->count(), [
            'category' => 'bulk_campaigns',
        ]);
        $sellers = User::query()->where('id', $seller_ids)->get();
        try {
            foreach ($bulks as $bulk_data) {
                $seller = $sellers->where('id', $bulk_data->seller_id)->first();
                $bulk = ImportCampaignsData::query()->whereKey($bulk_data->id)->first();
                if (!$bulk || !$seller) {
                    continue;
                }
                $campaign = Product::query()->onSellerConnection($seller)->whereKey($bulk->campaign_id)->first();
                if (empty($campaign)) {
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::FAILED,
                        'logs' => 'Campaign maybe deleted.'
                    ]);
                    continue;
                }
                $bulk->update(['temp_status' => $temp_status]);
                $image = File::query()->onSellerConnection($seller)->where([
                    'campaign_id' => $bulk->campaign_id,
                    'type' => FileTypeEnum::IMAGE,
                ])->whereNull('type_detail')->exists();
                $design_3d = File::query()->onSellerConnection($seller)->select('file_url')->where([
                    'campaign_id' => $bulk->campaign_id,
                    'type' => FileTypeEnum::DESIGN,
                    'option' => FileRenderType::RENDER_3D
                ])->whereNull('type_detail')->first();
                if (empty($image) || empty($design_3d)) {
                    $logs = 'Invalid design rendered data';
                    if (empty($image)) {
                        $logs = 'Invalid image data';
                    }
                    if (empty($design_3d)) {
                        $logs = 'Invalid design 3d data';
                    }
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::PENDING,
                        'system_status' => null,
                        'logs' => $logs
                    ]);
                    if ($campaign->status === ProductStatus::ACTIVE) {
                        Product::query()->onSellerConnection($seller)->filterByProductOrCampaignIds([$bulk->campaign_id])->update([
                            'status' => ProductStatus::DRAFT,
                            'sync_status' => 0,
                        ]);
                    }
                    continue;
                }
                if (empty(file_exists_on_storage($design_3d->file_url)) || (file_size_on_storage($design_3d->file_url) < to_byte(50, 'KB') && CampaignService::isDesignTransparent($design_3d->file_url))) {
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::PENDING,
                        'system_status' => null,
                        'mockups' => null,
                        'logs' => 'Design 3D file is too small'
                    ]);
                    Product::query()->onSellerConnection($seller)->filterByProductOrCampaignIds([$bulk->campaign_id])->update([
                        'status' => ProductStatus::DRAFT,
                        'sync_status' => 0,
                    ]);
                    graylogInfo('[Schedule] Design 3D file is too small', [
                        'category' => 'bulk_campaigns',
                        'campaign_id' => $bulk->campaign_id,
                        'bulk_id' => $bulk->id,
                        'seller_id' => $bulk->seller_id,
                        'file_url' => $design_3d->file_url,
                    ]);
                }
            }
        } catch (\Throwable $e) {
            logException($e, 'correctBulkCampaigns', 'bulk_campaign');
        }
    }

    /**
     * @return void
     */
    private function blockCampaigns()
    {
        try {
            $excludeIds = [276076805,433643371,433217524];
            $sellerIds = [1054703,104704,1056700,121658,103688,1054823,104238,104342,101774,1054876,1054744,104705,104686,1055143,1055110,104952,1054748,1054685,1054826,1055111,1055141,121981,1054661,103689,103288,1054706,1224100,1057124,1055090,122973,104227,105238,1054676,1224090,136594,1056158,1054772,105257,1056678,105083,1223560,1223787,1056157,1055716,1055767,1056136,1057275,1055247,1055094,103353,122570,1315035,103460,1021,1056127,1056798,101725,1057820,1056969,864,1058109,1056336,1056970,1056788,1056144,1056102,1054709,1056797,1055125,103405,122726,1056285,103511,1056290,1057278,103940,1056287,101641,101082,1056786,1056789,1056128,1056971,1056999,1056802,101172,1056973,1056972,1056810,1058044,103192,1057250,101636,1056790,102916,101493,1056995,1056801,101729,103179,1056809,1056800,1056785,1057684,101281,1058047,1057157,1164016,1057682,104520,1056787,1058029,1057257,1055233,1223632,1056996,1057312,1058046,1223627,1057156,1058135,1055337,1056062,1057187,1056355,104251,1057253,1056955,1058048,103621,1056061,1057276,1057400,1056954,1057251,101730,102819,102437,1057248,1055596,1056811,1056706,1056975,104663,102790,1056993,1057281,1056803,1055087,1056291,101320,1057680,103392,103722,1056799,1057000,148771,1164066,1058045,1164038,1055592,1055579,1056202,1057263,1056286,1056237,101057,122321,102949,101799,1057301,1164026,1057413,101647,102280,101733,1058021,1056101,1056998,102001,1163875,1164018,103001,139611,104721,1054669,1057123,1056684,121555,1056132,104311,1223936,1057118,122353,103648,103141,1055021,104320];
            $campaignsIds = [42482260,42312162,104224931,104144473,12803223,42263584,42263425,42312318,58523831,58524710,58524558,2360070,47581020,59556205,59556362,2360619,2360167,42482713,42311974,54012006,54099541,42263229,64286396,58597330,55097713,59556275,54016136,54015590,54099118,54013998,42545266,42263510,2359599,54012177,58525400,58597445,58525016,55097329,54099433,54015916,64288153,64286740,2360312,64286122,111996850,104145214,2359746,58525194,104144546,47579378,45126047,54011761,12855578,54099332,59557237,43951228,48594965,255362865,64286912,104144520,2359462,47589821,58525591,47523022,104145240,64286088,64288459,54014890,111716107,47586466,47521330,58525375,104144661,2359829,12855504,42312475,42482416,64286228,54013769,47583121,58524537,58524748,57644281,104145113,54015446,54014650,64289146,47522205,47584049,2359676,141324954,54098894,104145338,104144817,58525172,42482575,42545371,47585031,104145072,104144923,64289712,58149609,42441347,57644066,47590165,42441804,54013493,104144929,57644384,42263324,49240121,49240280,64286532,47519769,12855075,104145167,123011275,47520861,64288853,64286756,64289497,54014292,58524446,58524975,104145161,58597545,104225007,64286021,2359899,58523781,104144726,64286428,58150742,58524518,64286282,104145009,58149673,42263075,2360007,54015228,42545480,58597664,64286604,104144752,58597748,55098182,47585583,54015147,104224843,54098734,47586800,54012225,232317449,47521789,357266344,54013701,54011555,66787664,70436282,44437209,113895132,114056267,231463848,295637671,66787597,66787701,66787343,70427760,320191896,70484469,113895702,200683173,70427648,114056428,114056212,66787580,66787613,70436195,114056001,66787544,70436302,70436216,114056734,122460917,66787642,70436628,200683074,114056396,70485613,66787318,249078443,70483957,70484332,70484145,113894703,70485619,70436835,70427328,122461135,113896964,412867059,70427485,26514186,113896541,113896124,66787716,433643371,273189040,123475473,124656790,3498638,130930632,130882434,2410179,175282858,69744264,107692042,89276267,45445718,107158850,119194848,56588572,110171755,56588245,148758093,99556721,56588716,125426642,105607122,130928388,56588913,19688252,34421419,79628481,186306580,18331948,99679828,75259102,87278639,95933753,111125934,110501130,114837243,34294888,1766094,56588113,126235568,130399770,130186377,130181968,143049670,110833109,89605184,88689499,72364740,111039963,95299619,124271126,117207506,79161767,119631563,120299706,110858151,110988050,87971671,264862931,278142551,129951485,242583782,12549487,60282794,130929935,123768808,166816198,123743019,4762097,85772622,83815499,88430405,120420436,137984574,112023285,124722043,97386201,125426721,120299693,77857427,60625017,131392481,107380946,130874113,100003375,110172820,135977419,429390042,107686983,104913822,148444469,130078175,123742916,123742796,122828013,115512226,137604666,130350594,71795879,164507587,157071926,216072093,160215010,218264054,80994651,78501017,110173903,87816546,166813599,168117655,264846894,109256488,10193352,138405567,89520752,433217524,123473196,110502149,205223572,83318575,36069782,93152055,148758175,111040425,107942636,131532916,46698607,30449928,136327907,115493940,70331504,80993249,16575807,60067794,120420442,147229676,88522153,110756319,28230500,159029525,28335850,105016453,60289967,109454555,87973273,56588624,369737116,123742565,103683967,216072665,127709596,158423502,90400627,35452023,71794733,84223953,189127212,83004148,88129411,124492398,110467435,121355817,265199093,2980814,440464684,123475995,29753203,32890381,112672992,130875487,205582624,125425968,369739400,441841395,125974470,64324199,122255776,88425685,148574406,106325230,88250913,120420444,88894470,63388569,106597506,86369847,109777188,130337070,89261368,125903023,103221003,162769186,211778419,124271538,126208230,72360366,87984976,88615466,148737845,199572624,130554898,120299708,124289690,97384296,126304609,132626822,10188950,87786572,126205967,82709163,83880053,49241610,15533391,99844557,130024975,104773508,115495062,92692269,76997870,72563125,111040827,78695634,120299670,23864349,73503136,163933410,89276833,77710050,115466602,124724327,181292367,88403398,242228509,116379949,417505109,129632269,109883020,131082792,125425242,131043304,123772254,187223820,140499605,130395181,124130317,199572721,129175703,67953459,160149742,264836872,60286396,369736698,80525109,22916275,3497547,130931257,93152044,175272993,68749868,148738459,89257940,83804285,167425653,166818184,56588379,166728841,265199869,326341434,31101809,84635000,121679526,122458390,127241102,123475689,121612159,123584562,182171781,70560982,2187518,56589819,101441019,110502929,116321019,436290174,70148800,63814537,122255299,369736945,88207257,60469686,56589346,89106712,131695373,56588804,56588442,228019016,121678799,216072519,216072252,18332004,110433278,93152078,84007497,107747603,123836767,68326415,326448511,60516249,116136227,39127127,60462627,153157040,88796529,56589520,76596183,158323899,131141039,93152016,119545440,56589096,920169,88850542,27555562,69370958,60286343,66486966,73876960,115500778,81878877,121112197,130835381,106225775,166779315,103685099,2577591,68534917,10847710,131540059,60170820,126235235,118666941,72733491,208163869,139026465,264847259,100826770,131530742,175277675,167404122,216072124,216072412,265195074,105384445,32978280,83878180,83881915,79122662,112632759,148758524,105459908,124385611,89734018,90431186,86810995,124766596,120420482,216072303,67383894,18878606,123472667,27574089,135976511,60068461,276076805,130929094,228018568,110858996,131229610,130926505,74059009,2673741,98040692,186095375,166741781,128348362,88851955,89699557,6815151,265201756,329036131,329036213,329036177,44742056,51967089,45467143,436290004,247408391,67683598,67609884,152853320,148563103,67609693,119234798,67694041,417395305,152852809,71051626,67683481,67682800,241385195,67693715,67608723,233591647,67610032,152854075,119917812,67679368,67679373,71051816,67694181,67682706,417408089,247408317,67693637,152852604,67609818,111728216,148563618,67873125,67694177,67609785,67678950,67609936,71051472,71051966,67682528,67683442,67682770,16867143,67609901,67693864,67610017,67683577,417408110,67683233,152852994,67683286,67682873,247408899,67679363,67682611,67682753,67679343,71051570,48069525,148563783,148563413,270627262,152853542];
            $slugs = Slug::query()
                ->select('seller_id')
                ->selectRaw('GROUP_CONCAT(campaign_id) as ids')
                ->whereIn('seller_id', $sellerIds)
                ->whereIn('campaign_id', $campaignsIds)
                ->whereNotIn('campaign_id', $excludeIds)
                ->groupBy('seller_id')
                ->get();
            foreach ($slugs as $slug) {
                $seller = User::query()->where('id', $slug->seller_id)->first();
                if (!$seller) {
                    continue;
                }
                $campaignIds = Campaign::query()->onSellerConnection($seller)->select('id')->whereIn('id', explode(',', $slug->ids))->where('status', ProductStatus::ACTIVE)->get()->pluck('id')->toArray();
                if (count($campaignIds) === 0) {
                    continue;
                }
                Product::query()->onSellerConnection($seller)->filterByProductOrCampaignIds($campaignIds)->update(['status' => ProductStatus::BLOCKED, 'sync_status' => 0]);
                MailService::sendNotificationBlockedCampaign($campaignIds, $seller);
                clearHtmlCacheSeller($seller->id, $campaignIds);
            }
        } catch (\Throwable $e) {
            logException($e, 'blockCampaigns', 'bulk_campaign');
        }
    }
}
