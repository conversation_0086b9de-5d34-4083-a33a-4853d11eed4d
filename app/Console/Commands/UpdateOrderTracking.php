<?php

namespace App\Console\Commands;

use App\Events\OrderProductFulfilled;
use App\Models\Order;
use App\Models\OrderProduct;
use Illuminate\Console\Command;

class UpdateOrderTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-order-tracking {order : The ID of Order}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update order tracking for order';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $order = Order::query()->with(['products:id,order_id,tracking_code,shipping_carrier,tracking_url'])->find($this->argument('order'));

        if (!$order) {
            return 0;
        }

        $order_product = $order->products->last();
        if (!$order_product) {
            return 0;
        }
        /** @var OrderProduct $order_product */
        $tracking = [
            'tracking_code' => $order_product->tracking_code,
            'shipping_carrier' => $order_product->shipping_carrier,
            'tracking_url' => $order_product->tracking_url,
        ];

        OrderProductFulfilled::dispatch($order->id, $tracking);
        return 0;
    }
}
