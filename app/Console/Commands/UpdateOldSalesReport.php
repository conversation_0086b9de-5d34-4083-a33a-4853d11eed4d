<?php

namespace App\Console\Commands;

use App\Actions\Admin\Analytic\InsertSaleReportAction;
use App\Models\SystemConfig;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Throwable;

/**
 * @deprecated This command is no longer used
 */
class UpdateOldSalesReport extends Command
{
    protected $signature = 'sales-report:update';

    protected $description = 'Update old sales report by hour';

    public function handle(): int
    {
        try {
            $endTime = SystemConfig::query()
                ->where('key', 'sale_report_update_timestamp')
                ->value('value');

            if (!$endTime) {
                $endTime = Carbon::parse('2023-06-05 10:00:00');
            } else {
                $endTime = Carbon::parse($endTime);
            }

            $startTime = $endTime->copy()->subHour();

            $request = new Request([
                'start_time' => $startTime,
                'end_time' => $endTime,
            ]);
            (new InsertSaleReportAction())->handle($request);

            SystemConfig::query()
                ->updateOrCreate(
                    ['key' => 'sale_report_update_timestamp'],
                    ['value' => $startTime->toDateTimeString()]
                );

            return 1;
        } catch (Throwable $e) {
            logException($e);
        }

        return 0;
    }
}
