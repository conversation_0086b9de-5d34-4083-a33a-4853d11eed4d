<?php

namespace App\Console\Commands;

use App\Enums\DateRangeEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\SystemConfigTypeEnum;
use App\Models\IndexOrder;
use App\Models\StatsOrder;
use App\Models\SystemConfig;
use Illuminate\Console\Command;

class CheckTodaySales extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check-today-sales';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto report today sales on Discord';

    protected $configKey = 'max_today_sales';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $todaySales = self::getTodaySales();

        // current record: https://discord.com/channels/874562708392005672/1183597934332354671/1183816416051347528
        $maxTodaySales = SystemConfig::getConfig($this->configKey, 370042.53);

        if ($todaySales > (float)$maxTodaySales) {
            self::sendDiscordMessage($todaySales, $maxTodaySales);
            SystemConfig::setConfig($this->configKey, [
                'value' => $todaySales,
                'status' => 1,
                'type' => SystemConfigTypeEnum::BACKEND
            ]);
        }

        telegramDebug('[check-today-sales] result: ' . $todaySales);
    }

    public static function sendDiscordMessage($todaySales, $previousSales): void
    {
        try {
            $message = ':rocket: Doanh thu hôm nay là **$' . formatCurrency($todaySales, 'USD', 'en-US') . '** (kỉ lục trước đó: **$' . formatCurrency($previousSales, 'USD', 'en-US') . '**).';
            $increasePercentage = ($todaySales - $previousSales) / $previousSales * 100;
            $message .= ' Tăng **' . number_format($increasePercentage) . '%** :chart_with_upwards_trend:';

            logToDiscord($message, 'sales_milestone');
        } catch (\Throwable $e) {
            logException($e);
        }
    }

    public static function getTodaySales(): float
    {
        $result = IndexOrder::query()
            ->addFilterAnalytic([], [
                'column' => StatsOrder::FILTER_COLUMN_DATE,
                'type' => DateRangeEnum::YESTERDAY,
            ])
            ->selectRaw("SUM(IF(type in ('" . OrderTypeEnum::CUSTOM . "', '" . OrderTypeEnum::REGULAR . "') , total_amount, 0)) AS total_sales")
            ->first();

        return $result->total_sales ?? 0;
    }
}
