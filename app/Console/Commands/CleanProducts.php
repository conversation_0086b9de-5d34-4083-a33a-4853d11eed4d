<?php

namespace App\Console\Commands;

use App\Jobs\CleanProductsDataJob;
use Illuminate\Console\Command;

class CleanProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system-clean-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean products data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        dispatch(new CleanProductsDataJob());
        return self::SUCCESS;
    }
}
