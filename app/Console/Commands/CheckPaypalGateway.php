<?php

namespace App\Console\Commands;

use App\Enums\PaymentMethodEnum;
use App\Models\PaymentGateway;
use App\Services\PayPal;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CheckPaypalGateway extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check-paypal-gateway';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check paypal gateway';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $paypalConfig = PaymentGateway::query()
            ->select('config')
            ->where([
                'gateway' => PaymentMethodEnum::PAYPAL,
                'active' => true
            ])
            ->whereNull('store_id')
            ->value('config');

        if (is_null($paypalConfig)) {
            $this->error('Paypal config not found');
            return self::INVALID;
        }

        $config = json_decode($paypalConfig, true);

        try {
            $gateway = new PayPal($config);

            if (!empty($gateway->getAccessToken())) {
                $this->info('Paypal gateway is working');
                return self::SUCCESS;
            }

            $this->error('Paypal gateway is not working');
            return self::FAILURE;
        } catch (\Throwable $e) {
            $cacheKey = 'paypal_gateway_error';

            if (Cache::has($cacheKey)) {
                Cache::forget($cacheKey);
                logToDiscord('Paypal gateway is not working: ' . $e->getMessage(), 'important');
                return self::FAILURE;
            }

            Cache::put($cacheKey, true, now()->addMinutes(10));
            return self::INVALID;
        }
    }
}
