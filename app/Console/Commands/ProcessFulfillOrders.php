<?php

namespace App\Console\Commands;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Facades\ProcessLock;
use App\Models\Order;
use App\Services\OrderService;
use Illuminate\Console\Command;

class ProcessFulfillOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'process-fulfill-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process Fulfill Orders';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        ProcessLock::handle($this->signature, callback: function () {
            try {
                $orders = Order::query()
                    ->select('order.*')
                    ->join('user', 'user.id', '=', 'order.seller_id')
                    ->where([
                        'order.status' => OrderStatus::PENDING,
                        'order.fulfill_status' => OrderFulfillStatus::UNFULFILLED,
                        'order.sen_fulfill_status' => OrderSenFulfillStatus::YES,
                    ])
                    ->whereIn('order.type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                    ->whereRaw('user.auto_fulfill = 1 and user.balance >= order.total_amount and order.updated_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)')
                    ->orderBy('order.created_at')
                    ->groupBy('order.id')
                    ->limit(100)
                    ->get();

                if ($orders->count() > 0) {
                    $orders->each(fn($order) => OrderService::processFulfillOrder($order));
                }
                return 0;
            } catch (\Throwable $e) {
                logException($e);
                return 1;
            }
        });
        return self::SUCCESS;
    }
}
