<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\CampaignStatusEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\SellerBillingType;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Elastic;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Models\Product;
use App\Models\SellerBilling;
use App\Models\Slug;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Throwable;

class ValidateData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system-data:validate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate Data';

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $CACHE_LAST_VALIDATED_TIME = 'cache_last_validated_time';
        $validateTime = now();

        try {
            $lastValidatedTime = cache()->get($CACHE_LAST_VALIDATED_TIME) ?? $validateTime->copy()->subMinutes(5);
        } catch (Throwable|NotFoundExceptionInterface|ContainerExceptionInterface $e) {
            $lastValidatedTime = $validateTime->copy()->subMinutes(5);
        }

        // validate seller balance
        $CACHE_VALIDATED_SELLER_IDS = 'validated_seller_ids';

        try {
            $validatedIds = cache()->get($CACHE_VALIDATED_SELLER_IDS) ?? [];
        } catch (Throwable|NotFoundExceptionInterface|ContainerExceptionInterface $e) {
            $validatedIds = [];
        }

        $recentUpdatedSellers = User::query()->select(['user.id', 'user.balance', 'seller_billing.id as transaction_id', 'seller_billing.balance as last_balance'])
            ->leftJoin('seller_billing', function($join) {
                $join->on('user.id', '=', 'seller_billing.seller_id');
                $join->on('seller_billing.id', '=', DB::raw("(select max(seller_billing.id) from seller_billing where seller_billing.seller_id=user.id and seller_billing.balance_type='default')"));
            })
            ->where('user.balance', '>', 0)
            ->where('user.updated_at', '>=', $lastValidatedTime)
            ->whereNotIn('user.id', $validatedIds)
            ->groupBy('user.id')
            ->get();

        $validatedIds = [];

        foreach ($recentUpdatedSellers as $seller) {
            if ($seller->balance !== $seller->last_balance) {
                User::query()->where('id', $seller->id)->update([
                    'status' => UserStatusEnum::FLAGGED,
                    'flag_log' => 'Balance updated unusual. Balance: ' . $seller->balance . '. Last balance: ' . $seller->last_balance . ' - transaction #' . $seller->transaction_id
                ]);
                $validatedIds[] = $seller->id;
                logToDiscord('Balance invalid! Seller #' . $seller->id . '. Balance: ' . $seller->balance . '. Last balance: ' . $seller->last_balance . ' - transaction #' . $seller->transaction_id, 'admin_warning');
            }
        }
        cache()->put($CACHE_VALIDATED_SELLER_IDS, $validatedIds, CacheKeys::CACHE_5m);

        // validate transaction
        $transactions = SellerBilling::query()
            ->where('updated_at', '>=', $lastValidatedTime)
            ->where('balance_type', SellerBalanceTypeEnum::DEFAULT)
            ->where('is_valid', true)
            ->get();

        if ($transactions->isNotEmpty()) {
            foreach ($transactions as $transaction) {
                if (!$transaction->validate()) {
                    $transaction->save();
                    logToDiscord('Transaction invalid! Seller #' . $transaction->seller_id . '. Transaction #' . $transaction->id, 'admin_warning');
                }
            }
        }

        // validate order amount after 10 min
        $delayTime = 10;
        $orders = Order::query()->where('payment_status', OrderPaymentStatus::PAID)->with(['products'])
            ->whereBetween('paid_at', [$lastValidatedTime->copy()->subMinutes($delayTime), $validateTime->copy()->subMinutes($delayTime)])
            ->get();
        foreach ($orders as $order) {
            $order->calculateOrder();
            if ($order->type === OrderTypeEnum::REGULAR || $order->type === OrderTypeEnum::CUSTOM) {
                if (abs($order->total_product_amount + $order->total_shipping_amount + $order->insurance_fee - $order->total_discount + $order->tip_amount - $order->total_amount) > 0.1) {
                    logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder invalid #$order->id . Total amount $order->total_paid is invalid.", 'admin_warning');
                }
            }
            if (abs($order->total_paid - $order->total_amount) > 0.1) {
                logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder invalid #$order->id . Total paid $order->total_paid is different from total amount $order->total_amount.", 'admin_warning');
            }
        }

        // validate order pay profit after 10 min
        $orders = Order::query()
            ->with(['products'])
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->whereBetween('paid_at', [$lastValidatedTime->copy()->subMinutes($delayTime), $validateTime->copy()->subMinutes($delayTime)])
            ->whereHas('products', function ($q) {
                $q->where('seller_profit', 0)->orWhereNull('seller_profit');
            })
            ->get();
        foreach ($orders as $order) {
            if ($order->isCustomServiceOrder()) {
                continue;
            }
            $order->calculateSellerProfit();
            $order->products->map(function ($product) {
                $product->saveQuietly();
            });
        }

        $orders = Order::query()
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->where('type', OrderTypeEnum::REGULAR)
            ->whereBetween('paid_at', [$lastValidatedTime->copy()->subMinutes($delayTime), $validateTime->copy()->subMinutes($delayTime)])
            ->whereDoesntHave('sellerBilling', function ($q) {
                $q->where('type', SellerBillingType::COMMISSION);
            })
            ->get();
        foreach ($orders as $order) {
            $paymentGateway = PaymentGateway::query()->select(['id', 'seller_id'])->find($order->payment_gateway_id);
            if ($paymentGateway && $paymentGateway->seller_id && $paymentGateway->seller_id !== User::SENPRINTS_SELLER_ID) {
                continue;
            }
            logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder #$order->id Order paid, but seller doesn't have commission transaction. Paid at: {$order->paid_at}, Now: " . now()->toDateTimeString(), 'admin_warning');
        }

        cache()->put($CACHE_LAST_VALIDATED_TIME, $validateTime, CacheKeys::CACHE_5m);

        // validate campaigns sync status
        $connections = User::query()->select('db_connection')->where('is_deleted', 0)->where('role', '!=', UserRoleEnum::CUSTOMER)->groupBy('db_connection')->get()->pluck('db_connection')->toArray();
        foreach ($connections as $connection) {
            $activeProductsInOver10Mins = Campaign::query()
                ->on($connection)
                ->select(['id', 'status', 'seller_id', 'slug'])
                ->where('updated_at', '<=', now()->subMinutes(5))
                ->where('updated_at', '>=', now()->subMinutes(15))
                ->where('status', ProductStatus::ACTIVE)
                ->get();
            $activeProductsInOver10Mins->groupBy('seller_id')->each(function ($collection, $sellerId) use ($connection) {
                // Check and sync slugs
                $slugs = $collection->pluck('slug')->toArray();
                if (count($slugs) > 0) {
                    $createdSlug = collect($slugs)
                        ->chunk(200)
                        ->flatMap(fn($chunk) => Slug::query()->whereIn('slug', $chunk)->pluck('slug'))
                        ->all();
                    $notCreatedSlug = array_values(array_diff($slugs, $createdSlug));
                    if (count($notCreatedSlug) > 0) {
                        Slug::query()->insertOrIgnore($collection->filter(fn ($item) => in_array($item->slug, $notCreatedSlug, true))->map(fn ($item) => ['seller_id' => $sellerId, 'campaign_id' => $item->id, 'slug' => $item->slug])->toArray());
                    }
                }
                // Sync to elastic
                $arrFilterElastic = [];
                $arrFilterElastic['ids'] = $collection->pluck('id')->toArray();
                $arrFilterElastic['status'] = CampaignStatusEnum::DRAFT;
                $arrFilterElastic['seller_id'] = $sellerId;
                [$campaigns, $total] = (new Elastic())->getCampaign(['id'], $arrFilterElastic, $collection->count());
                if (empty($campaigns) || $total <= 0) {
                    return;
                }
                $campaignIds = Arr::pluck($campaigns, 'id');
                if (count($campaignIds) > 0) {
                    try {
                        (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignIds, sellerId: $sellerId);
                    } catch (\Throwable $e) {
                        Product::query()->on($connection)->filterByProductOrCampaignIds($campaignIds)->update(['sync_status' => 0]);
                    }
                }
                unset($campaigns, $total, $arrFilterElastic, $campaignIds);
            });
            unset($activeProductsInOver10Mins);
        }
    }
}
