<?php

namespace App\Console\Commands;

use App\Enums\CacheTime;
use App\Enums\DiscordChannel;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Jobs\ScanCompleteOrderPaymentJob;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Models\PgWebhookLogs;
use App\Services\OrderService;
use App\Traits\Encrypter;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ProcessOrderPendingPayment extends Command
{
    use Encrypter;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:complete-pending-payment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This is a command handle complete pending payment for order';

    /**
     * Execute the console command.
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->processOrderPendingStripePayment();
        $this->processOrderPendingPayPalPayment();

        // Check if the database is in sync with the master database
        $connection = 'mysql_main_us';
        $secondsBehindMaster = getDatabaseSecondBehindMaster($connection);
        if ($secondsBehindMaster === 0) {
            $orderIds = Order::on($connection)
                ->select('id')
                ->where('payment_status', OrderPaymentStatus::PAID)
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                ->where('paid_at', '>=', now()->subHours(2))
                ->where('paid_at', '<=', now()->subMinutes(5))
                ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
                ->get()
                ->pluck('id')
                ->toArray();
            if (count($orderIds) === 0) {
                return;
            }
            $masterOrderIds = Order::query()
                ->select('id')
                ->whereIn('id', $orderIds)
                ->where('payment_status', OrderPaymentStatus::PAID)
                ->get()
                ->pluck('id')
                ->toArray();

            $needToProcessOrderIds = array_values(array_diff($orderIds, $masterOrderIds));
            if (count($needToProcessOrderIds) > 0) {
                logToDiscord("Orders paid on US need to sync to SG: " . implode(', ', $needToProcessOrderIds), DiscordChannel::ADMIN_WARNING);
                OrderService::syncDataOrdersFromUsToSg(orderId: $needToProcessOrderIds);
            }
        }
    }

    /**
     * @return int
     */
    private function processOrderPendingStripePayment()
    {
        $orders = Order::query()
            ->whereNull('paid_at')
            ->whereNotNull('payment_gateway_id')
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->excludeTest()
            ->where(function ($q) {
                $q->whereNull('transaction_id')->orWhere('transaction_id', '');
            })
            ->where('created_at', '>=', now()->clone()->subWeek()->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subMinutes(15)->toDateTimeString(), now()->clone()->subMinutes(10)->toDateTimeString()])
            ->orderBy('id')
            ->get();
        $excludeOrderIds = [];
        if ($orders->isNotEmpty()) {
            foreach ($orders as $idx => $order) {
                $excludeOrderIds[] = $order->id;
                ScanCompleteOrderPaymentJob::dispatch($order)->delay(now()->addSeconds($idx));
            }
        }

        // Order has webhook data
        $query = Order::query()
            ->whereNull('paid_at')
            ->whereNotNull('payment_gateway_id')
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->excludeTest()
            ->when(!empty($excludeOrderIds), function ($q) use ($excludeOrderIds) {
                $q->whereNotIn('id', $excludeOrderIds);
            })
            ->where('created_at', '>=', now()->clone()->subWeek()->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subMinutes(15)->toDateTimeString(), now()->clone()->subMinutes(10)->toDateTimeString()])
            ->where('payment_method', 'like', PaymentMethodEnum::STRIPE . '%');
        $full_sql = $query->toRawSql();
        $pendingPaymentOrders = $query->get();
        if ($pendingPaymentOrders->isEmpty()) {
            return 0;
        }
        graylogInfo('[STRIPE] Start scan and complete orders.', [
            'category' => 'pending_payment_order',
            'gateway' => PaymentMethodEnum::STRIPE,
            'raw_query' => $full_sql,
            'total_orders' => $pendingPaymentOrders->count(),
        ]);
        foreach ($pendingPaymentOrders as $order) {
            $order_number = $order->order_number;
            if (!preg_match('/\d-\w+$/', $order_number)) {
                return 0;
            }
            $order_id = Str::of($order_number)->explode('-')->last();
            $paymentLog = PgWebhookLogs::query()
                ->whereNotNull('order_id')
                ->where('order_id', $order_id)
                ->whereIn('event_type', ['charge.succeeded', 'payment_intent.succeeded'])
                ->orderByDesc('created_at')
                ->first();
            if (!$paymentLog) {
                continue;
            }
            $payment_gateway_id = (int)$order->payment_gateway_id;
            $actual_payment_gateway_id = (int)$paymentLog->payment_gateway_id;
            $payment = json_decode($paymentLog->payload, false);
            if ($paymentLog->event_type === 'payment_intent.succeeded') {
                $transaction_id = $payment?->data?->object?->id;
            } else {
                $transaction_id = $payment?->data?->object?->payment_intent;
            }
            $total_amount = (float)$order->total_amount;
            $total_paid = (float)$payment?->data?->object?->amount;
            if (empty($transaction_id) || empty($total_paid)) {
                continue;
            }
            $description = $payment?->data?->object?->description;
            $total_paid /= 100;
            $message = "Order #{$order->id} has not been paid, but a successful payment transaction has been recorded on the gateway." . PHP_EOL;
            $message .= 'Payment method: ' . PaymentMethodEnum::STRIPE . PHP_EOL;
            $message .= 'Total amount: ' . $total_amount . PHP_EOL;
            $message .= 'Total received amount: ' . $total_paid . PHP_EOL;
            if (!empty($payment_gateway_id)) {
                $message .= 'Current PGW ID: ' . $payment_gateway_id . PHP_EOL;
            }
            if ($payment_gateway_id !== $actual_payment_gateway_id) {
                $message .= 'Actual PGW ID: ' . $actual_payment_gateway_id . PHP_EOL;
                $order->payment_gateway_id = $actual_payment_gateway_id;
            }
            if (!empty($order->transaction_id)) {
                $message .= 'Current Transaction ID: ' . $order->transaction_id . PHP_EOL;
            }
            if ($order->transaction_id !== $transaction_id) {
                $message .= 'Actual Transaction ID: ' . $transaction_id . PHP_EOL;
                $order->transaction_id = $transaction_id;
            }
            if ($description) {
                $message .= 'Summary: ' . $description . PHP_EOL;
            }
            if ($total_paid === $total_amount) {
                if ($order->isDirty()) {
                    $order->save();
                }
                $order->paymentCompleted($order->total_amount, $order->transaction_id, false, null, null, $order->payment_gateway_id);
            }
            $this->logToDiscord($order->id, $message);
        }
        return 1;
    }

    /**
     * @param $order_id
     * @param $log
     * @param string $color
     * @return void
     */
    private function logToDiscord($order_id, $log, string $color = '15548997'): void
    {
        $embedDesc = [
            [
                'description' => $log,
                'color' => $color
            ]
        ];
        logToDiscord("https://admin.senprints.com/order/detail/" . $order_id, DiscordChannel::ADMIN_WARNING, false, true, 7, $embedDesc);
    }

    /**
     * @return int
     */
    private function processOrderPendingPayPalPayment()
    {
        $query = Order::query()
            ->whereNull('paid_at')
            ->whereNotNull('payment_gateway_id')
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->excludeTest()
            ->where('created_at', '>=', now()->clone()->subWeek()->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subMinutes(15)->toDateTimeString(), now()->clone()->subMinutes(10)->toDateTimeString()])
            ->where('payment_method', PaymentMethodEnum::PAYPAL)
            ->orderBy('id');
        $full_sql = $query->toRawSql();
        $pendingPaymentOrders = $query->get();
        if ($pendingPaymentOrders->isEmpty()) {
            return 0;
        }
        graylogInfo('[PAYPAL] Start scan and complete orders.', [
            'category' => 'pending_payment_order',
            'gateway' => PaymentMethodEnum::PAYPAL,
            'raw_query' => $full_sql,
            'total_orders' => $pendingPaymentOrders->count(),
        ]);
        foreach ($pendingPaymentOrders as $order) {
            $order_number = $order->order_number;
            if (!preg_match('/\d-\w+$/', $order_number)) {
               continue;
            }
            $order_id = Str::of($order_number)->explode('-')->last();
            $paymentLog = PgWebhookLogs::query()
                ->whereNotNull('order_id')
                ->where('order_id', $order_id)
                ->where('event_type', 'PAYMENT.CAPTURE.COMPLETED')
                ->orderByDesc('created_at')
                ->first();
            if (!$paymentLog) {
                continue;
            }

            $payment_gateway_id = (int)$order->payment_gateway_id;
            $actual_payment_gateway_id = (int)$paymentLog->payment_gateway_id;
            $payment = json_decode($paymentLog->payload, false);
            $transaction_id = $payment?->resource?->id;
            $total_amount = (float)$order->total_amount;
            $total_paid = (float)$payment?->resource?->amount?->value;
            if (empty($transaction_id) || empty($total_paid)) {
                continue;
            }
            if (empty($actual_payment_gateway_id)) {
                $payee_email = $payment?->resource?->payee?->email_address;
                if (!empty($payee_email)) {
                    $actual_payment_gateway_id = cacheAlt()->remember(md5($payee_email . '-' . PaymentMethodEnum::PAYPAL), CacheTime::CACHE_24H, function () use ($payee_email) {
                        return (int)PaymentGateway::query()->select('id')->where([
                            'gateway' => PaymentMethodEnum::PAYPAL,
                            'account_id' => $payee_email
                        ])->value('id');
                    });
                }
            }
            $message = "Order #{$order->id} has not been paid, but a successful payment transaction has been recorded on the gateway." . PHP_EOL;
            $message .= 'Payment method: ' . PaymentMethodEnum::PAYPAL . PHP_EOL;
            $message .= 'Total amount: ' . $total_amount . PHP_EOL;
            $message .= 'Total received amount: ' . $total_paid . PHP_EOL;
            if (!empty($payment_gateway_id)) {
                $message .= 'Current PGW ID: ' . $payment_gateway_id . PHP_EOL;
            }
            if ($payment_gateway_id !== $actual_payment_gateway_id) {
                $message .= 'Actual PGW ID: ' . $actual_payment_gateway_id . PHP_EOL;
                $order->payment_gateway_id = $actual_payment_gateway_id;
            }
            if (!empty($order->transaction_id)) {
                $message .= 'Current Transaction ID: ' . $order->transaction_id . PHP_EOL;
            }
            if ($order->transaction_id !== $transaction_id) {
                $message .= 'Actual Transaction ID: ' . $transaction_id . PHP_EOL;
                $order->transaction_id = $transaction_id;
            }
            if ($total_paid === $total_amount) {
                if ($order->isDirty()) {
                    $order->save();
                }
                $order->paymentCompleted($order->total_amount, $order->transaction_id, false, null, null, $order->payment_gateway_id);
                $message .= 'Order has been completed.' . PHP_EOL;
            }
            $this->logToDiscord($order->id, $message);
        }
        return 1;
    }
}
