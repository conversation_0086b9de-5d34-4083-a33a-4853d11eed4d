<?php

namespace App\Console\Commands;

use App\Enums\CustomEmailLogEnum;
use App\Enums\CustomEmailTemplateEnum;
use App\Models\CustomEmailLog;
use App\Models\CustomEmailTemplate;
use App\Models\Order;
use App\Services\CustomEmailTemplateService;
use App\Services\StoreService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;

class SendCustomEmailTemplate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-custom-email-template';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try{
            $templateId = $this->takeOneTemplateId();

            $this->graylogSendCustomEmail('Start Send Custom Email', $templateId);

            if (is_null($templateId)) {
                $this->graylogSendCustomEmail('No Template Id Found', $templateId);
                return false;
            }

            $data = $this->getCustomEmailLogs($templateId);

            if (empty($data)) {
                $this->graylogSendCustomEmail('No Template Id Found', $templateId);
                return false;
            }

            [$cancelData, $sentData] = $this->filterData($data);
            $this->updateStatus(array_column($cancelData, 'id'), 3);

            if (empty($sentData)) {
                $this->graylogSendCustomEmail('No Sent Data Found', $templateId);
                return false;
            }
            $template = $this->translateContentTemplate($templateId);
            $dataWithConfig = $this->attachTemplate($sentData, $template);
            if (empty($dataWithConfig)) {
                $this->graylogSendCustomEmail('No Data With Config Found', $templateId);
                return false;
            }

            $this->updateStatus(array_column($sentData, 'id'));
            // -------------------------------
            // Send Email
            // -------------------------------
            $this->sendEmail($dataWithConfig);

            // -------------------------------
            // Update Custom Email Template Status
            // -------------------------------
            $this->updateCustomEmailtemplate($templateId);

            return true;
        }catch (\Throwable $e) {
            graylogError("Send Custom Email Job: \n\r {$e}", [
                'category' => 'send_custom_email',
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    private function takeOneTemplateId()
    {
        // -------------------------------
        // Take only 1 template id each time
        // -------------------------------

        $templateIds = CustomEmailLog::query()
            ->select('template_id')
            ->whereHas('emailCustomTemplate', function ($query) {
                $query->where('status', CustomEmailTemplateEnum::SAVED);
            })
            ->getScheduled()
            ->groupBy('template_id')
            ->pluck('template_id')
            ->toArray();

        return empty($templateIds) ? null : $templateIds[0];
    }

    public function updateCustomEmailTemplate($templateId): void
    {
        $isExist = $this->isTemplateScheduled($templateId);

        $template = CustomEmailTemplate::query()
            ->select('id','default_template_id')
            ->where('id', $templateId)
            ->first();

        // -------------------------------
        // Update end_sent_email_at for Current Template
        // -------------------------------
        if (!$isExist) {
            $template->update([
                'end_sent_email_at' => now(),
                'updated_at' => now()
            ]);
        }

        $builderDefaultTemplate = CustomEmailTemplate::query()->where('id', $template->default_template_id);

        $status = $isExist
            ? CustomEmailTemplateEnum::PROCESSING
            : CustomEmailTemplateEnum::IDLE;

        $data = [
            'status' => $status,
            'updated_at' => now()
        ];

        // -------------------------------
        // Update status for Default Template
        // -------------------------------
        $builderDefaultTemplate->update($data);
    }

    private function sendEmail ($dataWithConfig): void
    {
        foreach ($dataWithConfig as $item) {
            sendEmail($item['config']);
        }
    }

    public function attachTemplate($sentData, $template) :array
    {
        $defaultVariables = [
            'first_name' => '',
            'last_name' => '',
            'support_email' => '',
            'store_name' => ''
        ];

        if (!empty($template)) {
            return array_map(function ($item) use ($template, $defaultVariables) {
                $orderInfo = Arr::get($item, 'order', []);

                if (!empty($orderInfo)) {
                    $defaultVariables['first_name'] = $item['order']['first_name'] ?? '';
                    $defaultVariables['last_name'] = $item['order']['last_name'] ?? '';
                    $defaultVariables['support_email'] = $item['order']['seller']['email'] ?? '<EMAIL>';
                    $defaultVariables['store_name'] = $item['order']['store_name'] ?? '';
                    $store_id = Order::query()->find($orderInfo['id'])->store_id;
                    $template['store_info'] = StoreService::getStoreInfo($store_id);
                }

                $template['content'] = CustomEmailTemplateService::translateDefaultVariables($defaultVariables, $template['content']);
                $template['subject'] = CustomEmailTemplateService::translateDefaultVariables($defaultVariables, $template['subject']);
                $template['greeting'] = CustomEmailTemplateService::translateDefaultVariables($defaultVariables, $template['greeting']);

                $item['config'] = $this->prepareConfigToSendMail($item['user_id'], $item['email'], $template);
                return $item;
            }, $sentData);
        }

        return [];
    }

    public function prepareConfigToSendMail($userId, $userEmail, $templateObj): array
    {
        return CustomEmailTemplateService::prepareConfigToSendMail($userId, $userEmail, $templateObj);
    }

    private function uniqueTemplateIds(array $dataFiltered): array
    {
        return array_unique(array_column($dataFiltered, 'template_id'));
    }

    private function translateContentTemplate(string $templateId) : array
    {
        return CustomEmailTemplateService::translateContentTemplate($templateId);
    }

    private function getCustomEmailLogs($templateId = null) :array
    {
        $data = CustomEmailLog::query()
            ->getScheduled()
            ->where('template_id', $templateId)
            ->with(['order:id,customer_name,store_name,seller_id',
                'order.seller:id,email'])
            ->limit(500)
            ->get()
            ->toArray();

        return array_map(function ($item) {
            if (empty($item['order'])) {
                return $item;
            }

            $customerName = Arr::get($item, 'order.customer_name', '');

            $nameParts = explode(' ', $customerName);

            if (count($nameParts) == 1) {
                $firstName = $nameParts[0];
                $lastName = '';
            } else {
                $firstName = array_shift($nameParts);
                $lastName = implode(' ', $nameParts);
            }

            unset($item['order']['customer_name']);
            $item['order']['first_name'] = $firstName;
            $item['order']['last_name'] = $lastName;

            return $item;
        }, $data);
    }

    private function isTemplateScheduled($templateId): bool
    {
        return CustomEmailLog::query()
            ->where('template_id', $templateId)
            ->getScheduled()
            ->exists();
    }

    private function filterData(array $data): array
    {
        $cancelData = array_filter($data, function ($item) {
            return $item['user_id'] === null || $item['email'] === null || $item['email'] === '';
        });

        $cancelDataIds = array_column($cancelData, 'id');

        $sentData = array_filter($data, function ($item) use ($cancelDataIds) {
            return !in_array($item['id'], $cancelDataIds);
        });

        return [$cancelData, $sentData];
    }

    private function updateStatus(array $dataIds, int $type = 1) : bool
    {
        if (empty($dataIds)) {
            return false;
        }

        $builder = CustomEmailLog::query()->whereIn('id', $dataIds);

        return match ($type) {
            1 => $this->markAsSent($builder),
            3 => $this->markAsCancelled($builder),
            default => false,
        };
    }

    private function markAsSent($builder) : bool
    {
        return $builder->update([
                'status' => CustomEmailLogEnum::SENT,
                'sent_at' => now(),
                'updated_at' => now(),
            ]) > 0;
    }

    private function markAsCancelled($builder) : bool
    {
        return $builder->update([
                'status' => CustomEmailLogEnum::CANCELLED,
                'updated_at' => now(),
            ]) > 0;
    }

    private function graylogSendCustomEmail($message, $templateId)
    {
        graylogInfo($message, [
            'category' => 'send_custom_email',
            'template_id' => $templateId,
        ]);
    }
}
