<?php

namespace App\Console\Commands;

use App\Services\SeventeenTrack;
use Illuminate\Console\Command;

class ScanChangeRegisterTrackingCodeWithCarrierCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:re-register-tracking-code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan re-register tracking code with custom shipping carriers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        SeventeenTrack::scanRegisterTrackingCodeWithDiffCarrier();
    }
}
