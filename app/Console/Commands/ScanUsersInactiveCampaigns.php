<?php

namespace App\Console\Commands;

use App\Enums\InactiveEnum;
use App\Traits\InactiveTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ScanUsersInactiveCampaigns extends Command
{
    use InactiveTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:users-inactive-campaigns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'scan users who have inactive campaigns and send warning mail to them';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->type = InactiveEnum::CAMPAIGN;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        Artisan::call('scan:users-inactive --type=' . $this->type);
        return 1;
    }
}
