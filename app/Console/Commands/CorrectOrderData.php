<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\DiscordChannel;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Jobs\ProcessOrderCompleted;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CorrectOrderData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:correct-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Correct order data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $orders = Order::query()
            ->select('id', 'type')
            ->where([
                'payment_status' => OrderPaymentStatus::PAID,
                'total_seller_profit' => 0
            ])
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->where('paid_at', '<', now()->subMinutes(5))
            ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
            ->limit(100)
            ->get();

        if ($orders->count() > 0) {
            foreach ($orders as $order) {
                if ($order->isCustomServiceOrder()) {
                    continue;
                }
                ProcessOrderCompleted::dispatch($order->id)->onQueue('order');
            }
        }

        $orders = Order::query()
            ->select('id', 'type')
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->where('paid_at', '<=', now()->subMinutes(5))
            ->where('paid_at', '>=', now()->subMinutes(15))
            ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
            ->whereDoesntHave('products')
            ->get();
        $cache = Cache::store('database');
        if ($orders->count() > 0) {
            foreach ($orders as $order) {
                $cacheKey = md5('order_invalid_has_no_products_' . $order->id);
                if ($order->isCustomServiceOrder() || $order->isServiceOrder() || $cache->has($cacheKey)) {
                    continue;
                }
                $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
                logToDiscord("https://admin.senprints.com/order/detail/" . $order->id . "\r\n[URGENT] Order invalid #$order->id . Order has no products.", DiscordChannel::ADMIN_WARNING);
            }
        }
    }
}
