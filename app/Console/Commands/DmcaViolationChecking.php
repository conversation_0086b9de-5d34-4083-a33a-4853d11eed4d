<?php
namespace App\Console\Commands;
use App\Services\CampaignService;
use Illuminate\Console\Command;

class DmcaViolationChecking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dmca_violation:check';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'DMCA Violation Checking';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        CampaignService::takedownNotifiedTmCampaign();
        return 0;
    }
}
