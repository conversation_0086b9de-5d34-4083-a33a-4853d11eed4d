<?php

namespace App\Console\Commands;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\AbandonedLogTypeEnum;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Models\AbandonedLog;
use App\Models\NotificationSetting;
use App\Models\Order;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use InvalidArgumentException;

class AbandonedCartFind extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'abandoned-cart:find';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find abandoned carts.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Exception
     */
    public function handle()
    {
        $currentTime = now('utc');
        $thirtyDaysAgo = now('utc')->subDays(30);
        // get all valid cart jobs
        $abandonedLogs = AbandonedLog::query()
            ->where('schedule_time', '<=', $currentTime)
            ->where('updated_at', '>=', $thirtyDaysAgo)
            ->where('status', AbandonedLogStatusEnum::SCHEDULED)
            ->where(function ($query) {
                $query->where('notification_key', '');
                $query->orWhereNull('notification_key');
            })
            ->get();

        if ($abandonedLogs->isNotEmpty()) {
            foreach ($abandonedLogs as $abandonedLog) {
                $setting = self::getFirstSetting($abandonedLog);
                if ($setting === null) {
                    $abandonedLog->delete();
                    continue;
                }
                $createdAt = new Carbon($abandonedLog->created_at);
                $scheduleTime = $createdAt->addSeconds($setting->send_after);
                $order = $abandonedLog->order;
                if ($abandonedLog->type == AbandonedLogTypeEnum::EMAIL) {
                    $to = $order->customer_email;
                } else {
                    $to = $order->customer_phone;
                }
                if (empty($to)) {
                    $abandonedLog->delete();
                    continue;
                }
                $abandonedLog->replicate()->fill([
                    'to' => $to,
                    'notification_key' => $setting->type,
                    'status' => AbandonedLogStatusEnum::PENDING,
                    'schedule_time' => $scheduleTime,
                    'subject' => $setting->subject,
                    'body' => $setting->content,
                    'cart_key' => generateUUID(),
                ])->save();
                $abandonedLog->delete();
            }
        }

        return 0;
    }

    /**
     * @param AbandonedLog $log
     * @return NotificationSetting|null
     */
    public static function getFirstSetting(AbandonedLog $log)
    {
        $type = $log->type === AbandonedLogTypeEnum::EMAIL ? 'email' : 'sms';
        $settings = $log->notifications->groupBy('channel');

        if ($settings->has($type)) {
            $setting = $settings[$type]->sortby('id')->first();
            if (!empty($setting)) {
                return $setting;
            }
        }

        return null;
    }

    /**
     * @param Collection $orderIds
     * @return Collection|null
     * @throws InvalidArgumentException
     */
    public static function abandonedOrderIds(Collection $orderIds)
    {
        if ($orderIds->count() === 0) {
            return null;
        }

        $orderIds = $orderIds->toArray();
        $abandonedOrderIds = Order::query()
            ->whereIn('id', $orderIds)
            ->where([
                'status' => OrderStatus::PENDING,
                'type' => OrderTypeEnum::REGULAR
            ])
            ->get();

        if ($abandonedOrderIds->isEmpty()) {
            return null;
        }

        return $abandonedOrderIds;
    }
}
