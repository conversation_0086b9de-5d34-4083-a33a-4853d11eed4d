<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductReviewRequestStatusEnum;
use App\Jobs\SendReviewRequestMail;
use App\Models\Order;
use Illuminate\Console\Command;

class CreateReviewRequestJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product-review:create-review-request-job';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create review request job';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orders = Order::query()
            ->where([
                'status' => OrderStatus::COMPLETED,
                'review_request_status' => ProductReviewRequestStatusEnum::PENDING
            ])->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->whereNotNull('received_at')
            ->whereNotNull('customer_email')
            ->whereDoesntHave('productReviews')
            ->limit(10)
            ->get('id');

        if ($orders->isNotEmpty()) {
            Order::query()
                ->whereIn('id', $orders->pluck('id')->toArray())
                ->update(['review_request_status' => ProductReviewRequestStatusEnum::PROCESSING]);

            foreach ($orders as $order) {
                SendReviewRequestMail::dispatch($order->id);
            }
        }

        return 0;
    }
}
