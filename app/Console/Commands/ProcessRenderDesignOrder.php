<?php

namespace App\Console\Commands;

use App\Enums\DesignTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductPrintType;
use App\Jobs\RenderPrintJob;
use App\Models\Design;
use App\Models\File;
use App\Models\Order;
use App\Services\FulfillmentService;
use Illuminate\Console\Command;

class ProcessRenderDesignOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:render-design';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process render design for orders';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \Exception
     */
    public function handle(): int
    {
        $orders = Order::query()
            ->with('order_products')
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->where('paid_at', '>=', now()->subMinutes(10))
            ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
            ->orderBy('order.created_at')
            ->limit(500)
            ->get();
        if ($orders->isEmpty()) {
            return 0;
        }
        foreach ($orders as $order) {
            if ($order->order_products->isEmpty() || $order->isCustomServiceOrder()) {
                continue;
            }
            $logs = [];
            foreach ($order->order_products as $orderProduct) {
                if ($orderProduct->full_printed === ProductPrintType::PRINT_3D_FULL || $orderProduct->full_printed === ProductPrintType::AOP) {
                    continue;
                }
                // get custom color if product is full_printed
                $color = null;
                $size = null;
                if ($orderProduct->isFullPrintedType()) {
                    $options = json_decode($orderProduct->options, true, 512, JSON_THROW_ON_ERROR);
                    if (isset($options['color'])) {
                        $color = correctOptionValue($options['color']);
                    }
                    if (isset($options['size'])) {
                        $size = correctOptionValue($options['size']);
                    }
                }
                $seller = $orderProduct->seller;
                $files = File::query()
                    ->onSellerConnection($seller)
                    ->where([
                    'product_id' => $orderProduct->product_id,
                    'type' => FileTypeEnum::DESIGN,
                    'option' => FileRenderType::PRINT
                ])
                    ->when(!empty($size), function ($q) use ($size) {
                        // check if $size includes column print_space
                        return $q->where(function ($q) use ($size) {
                            return $q->whereRaw("INSTR('$size', `print_space`)")
                                ->orWhere('print_space', 'default');
                        });
                    })
                    ->whereNull('file_url_2')
                    ->whereNotNull('design_json')
                    ->get();
                if ($files->isNotEmpty()) {
                    $files->map(function (File $file) use ($order, $orderProduct, $color, &$logs) {
                        if (!$orderProduct->personalized && $color && $color !== 'white' && $orderProduct->isFullPrintedType()) {
                            $design = Design::query()->where([
                                'order_id' => $order->id,
                                'product_id' => $orderProduct->product_id,
                                'order_product_id' => $orderProduct->id,
                                'type' => DesignTypeEnum::PRINT,
                                'print_space' => $file->print_space,
                            ])
                                ->whereNull('file_url')
                                ->first();
                            if ($design) {
                                [$design->design_json,] = FulfillmentService::updateUrlOnDesignJson($design->design_json);
                                RenderPrintJob::dispatch($design, 'design')->onQueue('render');
                                $logs[] = 'Order Product ID: ' . $orderProduct->id . PHP_EOL . 'Print Space: ' . $file->print_space . PHP_EOL . 'Campaign ID: ' . $orderProduct->campaign_id . PHP_EOL . 'Design ID: ' . $design->id . PHP_EOL . 'Product Name: ' . $orderProduct->product_name;
                            }
                        } else {
                            [$file->design_json,] = FulfillmentService::updateUrlOnDesignJson($file->design_json);
                            $file->order_id = $order->id;
                            $file->order_product_id = $orderProduct->id;
                            RenderPrintJob::dispatch($file, 'file')->onQueue('render');
                            $logs[] = 'Order Product ID: ' . $orderProduct->id . PHP_EOL . 'Print Space: ' . $file->print_space . PHP_EOL . 'Campaign ID: ' . $orderProduct->campaign_id . PHP_EOL . 'File ID: ' . $file->id . PHP_EOL . 'Product Name: ' . $orderProduct->product_name;
                        }
                    });
                }
                $designs = Design::query()->where([
                    'type' => DesignTypeEnum::PRINT,
                    'order_product_id' => $orderProduct->id
                ])
                    ->whereNull('file_url')
                    ->whereNotNull('design_json')
                    ->get();
                if ($designs->isNotEmpty()) {
                    $designs->map(function (Design $design) use ($orderProduct, &$logs) {
                        [$designJson, $isChanged] = FulfillmentService::updateUrlOnDesignJson($design->design_json, false);
                        $objects = data_get($designJson, 'objects', []);
                        if (!empty($objects)) {
                            if ($isChanged) {
                                $design->design_json = json_encode($designJson, JSON_THROW_ON_ERROR);
                                $design->save();
                            }
                            RenderPrintJob::dispatch($design, 'design')->onQueue('render');
                            $logs[] = 'Order Product ID: ' . $design->order_product_id . PHP_EOL . 'Print Space: ' . $design->print_space . PHP_EOL . 'Campaign ID: ' . $orderProduct->campaign_id . PHP_EOL . 'Design ID: ' . $design->id . PHP_EOL . 'Product Name: ' . $orderProduct->product_name;
                        }
                    });
                }
            }
            if (!empty($logs)) {
                $this->log('Re-render design for Order ID: ' . $order->id, $logs);
            }
        }
        return 1;
    }

    /**
     * @param string $message
     * @param array $logs
     * @return void
     */
    private function log(string $message, array $logs): void
    {
        $embeds = [
            [
                'description' => count($logs) > 1 ? implode(PHP_EOL . str_repeat('-', 40) . PHP_EOL, $logs) : $logs[0],
                'color' => 5763719
            ]
        ];
        logToDiscord($message, 'render_design', false, true, 7, $embeds);
    }
}
