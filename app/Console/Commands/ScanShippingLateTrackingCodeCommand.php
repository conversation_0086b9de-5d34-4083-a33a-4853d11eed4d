<?php

namespace App\Console\Commands;

use App\Facades\ProcessLock;
use App\Services\OrderService;
use Illuminate\Console\Command;

class ScanShippingLateTrackingCodeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:shipping-late:register-tracking-code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan register tracking code for shipping late orders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ProcessLock::handle($this->signature, callback: function () {
            OrderService::scanRegisterTrackingCode();
        });
        return self::SUCCESS;
    }
}
