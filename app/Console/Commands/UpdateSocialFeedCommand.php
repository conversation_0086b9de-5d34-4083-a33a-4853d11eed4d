<?php

namespace App\Console\Commands;

use App\Jobs\UpdateSocialFeedJob;
use Illuminate\Console\Command;

class UpdateSocialFeedCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-social-feed-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            UpdateSocialFeedJob::dispatch();
        } catch (\Throwable $exception) {
            logException($exception, 'UpdateSocialFeedCommand@handle');
        }
    }
}
