<?php

namespace App\Console\Commands;

use App\Enums\InactiveEnum;
use App\Enums\StoreDomainStatusEnum;
use App\Enums\UserStatusEnum;
use App\Library\DomainManagement\DomainClient;
use App\Models\SellerDomain;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Services\CloudFlareCustomHostname;
use App\Services\InactiveService;
use App\Traits\InactiveTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Exception;

class ScanExpiringDomains extends Command
{
    use InactiveTrait;

    protected array $manualDomains = [];
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:expiring-domains';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan expiring domains and send warning mail to them';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->processId = InactiveService::generateProcessId();
        $this->token = InactiveService::generateToken();
        $this->fileType = 'Command';
        $this->fileName = $this->fileType . ':' . basename(__FILE__);
        $this->limit = isEnvLocalOrDev() ? 10 : 100;
        $this->count = isEnvLocalOrDev() ? 250 : 1000;
        $this->type = InactiveEnum::EXPIRING_DOMAINS;
        $this->category = ['category' => $this->type];
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        try {
            $this->scanToDeleteInactiveDomain();
            $this->sendMailAlert();
            return 1;
        } catch (Exception $exception) {
            InactiveService::logExceptionToGraylog($this->processId, $this->fileName, $this->type, $exception);
            return 0;
        }
    }

    // *** Domains that are not pointed to our server are considered inactive
    private function scanToDeleteInactiveDomain(): void
    {
//      *** SCAN
        $lastSellerId = Cache::get('last_store_domain_seller_id', 0);
        $storeDomains = $this->getStoreDomains($lastSellerId);

        if ($storeDomains->isEmpty()) {
            Cache::put('last_store_domain_seller_id', 0);
            $storeDomains = $this->getStoreDomains($lastSellerId);
        }

        $lastSellerId = $storeDomains->last()->seller_id ?? 0;
        Cache::put('last_store_domain_seller_id', $lastSellerId);
        [$checkingNull, $verifiedDomains] = $this->checkingInactiveDomain($storeDomains);

//      ***  LOG
        $this->logToGrayLogInactiveDomain($checkingNull, $verifiedDomains);
        $this->logWarningToDiscordInactiveDomain($checkingNull, $verifiedDomains);


//      *** DELETE
//      $this->deleteInactiveDomain($checkingNull);
    }

    private function checkingInactiveDomain($storeDomains): array
    {
        $checkingNull = [];
        $verifiedDomains = [];

        foreach ($storeDomains as $storeDomain) {
            try {
                // *** CHECKING
                $response = verifyDnsDomain($storeDomain->domain);

                // *** TAKE TO DELETE
                if (is_null($response)) {
                    $checkingNull[] = [
                        "id" => $storeDomain->id,
                        "domain" => $storeDomain->domain,
                        "is_default" => $storeDomain->is_default,
                        "store_id" => $storeDomain->store_id,
                        "status" => $storeDomain->status,
                        "cloudflare_custom_hostname_id" => $storeDomain->cloudflare_custom_hostname_id
                    ];
                } else {
                    // *** SKIP
                    $verifiedDomains[] = $storeDomain->domain;
                }
            } catch (Exception $e) {
                // *** LOG & SKIP
                $context = $this->category;
                $context = array_merge($context, [
                    'store_domain_id' => $storeDomain->id,
                    'domain' => $storeDomain->domain,
                    'exception' => $e->getMessage()
                ]);
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Scan Domains Exception:', $context, true);
            }
        }

        return [
            $checkingNull,
            $verifiedDomains
        ];
    }

    public function manualDeleteInactiveDomain(array $domains): void
    {
        $this->manualDomains = $domains;

        // *** GET
        $domains = array_unique($domains);
        $storeDomains = $this->getManualStoreDomains($domains);

        if (!empty($storeDomains)) {
            [$checkingNull, $verifiedDomains] = $this->checkingInactiveDomain($storeDomains);
            $checkingNull = array_filter($checkingNull);
            if (!empty($checkingNull)) {
                $this->deleteInactiveDomain($checkingNull, true);
            } else {
                $this->logEmptyDomains();
            }
        } else {
            $this->logEmptyDomains();
        }
    }

    private function deleteInactiveDomain(array $checkingNull, bool $manual = false): void
    {
        InactiveService::logToGraylog($this->processId, $this->fileName, '[Start Manual] Delete inactive domains', $this->category);

        if ($manual) {
            $user = currentUser();
            if (is_null($user->getInfo()) || !$user->isAdmin()) {
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Unauthorized user tried to delete inactive domains', $this->category);
                return;
            }

            $this->logDeletedInfoToDiscord($checkingNull, true);
        } else {
            $this->logDeletedInfoToDiscord($checkingNull);
        }

        if (!empty($checkingNull)) {
            // 1. Delete cloudflare_custom_hostname_id
            try {
                $cloudflareCustomHostnameIds = array_column($checkingNull, 'cloudflare_custom_hostname_id');
                if (!empty($cloudflareCustomHostnameIds)) {
                    foreach ($cloudflareCustomHostnameIds as $id) {
                        if (!is_null($id)) {
                            (new CloudFlareCustomHostname())->deleteCustomHostname($id);
                        }
                    }
                }
            } catch (Exception $e) {
                InactiveService::logExceptionToGraylog($this->processId, $this->fileName, $this->type, $e);
            }

            // 2. Delete records (include domain which is store default domain (1) and domain which not(2))
            // process step take by: StoreDomainController func destroy

            // A.
            foreach ($checkingNull as $item) {
                if ($item['status'] == StoreDomainStatusEnum::ACTIVATED) {
                    DomainClient::instance()->destroy($item['domain']);
                    if (isset($item['store_id'])) {
                        clearStoreCache($item['store_id']);
                    }
                }
            }

            // B.
            $deletedStoreDomainIds = array_column($checkingNull, 'id');
            $updateStore = false;
            try {
                DB::beginTransaction();
                StoreDomain::query()
                    ->whereIn('id', $deletedStoreDomainIds)
                    ->delete();
                DB::commit();
                $updateStore = true;
            } catch (Exception $e) {
                InactiveService::logExceptionToGraylog($this->processId, $this->fileName, $this->type, $e);
                DB::rollBack();
            }

            // C.
            if ($updateStore) {
                $domainDefault = array_filter($checkingNull, function ($item) {
                    return $item['is_default'] == 1;
                });
                $storeIds = array_column($domainDefault, 'store_id');
                Store::query()->whereIn('id', $storeIds)->update(['domain_status' => null, 'domain_expired_at' => null, 'domain' => null]);
            }

            $context = $this->category;
            $context["deletedStoreDomainIds"] = $deletedStoreDomainIds;
            $context["cloudflareCustomHostnameIds"] = $cloudflareCustomHostnameIds;
            InactiveService::logToGraylog($this->processId, $this->fileName, 'Deleted inactive domains', $context, true);
        }
    }

    private function sendMailAlert(): void
    {
        $lastSellerDomainId = Cache::get('last_seller_domain_id', 0);
        $sellerDomains = $this->getSellerDomains($lastSellerDomainId);

        if ($sellerDomains->isEmpty()) {
            Cache::put('last_seller_domain_id', 0);
            $sellerDomains = $this->getSellerDomains($lastSellerDomainId);
        }

        $lastSellerDomainId = $sellerDomains->last()->id ?? 0;
        Cache::put('last_seller_domain_id', $lastSellerDomainId);

        $arrMailed = [];
        foreach ($sellerDomains as $sellerDomain) {
            if (is_null($sellerDomain->seller)) {
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Seller domain id: ' . $sellerDomain->id . ' has no seller', $this->category, true);
                continue;
            }

            $shouldCheck = InactiveService::checkSentAtTime($sellerDomain->notification_at);

            if (!$shouldCheck) {
                continue;
            }

            $shouldCheck = $sellerDomain->seller->balance < $sellerDomain->renewal_price || !$sellerDomain->auto_renew;

            if ($shouldCheck && !is_null($sellerDomain->domain_expired_at)) {
                $domainExpireAt = Carbon::parse($sellerDomain->domain_expired_at);
                $diffInDays = Carbon::now()->diffInDays($domainExpireAt, false);
                $sendMail = false;

                // Check for specific days or if the days are past due
                if (in_array($diffInDays, [30, 3, 1]) || $diffInDays <= 0) {
                    $config = $this->configBeforeSendMail($sellerDomain->seller->id, $sellerDomain->seller->email);

                    // unique hash for each email
                    $config['hash'] = gen_unique_hash();
                    $sendMail = sendEmail($config);
                }

                // Update notification date if an email was sent
                if ($sendMail) {
                    $sellerDomain->notification_at = Carbon::now();
                    $sellerDomain->save();
                    $arrMailed[] = $sellerDomain->id;
                }
            }
        }

        if (!empty($arrMailed)) {
            InactiveService::logToGraylog($this->processId, $this->fileName, 'Seller domains mailed: ' . implode(',', $arrMailed), $this->category, true);
        } else {
            InactiveService::logToGraylog($this->processId, $this->fileName, 'No seller domains mailed', $this->category, true);
        }
    }

    private function getSellerDomains($lastSellerDomainId)
    {
        return SellerDomain::query()
            ->where('id', '>', $lastSellerDomainId)
            ->with(['seller' => function ($query) {
                $query->select('id', 'email', 'balance')
                    ->where('status', '!=', UserStatusEnum::HARD_BLOCKED);
            }])
            ->orderBy('id')
            ->limit($this->limit)
            ->get();
    }

    private function getManualStoreDomains($domains)
    {
        return StoreDomain::query()
            ->with(['store' => function ($query) {
                $query->select('id', 'domain', 'domain_status', 'domain_expired_at');
            }])
            ->whereIn('domain', $domains)
            ->get();
    }

    private function getStoreDomains($lastSellerId)
    {
        return StoreDomain::query()
            ->where('seller_id', '>', $lastSellerId)
            ->where('status', '!=', StoreDomainStatusEnum::PENDING)
            ->with(['store' => function ($query) {
                $query->select('id', 'domain', 'domain_status', 'domain_expired_at');
            }])
            ->orderBy('seller_id')
            ->limit($this->limit)
            ->get();
    }

    private function configBeforeSendMail($userId, $userEmail): array
    {
        $baseUrl = isEnvLocalOrDev() ? 'https://seller-v2.dev.senprints.net/' : 'https://seller.senprints.com/';
        $dataSendMailLog = [
            'sellerId' => $userId,
        ];
        $payouts = $baseUrl . 'payouts';
        return [
            'to' => $userEmail,
            'template' => 'seller.expire_domain',
            'data' => [
                'base_url' => $baseUrl,
                'subject' => 'Urgent: Domain Renewal Reminder ',
                'payouts' => $payouts,
            ],
            'sendMailLog' => $dataSendMailLog
        ];
    }

    private function logToGrayLogInactiveDomain(array $checkingNull, array $verifiedDomains): void
    {
        $checkingNullDomains = array_column($checkingNull, 'domain');
        $context = $this->category;
        $context = array_merge($context, [
            'verifiedDomains' => $verifiedDomains,
            'checkingNullDomains' => $checkingNullDomains
        ]);
        InactiveService::logToGraylog($this->processId, $this->fileName, 'Scan Inactive Domains', $context, true);
    }

    private function logWarningToDiscordInactiveDomain(array $checkingNull, array $verifiedDomains): void
    {
        $br = PHP_EOL;
        $message = ':warning: **Scan inactive-domains: The following domains are not pointed to our server:** ' . $br;
        $message .= 'Graylog process id: ' . $this->processId . $br;
        $message .= 'Graylog category: ' . $this->type . $br;
        $message .= 'Total Domains inactive: ' . count($checkingNull) . $br;
        $message .= 'Total Domains verified: ' . count($verifiedDomains) . $br;

        if (count($checkingNull) > 0) {
            $message .= '**List of inactive domains:** ' . $br;
            foreach ($checkingNull as $item) {
                $message .= '> "' . $item['domain'] . '",' . $br;
            }
        }

        if (count($verifiedDomains) > 0) {
            $message .= '**List of verified domains:** ' . $br;
            foreach ($verifiedDomains as $item) {
                $message .= '> "' . $item . '",' . $br;
            }
        }

        logToDiscord($message, 'domain_manager');
    }

    private function logDeletedInfoToDiscord(array $checkingNull, bool $manual = false): void
    {
        $br = PHP_EOL;

        if ($manual) {
            $message = ':sos: **Begin Manual delete: Inactive domains:** ' . $br;
            $message .= 'Admin: ' . currentUser()->getInfo()->name . $br;
            $message .= 'Email: ' . currentUser()->getInfo()->email . $br;
            $message .= 'Domains: ' . implode(', ', $this->manualDomains) . $br;
        } else {
            $message = ':sos: **Deleted: Inactive domains:** ' . $br;
        }

        $message .= 'Graylog process id: ' . $this->processId . $br;
        $message .= 'Graylog category: ' . $this->type . $br;

        if (!empty($checkingNull)) {
            $message .= 'Total: ' . count($checkingNull) . $br;
            $message .= '(Check on graylog for result)' . $br;

            foreach ($checkingNull as $item) {
                $message .= '> ' . $item['domain'] . ',' . $br;
            }
        } else {
            $message .= 'No domains to delete' . $br;
        }

        logToDiscord($message, 'domain_manager');
    }

    private function logEmptyDomains(): void
    {
        InactiveService::logToGraylog($this->processId, $this->fileName, 'Empty domains to delete', $this->category);
        $this->logDeletedInfoToDiscord([], true);
    }
}
