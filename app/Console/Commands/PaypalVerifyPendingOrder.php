<?php

namespace App\Console\Commands;

use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\PaymentMethodEnum;
use App\Http\Controllers\Storefront\PaypalController;
use App\Models\Order;
use App\Models\PgWebhookLogs;
use Illuminate\Console\Command;

class PaypalVerifyPendingOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paypal-verify-pending-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify pending order with payment method is Paypal';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $orders = Order::query()
            ->where('payment_method', PaymentMethodEnum::PAYPAL)
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->whereIn('payment_status', [
                OrderPaymentStatus::UNPAID,
                OrderPaymentStatus::PENDING,
                OrderPaymentStatus::FAILED,
            ])
            ->whereNotNull('transaction_id')
            ->where('created_at', '>=', now()->subWeek())
            ->where('updated_at', '>=', now()->subHour())
            ->get();

        if ($orders->isEmpty()) {
            return self::SUCCESS;
        }

        foreach ($orders as $order) {
            $transactionReference = $order->transaction_id;
            if (!$transactionReference) {
                continue;
            }
            if (str_starts_with($transactionReference, 'pi_')) {
                $payment_gateway_id = PgWebhookLogs::query()->where('gateway', PaymentMethodEnum::STRIPE)->whereNotNull('order_id')->where('order_id', $order->id)->orderByDesc('created_at')->value('payment_gateway_id');
                $data = [
                    'payment_method' => PaymentMethodEnum::STRIPE,
                ];
                if (!empty($payment_gateway_id) && (int) $payment_gateway_id !== (int) $order->payment_gateway_id) {
                    $data['payment_gateway_id'] = $payment_gateway_id;
                }
                $order->update($data);
                continue;
            }
            $webhookResult = PgWebhookLogs::query()
                ->where('gateway', PaymentMethodEnum::PAYPAL)
                ->whereNotNull('order_id')
                ->where('order_id', $order->id)
                ->where('event_type', 'PAYMENT.CAPTURE.COMPLETED')
                ->orderByDesc('created_at')
                ->first();
            if ($webhookResult) {
                $payment = json_decode($webhookResult->payload, false);
                $status = $payment?->resource?->status;
                $transaction_id = $payment?->resource?->id;
                if ($status === 'COMPLETED' && (float)$payment?->resource?->amount?->value === (float)$order->total_amount) {
                    $order->paymentCompleted($order->total_amount, $transaction_id, false, null, null, $order->payment_gateway_id);
                }
                continue;
            }
            $result = (new PaypalController())->verifyOrderPurchase($order);
            if ($result['success']) {
                $this->info('Order #' . $order->id . ' - ' . $result['message']);
            } else {
                $this->error('Order #' . $order->id . ' - ' . $result['message']);
            }
        }

        return self::SUCCESS;
    }
}
