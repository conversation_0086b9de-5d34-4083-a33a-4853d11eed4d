<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\MoveUserToCustomer;
class MigrateUserToCustomer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-user-to-customer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move users to customers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        MoveUserToCustomer::dispatch()->onQueue('default');
    }
}
