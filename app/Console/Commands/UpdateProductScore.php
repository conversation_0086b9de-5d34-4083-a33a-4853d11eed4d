<?php

namespace App\Console\Commands;

use App\Models\OrderProduct;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateProductScore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:update-score';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update product score';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $startTime = round(microtime(true) * 1000);
        $campaignIds = OrderProduct::whereNotNull('campaign_id')
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->where('order.paid_at', '>=', now()->subHour())
            ->groupBy('order_product.campaign_id')
            ->pluck('order_product.campaign_id');

        $totalUpdated = $campaignIds->count();
        foreach ($campaignIds as $campaignId) {
            $totalUpdated += $this->updateProductScore($campaignId);
        }

        $log = false;
        if ($log && $campaignIds->count() > 0) {
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            logToDiscord('Update product score: ' . $totalUpdated . ' / ' . $time . 'ms');
        }
    }

    private function updateProductScore($campaignId)
    {
        $stats = OrderProduct::where('campaign_id', $campaignId)
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->where('order.paid_at', '>=', now()->subDays(30))
            ->select('order_product.product_id')
            ->addSelect(DB::raw('sum(order_product.quantity) as quantity'))
            ->groupBy('order_product.product_id')
            ->get();

        $totalSales = 0;
        foreach ($stats as $stat) {
            $totalSales += $stat->quantity;
        }
        $arrayScore = getScoreProductBySales($totalSales * 0.1);
        Product::where('id', $campaignId)->orWhere('campaign_id', $campaignId)
            ->update(
                [
                    'sales_score' => $totalSales * 0.1,
                    'time_score' => $arrayScore['timestamp'],
                    'score' => $arrayScore['score'],
                    'sync_status' => 0
                ]
            );
        foreach ($stats as $stat) {
            $sales = $stat->quantity * 0.9 + $totalSales * 0.1;
            $arrayScore = getScoreProductBySales($sales);
            Product::where('id', $stat->product_id)
                ->update(
                    [
                        'sales_score' => $sales,
                        'time_score' => $arrayScore['timestamp'],
                        'score' => $arrayScore['score'],
                        'sync_status' => 0
                    ]
                );
        }
        return $stats->count();
    }
}
