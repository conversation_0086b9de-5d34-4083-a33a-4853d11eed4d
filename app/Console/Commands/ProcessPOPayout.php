<?php

namespace App\Console\Commands;

use App\Enums\EnvironmentEnum;
use App\Facades\ProcessLock;
use App\Library\Payoneer\Payoneer;
use App\Models\SellerBilling;
use Illuminate\Console\Command;

class ProcessPOPayout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'po-payout:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process payouts for method Payoneer';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessLock::handle($this->signature, callback: function () {
            $payoneer = Payoneer::instance();
            try {
                $sellerBilling = SellerBilling::getPOProcessBilling();
                $transactionCompletedIds = [];
                $transactions = [];
                $cancelClientRefIds = [];
                $transactionInfo = [];
                graylogInfo("ProcessPOPayout: Starting", [
                    'category' => 'process_payout_payoneer',
                    'user_type' => 'system',
                    'action' => 'schedule',
                    'total' => $sellerBilling->count(),
                    'process_ids' => $sellerBilling->pluck('id')->toArray()
                ]);
                if ($sellerBilling->count() > 0) {
                    $sellerBilling->each(function ($bill) use (&$transactions, $payoneer, &$transactionCompletedIds, &$cancelClientRefIds, &$transactionInfo) {
                        $continue = true;
                        if (POMassPayOutProdTesting() && !POMassPayOutProdTestingByIds($bill->seller_id) && app()->environment(EnvironmentEnum::PRODUCTION)) {
                            $continue = false;
                            graylogInfo("ProcessPOPayout: Skip process", [
                                'category' => 'process_payout_payoneer',
                                'user_type' => 'system',
                                'action' => 'schedule',
                                'bill_id' => $bill->id,
                            ]);
                        }
                        if ($continue) {
                            $amount = abs($bill->amount);
                            $additional_info = json_decode($bill->payment_account->additional_info, true);
                            $clientRefId = Payoneer::MASSPAYOUT_CLIENT_REF_ID_PREFIX . $bill->id . '_' . $bill->seller_id;
                            $transactionStatus = $payoneer->getPayoutStatus($clientRefId);
                            $dollarPrefix = '$';
                            graylogInfo("ProcessPOPayout: Running", [
                                'category' => 'process_payout_payoneer',
                                'user_type' => 'system',
                                'action' => 'schedule',
                                'transaction_status' => $transactionStatus,
                                'bill_id' => $bill->id,
                            ]);
                            if (is_null($transactionStatus)) {
                                $transactions[] = [
                                    'client_reference_id' => $clientRefId,
                                    'payee_id' => $additional_info['payoneer_id'],
                                    'description' => "Payout {$bill->id} to Payoneer {$bill->payment_account->account_id} for {$dollarPrefix}{$amount}",
                                    'currency' => 'USD',
                                    'amount' => $amount
                                ];
                            } elseif (
                                isset($transactionStatus['status'])
                                && strtolower($transactionStatus['status']) === 'transferred'
                            ) {
                                $transactionCompletedIds[] = $bill->id;
                                $transactionInfo[$bill->id] = [
                                    'payout_id' => $transactionStatus['payout_id'],
                                ];
                            } elseif (
                                isset($transactionStatus['status'])
                                && strtolower($transactionStatus['status']) === 'pending'
                            ) {
                                $cancelClientRefIds[] = [$bill->id, $bill->seller_id];
                            }
                        }
                    });
                }
                // free memory $sellerBilling
                unset($sellerBilling);
                graylogInfo("ProcessPOPayout: Completed", [
                    'category' => 'process_payout_payoneer',
                    'user_type' => 'system',
                    'action' => 'schedule',
                    'transactionCompletedIds' => json_encode($transactionCompletedIds, JSON_THROW_ON_ERROR),
                    'transactionInfo' => json_encode($transactionInfo, JSON_THROW_ON_ERROR),
                    'transactions' => json_encode($transactions, JSON_THROW_ON_ERROR),
                    'cancelClientRefIds' => json_encode($cancelClientRefIds, JSON_THROW_ON_ERROR),
                ]);
                if (!empty($transactionCompletedIds)) {
                    SellerBilling::updateStatusToCompleted($transactionCompletedIds, $transactionInfo);
                    // free memory $transactionCompletedIds
                    unset($transactionCompletedIds);
                }
                if (!empty($transactions) && !is_null($payoneer)) {
                    $payoneer->massPayout($transactions);
                    // free memory $transactions
                    unset($transactions);
                }
            } catch (\Exception $e) {
                graylogError("ProcessPOPayout: Cron Error! - Message: {$e->getMessage()} - \n\r Trace info: {$e->getTraceAsString()}", [
                    'category' => 'process_payout_payoneer_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'schedule'
                ]);
            }

            return 0;
        });
        return 0;
    }
}
