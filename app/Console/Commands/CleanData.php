<?php

namespace App\Console\Commands;

use App\Jobs\CleanDataJob;
use Illuminate\Console\Command;

class CleanData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system-data:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean Data';

    public function handle(): int
    {
        dispatch(new CleanDataJob());
        return self::SUCCESS;
    }
}
