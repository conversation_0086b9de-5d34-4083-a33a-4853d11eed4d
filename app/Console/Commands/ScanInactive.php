<?php

namespace App\Console\Commands;

use App\Enums\CacheTime;
use App\Enums\InactiveEnum;
use App\Jobs\CheckUserHaveInactiveCampaign;
use App\Jobs\CheckUserHaveInactiveLastLogin;
use App\Models\SystemConfig;
use App\Services\InactiveService;
use App\Traits\InactiveTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ScanInactive extends Command
{
    use InactiveTrait;

    private bool $shouldRunQueue = false;
    private ?string $appointMail = null;
    private bool $shouldSend1StMail = false;
    private array $appointSellerIds = [];
    private string $cache = 'last_user_id';
    private int $lastUserId = 0;


    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:users-inactive {--type=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'scan users who have inactive {type}';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->processId = InactiveService::generateProcessId();
        $this->token = InactiveService::generateToken();
        $this->fileType = 'Command';
        $this->fileName = $this->fileType . ':' . basename(__FILE__);
        $this->limit = isEnvLocalOrDev() ? 10 : 5;
        $this->count = isEnvLocalOrDev() ? 250 : 1000;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        try {
            $this->type = $this->option('type');
            $this->category = ['category' => $this->type];
            $this->cacheKey = $this->type . '_' . $this->cache;

            // Nếu trong system_config : $this->type có status = 1 (bật) thì mới có $systemConfig ? $systemConfig === null
            $systemConfig = InactiveService::getActiveSystemConfig($this->type);
            $this->configBeforeScan($systemConfig);
            // last User Id ưu tiên lấy từ cache, nếu cache null || === 0 thì lấy từ system_config
            $lastUserId = $this->getLastUserScanId();
            $startEsTime = round(microtime(true) * 1000);

            if (!empty($this->appointSellerIds)) {
                $sellers = InactiveService::getLimitAppointSellerIds($this->appointSellerIds, $lastUserId, $this->limit);
                $endEsTime = round(microtime(true) * 1000);
                $esTime = InactiveService::getEstimatedTime($startEsTime, $endEsTime);
                InactiveService::logToGraylog($this->processId, $this->fileName, 'LastUserId: ' . $lastUserId . ' - Total Time to get sellers by [system_config]: ' . $esTime . 'ms', $this->category);
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Scan ' . count($sellers) . ' sellers by [system_config]: ' . $this->type, $this->category);
            } else {
                if ($this->type === InactiveEnum::CAMPAIGN) {
                    $sellers = InactiveService::getLimitedInactiveCampaignSellerIds($lastUserId, $this->limit);
                } else if ($this->type === InactiveEnum::LAST_LOGIN) {
                    $sellers = InactiveService::getLimitedSellerIds($lastUserId, $this->limit);
                }

                $endEsTime = round(microtime(true) * 1000);
                $esTime = InactiveService::getEstimatedTime($startEsTime, $endEsTime);
                InactiveService::logToGraylog($this->processId, $this->fileName, 'LastUserId: ' . $lastUserId . ' - Total Time to get sellers to scan: ' . $esTime . 'ms', $this->category);
            }

            if (empty($sellers)) {
                // Nếu sellers trống, kiểm tra xem còn user id nào lớn hơn id cuối cùng của lượt quét getLimitedInactiveCampaignSellerIds không?
                $sellers = InactiveService::getUserIdBigger($lastUserId,  $this->limit);
                // Nếu không còn seller nào nữa, reset về 0 để quét lại từ đầu
                if (empty($sellers)) {
                    $this->saveEndUserId(0);
                    InactiveService::logToGraylog($this->processId, $this->fileName, '[Empty sellers] reset last user id to 0', $this->category, true);
                    return 1;
                }
                // Nếu còn sellers, set cache = id phần tử cuối cùng của mảng sellers đó
                end($sellers);
                $endUserId = key($sellers);
                $this->saveEndUserId($endUserId);
                return 1;
            }

            $startUserId = $lastUserId;
            end($sellers);
            $endUserId = key($sellers);

            InactiveService::logToGraylog($this->processId, $this->fileName, 'Check ' . count($sellers) . ' users from > id: ' . $startUserId . '- to:' . $endUserId, $this->category);

            $this->saveEndUserId($endUserId);
            InactiveService::logToGraylog($this->processId, $this->fileName, '[Cache] - Set cache endUserId to: ' . $endUserId, $this->category, true);

            //*** Job
            if ($this->shouldRunQueue) {
                switch ($this->type) {
                    case InactiveEnum::CAMPAIGN:
                        CheckUserHaveInactiveCampaign::dispatch($sellers,
                            $this->processId,
                            $this->token,
                            $this->type,
                            $this->category,
                            $this->limit,
                            $this->count,
                            $this->shouldSend1StMail,
                            $this->appointMail)->onQueue($this->type);
                        break;
                    case InactiveEnum::LAST_LOGIN:
                        CheckUserHaveInactiveLastLogin::dispatch($sellers,
                            $this->processId,
                            $this->token,
                            $this->type,
                            $this->category,
                            $this->limit,
                            $this->count,
                            $this->shouldSend1StMail,
                            $this->appointMail)->onQueue($this->type);
                        break;
                }
            } else {
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Queue OFF', $this->category, true);
            }
            return 1;
        } catch (\Exception $e) {
            InactiveService::logExceptionToGraylog($this->processId, $this->fileName, $this->type, $e);
            return 0;
        }
    }

    private function configBeforeScan($systemConfig): void
    {
        if (!is_null($systemConfig)) {
            $this->shouldRunQueue = true;
            if(!is_null($systemConfig->json_data)) {
                $jsonData = json_decode($systemConfig->json_data, true);

                $this->appointSellerIds = InactiveService::getAppointSellerIds($jsonData);
                $this->shouldSend1StMail = InactiveService::shouldDo( $jsonData, 'send_1st_mail');
                $this->appointMail = InactiveService::getAppointMail($jsonData);
                $this->lastUserId = InactiveService::getSystemConfigLastUserId($jsonData, $this->cacheKey);
            }
        }
    }

    private function saveEndUserIdToSystemConfig($endUserId): void
    {
        $systemConfigValue = SystemConfig::query()->where('key', $this->type)->pluck('json_data')->first();

        if (is_null($systemConfigValue)) {
            InactiveService::logToGraylog($this->processId, $this->fileName, 'Cannot find system config with key: ' . $this->type, $this->category, true);
            return;
        }

        $systemConfigValue = json_decode($systemConfigValue, true);
        $systemConfigValue[$this->cacheKey] = $endUserId;
        $systemConfigValue = json_encode($systemConfigValue);
        SystemConfig::query()->where('key', $this->type)->update(['json_data' => $systemConfigValue]);
        InactiveService::logToGraylog($this->processId, $this->fileName, 'Update system config - key: ' . $this->cacheKey . ' - value: ' . $endUserId, $this->category, true);
    }

    private function getLastUserScanId() :int
    {
        // if cache not exist -> get from system config
        $lastUserId = Cache::get($this->cacheKey, function () {
            //get from system config
            InactiveService::logToGraylog($this->processId, $this->fileName, 'use system config to get last user id', $this->category);
            return $this->lastUserId;
        });

        if ($lastUserId === 0) {
            InactiveService::logToGraylog($this->processId, $this->fileName, 'use system config to get last user id', $this->category);
            $lastUserId = $this->lastUserId;
        }

        return $lastUserId;
    }

    private function saveEndUserId($endUserId): void
    {
        //save last user id to cache and system config
        Cache::put($this->cacheKey, $endUserId, CacheTime::CACHE_24H);
        $this->saveEndUserIdToSystemConfig($endUserId);
    }
}
