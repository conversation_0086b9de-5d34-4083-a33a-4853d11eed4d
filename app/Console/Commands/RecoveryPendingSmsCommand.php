<?php

namespace App\Console\Commands;

use App\Enums\AbandonedLogStatusEnum;
use App\Facades\ProcessLock;
use App\Models\AbandonedLog;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class RecoveryPendingSmsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'abandoned:pending';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessLock::handle($this->signature, callback: function () {
            $logs = AbandonedLog::query()
                ->where('status', AbandonedLogStatusEnum::PENDING)
                ->whereBetween('created_at', [Carbon::now()->subMonth(), Carbon::now()])
                ->get();

            if ($logs->isNotEmpty()) {
                $logs->each(function (AbandonedLog $log) {
                    if ($log->order) {
                        $log->order->recoverBySms($log->notification, $log);
                    }
                });
            }
            return 0;
        });
        return 0;
    }
}
