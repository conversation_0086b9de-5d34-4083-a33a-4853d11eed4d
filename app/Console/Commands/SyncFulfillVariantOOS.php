<?php

namespace App\Console\Commands;

use App\Enums\ProductType;
use App\Jobs\Suppliers\SyncVariantOOS;
use App\Models\ProductVariant;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

class SyncFulfillVariantOOS extends Command
{
    public const DEFAULT_ID = -1;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:supplier-oos {--supplier-id=-1} {--product-id=-1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ sản phẩm biến thể hết hàng của nhà cung cấp với sản phẩm biến thể tương ứng trên hệ thống';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $supplierId = (int) $this->option('supplier-id');

        suppliers()->each(function($cfg) use($supplierId) {
            if ($supplierId !== self::DEFAULT_ID && $supplierId !== $cfg['supplier_id']) {
                return;
            }

            if (! $handler = data_get($cfg, 'sync_oss_variant')) {
                return;
            }

            $this->variants($cfg['supplier_id'])->chunk(20)->each(
                fn(Collection $variants) => SyncVariantOOS::dispatch($variants, $handler, $cfg)
            );
        });

        return 0;
    }

    /**
     * @param     int     $supplierId
     *
     * @return Collection
     */
    public function variants(int $supplierId): Collection
    {
        return ProductVariant::query()->joinSub(function(Builder $q) use($supplierId) {
            // Sản phẩm fulfill
            $q->select('product.id', 'pfm.product_id as template_id')
                ->distinct()
                ->from('product')
                ->join('product_fulfill_mapping as pfm', 'pfm.fulfill_product_id', '=', 'product.id')
                ->where('product.supplier_id', $supplierId)
                ->where('product_type', ProductType::FULFILL_PRODUCT);

            // Khi có tùy chọn product id(fulfill_product_id) thì lấy theo id
            $productId = (int) $this->option('product-id');
            if ($productId !== self::DEFAULT_ID) {
                return $q->where('product.id', $productId);
            }

        }, 'fulfill_product', 'fulfill_product.id', 'product_variant.product_id')
        ->get(['template_id', 'product_id', 'variant_key', 'location_code', 'out_of_stock', 'sku']);
    }
}
