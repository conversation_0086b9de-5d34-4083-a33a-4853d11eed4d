<?php

namespace App\Console\Commands;

use App\Enums\OrderPaymentStatus;
use App\Http\Controllers\Storefront\PaypalController;
use App\Models\Order;
use App\Services\Graylog;
use App\Traits\Encrypter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class CorrectOrderPaypalPayment extends Command
{
    use Encrypter;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'correct-order-paypal-payment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * @return void
     * @throws \Throwable
     */
    public function handle()
    {
        $orders = Order::query()
            ->selectRaw('id, order_number, type, payment_method, payment_gateway_id, payment_log')
            ->where('payment_method', 'like', 'stripe%')
            ->where('paid_at', '>=', '2025-06-05 00:00:00')
            ->where('paid_at', '<=', '2025-06-08 23:59:59')
            ->orderBy('paid_at')
            ->get();
        $this->info('Total orders: ' . $orders->count());
        $orders->chunk(100)->each(function ($chunk) {
            $graylogServiceInstance = new Graylog;
            $orderIds = $chunk->pluck('order_number')->map(function ($orderNumber) {
                return Str::of($orderNumber)->explode('-')->last();
            })->toArray();
            $this->info(str_repeat('-', 51));
            $this->warn('Processing total orders: ' . count($orderIds));
            $body = [
                'query' => 'category: "verify_paypal" AND order_id: ("' . implode('","', $orderIds) . '")',
                "fields" => [
                    "message",
                    "order_id",
                    "callback_url",
                    "request_params",
                ],
                "from" => 0,
                "size" => 50,
                "timerange" => [
                    "type" => "relative",
                    "from" => 604800,
                ],
                "sort" => "timestamp",
                "sort_order" => "desc"
            ];
            try {
                $apiResponse = $graylogServiceInstance->queryLogsFromGraylog($body, logStream: 'backend_api_streams');
                $fullData = $graylogServiceInstance->handleQueryResponse($apiResponse);
                if (empty($fullData)) {
                    return;
                }
                $fullData = collect($fullData)->unique('callback_url')->values()->all();
                $paypalOrderIds = [];
                $paypalOrderTokens = [];
                collect($fullData)->pluck('request_params')->map(function ($params) use (&$paypalOrderIds, &$paypalOrderTokens, $fullData) {
                    $rawParams = $params;
                    $record = collect($fullData)->filter(fn ($item) => $item['request_params'] === $rawParams)->first();
                    $params = json_decode($params, true, 512, JSON_THROW_ON_ERROR);
                    $paypalOrderIds[$params['gateId']][$params['token']] = $params['token'];
                    $paypalOrderTokens[$params['token']] = $record['order_id'];
                    return $params;
                });
                foreach ($paypalOrderIds as $gateId => $arrayPaypalOrderId) {
                    foreach ($arrayPaypalOrderId as $paypalOrderId) {
                        $orderId = $paypalOrderTokens[$paypalOrderId];
                        $order = $chunk->filter(fn ($item) => (int)Str::of($item->order_number)->explode('-')->last() === (int)$orderId)->first();
                        $orderDetail = (new PaypalController())->verifyOrder($gateId, $paypalOrderId, forceUpdateTransactionId: false);
                        if (!empty($orderDetail)) {
                            $this->info('Order ID: ' . data_get($order, 'id') . ', Order Number: ' . data_get($order, 'order_number') . ', Paypal Order ID: ' . $paypalOrderId . ', Gate ID: ' . $gateId . ', Capture status: ' . data_get($orderDetail, 'status'));
                        }
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e->getMessage());
            }
        });
        $this->info('Done!');
    }

    /**
     * @return void
     */
    private function getDuplicateOrderPayments()
    {
        $orders = Order::query()
            ->selectRaw('id, order_number, type, payment_method, payment_gateway_id, payment_log')
            ->whereIn('id', [402230369,402230746,402230748,403130368,402229472,403130416,402230832,402230835,402230855,403130439,403130454,402230910,403130462,402230923,403130467,402230929,402230935,402230938,402230957,402230987,403130490,403130495,402230999,402231006,403130512,402231025,402231090,402231091,402231092,402231102,402231113,402231133,402231140,402231212,402231236,402231267,402231271,402231310,402231315,402231347,402231337,402231355,402231371,403130595,402231392,402231476,402231231,402231483,402231506,403130639,402231519,402231548,403130676,403130681,402231642,403130620,402231678,403130700,402231687,402231688,402231727,402231745,403130766,402231831,402231850,403130778,403130741,402231858,402231868,402231872,403130797,402231926,402231949,402231953,402231952,402231963,402231996,402232073,402194008,402232079,402232082,402232083,402232124,402232127,402232132,402232142,402232164,402232181,402232204,403130840,402232218,402232222,402232238,402232261,402232299,402232311,402232320,402232330,402232332,402232350,402232371,402232374,402232376,402232425,402232430,402232443,402232455,402232419,402232462,402232466,402232477,402232482,402232505,402232512,402232519,402232562,403130849,402232611,402232595,402232636,402232642,402232652,402232658,402232672,402232700,402232716,402232756,402232767,402232820,402232836,402232848,402232832,402232878,402232903,402232916,402232913,402232901,402232923,402232924,402232947,402232971,402232991,402233009,402233013,402233023,402233028,402233107,402233113,402233145,402233149,402233159,402233110,402233190,402233197,402233205,402233206,402233210,402233224,402233187,403130857,402233280,403130883,402233304,403130885,403130886,402233312,403130888,403130890,403130910,402233353,403130915,402233363,402233369,402233373,402233374,403130938,402233378,402233356,402233350,402233314,402233254,403130858,402233203,402233199,402233158,402233156,403130855,403130854,402233084,402233049,402233019,402232986,402232904,402232877,402232860,402232831,402232817,403130853,402232657,402232650,402232644,402232629,402232499,402232453,1413676,402232312,402232271,403130844,402232205,402232172,402232148,402232125,402232110,402232088,402232084,403130833,403130815,402231957,403130794,403130792,403130789,403130786,402231815,402231802,402231760,402231735,402231713,403130713,403130702,402222300,402231605,403130670,402231553,402231432,402231513,402231497,402231489,402231443,403130615,403130606,402231410,402231376,402231382,403130589,402231309,402231299,402231203,402231152,402231146,402231144,402231101,402231041,402231068,403130504,402230998,403130491,403130480,402230947,402230912,402230826,402230820,403130403,402230809,403130395,403130373,403130367,403130366,402230767,402230752,403130351])
            ->get();
        $graylogServiceInstance = new Graylog;
        foreach ($orders as $order) {
            $this->info(str_repeat('-', 51));
            $this->warn('Order ID: ' . $order->id . ', Order Number: ' . $order->order_number);
            $order_id = Str::of($order->order_number)->explode('-')->last();
            $body = [
                'query' => 'category: "verify_paypal" AND order_id: ' . $order_id,
                "fields" => [
                    "message",
                    "callback_url",
                    "request_params",
                ],
                "from" => 0,
                "size" => 50,
                "timerange" => [
                    "type" => "relative",
                    "from" => 604800,
                ],
                "sort" => "timestamp",
                "sort_order" => "desc"
            ];
            try {
                $apiResponse = $graylogServiceInstance->queryLogsFromGraylog($body, logStream: 'backend_api_streams');
                $fullData = $graylogServiceInstance->handleQueryResponse($apiResponse);
                if (empty($fullData)) {
                    continue;
                }
                $fullData = collect($fullData)->unique('callback_url')->values()->all();
                $paypalOrderIds = [];
                collect($fullData)->pluck('request_params')->map(function ($params) use (&$paypalOrderIds, $order) {
                    $params = json_decode($params, true, 512, JSON_THROW_ON_ERROR);
                    $paypalOrderIds[($params['gateId'] ?? $order->payment_gateway_id)][$params['token']] = $params['token'];
                    return $params;
                });
                foreach ($paypalOrderIds as $gateId => $arrayPaypalOrderId) {
                    foreach ($arrayPaypalOrderId as $paypalOrderId) {
                        $orderDetail = (new PaypalController())->verifyOrder($gateId, $paypalOrderId, forceUpdateTransactionId: false);
                        if (!empty($orderDetail)) {
                            $this->info('Order ID: ' . $order->id . ', Paypal Order ID: ' . $paypalOrderId . ', Gate ID: ' . $gateId . ', Capture status: ' . data_get($orderDetail, 'status'));
                        }
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e->getMessage());
            }
        }
    }

    /**
     * @return void
     */
    private function completeOrders()
    {
        $orders = Order::query()
            ->selectRaw('id, order_number, type, payment_method, payment_gateway_id, payment_log')
            ->where('payment_status', OrderPaymentStatus::FAILED)
            ->whereNotNull('payment_method')
            ->where(function ($q) {
                $q->where('updated_at', '>=', '2025-06-06 00:00:00')->orWhere('created_at', '>=', '2025-06-06 00:00:00');
            })
            ->orderBy('created_at')
            ->get();
        $graylogServiceInstance = new Graylog;
        foreach ($orders as $order) {
            $this->info(str_repeat('-', 50));
            $this->warn('Order ID: ' . $order->id . ', Order Number: ' . $order->order_number);
            $order_id = Str::of($order->order_number)->explode('-')->last();
            $body = [
                'query' => 'category: "verify_paypal" AND order_id: ' . $order_id,
                "fields" => [
                    "message",
                    "callback_url",
                    "request_params",
                ],
                "from" => 0,
                "size" => 50,
                "timerange" => [
                    "type" => "relative",
                    "from" => 604800,
                ],
                "sort" => "timestamp",
                "sort_order" => "desc"
            ];
            try {
                $apiResponse = $graylogServiceInstance->queryLogsFromGraylog($body, logStream: 'backend_api_streams');
                $fullData = $graylogServiceInstance->handleQueryResponse($apiResponse);
                if (empty($fullData)) {
                    continue;
                }
                foreach ($fullData as &$data) {
                    if (!str_starts_with($data['callback_url'], "http://backend-api-service")) {
                        continue;
                    }
                    $data['callback_url'] = str_replace("http://backend-api-service", "https://api-sg.senprints.net", $data['callback_url']);
                    $result = Http::get($data['callback_url']);
                    if ($result->failed()) {
                        $this->info('Failed to call callback url: ' . $data['callback_url']);
                        continue;
                    }
                    $response = $result->json();
                    if (!$response['success']) {
                        $this->info('Failed to call callback url: ' . $result->body());
                        continue;
                    }
                    $this->info('Ok: ' . $data['message']);
                    $this->info('https://admin.senprints.com/order/detail/' . $order->id);
                    break;
                }
                unset($data);
            } catch (\Throwable $e) {
                $this->error($e->getMessage());
            }
        }
    }
}
