<?php

namespace App\Rules\CampaignStore;

use Illuminate\Contracts\Validation\Rule;

class IsEndTimeValid implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $currentTime = time(); // Get current time.
        $endTime = strtotime($value); // Get end time.

        // If end time <= current then return false.
        return $endTime > $currentTime;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'The End Time field is invalid.';
    }
}
