<?php

namespace App\Rules\Store\Page;

use App\Enums\PageTypeEnum;
use App\Models\Page;
use Illuminate\Contracts\Validation\Rule;

class CheckTemplateIdRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    private string $type;

    public function __construct(string $type)
    {
        $this->type = $type;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (empty($value)) {
            return true;
        }

        $type = $this->type === PageTypeEnum::CUSTOM
            ? PageTypeEnum::CUSTOM
            : PageTypeEnum::TEMPLATE;

        return Page::query()
            ->where([
                'id' => $value,
                'type' => $type
            ])
            ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Template ID do not exists.';
    }
}
