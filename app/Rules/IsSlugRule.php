<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class IsSlugRule implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  $attribute
     * @param  $value
     *
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $pattern = "/^[a-z0-9]+(?:-[a-z0-9]+)*$/i";
        return preg_match($pattern, $value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'Slug is invalid.';
    }
}
