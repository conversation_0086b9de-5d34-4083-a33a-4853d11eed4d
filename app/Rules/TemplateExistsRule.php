<?php

namespace App\Rules;

use App\Enums\ProductType;
use App\Models\Product;
use Illuminate\Contracts\Validation\Rule;

class TemplateExistsRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return Product::where('product_type', ProductType::TEMPLATE)
            ->where('id', $value)
            ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Template ID is not exists.';
    }
}
