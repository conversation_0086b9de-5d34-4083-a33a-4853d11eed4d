<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Http;

class Recaptcha implements Rule
{
    protected $action;
    protected $hideLog = true;


    /**
     * Create a new rule instance.
     *
     * @param string $action
     */
    public function __construct(string $action)
    {
        $this->action = $action;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     */
    public function passes($attribute, $value): bool
    {
        // submit to Google
        $secret = config('recaptcha.secret');

        if (!$secret) {
            return false;
        }

        $res = Http::asForm()
            ->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret' => $secret,
                'response' => $value
            ]);

        if ($res->failed()) {
            return false;
        }

        $json = $res->json();

        return (isset($json['success'], $json['action'])
            && $json['success']
            && $json['action'] === $this->action);
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return 'Cannot verify reCaptcha.';
    }
}
