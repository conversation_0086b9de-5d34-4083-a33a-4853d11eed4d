<?php

namespace App\Rules\Analytic;

use App\Enums\AdOptionEnum;
use Illuminate\Contracts\Validation\Rule;

class AdOptionRule implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        return in_array($value,AdOptionEnum::asArray());
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'The ad option must be: ' . implode(',', AdOptionEnum::asArray());
    }
}
