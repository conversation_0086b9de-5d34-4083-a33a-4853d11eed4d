<?php

namespace App\Rules\Seller;

use App\Models\Store;
use Illuminate\Contracts\Validation\Rule;

class StoreRule implements Rule
{
    public function passes($attribute, $value): bool
    {
        return Store::query()
            ->where([
                'id' => $value,
                'seller_id' => currentUser()->getUserId()
            ])
            ->exists();
    }

    public function message(): string
    {
        return 'Store not found';
    }
}
