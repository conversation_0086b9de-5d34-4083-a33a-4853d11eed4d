<?php

namespace App\Rules\Seller\Store;

use App\Enums\StoreDomainStatusEnum;
use App\Models\StoreDomain;
use Illuminate\Contracts\Validation\Rule;

class CustomDomainIdRule implements Rule
{
    protected $Id = null;
    protected $storeId = null;
    protected $sellerId = null;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($Id, $storeId, $sellerId)
    {
        $this->Id = $Id;
        $this->storeId = $storeId;
        $this->sellerId = $sellerId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (is_null($this->storeId) || is_null($this->sellerId)) {
            return false;
        }
        /**
         * Remove where('seller_id', $this->sellerId)
         * Current user is not seller (admin or support)
         * @updatedBy ChanhDN
         */
        return StoreDomain::query()
            ->where([
                'id' => $this->Id,
                'store_id' => $this->storeId,
                'domain' => $value,
                'status' => StoreDomainStatusEnum::PENDING
            ])
            ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The domain is not already been taken.';
    }
}
