<?php

namespace App\Observers;

use App\Models\Template;
use Illuminate\Support\Facades\Auth;

class TemplateObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(Template $product): void
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Template $product): void
    {
    }

    /**
     * Handle the Product "deleting" event.
     */
    public function deleting(Template $product): void
    {
        logToDiscord('Deleting product of supplier: ' . $product->supplier_id . ' for product id : ' . $product->id, 'fulfill_product', true);
        graylogError('Deleting product of supplier', [
            'category' => 'product_observer_deleting',
            'product_id' => $product->id,
            'trace' => debug_backtrace(),
            'product' => $product->toArray(),
            'user' => Auth::user(),
        ]);
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Template $product): void
    {
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Template $product): void
    {
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Template $product): void
    {
        logToDiscord('Deleting product of supplier: ' . $product->supplier_id . ' for product id : ' . $product->id, 'fulfill_product', true);
        graylogError('Deleting product of supplier', [
            'category' => 'product_observer_deleting',
            'product_id' => $product->id,
            'trace' => debug_backtrace(),
            'product' => $product->toArray(),
            'user' => Auth::user(),
        ]);
    }
}
