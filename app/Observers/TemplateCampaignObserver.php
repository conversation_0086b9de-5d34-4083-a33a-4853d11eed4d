<?php

namespace App\Observers;

use App\Models\TemplateCampaign;
use App\Models\User;
use Modules\Campaign\Jobs\SyncSlugJob;

class TemplateCampaignObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(TemplateCampaign $product): void
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(TemplateCampaign $product): void
    {
    }

    /**
     * Handle the Product "deleting" event.
     */
    public function deleting(TemplateCampaign $product): void
    {
        // Check if the product is a campaign or template and has a seller_id will be deleted the slug on the slugs table
        if ($product->seller_id) {
            $seller = User::query()->select(['id', 'email', 'sharding_status', 'db_connection'])->whereKey($product->seller_id)->first();
            SyncSlugJob::dispatch(ids: [$product->id], seller: $seller, isUpsert: false);
        }
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(TemplateCampaign $product): void
    {
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(TemplateCampaign $product): void
    {
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(TemplateCampaign $product): void
    {
        // Check if the product is a campaign or template and has a seller_id will be deleted the slug on the slugs table
        if ($product->seller_id) {
            $seller = User::query()->select(['id', 'email', 'sharding_status', 'db_connection'])->whereKey($product->seller_id)->first();
            SyncSlugJob::dispatch(ids: [$product->id], seller: $seller, isUpsert: false);
        }
    }
}
