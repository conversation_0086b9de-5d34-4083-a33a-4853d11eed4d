<?php

namespace App\Providers\FulfillAPI\Printway;

use App\Models\FulfillProduct;
use App\Providers\FulfillAPI\ObjectFulfill;
use Throwable;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/orders/v2/create-order';
    protected const ENDPOINT_CANCEL_ORDER = '/orders/v1/cancel-order';
    protected const ENDPOINT_CRAWL_ORDER = '/orders/v1/detail';
    protected const ENDPOINT_CRAWL_PRODUCT = '/products/v1/all';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("PW-ACCESS-TOKEN", $provider['token']);
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params = ''): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->params           = [
                    'order_id' => $params
                ];
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
            case self::METHOD_CRAWL_PRODUCT:
                $this->method_url = 'GET';
                break;
        }
    }

    protected array $allow_crawl_products_name = [
        '2 Layered Mix Ornament (VN)',
        'Custom 2-Layered Wooden Art (VN)',
        '2 Layered Wooden Ornament (VN)',
        'Custom 3-Layered Wooden Art (VN)',
        'Custom 4-Layered Wooden Art (VN)',
        'Star Aluminium Ornament (VN)',
        'Aluminum Medallion Photo Ornament (AU)',
        'Custom Shaped Acrylic Ornament (VN)',
        'Aluminum Snowflake Ornament (AU)',
        'Aluminum Scalloped Photo Ornament (AU)',
        'Aluminum Square Photo Ornament (AU)',
        'Circle Aluminium Ornament (VN)',
        'Circle Acrylic/Wooden Ornament (VN)',
        'Circle Ceramic Ornament (VN)',
        'Christmas Stocking (VN)',
        'Custom Shape Car Ornament (VN)',
        'Droplets Aluminium Ornament (VN)',
        'Elip Aluminium Ornament (VN)',
        'Elip Acrylic/Wooden Ornament (VN)',
        'Heart Aluminium Ornament (VN)',
        'Heart Acrylic/Wooden Ornament (VN)',
        'Heart Ceramic Ornament (VN)',
        'Led Acrylic Ornament (VN)',
        'Medallion Aluminium Ornament (UK)',
        'Medallion Acrylic/Wooden Ornament (VN)',
        'Medallion Aluminium Ornament (VN)',
        'Oval Ceramic Ornament (VN)',
        'Custom Shape Wooden Ornament (VN)',
        'Scalloped Aluminium Ornament (VN)',
        'Star Acrylic/Wooden Ornament (VN)',
        'Star Ceramic Ornament (VN)',
        'Aluminum Square Ornament (UK)',
        'Transparent Acrylic Car Ornament (VN)',
        'Transparent Acrylic Ornament (VN)',
        'Short Sleeve T-shirt (AU)',
        'Youth Unisex T-shirt (AU)',
        'Long Sleeve T-Shirt (AU)',
        'Unisex Hoodie (AU)',
        'Crewneck Sweatshirt (AU)',
        'Toddler Unisex T-Shirt (AU)',
        'Women Crop Tee (AU)',
        'Men Tank Top (AU)',
        'Women Tank Top (AU)',
        'Polo Shirt (AU)',
        'Premium Short Sleeve T-shirt (AU)',
        'Premium Unisex Hoodie (AU)',
    ];

    public function crawlProductsAndProductVariantsJob(): void
    {
        $objectCrawlProduct = $this;
        $objectCrawlProduct->setMethod($this::METHOD_CRAWL_PRODUCT);
        $objectCrawlProduct->setParams();

        $responseModel = $objectCrawlProduct->getModel(self::MODEL_RESPONSE);

        $arr                 = [];
        $this->arr_new_color = [];
        $objectProductModel  = $objectCrawlProduct->getModel(self::MODEL_CRAWL_PRODUCT);
        $response            = $objectCrawlProduct->sendData(null, null, false);
        // $response = cache()->rememberForever(
        //     'crawl_variant' . $this->supplier_id,
        //     function () use ($objectCrawlProduct) {
        //         return $objectCrawlProduct->sendData(null, null, false);
        //     }
        // );
        $responseModel->mappingCrawlProducts($response);
        $data = $responseModel->getResponseData();
        foreach ($data as $each) {
            try {
                if (!$this->checkAllowCrawlProduct($each['product_name'])) {
                    continue;
                }
                $object  = clone $objectProductModel;
                $product = $object->mapping($each);
                if (empty($product)) {
                    continue;
                }
                // if (!empty($object->getNewColors())) {
                //     foreach ($object->getNewColors() as $color) {
                //         $this->arr_new_color[] = $color;
                //     }
                // }
                $arr[] = $product;
            } catch (Throwable $e) {
                logException($e, __FUNCTION__, 'fulfill_product', true);
                continue;
            }
        }
        $this->arr_product_id_insert = [];
        $this->arr_product_sku       = [];
        $this->arr_variant           = [];
        foreach ($arr as $product) {
            $productSku    = $product['product']['sku'];
            $parentProduct = FulfillProduct::updateOrCreate(
                [
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ],
                $product['product']
            );
            foreach ($product['variants'] as $variant) {
                $variant['product_id'] = $parentProduct->id;
                $this->arr_variant[]   = $variant;
            }

            $this->arr_product_sku[]       = $productSku;
            $this->arr_product_id_insert[] = $parentProduct->id;
        }
        $this->handleAfterCrawlProduct();
        // $this->handleNewColor();
        $this->handleAfterCrawlVariant();
    }
}
