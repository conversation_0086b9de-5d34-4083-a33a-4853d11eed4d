<?php

namespace App\Providers\FulfillAPI\Printway\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductVariantModel extends AbstractModel
{
    public static function mapping(array $sku, array $options): array
    {
        return [
            'sku'          => $sku['item_sku'],
            'base_cost'    => $sku['tier_cost'],
            'variant_key'  => getVariantKey($options),
            'out_of_stock' => self::getOutOfStock($sku['availability']),
        ];
    }

    /** @noinspection PhpParameterNameChangedDuringInheritanceInspection */
    public static function mappingOptions($option, $type = 'all', $productName = '', $hadParsed = false): string
    {
        $option = parent::parseOption($option);
        /** @noinspection PhpSwitchStatementWitSingleBranchInspection */
        /** @noinspection DegradedSwitchInspection */
        switch ($type) {
            case ProductOptionEnum::SIZE:
                if (in_array($option, [
                    'default size',
                    'default',
                    'pack1',
                ])) {
                    return '';
                }
                break;
        }

        return parent::mappingOptions($option, $type, $productName, true);
    }
}
