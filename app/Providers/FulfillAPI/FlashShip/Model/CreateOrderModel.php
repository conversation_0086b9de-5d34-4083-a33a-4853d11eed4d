<?php

namespace App\Providers\FulfillAPI\FlashShip\Model;

use App\Enums\CountryEnum;
use App\Enums\FulfillMappingEnum;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\AbstractModel;
use RuntimeException;

class CreateOrderModel extends AbstractModel
{
    private array $payload;

    public function setItems(OrderProduct $value): CreateOrderModel
    {
        $this->payload['products'][] = $this->makeItems($value);

        return $this;
    }

    /**
     {
        "order_id": "order_id_0000001", // if order_id is empty system will return you a random order_id, order_id is unique
        "buyer_first_name": "Jack", // required
        "buyer_last_name": "Sparrow",
        "buyer_email": "<EMAIL>",
        "buyer_phone": "766612489",
        "buyer_address1": "18462 Edinbrook Ln", // required
        "buyer_address2": "",
        "buyer_city": "Westfield", // required
        "buyer_province_code": "IN", // required and use abbreviations for US state names
        "buyer_zip": "46074", // required
        "buyer_country_code": "US", // default value is US
        "shipment": "1", // has 3 value: 1: FirstClass; 2: Priority; 3: RushProduction; 6: Expedite
        "link_label":null,
        "products": [
            {
                "variant_id": 12128, // required - list variant in description
                "printer_design_front_url": "https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing", // Minimum requirement must have one URL (front ,back,right,left,neck)
                "printer_design_back_url": null, // Minimum requirement must have one URL (front ,back,right,left,neck)
                "printer_design_right_url": null,// Minimum requirement must have one URL (front ,back,right,left,neck)
                "printer_design_left_url": null,// Minimum requirement must have one URL (front ,back,right,left,neck)
                "printer_design_neck_url": null,// Minimum requirement must have one URL (front ,back,right,left,neck)
                "mockup_front_url": "https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing",
                "mockup_back_url": null,
                "mockup_right_url": null,
                "mockup_left_url": null,
                "mockup_neck_url": null,
                "quantity": 2, // required
                "note": ""
            }
        ]
     }
     * @param $order
     *
     * @return void
     */
    public function setOrder($order): void
    {
        $this->payload = [
            "order_id" => $this->fulfill_order_id = $this->getReferenceId($order),
            "buyer_first_name"      => !empty($this->customer_name['first_name']) ? $this->customer_name['first_name'] : '_',
            "buyer_last_name"       => $this->customer_name['last_name'],
            "buyer_email"           => $order->customer_email ?? self::SUPPORT_EMAIL,
            "buyer_phone"           => $order->customer_phone ?? self::SUPPORT_PHONE,
            "buyer_address1"        => $this->customer_address['primary'],
            "buyer_address2"        => $this->customer_address['addition'],
            "buyer_city"            => $order->city,
            "buyer_province_code"   => $this->getProvinceCode($order),
            "buyer_zip"             => $order->postcode,
            "buyer_country_code"    => $order->country,

            // 1: FirstClass; 2: Priority; 3: RushProduction; 6: Expedite
            "shipment"              => $this->mapShippingMethod($order->shipping_method, $order->country),
            "products" => [],

            ...$this->overrideOrder($order)
        ];
    }

    /**
     * @param     \App\Models\Order     $order
     *
     * @return string
     */
    public function getProvinceCode(Order $order): string
    {
        if ($order->state) {
            if (strlen($order->state) === 2) {
                return $order->state;
            }
            if ($code = CountryEnum::toStateCode($order->country, $order->state)) {
                return $code;
            }
        }
        if (!empty($order->shipping_label)) {
            return 'CA';
        }
        throw new RuntimeException("Cannot find province code for {$order->country}, city: {$order->city}, state: {$order->state}");
    }

    /**
     * @param     string     $shippingMethod
     * @param     string     $countryCode
     *
     * @return int|string
     */
    public function mapShippingMethod(string $shippingMethod, string $countryCode)
    {
        return $this->getByMapping($shippingMethod, FulfillMappingEnum::SHIPPING_METHOD, $countryCode, true) ?: 1;
    }

    public function getPublicAttrs(): array
    {
        return $this->payload;
    }

    /**
     * @param $value
     * @param $type
     * @param $location
     * @param bool $strict
     * @return string
     */
    public function mappingPrintSpace($value, $type, $location = null, bool $strict = false): string
    {
        if ($type === FulfillMappingEnum::PRINT_SPACES && !in_array($value, ['front', 'back', 'right_sleeve', 'left_sleeve'])) {
            return 'front';
        }

        return $this->getByMapping($value, $type, $location, $strict);
    }

    /**
     * @param     \App\Models\OrderProduct     $op
     *
     * @return array
     */
    private function makeItems(OrderProduct $op): array
    {
        $designs = $this->getDesignOnPrintSpaces($op->files);
        return [
            "variant_id"                  => (string) $op->fulfill_sku,
            "printer_design_front_url"    => optional($designs['front'])->design_url ?: null,
            "printer_design_back_url"     => optional($designs['back'])->design_url ?: null,
            "printer_design_right_url"    => optional($designs['right_sleeve'])->design_url ?: null,
            "printer_design_left_url"     => optional($designs['left_sleeve'])->design_url ?: null,
            "printer_design_neck_url"     => "",
            "mockup_front_url"            => optional($designs['front'])->mockup_url ?: null,
            "mockup_back_url"             => optional($designs['back'])->mockup_url ?: null,
            "mockup_right_url"            => optional($designs['right_sleeve'])->mockup_url ?: null,
            "mockup_left_url"             => optional($designs['left_sleeve'])->mockup_url ?: null,
            "mockup_neck_url"             => "",
            "quantity"                    => $op->quantity,
            "note"                        => ""
        ];
    }

    /**
     * @param $order
     *
     * @return array
     */
    protected function overrideOrder($order): array
    {
        return [];
    }
}
