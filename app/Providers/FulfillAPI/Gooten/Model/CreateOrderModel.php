<?php

namespace App\Providers\FulfillAPI\Gooten\Model;

use App\Enums\CountryEnum;
use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public array $ShipToAddress;
    public array $BillingAddress;
    public array $Items = [];
    public array $Meta = [];
    public string $SourceId;
    public string $IsPartnerSourceIdUnique = 'true';
    public string $IsInTestMode;
    private string $ShipType;

    public function setMode($mode): void
    {
        $this->IsInTestMode = ($mode === 'production') ? 'false' : 'true';
    }

    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->SourceId = $this->getReferenceId($order);
        $this->setAddress($order);
        $this->setShippingType();
    }

    public function setItems($product): void
    {
        $this->Items[] = [
            'SourceId' => $this->getReferenceId($product),
            'Quantity' => $product->quantity,
            'SKU'      => $product->fulfill_sku,
            'ShipType' => $this->ShipType,
            'Images'   => $this->getImages($product),
        ];
    }

    protected function getImages($product): array
    {
        $arr           = [];
        $arrPrintSpace = [];

        foreach ($product->files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );

            if (!in_array($printSpace, $arrPrintSpace)) {
                $arr[]           = [
                    'Url'          => $file->design_url,
                    'ThumbnailUrl' => $file->mockup_url,
                ];
                $arrPrintSpace[] = $printSpace;
            }
        }

        if (count($arrPrintSpace) === 1) {
            if (is_string($product->options)) {
                $options = json_decode($product->options, true);
            } else {
                $options = $product->options;
            }
            if (isset($options['print_side']) && $options['print_side'] === 'double sided') {
                $arr[] = $arr[0];
            }
        } elseif ($arrPrintSpace === ['back', 'front']) {
            $arr = array_reverse($arr);
        }

        return $arr;
    }

    protected function setAddress($order): void
    {
        $this->ShipToAddress = $this->BillingAddress = [
            "FirstName"         => $this->customer_name['first_name'],
            "LastName"          => $this->customer_name['last_name'],
            "Line1"             => $this->customer_address['primary'],
            "Line2"             => $this->customer_address['addition'],
            "City"              => $this->customer_address['city'],
            "State"             => $this->customer_address['state_code'],
            "CountryCode"       => $order->country,
            "PostalCode"        => $order->postcode,
            "IsBusinessAddress" => "false",
            "Phone"             => $order->customer_phone ?? self::SUPPORT_PHONE,
            "Email"             => $order->customer_email ?? self::SUPPORT_EMAIL,
        ];

        if (CountryEnum::isEu($order->country)) {
            $this->ShipToAddress['Line2'] .= ', ioss: ' . ($order->ioss_number ?? 'IN2330015967'); // ioss number for EU
            $this->BillingAddress['Line2'] .= ', ioss: ' . ($order->ioss_number ?? 'IN2330015967'); // ioss number for EU
        }
    }

    protected function setShippingType(): void
    {
        $this->ShipType = $this->mapped_shipping_methods[0];
    }
}
