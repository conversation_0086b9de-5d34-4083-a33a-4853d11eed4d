<?php

namespace App\Providers\FulfillAPI;

use App\Models\Order;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class CustomerAddress
{
    protected ?Order $order = null;
    protected string $stateCode = '';
    protected string $state = '';
    protected string $city = '';
    protected string $primary = '';
    protected string $addition = '';
    protected string $addition_original = '';
    protected string $full = '';
    protected string $street = '';
    protected string $house_full_number = '';
    protected string $house_number = '';
    protected string $house_number_text = '';
    protected string $mailbox_number = '';
    protected string $mailbox_number_text = '';
    protected string $house_number_addition = '';

    /**
     * @param     \App\Models\Order     $order
     *
     * @return $this
     */
    public function setOrder(Order $order): static
    {
        $this->order = $order;

        return $this;
    }

    /**
     * @return $this
     */
    public function parse(): static
    {
        return $this->handleState()
            ->handleStateCode()
            ->handleCity()
            ->handlePrimary()
            ->handleAddition()
            ->handleFull()
            ->handleStreetAndNumber()
            ->handleMailboxNumber()
            ->handleHouseNumber()
            ->handleNote();
    }

    /**
     * @return array
     */
    public function all(): array
    {
        return [
            'state'         => $this->state,
            'state_code'    => $this->stateCode,
            'city'          => $this->city,
            'primary'       => $this->primary,
            'addition'      => $this->addition,
            'addition_original' => $this->addition_original,
            'full'          => $this->full,
            'street'        => $this->street,
            'house_number'  => $this->house_number,
            'house_number_text' => $this->house_number_text,
            'mailbox_number' => $this->mailbox_number,
            'mailbox_number_text' => $this->mailbox_number_text,
            'house_number_addition' => $this->house_number_addition,
            'house_full_number' => $this->house_full_number,
        ];
    }

    /**
     * @return $this
     */
    public function handleState(): static
    {
        $this->state = Str::ascii($this->order->state ?? '');

        return $this;
    }

    /**
     * @return $this
     */
    public function handleStateCode(): static
    {
        if (is_null($this->order->state)) {
            $this->stateCode = '';
        } else {
            $state = ucwords(trim($this->order->state));

            if ($this->order->country === 'US' && isset(self::usState()[$state])) {
                $this->stateCode = self::usState()[$state] ?? '';
            } else {
                $state = strtoupper(substr($state, 0, 2));

                $this->stateCode = Str::ascii($state) ?? '';
            }
        }

        return $this;
    }

    /**
     * @return $this
     */
    public function handleCity(): static
    {
        $this->city = Str::ascii($this->order->city);

        return $this;
    }

    /**
     * @return $this
     */
    public function handlePrimary(): static
    {
        $this->primary = preg_replace(
            '/\s*[\'\"]\s*/',
            '',
            Str::ascii($this->order->address)
        );

        return $this;
    }

    /**
     * @return $this
     */
    public function handleAddition(): static
    {
        $this->addition = preg_replace(
            '/\s*[\'\"]\s*/',
            '',
            Str::ascii($this->order->address_2)
        );
        $this->addition_original = $this->addition;

        return $this;
    }

    /**
     * @return $this
     */
    public function handleFull(): static
    {
        $this->full = collect([
            $this->primary, $this->addition
        ])->filter()->join(', ');

        return $this;
    }

    /**
     * @return $this
     */
    public function handleStreetAndNumber(): static
    {
        if (stripos($this->primary, 'packstation') !== false) {
            return $this->separateCasePackstation();
        }
        $this->street = preg_replace('/\d+/', '', $this->primary); // Times City
        if (empty($this->house_number)) {
            $segments = Str::of($this->primary)
                ->explode(' ')
                ->reject(static fn ($v) => empty($v))
                ->reject(static fn ($v) => in_array($v, [',', '.', '-', '/', '\\', ' ',], true));
            foreach ($segments as $index => $match) {
                if (preg_match('/\d+/', $match)) {
                    $this->house_full_number     = $match; // 3A
                    $this->house_number          = filter_var($match, FILTER_SANITIZE_NUMBER_INT); // 3
                    $this->house_number_addition = preg_replace('/[^a-zA-Z]+/', '', $match); // A
                    $segments->forget($index);
                    break;
                }
            }
            if (count($segments) > 1) {
                $this->street = $segments->implode(' '); // Times City
            }
        } else if (!empty($this->order->house_number)) {
            $this->handleHouseNumber();
        }
        return $this;
    }

    /**
     * @return $this
     */
    public function handleMailboxNumber()
    {
        if (!empty($this->order->mailbox_number)) {
            $this->mailbox_number = $this->order->mailbox_number;
            $this->mailbox_number_text = AbstractModel::MAILBOX_NUMBER_TEXT . $this->order->mailbox_number;
            $this->full .= ' (' . $this->mailbox_number_text . ')';
            $this->addition .= ' (' . $this->mailbox_number_text . ')';
        }
        return $this;
    }

    public function handleNote()
    {
        if ($this->order->order_note) {
            $this->addition .= ' (' . AbstractModel::ORDER_NOTE_TEXT . $this->order->order_note . ')';
        }

        return $this;
    }

    public function handleHouseNumber()
    {
        if ($this->order->house_number) {
            $this->house_number = filter_var($this->order->house_number, FILTER_SANITIZE_NUMBER_INT);
            $this->house_full_number = $this->order->house_number;
            $this->house_number_text = AbstractModel::HOUSE_NUMBER_TEXT . $this->order->house_number;
            $this->full .= ' (' . $this->house_number_text . ')';
            $this->addition .= ' (' . $this->house_number_text . ')';
        }
        return $this;
    }

    /**
     * @return string[]
     */
    public static function usState(): array
    {
        return [
            'Alabama' => 'AL',
            'Alaska' => 'AK',
            'Arizona' => 'AZ',
            'Arkansas' => 'AR',
            'California' => 'CA',
            'Colorado' => 'CO',
            'Connecticut' => 'CT',
            'Delaware' => 'DE',
            'District Of Columbia' => 'DC',
            'Florida' => 'FL',
            'Georgia' => 'GA',
            'Hawaii' => 'HI',
            'Idaho' => 'ID',
            'Illinois' => 'IL',
            'Indiana' => 'IN',
            'Iowa' => 'IA',
            'Kansas' => 'KS',
            'Kentucky' => 'KY',
            'Louisiana' => 'LA',
            'Maine' => 'ME',
            'Maryland' => 'MD',
            'Massachusetts' => 'MA',
            'Michigan' => 'MI',
            'Minnesota' => 'MN',
            'Mississippi' => 'MS',
            'Missouri' => 'MO',
            'Montana' => 'MT',
            'Nebraska' => 'NE',
            'Nevada' => 'NV',
            'New Hampshire' => 'NH',
            'New Jersey' => 'NJ',
            'New Mexico' => 'NM',
            'New York' => 'NY',
            'North Carolina' => 'NC',
            'North Dakota' => 'ND',
            'Ohio' => 'OH',
            'Oklahoma' => 'OK',
            'Oregon' => 'OR',
            'Pennsylvania' => 'PA',
            'Rhode Island' => 'RI',
            'South Carolina' => 'SC',
            'South Dakota' => 'SD',
            'Tennessee' => 'TN',
            'Texas' => 'TX',
            'Utah' => 'UT',
            'Vermont' => 'VT',
            'Virginia' => 'VA',
            'Washington' => 'WA',
            'West Virginia' => 'WV',
            'Wisconsin' => 'WI',
            'Wyoming' => 'WY',
            'American Samoa' => 'AS',
            'Guam' => 'GU',
            'Northern Mariana Islands' => 'MP',
            'Puerto Rico' => 'PR',
            'U.S. Virgin Islands' => 'VI',
            'U.S. Minor Outlying Islands' => 'UM',
            'Marshall Islands' => 'MH',
            'Micronesia' => 'FM',
            'Palau' => 'PW',
            'U.S. Armed Forces – Americas' => 'AA',
            'U.S. Armed Forces – Europe' => 'AE',
            'U.S. Armed Forces – Pacific' => 'AP',
            'Panama Canal Zone' => 'CZ',
            'Philippine Islands' => 'PI',
            'Trust Territory Of The Pacific Islands' => 'TT',
        ];
    }

    /**
     * @return $this
     */
    protected function separateCasePackstation(): static
    {
        // 20186945 Packstation 203
        $this->street = 'Packstation';
        preg_match_all('/\d+/', $this->primary, $matches);
        $this->house_full_number     = Arr::get($matches, '0.0', ''); // 20186945
        $this->house_number          = Arr::get($matches, '0.0', ''); // 20186945
        $this->house_number_addition = Arr::get($matches, '0.1', ''); // 203

        return $this;
    }

    /**
     * @param string $houseNumber
     * @param string $address
     * @return int|null
     */
    public function isHouseNumberInAddress(string $houseNumber, string $address): int|null
    {
        if (empty($houseNumber) || empty($address)) {
            return null;
        }
        if (str_contains($address, $houseNumber)) {
            $houseNumber = preg_quote($houseNumber, '/');
            $pattern = '/^' . $houseNumber . '\b|\b' . $houseNumber . '$|\s' . $houseNumber . '\b|\b' . $houseNumber . '\s|[,.]' . $houseNumber . '\b|\b' . $houseNumber . '[,.]|' . $houseNumber . '(?!.*\bstreets?\b)/i';
            if (preg_match($pattern, $address, $matches, PREG_OFFSET_CAPTURE)) {
                return $matches[0][1];
            }
        }
        return null;
    }
}
