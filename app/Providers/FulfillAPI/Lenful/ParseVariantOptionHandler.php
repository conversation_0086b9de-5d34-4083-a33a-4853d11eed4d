<?php

namespace App\Providers\FulfillAPI\Lenful;

use App\Enums\ProductOptionEnum;
use Illuminate\Support\Str;
use RuntimeException;
use Throwable;

class ParseVariantOptionHandler
{
    /**
     * @var callable|null
     */
    private $exceptionHandler = null;

    /**
     * @throws Throwable
     */
    public function handle(array $variant): array
    {
        $options = [];

        try {
            if ( ! data_get($variant, 'sku')) {
                throw new RuntimeException(json_encode($variant));
            }

            $options = $this->parseOptions(
                data_get($variant, 'options') ?: []
            );
        } catch (Throwable $e) {
            if ( ! $this->exceptionHandler) {
                throw $e;
            }

            call_user_func($this->exceptionHandler, $e, $variant);
        }

        return $options;
    }

    /**
     * @param array $options
     * @return array
     */
    private function parseOptions(array $options): array
    {
        return array_reduce(array_keys($options), function ($result, $key) use ($options) {
            // Vì dữ liệu trả về từ lenful không đồng nhất. Trong trường hợp option value
            // là mảng thì sẽ đệ quy lại hàm parseOptions
            if (is_array($options[$key])) {
                return array_merge($result, $this->parseOptions($options[$key]));
            }

            if ($option = $this->standardized($key)) {
                $result[$option] = Str::lower($options[$key]);
            }

            return $result;
        }, []);
    }

    /**
     * @param string $key
     * @return bool|string|null
     */
    protected function standardized(string $key)
    {
        $map = [
            'base color'                            => ProductOptionEnum::COLOR,
            'base-color'                            => ProductOptionEnum::COLOR,
            'color'                                 => ProductOptionEnum::COLOR,
            'outsole\'s color'                      => ProductOptionEnum::COLOR,
            'outsoles-color'                        => ProductOptionEnum::COLOR,
            'caps-base-color'                       => ProductOptionEnum::COLOR,
            'soles-color'                           => ProductOptionEnum::COLOR,
            'jackets-base-color'                    => ProductOptionEnum::COLOR,
            'hoodies-base-color'                    => ProductOptionEnum::COLOR,
            'sleeves-base-color'                    => ProductOptionEnum::COLOR,
            'jackets-base-color-body-sleeves'       => ProductOptionEnum::COLOR,
            'color-sole'                            => ProductOptionEnum::COLOR,
            'sleeves-color'                         => ProductOptionEnum::COLOR,
            'outsole-strip-color'                   => ProductOptionEnum::COLOR,
            'color-sole-laces'                      => ProductOptionEnum::COLOR,
            'mugs-base-colors'                      => ProductOptionEnum::COLOR,
            'base-shirts-colors'                    => ProductOptionEnum::COLOR,
            'bags-base-color'                       => ProductOptionEnum::COLOR,
            'jackets-base-colors'                   => ProductOptionEnum::COLOR,
            'inner-fleeces-color'                   => ProductOptionEnum::COLOR,
            'straps-color'                          => ProductOptionEnum::COLOR,
            'caps-base-colors'                      => ProductOptionEnum::COLOR,
            'rompers-base-color'                    => ProductOptionEnum::COLOR,
            'shirts-base-colors'                    => ProductOptionEnum::COLOR,
            'shirts-base-color'                     => ProductOptionEnum::COLOR,
            'hoodies-base-colors'                   => ProductOptionEnum::COLOR,
            'color-border'                          => ProductOptionEnum::COLOR,

            'size'                                  => ProductOptionEnum::SIZE,
            'size (us)'                             => ProductOptionEnum::SIZE,
            'size-us'                               => ProductOptionEnum::SIZE,
            'size (inch)'                           => ProductOptionEnum::SIZE,
            'size-inch'                             => ProductOptionEnum::SIZE,
            'size-inches'                           => ProductOptionEnum::SIZE,
            'size-as'                               => ProductOptionEnum::SIZE,
            'size-uk'                               => ProductOptionEnum::SIZE,
            'size-eu'                               => ProductOptionEnum::SIZE,
            'size-au'                               => ProductOptionEnum::SIZE,
            'hoodies-size'                          => ProductOptionEnum::HOODIES_SIZE,
            'long-pants-size'                       => ProductOptionEnum::LONG_PANTS_SIZE,
            'sport-bras-size'                       => ProductOptionEnum::SPORT_BRAS_SIZE,
            'shorts-size'                           => ProductOptionEnum::SHORTS_SIZE,
            'briefs-size'                           => ProductOptionEnum::BRIEFS_SIZE,
            'jackets-size'                          => ProductOptionEnum::SIZE,
            'crop-top-hoodies-size'                 => ProductOptionEnum::CROP_TOP_HOODIES_SIZE,
            'leggings-size'                         => ProductOptionEnum::LEGGINGS_SIZE,
            'size-cm'                               => ProductOptionEnum::SIZE_CM,
            'shirts-size'                           => ProductOptionEnum::SHIRTS_SIZE,
            'tank-tops-size'                        => ProductOptionEnum::TANK_TOPS_SIZE,

            'models'                                => ProductOptionEnum::MODEL,
            'shoes-models'                          => ProductOptionEnum::MODEL,
            'phones-models'                         => ProductOptionEnum::MODEL,

            'gender'                                => ProductOptionEnum::GENDER,

//            'shirt\'s base color'                   => ProductOptionEnum::MATERIAL,
//            'shirts-base-color'                     => ProductOptionEnum::MATERIAL,

            'printing area'                         => ProductOptionEnum::PRINT,
            'printing-area'                         => ProductOptionEnum::PRINT,
            'print-area'                            => ProductOptionEnum::PRINT,
            'shirts-base-color-shirt-sleeves'       => ProductOptionEnum::PRINT,
            'duvet-cover-print'                     => ProductOptionEnum::PRINT,

            'shape'                                 => ProductOptionEnum::SHAPE,

            'capacity'                              => ProductOptionEnum::CAPACITY,
            'capacity-shape'                        => ProductOptionEnum::CAPACITY,

            'pack'                                  => ProductOptionEnum::PACK,

            'style'                                 => ProductOptionEnum::OPTION,
            'option'                                => ProductOptionEnum::OPTION,
            'options'                               => ProductOptionEnum::OPTION,
            'additional'                            => ProductOptionEnum::ADDITIONAL,
            ''                                      => ProductOptionEnum::NONE,
            'neck-option'                           => ProductOptionEnum::OPTION,
            'including-wooden-handle'               => ProductOptionEnum::OPTION,
        ];

        return $map[Str::lower($key)]  ?? null;
    }
}
