<?php

namespace App\Providers\FulfillAPI\Lenful;

use App\Providers\FulfillAPI\Common\TokenRetriever;
use App\Providers\FulfillAPI\ObjectFulfill;
use Exception;
use Throwable;

class Provider extends ObjectFulfill
{
    protected  array $allow_crawl_products_name = [
        '2D Trucker Cap',
        '2D Unisex Cap',
        '3D Back Pack',
        '3D Baseball Button Jacket',
        '3D Baseball Jersey (Shortsleeves)',
        '3D Baseball Zipper Jacket',
        '3D Baseball Zipper Jacket',
        '3D Batwing Pocket Dress',
        '3D Bikini',
        '3D Cap',
        '3D Crew-Neck Dress',
        '3D Criss-Cross Tank Top',
        '3D Crocs',
        '3D Crop-Top Hoodie',
        '3D Crop-Top Hoodie and Leggings Set',
        '3d CRs',
        '3D Drawstring Bag',
        '3D Face mask',
        '3D Fleece Hoodie',
        '3D Fleece Hoodie Dress',
        '3D Fleece Oodie',
        '3D Fleece Zipper Hoodie',
        '3D Football Jersey',
        '3D Full Overprinted Blanket',
        '3D Glitter Tumbler',
        '3D Hawaiian Shirt',
        '3D Hawaiian Shirt and Shorts Set',
        '3D High Socks',
        '3D High-Waisted Bikini',
        '3D Hiking Long Sleeve Hoodie',
        '3D Hockey Jersey',
        '3D Hollow Tank Top',
        '3D Hollow Tank Top and Leggings Set',
        '3D Hooded Button Baseball Jacket',
        '3D Hooded Long Sleeves Shirt',
        '3D Hooded Sweater',
        '3D Hooded T-Shirt',
        '3D Hoodie',
        '3D Hoodie and Leggings Set',
        '3D Hoodie and Long Pants Set',
        '3D Hoodie Dress',
        '3D Hoodie Gaiter',
        '3D In-House Slippers',
        '3D Jersey Shorts',
        '3D Jersey Tank Top',
        '3D Kids\' Crocs',
        '3D Kids\' Football Jersey',
        '3D Kids\' Hawaiian Shirt',
        '3D Kids\' Hoodie',
        '3D Kids\' Long Pants',
        '3D Kids\' Shorts',
        '3D Kids\' Sweater',
        '3D Kids\' Sweatshirt',
        '3D Kids\' T-Shirt',
        '3D Kids\' Zipper Hoodie',
        '3D Laundry Basket',
        '3D Leather Tote Bag',
        '3D Leggings',
        '3D Long Pants',
        '3D Long Sleeves Hawaiian Shirt',
        '3D Long Sleeves Shirt',
        '3D Luggage Cover',
        '3D Men\'s Boxer',
        '3D Neck Gaiter',
        '3D Off Shoulder Dress',
        '3D Oven Mitts And Pot-Holder Set 1',
        '3D Oven Mitts And Pot-Holder Set 2',
        '3D Phone Case',
        '3D Pillow Case',
        '3D Polo Shirt',
        '3D Regular Tumbler',
        '3D Rompers',
        '3D Shorts',
        '3D Sleeveless Flared Dress',
        '3D Sleeveless Zipper Hoodie',
        '3D Snapback Cap',
        '3D Spare Tire Cover',
        '3D Sport Bra',
        '3D Spread-Collar Hawaiian Shirt',
        '3D Stand-Collar Zipper Jacket',
        '3D Strap Swimsuit',
        '3D Sweater',
        '3D Sweatshirt',
        '3D Swimsuit',
        '3D T-Shirt',
        '3D Tank Top',
        '3D Tote Bag',
        '3D Umbrella',
        '3D Varsity Zipper Tracksuit Set',
        '3D Women\'s Baseball Jersey',
        '3D Women\'s Casual Shirt',
        '3D Women\'s Handbag',
        '3D Women\'s Purse',
        '3D Women\'s Shoulder Bag',
        '3D Women\'s Sport Shorts',
        '3D Women\'s Sportwear Set',
        '3D Women\'s Tank Top',
        '3D Women\'s V-Neck T-Shirt',
        '3D Wrist Rest Mouse Pad',
        '3D Zipper Bomber Jacket',
        '3D Zipper Hoodie',
        '3D Zipper Hoodie and Long Pants Set',
        '3pcs Bedding Set (AU Size)',
        '3pcs Bedding Set (UK Size)',
        '3pcs Bedding Set (US Size)',
        '40 OZ Curved Tumbler',
        '4pcs Bedding Set (AU Size)',
        '4pcs Bedding Set (UK Size)',
        '4pcs Bedding Set (US Size)',
        'Acrylic Ornament',
        'Air Force Shoes',
        'Air Jordan 11 Shoes',
        'Air Jordan 13 Shoes',
        'Air Jordan 4 Shoes',
        'Air Jordan High Top Shoes',
        'Air Jordan Low Top Shoes',
        'Aqua Shoes',
        'Audemars Piguet Royal Oak Watch',
        'Auto Sunshade',
        'Bath Mat Set',
        'Bathroom Set',
        'Beach Towel',
        'Canvas 1 Panel (Framed)',
        'Canvas 1 Panel (No Framed)',
        'Canvas 3 Panels (Framed)',
        'Canvas 3 Panels (No Framed)',
        'Canvas 4 Panels (Framed)',
        'Canvas 4 Panels (No Framed)',
        'Canvas 5 Panels (Framed)',
        'Canvas 5 Panels (No Framed)',
        'Canvas Car Flag',
        'Canvas Garden Flag',
        'Canvas House Flag',
        'Car Floor Mat',
        'Car Headrest Covers',
        'Car Seat Cover',
        'Coir Doormat',
        'Diaper Bag',
        'Doormat',
        'Fiber Fleece Beach Towel',
        'Flip-Flops',
        'Fluffy Fleece Doormat',
        'Hooded Blanket',
        'Kids\' Air Jordan High Top Shoes',
        'LED Mouse Pad',
        'Linen Car Flag',
        'Linen Garden Flag',
        'Linen House Flag',
        'Metal Bone-Shaped Pet Tag',
        'Metal Pins Badge',
        'Metal Round-Shaped Pet Tag',
        'Metal Sign',
        'New Rectangle Area Rug',
        'New Rectangle Area Rug Version 2',
        'New Round Area Rug',
        'New Round Area Rug Version 2',
        'NMD Human Shoes',
        'Outdoor Flag',
        'Puzzle',
        'PVC Stair Decal',
        'Quilt',
        'Quilt Set',
        'Round Area Rug',
        'Round Wooden Sign',
        'Rugby Jerey',
        'Saddle Bag',
        'Satin 3pcs Bedding Set (US Size)',
        'Satin 4pcs Bedding Set (US Size)',
        'SB Dunk Low Shoes',
        'Seat Belt Cover',
        'Sherpa Blanket',
        'Shower Curtains',
        'US 12 OZ Tumbler',
        'US 20 OZ Curved Tumbler',
        'US 20 OZ Tumbler',
        'US 30 OZ Tumbler',
        'US Accent Mug 11 OZ',
        'US Accent Mug 15 OZ',
        'US Aluminum ornaments Benelux',
        'US Cozy Blanket 30x40',
        'US Cozy Blanket 50x60',
        'US Cozy Blanket 60x80',
        'US Plastic Covering Wooden Ornaments Circle',
        'US Plastic Covering Wooden Ornaments Heart',
        'US Plastic Covering Wooden Ornaments Oval',
        'US Plastic Covering Wooden Ornaments Star',
        'Woven Blanket',
        '2D Long Pants',
        '2D Cargo Hoodie',
        'Hoodie 2D DTG',
        'Sweatshirt 2D DTG',
        'T-Shirt 2D Unisex DTG',
        '2D Parka Jacket',
        'New 2D Hoodie (AS Size)',
        '2D Pocket T-Shirt (US Size)',
        '2D Quarter Zip Hoodie',
        '2D Baseball Jacket',
        '2D Fleece Zipper Hoodie',
        '2D Unisex Mineral Wash T-Shirt (DTF)',
        '2D Denim Cap',
        '2D Puff Jacket 2',
        '2D Men Zip Pocket Fleece Leather Jacket Coat',
        '2D Hooded Denim Jacket',
        '2D Tank Top (US Size)',
        'US Hoodie 2D',
        'US Unisex T-Shirt 2D',
        'US Long Sleeve T-shirt 2D',
        'US Women\'s V-Neck T-shirt 2D',
        'US Youth T-Shirt 2D',
        'US Unisex Tank 2D',
        'US Ladies Racerback Tank 2D',
        '2D Long Sleeves Shirt (US Size)',
        '2D Sweatshirt (US Size)',
        '2D Hoodie (US Size)',
        '2D T-Shirt (AS Size)',
        '2D T-Shirt (US Size)',
        '2D Raglan T-Shirt',
        '2D Leather Biker Jacket',
        '2D Leather Bomber Jacket',
        '2D Raglan Long Sleeves Shirt',
        '2D Raglan Shirt (3/4 Sleeves)',
        '2D Sweatshirt (AS Size)',
        '2D Hooded Leather Jacket',
        '2D Baby Romper',
        '2D Long Sleeves Shirt (AS Size)',
        '2D Hoodie (AS Size)',
        'US Comfort Colors Crewneck Sweatshirt',
        'US Camping Mug',
        'US Canvas Tote Bag',
        'US Comfort Colors T-shirt Unisex',
        'US Poster',
        'US Landscape Canvas',
        'US Portrait Canvas',
        'US Ceramic Ornament Heart',
        'US Color Changing Mug 15Oz',
        'US Color Changing Mug 11Oz',
        'US White Ceramic Mug 15Oz',
        'US Black Ceramic Mug 15Oz',
        'US White Ceramic Mug 11Oz',
        'US Black Ceramic Mug 11Oz',
        'US Square Canvas',
        'US Youth Crewneck Sweatshirt',
        'US Crewneck Sweatshirt',
        'US Bella Canvas Unisex T-Shirt',
        'US Next Level Unisex T-Shirt',
        'US Premium Ladies\' T-Shirts',
        'US Unisex Short Sleeve V-Neck Tee',
        'US Zipper Hoodie',
        'US Toddler T-Shirt',
        'US Sherpa Blanket 60x80',
        'US Sherpa Blanket 50x60',
        'US Youth Hoodie',
        'US Ceramic Ornament Star',
        'US Ceramic Ornament Circle',
        'US Ceramic Ornament Oval'
    ];

    public const ENDPOINT_CREATE_ORDER          = '';
    public const ENDPOINT_CANCEL_ORDER          = '';
    public const ENDPOINT_CRAWL_ORDER           = '/order/';
    public const ENDPOINT_CRAWL_PRODUCT         = '/product/';
    public const ENDPOINT_CRAWL_PRODUCT_VARIANT = '/product/';

    private array $config = [];

    protected int $page = 1;

    protected array $response = [];
    protected array $values   = [];

    protected TokenRetriever $tokenRetriever;

    /**
     * @param array $provider
     *
     * @return void
     * @throws Exception
     */
    public function setProvider(array $provider): void
    {
        parent::setProvider(
            $this->config = $provider
        );

        $this->tokenRetriever = $this->tokenRetriever()->withConfigArray($provider);
    }

    /**
     * @override ObjectFulfill::tokenRetriever()
     *
     * @return \App\Providers\FulfillAPI\Common\TokenRetriever
     */
    public function tokenRetriever(): TokenRetriever
    {
        return app(\App\Providers\FulfillAPI\Lenful\TokenRetriever::class)
            ->withConfigArray($this->config);
    }

    /**
     * @throws Throwable
     */
    public function setParams($params): void
    {
        $this->setHeader('Authorization', 'Bearer ' . $this->tokenRetriever()->retrieve());
        $this->refreshApi();

        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->setApiWithEndpoint();
                $this->api .= sprintf($this->config['endpoints']['order_create'], $this->config['store_id']);
                $this->params = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url = 'POST';
                break;

            case self::METHOD_CANCEL_ORDER:
                $this->setApiWithEndpoint();
                $this->api .= $this->config['endpoints']['order_cancel'];
                $this->params = [$params->getFulfillOrderId()];
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url = 'PUT';
                break;

            case self::METHOD_CRAWL_ORDER:
                $this->setApiWithEndpoint();
                $this->api .= $params;
                $this->method_url = 'GET';
                $this->fulfill_order_id = $params;
                break;

            case self::METHOD_CRAWL_PRODUCT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->params = $params;
                $this->api .= '?' . http_build_query($params);
                $this->method_url = 'GET';
                break;

            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api .= $params['sku'];
                $this->method_url = 'GET';
                break;
        }
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::crawlProductsAndProductVariantsJob()
     *
     * @throws Exception
     * @throws \Throwable
     */
    public function crawlProductsAndProductVariantsJob(): void
    {
        $this->crawlProductsWithVariants();
    }

    /**
     * @throws \Throwable
     */
    public function sendData($body = null, ?string $userId = null, $saveLog = true)
    {
        $this->setHeader('Authorization', 'Bearer ' . $this->tokenRetriever->retrieve());

        return parent::sendData($body, $userId, $saveLog);
    }
}
