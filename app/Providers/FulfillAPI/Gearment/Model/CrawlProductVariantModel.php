<?php

namespace App\Providers\FulfillAPI\Gearment\Model;

use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductVariantModel extends AbstractModel
{
    public static function mapping(array $variant, array $options): array
    {
        return [
            'sku'          => $variant['variant_id'],
            'base_cost'    => $variant['net_price'] ?? $variant['price'],
            'variant_key'  => getVariantKey($options),
            'out_of_stock' => self::getOutOfStock($variant['availability_status']),
        ];
    }
}
