<?php

namespace App\Providers\FulfillAPI\MonsterDigital;

use App\Providers\FulfillAPI\AbstractSyncVariantOOS;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class SyncVariantOOS extends AbstractSyncVariantOOS
{
    /**
     * @param     \Illuminate\Support\Collection     $variants     *
     *
     * @return \Illuminate\Support\Collection
     *
     * @throws \JsonException
     */
    public function fetchData(Collection $variants): Collection
    {
        $cfg = $this->getSupplierConfig();
        $SKUs = json_encode($variants->pluck('sku')->toArray(), JSON_THROW_ON_ERROR);
        $payload = [
            'key' => $cfg['token'],
            'data' => $SKUs,
            'signature' => sha1($cfg['secret'] . $cfg['token'] . $SKUs),
        ];
        $responses = Http::post(
            $cfg['api'] . '/product/GetInventoryBySKUs',
            $payload
        );

        return collect($responses->json() ?: [])->keyBy('upc')->map(
            fn($response, $sku) => $this->parseStockQty($response, $sku)
        );
    }

    /**
     * @param                $response
     * @param     string     $sku
     *
     * @return int|null
     */
    public function parseStockQty($response, string $sku): ?int
    {
        if (! is_numeric($qty = data_get($response, 'available_qty'))) {
            return null;
        }

        return (int) $qty;
    }
}
