<?php

namespace App\Providers\FulfillAPI\Textildruck;

use App\Providers\FulfillAPI\AbstractSyncVariantOOS;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class SyncVariantOOS extends AbstractSyncVariantOOS
{
    /**
     * @param     \Illuminate\Support\Collection     $variants     *
     *
     * @return PromiseInterface|Response
     */
    public function fetchData(Collection $variants): \Illuminate\Support\Collection
    {
        $responses = Http::pool(function(Pool $pool) use($variants) {
            $cfg = $this->getSupplierConfig();
            $auth = [
                "X-API-Key" => $cfg['key'],
                "ClientSecret" => $cfg['secret']
            ];

            $variants->map(
                fn($v) => $pool->as($v->sku)->withHeaders($auth)->get($cfg['api'] . '/stock/' . $v->sku)
            );
        });

        return collect($responses)->map(
            fn($response, $sku) => $this->parseStockQty($response, $sku)
        );
    }

    /**
     * @param                $response
     * @param     string     $sku
     *
     * @return mixed
     */
    public function parseStockQty($response, string $sku): ?int
    {
        try {
            $qty = data_get($response->json(), $sku);

            if (! is_numeric($qty)) {
                return null;
            }

            return (int) $qty;
        } catch (\Throwable $e) {
            return null;
        }
    }
}
