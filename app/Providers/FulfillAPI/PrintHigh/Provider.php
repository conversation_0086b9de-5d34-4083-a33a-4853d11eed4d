<?php

namespace App\Providers\FulfillAPI\PrintHigh;

use App\Models\FulfillProduct;
use App\Providers\FulfillAPI\ObjectFulfill;
use Throwable;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/orders/';
    protected const ENDPOINT_CRAWL_ORDER = '/orders/'; ///orders/{orderId}
    protected const ENDPOINT_CRAWL_PRODUCT = '/public/catalog';

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params = ''): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->api              .= $params;
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
            case self::METHOD_CRAWL_PRODUCT:
                $this->method_url = 'GET';
                break;
        }
        $this->api .= '?token=' . $this->token;
    }

    public function crawlProductsAndProductVariantsJob(): void
    {
        $objectCrawlProduct = $this;
        $objectCrawlProduct->setMethod($this::METHOD_CRAWL_PRODUCT);

        $responseModel = $objectCrawlProduct->getModel(self::MODEL_RESPONSE);
        $objectCrawlProduct->setParams();

        $arr                 = [];
        $this->arr_new_color = [];
        $objectProductModel  = $objectCrawlProduct->getModel(self::MODEL_CRAWL_PRODUCT);
        $response            = $objectCrawlProduct->sendData(null, null, false);
        $responseModel->mappingCrawlProducts($response);
        $data = $responseModel->getResponseData();
        foreach ($data as $each) {
            try {
                $object  = clone $objectProductModel;
                $product = $object->mapping($each);
                if (!$this->checkAllowCrawlProduct($product)) {
                    continue;
                }
                if (!empty($object->getNewColors())) {
                    foreach ($object->getNewColors() as $color) {
                        $this->arr_new_color[] = $color;
                    }
                }
                $arr[] = $product;
            } catch (Throwable $e) {
                logException($e, __FUNCTION__, 'fulfill_product', true);
                continue;
            }
        }
        $this->arr_product_id_insert = [];
        $this->arr_product_sku       = [];
        $this->arr_variant           = [];
        foreach ($arr as $product) {
            $productSku  = $product['sku'];
            $product     = FulfillProduct::updateOrCreate(
                [
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ],
                $product
            );
            $variantKeys = generateVariantKeysByProductOptions(json_decode($product['options'], true));

            foreach ($variantKeys as $variantKey) {
                $this->arr_variant[] = [
                    'variant_key' => $variantKey,
                    'product_id'  => $product->id,
                    'sku'         => $productSku,
                ];
            }

            $this->arr_product_sku[]       = $productSku;
            $this->arr_product_id_insert[] = $product->id;
        }
        $this->handleAfterCrawlProduct();
        $this->handleNewColor();
        $this->handleAfterCrawlVariant();
    }
}
