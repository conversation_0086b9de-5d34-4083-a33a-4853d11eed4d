<?php

namespace App\Providers\FulfillAPI\Yoycol\Model;

use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CreateOrderModel extends AbstractModel
{
    public array $order = [
        'details'   => [],
        'ecOrderSn' => '',
    ];
    public string $shippingLevelStr;
    public array $recipientInfo = [];

    public function setOrder($order): void
    {
        $this->order['ecOrderSn'] = $this->fulfill_order_id = $this->getReferenceId($order, '.');

        $this->setShippingLevel();

        $this->recipientInfo = [
            'addressLine1'   => $this->customer_address['full'],
            'addressLine2'   => $this->customer_address['addition'],
            'city'           => $order->city ?? '',
            'countryIsoCode' => $order->country ?? '',
            'email'          => $order->customer_email ?? '',
            'firstName'      => $order->customer_name,
            'phone'          => $order->customer_phone ?? '',
            'postcode'       => $order->postcode ?? '',
            'stateCode'      => $order->state ?? '',
        ];
    }

    private function setShippingLevel(): void
    {
        $this->shippingLevelStr = Str::headline($this->mapped_shipping_methods[0]);
    }

    public function setItems($orderProduct): void
    {
        [$mockup, $design] = $this->getImages($orderProduct->files);

        $this->order['details'][] = [
            'ecSkuCode'      => $orderProduct->id,
            'skuCode'        => $orderProduct->fulfill_sku,
            'effectImages'   => [$mockup],
            'materialImages' => [$design],
            'quantity'       => $orderProduct->quantity,
        ];
    }

    private function getImages($files): array
    {
        $file = $files->first();

        return [$file->mockup_url, $file->design_url];
    }
}
