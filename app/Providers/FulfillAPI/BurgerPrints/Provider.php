<?php

namespace App\Providers\FulfillAPI\BurgerPrints;

use App\Providers\FulfillAPI\ObjectFulfill;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/v2';
    protected const ENDPOINT_CANCEL_ORDER = '/v1/cancel';
    protected const ENDPOINT_CRAWL_ORDER = '/v1/'; // /v1/{id}?api_key={token}&sandbox={bool}
    protected const ENDPOINT_CRAWL_ORDER_DETAILS = '/v2/check-log/'; // /v2/check-log/{id}

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $data                   = $params->getPublicAttrs();
                $data['api_key']        = $this->token;
                $this->params           = $data;
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $data                   = $params->getPublicAttrs();
                $data['api_key']        = $this->token;
                $data['sandbox']        = $this->mode !== 'production';
                $this->params           = $data;
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->api              .= $params;
                $this->api              .= '?api_key=' . $this->token;
                $this->api              .= '&sandbox=' . ($this->mode !== 'production' ? 'true' : 'false');
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
            case self::METHOD_CRAWL_ORDER_DETAILS:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api        .= $params;
                $this->method_url = 'GET';
                break;
        }
    }

    public function crawlOrder($fulfillOrderId, $orderId = null, $justCreated = false): array
    {
        if ($justCreated === true) {
            $this->setMethod($this::METHOD_CRAWL_ORDER_DETAILS);
        } else {
            $this->setMethod($this::METHOD_CRAWL_ORDER);
        }
        $this->setParams($fulfillOrderId);
        $this->setOrderId($orderId);
        $responseModel = $this->getModel(self::MODEL_RESPONSE);
        $response      = $this->sendData();

        return $responseModel->mappingCrawlOrder($response, $fulfillOrderId, $justCreated);
    }
}
