<?php

namespace App\Providers\FulfillAPI\BeefulUk\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\BeefulV2WebhookRepository;
use Illuminate\Support\Arr;
class ResponseModel extends AbstractResponseModel
{
    public const CANCELED = 'cancelled'; // Order is canceled by user
    public const ON_HOLD = 'onhold'; // Order is on hold due from service team for whatever reason
    public const IN_PRODUCTION = 'in_production'; // Order is currently processing by the system
    public const DOWNLOADED = 'downloaded'; // Order access are downloaded and waiting for submition
    public const ERROR = 'ERROR'; // Order has some error, probally due to processing fail ( system will send webhook or user can query for status to gather error )
    public const DRAFT = 'DRAFT'; // Order is in your waiting list but not yet ordered
    public const ORDERED = 'ordered'; // Order is in order list and waiting for our service team to accept ( API submition is automatically send to Order List after successfully DOWNLOADED)
    public const TRANSIT = 'transit'; // Order is in transit - aka shipping to customer
    public const DELIVERED = 'delivered'; // Order is delivered to customer
    public const SHIPPED = 'shipped';
    public const PRESSED = 'pressed';
    public const LABEL_PRINTED = 'label_printed';
    public const REPRINT = 'reprint';
    public const PRINTED = 'printed';
    public const NEW = 'new';
    public const TEST = 'test';
    public const REFUNDED = 'refunded';

    protected string $orderExistedRegex = '/Order (\d+(?:-\d+)?) already created/';

    /**
     * @param array $item
     *
     * @return array
     */
    private function findProduct(array $item): array
    {
        if (!array_key_exists('isGroup', $item)) {
            return [];
        }

        if ($item['isGroup'] === false) {
            return [$item];
        }

        if (!empty($item['variants'])) {
            return $this->variantsLooper($item['variants']);
        }

        return [];
    }

    /**
     * @param array $variants
     *
     * @return mixed
     */
    private function variantsLooper(array $variants): array
    {
        return array_reduce($variants, function ($carry, $item) {
            if (!is_array($item)) {
                return $carry;
            }

            return [...$carry, ...$this->findProduct($item)];
        }, []);
    }

    public function mappingCrawlProducts(array $array): void
    {
        $this->response_data = $this->variantsLooper($array);
    }

    /**
     * @return array
     */
    public function getParamsPagination(): array
    {
        return [];
    }

    /**
     * @return bool
     */
    public function getNexPage(): bool
    {
        return false;
    }

    /**
     * Hiện taị API bên này có vẻ chưa hoàn thiện, còn nhiều lỗi vặt,
     * response không giống doc. Cần theo dõi để bổ sung thêm
     *
     * @param $arr
     *
     * @return void
     */
    protected function setErrors($arr): void
    {
        if (!$order = $arr ?? null) {
            $this->errors[] = 'Missing response';
            return;
        }

        $message = Arr::get($arr, 'message');
        $error = Arr::get($arr, 'error');
        if (!empty($message) && !empty($error)) {
            $this->errors[] = $message;
            return;
        }

        $responseCode = Arr::get($arr, 'code');
        if (empty($responseCode)) {
            $this->errors[] = 'Not found order code !';
            return;
        }

        if (
            !empty($this->fulfill_order_id) &&
            $this->fulfill_order_id !== explode('-', $responseCode)[0] &&
            $this->fulfill_order_id !== $responseCode
        ) {
            $this->errors[] = 'Fulfill Order ID not found !';
            return;
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
//        return (bool) data_get($arr[0] ?? [], 'trackingRef');
        $responseCode = data_get($arr, 'code');
        return isset($responseCode);
    }

    /**
     * @override AbstractResponseModel::mappingCreateOrder()
     *
     * @param array $arr
     * @param               $orderProductIds
     * @param               $fulfillOrderId
     *
     * @return void
     */
    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $this->isFulfillOrderIdExisted ?? $arr['code'],
            OrderProductFulfillStatus::PENDING,
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->fulfill_order_id = $fulfillOrderId;
        if (!$this->handleError('Crawl Order', $arr, $fulfillOrderId)) {
            return [];
        }
        return (new BeefulV2WebhookRepository($arr, $this->supplier_id))
            ->withCrawlMode()
            ->get();
    }
}
