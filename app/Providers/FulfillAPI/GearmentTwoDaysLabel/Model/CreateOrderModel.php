<?php

namespace App\Providers\FulfillAPI\GearmentTwoDaysLabel\Model;

use App\Enums\CountryEnum;
use App\Enums\FbaFulfillBy;

class CreateOrderModel extends \App\Providers\FulfillAPI\Gearment\Model\CreateOrderModel
{
    public string $shipping_label_link;
    public string $shipping_service_type;
    public bool $agree_at_risk = false;

    /**
     * @param $order
     *
     * @return void
     */
    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->external_id = $this->order_id = $this->getReferenceId($order);

        $this->shipping_label_link = s3Url($order->shipping_label);

        if (CountryEnum::isEu($order->country)) {
            $this->ioss_number = $order->ioss_number ?? 'IN2330015967'; // ioss number for EU
        }
    }

    public function setItems($product): void
    {
        /** @var \App\Models\OrderProduct $product */
        $this->shipping_service_type = match($product->fulfill_fba_by) {
            FbaFulfillBy::BY_AMAZON => 'Fba',
            FbaFulfillBy::BY_POSHMARK => 'Poshmark',
            FbaFulfillBy::BY_TIKTOK => 'Tiktok',
            FbaFulfillBy::BY_ETSY => 'Etsy',
            FbaFulfillBy::BY_EBAY => 'Ebay',
            default => throw new \RuntimeException('Shipping service type error')
        };

        $arr = [
            'variant_id' => $product->fulfill_sku,
            'quantity'   => $product->quantity
        ];

        if ($this->shipping_service_type === 'Fba') {
            $arr['barcode_link'] = s3Url($product->barcode);
        }

        $this->setImages($arr, $product);

        $this->line_items[] = $arr;
    }
}
