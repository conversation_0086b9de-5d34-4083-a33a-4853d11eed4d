<?php

namespace App\Providers\FulfillAPI\PrintArrow\Model;

use App\Models\OrderProduct;
use App\Models\Product;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CreateOrderModel extends AbstractModel
{
    private array $payload;

    /*
     curl --location 'https://printarrows.com/rest/V1/vendors/order/import' \
        --data '{
            "orderData": {
                "firstname": "<PERSON>",
                "lastname": "Nunez",
                "country_id": "US",
                "address1": "3450 Riverstone Court",
                "address2": "Apt. 1231",
                "city": "Fort Worth",
                "region": "TX",
                "postcode": "76116",
                "seller_order_id": "576724930646938345jGY",
                "prepaid_label": "",
                "shipment_id": "",
                "shipping_method": "standard",
                "items": [
                    {
                        "qty": 1,
                        "product_id": "Classic T-Shirt",
                        "size": "L",
                        "color": "Military Green",
                        "designs": [
                            {
                                "side_name": "Right",
                                "design": "https://drive.google.com/file/d/1cY4T5F4NlRGa05AuTS-09C25fgFL1emA/view",
                                "emb": "https:\/\/d2hqirjcnz1l7u.cloudfront.net\/products\/d-572671-d-Tu5Ac-mrs-nunez-4500x5100.png",
                                "mockup": "https:\/\/d2hqirjcnz1l7u.cloudfront.net\/products\/d-572671-d-Tu5Ac-mrs-nunez-4500x5100.png",
                                "comment": "Please see the mockup closely before you start production"
                            },
                            {
                                "side_name": "Front",
                                "design": "https://drive.google.com/file/d/1cY4T5F4NlRGa05AuTS-09C25fgFL1emA/view",
                                "emb": "https:\/\/d2hqirjcnz1l7u.cloudfront.net\/products\/d-572671-d-Tu5Ac-mrs-nunez-4500x5100.png",
                                "mockup": "https:\/\/d2hqirjcnz1l7u.cloudfront.net\/products\/d-572671-d-Tu5Ac-mrs-nunez-4500x5100.png",
                                "comment": ""
                            }
                        ]
                    }
                ]
            }
        }
     *
     * @param $order
     *
     * @return void
     */
    public function setOrder($order): void
    {
        $shippingLabel = $order->shipping_label;
        $isLabelOrder = !empty($shippingLabel);
        $label = "";
        $trackingCode = "";
        if ($isLabelOrder) {
            $label = s3Url($shippingLabel) ;
            $firstProduct = $order->products->first();
            $trackingCode = $firstProduct->tracking_code ?? "";
        }
        $this->payload = [
            "orderData" => [
                "firstname" => !empty($this->customer_name['first_name']) ? $this->customer_name['first_name'] : '_',
                "lastname" => $this->customer_name['last_name'],
                "country_id" => $order->country_info->code,
                "address1" => $this->customer_address['primary'],
                "address2" => $this->customer_address['addition'],
                "city" => $this->customer_address['city'],
                "region" => $this->customer_address['state_code'] ?: 'OK',
                "postcode" => Str::length($order->postcode) !== 9 ? substr($order->postcode, 0, 5) : $order->postcode,
                "seller_order_id" =>  $this->fulfill_order_id = $this->order_id = $this->getReferenceId($order),
                "prepaid_label" => $label,
                "shipment_id" => $trackingCode,
                "shipping_method" => "standard",

            ],
        ];
    }

    /**
     * @param OrderProduct $value
     * @return $this
     */
    public function setItems(OrderProduct $value): \App\Providers\FulfillAPI\PrintArrow\Model\CreateOrderModel
    {
        $this->payload['orderData']['items'][] = $this->makeItems($value);

        return $this;
    }


    /**
     * @return array
     */
    public function getPublicAttrs(): array
    {
        return $this->payload;
    }

    /**
     * @param     \App\Models\OrderProduct     $op
     *
     * @return array
     */
    private function makeItems(OrderProduct $op): array
    {
        $supProduct = Product::query()
            ->where('supplier_id', $op->supplier_id)
            ->where('id', $op->fulfill_product_id)
            ->first();
        $size = trim(data_get($op->options, 'size'), '_');
        $color = trim(data_get($op->options, 'color'), '_');
        $designs = $this->getDesignOnPrintSpaces($op->files);
        return [
            "qty" => $op->quantity,
            "product_id" => $supProduct ? data_get($supProduct, 'sku', '') : '',
            "size" => Str::upper($size),
            "color" => Str::title($color),
            "design_front" => $designs['front']?->design_url ,
            "design_back" => $designs['back']?->design_url,
        ];
    }

}
