<?php

namespace App\Providers\FulfillAPI\PrintArrow;

use App\Providers\FulfillAPI\Common\ConfigurableTrait;
use Illuminate\Support\Facades\Http;

class TokenRetriever extends \App\Providers\FulfillAPI\Common\TokenRetriever
{
    use ConfigurableTrait;

    /**
     * @return object
     * @throws \Throwable
     */
    public function login(): object
    {
        try {
            return Http::asJson()
                ->timeout(30)
                ->post($this->tokenEndpoint(), $this->tokenPayload());
        } catch (\Throwable $e) {
            graylogError($e->getMessage(), [
                'category' => 'token_retriever',
                'supplier_id' => $this->config('supplier_id'),
            ]);
            throw $e;
        }
    }

    /**
     * @param object $response
     *
     * @return string
     */
    protected function extractToken(object $response): string
    {
        return $response->object() ?? '';
    }

    /**
     * @return string
     */
    protected function tokenEndpoint(): string
    {
        return $this->config('api') . $this->config('endpoints.token');
    }

    /**
     * @return array
     */
    protected function tokenPayload(): array
    {
        return [
            'username' => $this->config('username'),
            'password' => $this->config('password')
        ];
    }
}
