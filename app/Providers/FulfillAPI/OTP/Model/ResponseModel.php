<?php

namespace App\Providers\FulfillAPI\OTP\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\OTPWebhookRepository;

class ResponseModel extends AbstractResponseModel
{

    public function mappingCrawlProducts(array $arr): void
    {
        $this->response_data = $arr['data'];
    }

    protected function setErrors($arr): void
    {
        if ($arr['status_code'] > 300) {
            $this->errors[] = implode(';', data_get($arr, 'data.message') ?: []);
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return true;
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $fulfillOrderId,
            OrderProductFulfillStatus::PENDING,
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        return (new OTPWebhookRepository($arr, $this->supplier_id))
            ->withCrawlMode()
            ->get();
    }
}
