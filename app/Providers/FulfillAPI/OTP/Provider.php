<?php

namespace App\Providers\FulfillAPI\OTP;

use App\Providers\FulfillAPI\ObjectFulfill;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/orders/createOrder';
    protected const ENDPOINT_CRAWL_ORDER = '/orders/states/getCurrentStates';
    protected const ENDPOINT_CANCEL_ORDER = '/orders/cancelOrder/';
    protected const ENDPOINT_CRAWL_PRODUCT_VARIANT = '/stock/stockLookup/';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("Authorization", "Basic " . base64_encode($provider['username'] . ':' . $provider['password']));
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params = null): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params           = ['Order' => $params->getPublicAttrs()];
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->params           = [
                    'Orders' => [(string) ($this->fulfill_order_id = $params)]
                ];
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api           .= $params->getOrderId();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->params = [
                    'StockLookup' => [
                        'AllProducts' => true,
                        "Stock"       => "Hrbovice",
                    ]
                ];
                break;
        }
    }

    public function setOrderId($orderId): void
    {
        $this->order_id = $orderId;
    }

    /**
     * @param $body
     * @param $statusCode
     *
     * @return array
     */
    protected function prepareResponse($body, $statusCode): array
    {
        return [
            'status_code' => $statusCode,
            'data'        => $body,
        ];
    }
}
