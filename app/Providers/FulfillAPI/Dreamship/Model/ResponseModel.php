<?php

namespace App\Providers\FulfillAPI\Dreamship\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\DreamshipWebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    protected int $limit_product_per_page = 50;

    public function getParamsPagination(): array
    {
        return [
            'page'      => ++$this->page,
            'page_size' => $this->limit_product_per_page,
        ];
    }

    public function mappingCrawlProducts(array $arr): void
    {
        $this->response_data = $arr['data'];
        $this->nextPage      = $arr['paging']['next'];
    }

    public function mappingCrawlProductVariants(array $arr): void
    {
        $this->response_data = [$arr];
    }

    public function getProductVariants(array $arr): array
    {
        $this->product_name = $arr['name'];
        return $this->response_product_variants = $arr['item_variants'];
    }

    public function getProductId(array $arr): string
    {
        return $this->product_id = $arr['id'];
    }

    protected function setErrors($arr): void
    {
        if (Arr::has($arr, 'error')) {
            $errors         = $arr['error'];
            $this->errors[] = Arr::get($errors, 'message');
            $this->errors[] = Arr::get($errors, 'rejected_status');
            foreach (Arr::get($errors, 'errors', []) as $error) {
                foreach (Arr::get($error, 'details', []) as $detail) {
                    $this->errors[] = $detail['message'] . ' - ' . $detail['key'];
                }
            }
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['id']) && !empty($arr['line_items']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIdsHandle, $fulfillOrderId = null): void
    {
        $this->fulfill_order_id = $arr['id'];
        foreach ($arr['line_items'] as $orderProduct) {
            $this->setOrderProducts(
                [
                    'sku'      => $orderProduct['item_variant'],
                    'quantity' => $orderProduct['quantity'],
                    'cost'     => $orderProduct['total_cost'],
                ]
            );
            if ($orderProduct['quantity'] !== $orderProduct['active_quantity']) {
                $this->logException(
                    'Create Order',
                    'Have different fulfill quantity from active quantity',
                    $fulfillOrderId
                );
            }
        }
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['id'])) {
            // @see docs/suppliers/Dreamship/readme.txt
            return (new DreamshipWebhookRepository($arr, $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
