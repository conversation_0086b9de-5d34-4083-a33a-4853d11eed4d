<?php

namespace App\Providers\FulfillAPI\Dreamship;

use App\Providers\FulfillAPI\ObjectFulfill;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/orders/';
    protected const ENDPOINT_CANCEL_ORDER = '/orders/'; // /orders/{id}/cancel/
    protected const ENDPOINT_CRAWL_ORDER = '/orders/';
    protected const ENDPOINT_CRAWL_ORDERS = '/orders/';
    protected const ENDPOINT_CRAWL_PRODUCT = '/items/';
    protected const ENDPOINT_CRAWL_PRODUCT_VARIANT = '/items/'; // /items/{id}/
    protected const ENDPOINT_CRAWL_SHIPPING_RULE = '/items/'; // /items/{id}/

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("Authorization", "Bearer " . $provider['token']);
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api              .= $params->getFulfillOrderId() . '/cancel/';
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->api              .= $params;
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
            case self::METHOD_CRAWL_PRODUCT:
                $this->params     = $params;
                $this->method_url = 'GET';
                break;
            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api        .= $params;
                $this->method_url = 'GET';
                break;
            case self::METHOD_CRAWL_ORDERS:
                $this->method_url       = 'GET';
                $idsString = implode(',', $params);
                $this->api              .= "?ids=$idsString";
                break;
        }
    }

    public function crawlOrders($fulfillOrderIds): array
    {
        $this->setMethod($this::METHOD_CRAWL_ORDERS);
        $this->setParams($fulfillOrderIds);
        $responseModel = $this->getModel(self::MODEL_RESPONSE);
        $response      = $this->sendData();

        $responseArr = [];
        if (isset($response['data'])) {
            foreach ($response['data'] as $item) {
                $item['response_status_code'] = $response['response_status_code'] ?? null;
                $responseArr[] = $item;
            }
        }

        $ordersCrawlData = [];

        foreach ($responseArr as $op) {
            $fulfillOrderId = isset($op['id']) ? $op['id'] : null;
            $ordersCrawlData[] = $responseModel->mappingCrawlOrder($op, $fulfillOrderId);
        }

        return $ordersCrawlData;
    }
}
