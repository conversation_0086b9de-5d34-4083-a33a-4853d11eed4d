<?php

namespace App\Providers\FulfillAPI\JonDo\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    protected function setErrors($arr): void
    {
        /** @noinspection TypeUnsafeComparisonInspection */
        $this->errors[] = $arr['status'] != '1' ? Arr::get($arr, 'message') : null;
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['poNumber']) && !$this->checkHasError($arr);
    }

    public function checkAndThrowExceptionWhenNoResponse($arr, $fulfillOrderId): void
    {
        $arr = $arr['orderReply'];
        parent::checkAndThrowExceptionWhenNoResponse($arr, $fulfillOrderId);
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $arr['orderReply']['code'],
        );
    }
}
