<?php

namespace App\Providers\FulfillAPI\JonDo\Model;

use App\Providers\FulfillAPI\AbstractModel;
use Exception;

class CreateOrderModel extends AbstractModel
{
    public string $shippingType;
    public int $testMode;
    public string $poNumber;
    public string $firstName;
    public string $lastName;
    public string $address;
    public string $address2;
    public string $city;
    public ?string $state;
    public string $zip;
    public string $country;
    public string $phoneNumber;
    public array $orderItems;

    public function setMode($mode): void
    {
        $this->testMode = $mode !== 'production';
    }

    /**
     * @throws Exception
     */
    public function setOrder($order): void
    {
        $this->setShippingMethod();
        $this->fulfill_order_id = $this->poNumber = $this->getReferenceId($order);

        $this->firstName   = $this->customer_name['first_name'];
        $this->lastName    = $this->customer_name['last_name'];
        $this->address     = $this->customer_address['primary'];
        $this->address2    = $this->customer_address['addition'];
        $this->city        = $order->city;
        $this->state       = $order->state;
        $this->country     = $order->country;
        $this->zip         = $order->postcode;
        $this->phoneNumber = !empty($order->customer_phone) ? $order->customer_phone : self::SUPPORT_PHONE;
    }

    private function setShippingMethod(): void
    {
        $this->shippingType = $this->mapped_shipping_methods[0];
    }

    public function setItems($product): void
    {
        $this->orderItems[] = [
            'qt'            => $product->quantity,
            'code'          => $product->fulfill_sku,
            'itemNumber'    => $product->id,
            'imageLocation' => $this->getImage($product),
        ];
    }

    private function getImage($product): string
    {
        foreach ($product->files as $file) {
            return $file->design_url;
        }

        throw new \RuntimeException('This order product ' . $product->id . ' don\'t have any design file');
    }
}
