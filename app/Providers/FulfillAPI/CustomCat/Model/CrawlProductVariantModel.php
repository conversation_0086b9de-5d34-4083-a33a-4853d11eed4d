<?php

namespace App\Providers\FulfillAPI\CustomCat\Model;

use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;
use Throwable;

class CrawlProductVariantModel extends AbstractModel
{
    private string $color;

    public function mapping(array $product, $productName): array
    {
        $arr         = [];
        $this->color = self::mappingOptions($product['color'], 'color');

        foreach ($product['skus'] as $each) {
            $size  = self::mappingOptions($each['size'], 'size', $productName);
            $print = self::mappingOptions('', 'print', $productName);

            $arr[] = [
                'variant_key'  => $this->getVariantKey($size, $print),
                'out_of_stock' => !$each['in_stock'],
                'base_cost'    => $each['cost'],
                'sku'          => $product['product_color_id'],
            ];
        }

        return $arr;
    }

    private function getVariantKey($size, $print): string
    {
        return getVariantKey(
            [
                $this->color,
                $size,
                $print,
            ]
        );
    }

    private const COLOR_MAPPING = [
        'baby blue' => 'light blue',
        'cardinal'  => 'cardinal red',
        "whiteorange" => 'orange accent',
        "whitered" => 'red accent',
        "whitelight blue" => 'light blue accent',
        "whitelight green" => 'light green accent',
        "whitepink" => 'light pink accent',
        "whiteblack" => 'black accent',
        "whitemidnight blue => 'midnight blue accent'"
    ];


    /** @noinspection PhpParameterNameChangedDuringInheritanceInspection */
    public static function mappingOptions($option, $type = 'all', $productName = '', $hadParsed = false): string
    {
        $optionNotToMap = false;
        try {
            $option = parent::parseOption($option);

            switch ($type) {
                case 'color':
                    $arrMapping = self::COLOR_MAPPING;
                    break;
                case 'size':
                    if ($option === 'one size') {
                        if ((strpos($productName, 'Mug') !== false) && strpos($productName, 'oz') !== false) {
                            // ex: ... 11 oz Mug => 11oz
                            preg_match('/\d+ ?oz/', $productName, $matches);
                            $option = str_replace(' ', '', $matches[0]);
                        } elseif (strpos($productName, 'Blanket') !== false) {
                            // ex: ... Blanket 30x40 => 30x40in
                            $option = trim(Str::afterLast($productName, ' ')) . 'in';
                        } elseif (strpos($productName, 'Ornament') !== false) {
                            // ex: ... Ornament one size => ''
                            $option = '';
                        } elseif (strpos($productName, 'Face Mask') !== false) {
                            if (strpos($productName, 'Med/Lg') !== false) {
                                $option = 'l';
                            } elseif (strpos($productName, 'Sm/Med') !== false) {
                                $option = 'm';
                            }
                        }
                    } else if ($option === 'newborn') {
                        $option = 'new born';
                        $optionNotToMap = true;
                    } else {
                        // ex: yx => x | yxl => xl
                        $option = Str::remove('y', $option);
                    }
                    break;
                case 'print':
                    if (strpos($productName, 'Ornament') !== false) {
                        // ex: ... Ornament => 'one sided'
                        $option = 'one sided';
                    }
                    break;
            }
            $option = $arrMapping[$option] ?? $option;
        } catch (Throwable $e) {
            logToDiscord(
                __FUNCTION__
                . "\r\nException:" . $e->getMessage()
                . "\r\nOption:" . $option
                . "\r\nType:" . $type
                . "\r\nProduct Name:" . $productName
                . "\r\nHad Parsed:" . $hadParsed
                , 'fulfill_product'
                , true
            );
        }

        if ($optionNotToMap) {
            return $option;
        }

        return parent::mappingOptions($option, $type);
    }
}
