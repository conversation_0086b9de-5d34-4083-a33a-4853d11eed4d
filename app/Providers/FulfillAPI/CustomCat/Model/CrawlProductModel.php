<?php

namespace App\Providers\FulfillAPI\CustomCat\Model;

use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductModel extends AbstractModel
{
    private string $thumb_url;
    private array $variants = [];

    public function mapping(array $product): array
    {
        return [
            'sku'      => $product['catalog_product_id'],
            'product'  => [
                'product_type' => $this->product_type,
                'name'         => $product['product_name'],
                'options'      => $this->getOptions($product),
                'description'  => $this->getDescription($product),
                'thumb_url'    => $this->thumb_url,
            ],
            'variants' => $this->variants,
        ];
    }

    private function getDescription(array $product): string
    {
        $prefix = 'product_description_bullet';
        $string = '';
        foreach ($product as $key => $value) {
            if (strpos($key, $prefix) !== false) {
                $string .= $value;
            }
        }

        return $string;
    }

    private function getOptions(array $product): string
    {
        $arr           = [];
        $arr['color']  = [];
        $arr['size']   = [];
        $arr['print']  = [];
        $arrVariantKey = [];

        $print = CrawlProductVariantModel::mappingOptions('', 'print', $product['product_name']);
        if (!empty($print) && !in_array($print, $arr['print'])) {
            $arr['print'][] = $print;
        }

        foreach ($product['product_colors'] as $option) {
            $color          = CrawlProductVariantModel::mappingOptions($option['color'], 'color');
            $arr['color'][] = $color;

            foreach ($option['skus'] as $sku) {
                $size = CrawlProductVariantModel::mappingOptions(
                    $sku['size'],
                    'size',
                    $product['product_name']
                );
                if (!empty($size) && !in_array($size, $arr['size'])) {
                    $arr['size'][] = $size;
                }

                $variantKey = getVariantKey([$color, $size, $print]);
                if (!in_array($variantKey, $arrVariantKey)) {
                    $this->variants[] = [
                        'variant_key'  => $variantKey,
                        'out_of_stock' => !$sku['in_stock'],
                        'base_cost'    => $sku['cost'],
                        'sku'          => $sku['catalog_sku_id'],
                    ];
                    $arrVariantKey[]  = $variantKey;
                }
            }
            if (empty($this->thumb_url) && !empty($option['product_image'])) {
                $this->thumb_url = str_replace('//', '', $option['product_image']);
            }
        }
        // remove empty value
        $arr = array_filter($arr);

        return json_encode($arr);
    }
}
