<?php

namespace App\Providers\FulfillAPI\Printway_v2\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public string $order_id;
    public string $shipping_name;
    public string $firstName;
    public string $lastName;
    public string $shipping_phone;
    public string $shipping_email;
    public string $shipping_address1;
    public string $shipping_address2;
    public string $shipping_city;
    public string $shipping_zip;
    public string $shipping_province;
    public string $shipping_province_code;
    public string $shipping_country;
    public string $shipping_country_code;
    public string $shipping_service;
    public array $order_items = [];

    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->order_id = $this->getReferenceId($order);

        $this->shipping_name          = $order->customer_name ?? '';
        $this->firstName              = $this->customer_name['first_name'] ?? '';
        $this->lastName               = $this->customer_name['last_name'] ?? '';
        $this->shipping_phone         = $order->customer_phone ? convertPhoneNumber($order->customer_phone, $order->country) : '';
        $this->shipping_email         = $order->customer_email ?? '';
        $this->shipping_address1      = $this->customer_address['primary'];
        $this->shipping_address2      = $this->customer_address['addition'];
        $this->shipping_province      = $this->customer_address['state'];
        $this->shipping_province_code = $this->customer_address['state_code'];
        $this->shipping_city          = $this->customer_address['city'];
        $this->shipping_zip           = $order->postcode;
        $this->shipping_country       = $order->country_info->name;
        $this->shipping_country_code  = $order->country;
        $this->shipping_service       = $this->mapped_shipping_methods[0];
    }

    public function setItems($product): void
    {
        $arr = [
            'product_name'       => $product->product_name,
            'variant_title'      => $this->getVariantTitle($product->fulfill_sku),
            'quantity'           => $product->quantity,
            'variant_id'         => $product->fulfill_sku,
            'variant_note'       => '',
        ];
        $this->setImages($arr, $product->files);

        $this->order_items[] = $arr;
    }

    // PW-MALORM-DEFAULTSIZE-PACK1-ONESIDE
    // => DEFAULTSIZE / PACK1 / ONESIDE
    private function getVariantTitle($sku): string
    {
        $arr = explode('-', $sku);

        return implode(' / ', array_slice($arr, 2));
    }

    private function setImages(&$arr, $files): void
    {
        $arr['artwork_front'] = '';
        $arr['artwork_back']  = '';
        $arr['url_mockup']    = null;

        foreach ($files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES,
            );

            if ($printSpace === 'artwork_front') {
                $arr['artwork_front'] = $file->design_url;
                $arr['url_mockup']    ??= $file->mockup_url;
            } elseif ($printSpace === 'artwork_back') {
                $arr['artwork_back'] = $file->design_url;
                $arr['url_mockup']   ??= $file->mockup_url;
            }
        }
    }
}
