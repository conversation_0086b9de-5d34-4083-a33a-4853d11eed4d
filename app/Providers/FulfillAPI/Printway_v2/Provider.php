<?php

namespace App\Providers\FulfillAPI\Printway_v2;

use App\Providers\FulfillAPI\ObjectFulfill;
use Exception;

/**
 * @HaMyPrintWay
 *  - 1 variant bên em có thể sản xuất ở nhiều quốc gia, 1 quốc gia có thể có nhiều xưởng sản xuất.
 *    (made_in_location ==> quốc gia sản xuất, product_location ==> xưởng sản xuất)
 *  - Mỗi một sản phẩm/ variant sẽ có vùng in khác nhau anh ạ
 *  Q: còn location thì không phải là hỗ trợ vận chuyển đúng k ạ? tức là mình ship worldwide ạ?
 *  A: Trong mỗi khu vực xưởng sản xuất khác nhau thì sẽ có dịch vụ ship khác nhau
 *     Phần location này là bổ sung so với app cũ ở chỗ bên em ghép nhiều sản phẩm có tính chất
 *       tương tự nhau mà được sản xuất ở nhiều khu vực thôi anh ạ
 *       Vd như Classic T-shirt trước đó bên em phải tạo 4 sản phẩm cho 4 khu vực US, UK, EU, AU.
 *       Nhưng bây giờ thì chỉ có 1 mã sản phẩm là Classic T-shirt, khách hàng chỉ chọn location
 *       thôi là được thay vì phải chọn và tìm 4 mã sản phẩm khác nhau
 *  Q: cho em hỏi danh sách các area trong print_areas bên mình hỗ trợ những phương thức ship nào ạ?
 *  A: mình gửi các vị trí in nha
 *  'artwork_front', 'artwork_back', 'artwork_right', 'artwork_left', 'artwork_hood', 'artwork_bothsides'
 *   các phương thức vận chuyển bên mình thì đã có trong api product
 *  Q: Mình có danh sách phương thức vận chuyển không ạ? Hay theo từng product ạ?
 *  A: Bên mình sẽ ship theo product, để mình gửi bạn thông tin
 *       Made in VN
 *      - US Standard                           US
 *      - International                         ITN
 *      - AU Standard                           AUSTD
 *      - US Priority                           USP
 *
 *       Made in AU
 *      - AU Standard                           AUSTD
 *      - AU Express                            AUEX
 *      - AU International (New Zealand)        AUIT
 *
 *       Made in US
 *      - US Standard                           US
 *      - US Ground                             USGR
 *      - US 2 Days                             US2DAYS
 *      - International                         USIN
 *
 *       Made in CN
 *      - CN Standard                           CNS
 *      - International                         CNIN
 *      - CN Express                            CNE
 * Q: Khi mình tạo đơn qua API thì mình truyền qua key nào vậy ạ?
 * A: Bạn truyền mã code của phương thức vận chuyển qua field shipping_service giúp mình nha (ví dụ: International => ITN),
 * trong các api product thì đều có trả ra shipping service code ở trong đó rồi, bạn có thể kiểm tra ở trong đó giúp mình nha
 */
class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/order/create-new-order';
    protected const ENDPOINT_CANCEL_ORDER = '/order/cancel-order-api';
    protected array $allow_crawl_products_name = [
        'Led Acrylic Ornament',
        'Led Car Ornament',
        'Transparent Acrylic Ornament',
        'Transparent Acrylic Car Ornament',
        'Custom Shape Car Ornament',
        'Aluminum Ornament',
        'Ceramic Ornament',
        'Suncatcher Ornament',
        'Acrylic Ornament',
        'Wooden Ornament',
        '2 Layered Piece Wooden Ornament',
        '3 Layered Wooden Ornament',
        '2 Layered Wooden Ornament',
        '2 Layered Mix Piece Ornament',
        '2 Layered Mix Ornament',
        'Custom Shape Acrylic Ornament',
        'Custom Shape Wooden Ornament',
        '3 Layered Christmas Shaker Ornament',
        '5 Layered Christmas Shaker Ornament',
        'Glass Ornament',
        'Letter Holder Ornament',
        'Glitter Plastic Ornament',
        'Wooden Pallet Sign',
        'Rectangle Wooden Sign',
        'Leather Photo Keychain',
        'Custom Shape Acrylic Keychain',
        'Custom Shape Wooden Keychain',
        'Aluminium Keychain',
        'Custom Shape Wood Standing',
        'Round Wooden Sign - 9mm'
    ];

    public array $product_allow_refactor_option = [
        'Round Wooden Sign - 9mm',
        'Round Wooden Sign - 9mm - original',
        'Round Wooden Sign - 9mm - black'
    ];

    public array $product_not_allow_translate_options = [
        'Custom Shape Acrylic Keychain',
    ];

    protected const ENDPOINT_CRAWL_ORDER = '/order/detail';

    protected const ENDPOINT_CRAWL_PRODUCT = '/products/list';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("PW-ACCESS-TOKEN", $provider['token']);
    }

    /**
     * @override ObjectFulfill::setParams()
     *
     * @param $params
     *
     * @return void
     */
    public function setParams($params = ''): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->setApiWithEndpoint();
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->params           = [
                    'pw_order_id' => $params
                ];
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
            case self::METHOD_CRAWL_PRODUCT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->method_url = 'GET';
                $this->params = $params;
                break;

            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->params = $params;
                break;
        }
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::fetchData()
     *
     * @return array|mixed|null
     *
     * @throws \Exception
     */
    public function fetchData()
    {
        if ($this->method === static::METHOD_CRAWL_PRODUCT_VARIANT) {
            return $this->params['variants'];
        }

        return parent::fetchData();
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::crawlProductsAndProductVariantsJob()
     *
     * @throws Exception
     * @throws \Throwable
     */
    public function crawlProductsAndProductVariantsJob(): void
    {
        $this->crawlProductsWithVariants();
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::skipProduct()
     *
     * @param    array    $item
     *
     * @return bool
     */
    protected function skipProduct(array $item): bool
    {
        return ! $this->checkAllowCrawlProduct($item['product_name']);
    }
}
