<?php

namespace App\Providers\FulfillAPI\SwiftPOD\Model;

use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductVariantModel extends AbstractModel
{
    public function mapping(array $array): array
    {
        $sku = $array['sku'];

        $color = self::mappingOptions($array['color'], 'color');
        $size  = self::mappingOptions($array['size'], 'size');

        return [
            'name'         => $array['name'],
            'product_type' => $this->product_type,
            'supplier_id'  => $this->supplier_id,
            'sku'          => $array['style'],
            'color'        => $color,
            'size'         => $size,
            'variant'      => [
                'sku'          => $sku,
                'variant_key'  => getVariantKey([$color, $size]),
                'out_of_stock' => self::getOutOfStock($array['discontinued']),
            ]
        ];
    }

    protected static function getOutOfStock($status): bool
    {
        return $status;
    }
}
