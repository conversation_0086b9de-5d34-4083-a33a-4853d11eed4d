<?php

namespace App\Providers\FulfillAPI\QTCO;

use App\Providers\FulfillAPI\ObjectFulfill;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/order';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("Authorization", 'Bearer ' . $provider['token']);
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params = null): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
        }
    }
}
