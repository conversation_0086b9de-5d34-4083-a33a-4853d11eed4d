<?php

namespace App\Providers\FulfillAPI\MWW\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\MWWWebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    protected function setErrors($arr): void
    {
        $this->errors[] = Arr::get($arr, 'error');
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['data']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $arr['data']['id'],
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['data']['id'])) {
            // @see docs/suppliers/MWW/readme.txt
            return (new MWWWebhookRepository($arr, $this->supplier_id))
                ->setSupplierOrderId($fulfillOrderId)
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
