<?php

namespace App\Providers\FulfillAPI\MWW\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public array $data = [];
    public array $included = [];
    private float $weight = 0;

    public function setOrder($order): void
    {
        $this->data['type']                    = 'orders';
        $this->data['attributes']['vendor-po'] = $this->fulfill_order_id = $this->getReferenceId($order);
        $this->setAddress($order);
    }

    private function setAddress($order): void
    {
        $this->included[] = [
            'type'       => 'shipping-address',
            'attributes' => [
                'name'        => $order->customer_name,
                'address1'    => $order->address,
                'address2'    => $order->address_2,
                'city'        => $order->city,
                'state'       => $order->state,
                'country'     => $order->country,
                'postal-code' => $order->postcode,
                'email'       => $order->customer_email ?? '',
                'phone'       => $order->customer_phone ?? '',
            ]
        ];
    }

    public function setItems($product): void
    {
        $this->included[] = [
            'type'       => 'line-items',
            'attributes' => [
                'quantity'     => $product->quantity,
                'product-code' => $product->fulfill_sku,
                'designs'      => $this->getDesigns($product->files),
            ]
        ];
        $this->weight     += $product->quantity * $product->weight;
    }

    public function getWeight(): float
    {
        return $this->weight;
    }

    private function getDesigns($files): array
    {
        $arr           = [];
        $arrPrintSpace = [];

        foreach ($files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );
            if (!in_array($printSpace, $arrPrintSpace)) {
                $arr[]           = [
                    'location'         => $printSpace,
                    'image-remote-url' => $file->design_url,
                ];
                $arrPrintSpace[] = $printSpace;
            }
        }

        return $arr;
    }
}
