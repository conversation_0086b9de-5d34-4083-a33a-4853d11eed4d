<?php

namespace App\Providers\FulfillAPI\MWW;

use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Support\Facades\Http;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/orders';
    protected const ENDPOINT_CANCEL_ORDER = '/orders/'; // /orders/{id}
    protected const ENDPOINT_CRAWL_ORDER = '/orders/';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("Authorization", "auth-key=" . $provider['token']);
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    /**
     * @throws \Exception
     */
    public function sendData($response = null, ?string $userId = null, $saveLog = true)
    {
        $request = Http::withHeaders($this->headers)
            ->accept('application/vnd.api+json; version=1');

        if (!empty($this->params)) {
            $request = $request->withBody(json_encode($this->params), 'application/vnd.api+json');
        }

        /** @noinspection SuspiciousAssignmentsInspection */
        $response = $request
            ->send(
                $this->method_url,
                $this->api
            );
        $data     = $response->json();

        return parent::sendData($data, $userId);
    }

    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $data               = $params->getPublicAttrs();
                $data['test-order'] = ($this->mode !== 'production') ? 'test' : 'new';
                // set shipping method by total weight
                $data['data']['attributes']['shipping-method'] = ($params->getWeight() < 1) ? 'USFC' : 'USPM';

                $this->params           = $data;
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api              .= $params->getFulfillOrderId();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url       = 'PATCH';
                $this->params           = $params->getPublicAttrs();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->api              .= $params;
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
        }
    }
}
