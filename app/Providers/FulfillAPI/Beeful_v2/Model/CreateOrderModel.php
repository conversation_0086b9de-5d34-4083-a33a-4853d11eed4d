<?php

namespace App\Providers\FulfillAPI\Beeful_v2\Model;

use App\Models\OrderProduct;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;
use RuntimeException;

//{
//  "api_key": "34ac9f2d-85aa-4711-878c-d6e6e07bce3e",
//  "line_items": [
//    {
//      "variant_id": 363,
//      "product_name": "Retro College Football Mascot Crewneck Sweatshirt",
//      "quantity": 1,
//      "mockup": "https://img.cloudimgs.net/rx/1000/s2/p/287239162/55bdb84ef72bc32b51b4d2eb6678966e.jpeg",
//      "mockup_back": "https://img.cloudimgs.net/rx/1000/s2/p/287239162/55bdb84ef72bc32b51b4d2eb6678966e.jpeg",
//      "print_files": [
//        {
//            "key": "front",
//          "url": "https://img.cloudimgs.net/rx/1000/s4/l_p:3133799:76eb7db5664bb2d7/fl_cutter,fl_layer_apply/u_p:3133799:56ba75b83ef68e24/co_rgb:181818,e_colorize:100/fl_layer_apply/l_p:3133799:23b059_sh/fl_layer_apply/u_p:3133799:99f5f7fedf12aaa4/fl_layer_apply/c_thumb,w_1280/f_jpg/v1/p/264638345/f7781bca9d2c330b0d18cd076ea551aa/t/329ab0b15b3bdaf5.jpg"
//        }
//      ]
//    }
//  ],
//  "order_status": "new",
//  "ref_id": "so4961_576581331130",
//  "shipping_label": "https://zipimgs.com/supover/label/576581331130880690.pdf",
//  "shipping_method": "standard",
//  "address": {
//    "name": "jony smith",
//    "email": "<EMAIL>",
//    "phone": "+1931*****45",
//    "country": "United States",
//    "state": "Tennessee",
//    "city": "Cl*********",
//    "street1": "12** ****** *** *** *(B)",
//    "street2": null,
//    "zip": "37***"
//  }
//}
class CreateOrderModel extends AbstractModel
{
    public string $type = 'DTF';
    public array $payload = [];
    public string $shippingType = '';
    /**
     * @param $order
     *
     * @return void
     */
    public function setOrder($order): void
    {
        $this->setShippingMethod();
        if ($order->country_info->code !== 'US') {
            throw new RuntimeException('Only support US for now');
        }

        // Tại 1 thời điểm chỉ có 1 order
        $this->payload = [
            'ref_id' => $this->fulfill_order_id = $this->order_id = $this->getReferenceId($order),
            'shipping_label' => $order->shipping_label ? s3Url($order->shipping_label) : null,
            'shipping_method' =>  (empty($this->shippingType) || !in_array($this->shippingType, ['standard', 'priority'])) ? 'standard' : $this->shippingType,
            'address'     => [
                'name'                  => $order->customer_name ?? '',
                'phone'                 => $order->customer_phone ?? '',
                'email'                 => $order->customer_email ?? '',
                'street1'               => $this->customer_address['primary'],
                'street2'               => $this->customer_address['addition'],
                'state'                 => $this->customer_address['state_code'] ?: 'OK',
                'city'                  => $this->customer_address['city'],
                'country'               => $order->country_info->code,
                'zip'                   => Str::length($order->postcode) !== 9 ? substr($order->postcode, 0, 5) : $order->postcode,
            ],
            'orders'       => [],
        ];
    }

    /**
     * @return void
     */
    private function setShippingMethod(): void
    {
        $this->shippingType = $this->mapped_shipping_methods[0];
    }

    /**
     * @param OrderProduct $product
     *
     * @return void
     */
    public function setItems(OrderProduct $product): void
    {
        $designs = $this->getFrontBackDesign($product->files);
        $this->payload['line_items'][] = [
            'variant_id'            => $product->fulfill_sku,
            'quantity'              => $product->quantity,
            'product_name'          => $product->product_name,
            'mockup'                => $designs['front']?->mockup_url ?? '',
            'mockup_back'           => $designs['back']?->mockup_url ?? '',
            'print_files'           => $this->handlePrintPositions($designs)
        ];

    }

    /**
     * @param array $designs
     * @return array
     */
    private function handlePrintPositions (array $designs): array {
        $response = [];
        $availablePrintSpaces = ['front', 'back'];
        foreach ($designs as $index => $design) {
            if (in_array($index, $availablePrintSpaces) && isset($design?->design_url)) {
                $key = $index;
                $response [] = [
                    'key' => $key,
                    'url' => $design->design_url ?? ''
                ];
            }
        }
        return $response;
    }
}
