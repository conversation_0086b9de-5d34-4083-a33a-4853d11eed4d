<?php

namespace App\Providers\FulfillAPI\ShineOn\Model;

use App\Enums\FileTypeEnum;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    private array $payload;

    public function setItems(OrderProduct $value): CreateOrderModel
    {
        $this->payload["order"]['line_items'][] = $this->makeItems($value);

        return $this;
    }

    /**
     * {
     *      "order": {
     *          "source_id": **********,
     *          "email": "<EMAIL>",
     *          "shipment_notification_url": "<http://url.com/notification",>
     *          "line_items": [
     *              {
     *                  "store_line_item_id": **********,
     *                  "product_template": "PT-1001",
     *                  "quantity": 1
     *              }
     *          ],
     *          "shipping_address": {
     *              "name": "<PERSON> Norman",
     *              "address1": "Chestnut Street 92",
     *              "city": "Louisville",
     *              "zip": "40202",
     *              "country_code": "US"
     *          }
     *      }
     * }
     *
     * @param $order
     *
     * @return void
     */
    public function setOrder($order): void
    {
        if (empty($this->getWebhookToken())) {
            $shipment_notification_url = '';
        } else {
            $shipment_notification_url = sprintf($this->getBaseWebhookUrl(), $this->getWebhookToken());
        }
        $this->payload = [
            "order" => [
                "source_id"                 => $this->fulfill_order_id = $this->getReferenceId($order),
                "email"                     => $order->customer_email ?? self::SUPPORT_EMAIL,
                "shipment_notification_url" => $shipment_notification_url,
                "shipping_address"      => [
                    "name" => $order->customer_name,
                    "address1" => $this->customer_address['primary'],
                    "address2" => $this->customer_address['addition'],
                    "city" => $this->customer_address['city'],
                    "zip" => $order->postcode,
                    "country_code" => $order->country_info->code,
                    "province" => $order->state,
                    'province_code' => $this->customer_address['state_code'],
                    'phone' => $order->customer_phone ?? '',
                ],
                "line_items" => [],
            ]
        ];
    }

    public function getPublicAttrs(): array
    {
        return $this->payload;
    }

    /**
     * @param     \App\Models\OrderProduct     $op
     *
     * @return array
     */
    private function makeItems(OrderProduct $op): array
    {
        $design = $op->files
            ->filter(fn($f) => $f->type === FileTypeEnum::DESIGN)
            ->first(fn($file) => in_array($file->print_space, ['front', 'back', 'default']));
        $sku = $op->fulfill_sku;
        return [
            "properties" => [
                "print_url" => optional($design)->design_url ?: "",
            ],
            "store_line_item_id"  => $op->id,
            "product_template"    => $sku,
            "quantity"            => $op->quantity,
        ];
    }
}
