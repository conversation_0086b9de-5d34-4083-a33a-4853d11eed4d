<?php

namespace App\Providers\FulfillAPI\ShirtPlatform\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Exception;
use Throwable;

class CreateOrderModel extends AbstractModel
{
    public string $uniqueId;
    public array $customer;
    public array $country;
    public array $orderShipping = [
        //default
        'orderValue' => 40,
        'carrier' => [
            // Generic Standard
            // Generic Express
            // Generic Premium
            'id' => 0,
            'name' => ''
        ],
    ];
    public array $designs;

    /**
     * @throws Exception
     */
    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->uniqueId = $this->getReferenceId($order);
        $this->customer = $this->getCustomer($order);
        $this->country = [
            'code' => $order->country,
        ];

        $this->setShippingMethod($order);
    }

    private function setShippingMethod($order): void
    {
        try {
            $this->orderShipping['carrier'] = [
                'id' => $this->mapped_shipping_methods[1],
                'name' => $this->mapped_shipping_methods[0],
            ];
        } catch (Throwable $e) {
            throw new \RuntimeException(
                'Not found shipping method for this supplier ShirtPlatform. Please update fulfillMapping'
            );
        }
    }

    private function getCustomer($order): array
    {
        $address = [
            'street' => $this->customer_address['full'],
            'address2' => $this->getOrderNote($order, true),
            'city' => $order->city,
            'country' => $order->country_info->name,
            'firstName' => $this->customer_name['first_name'],
            'lastName' => $this->customer_name['last_name'],
            'name' => $order->customer_name,
            'phone' => $order->customer_phone,
            'email' => $order->customer_email,
            'zip' => $order->postcode,
            'countryCode' => $order->country,
            'stateName' => $order->state,
        ];

        return [
            'firstName' => $this->customer_name['first_name'],
            'lastName' => $this->customer_name['last_name'],
            'email' => $order->customer_email,
            'phone' => $order->customer_phone,
            'billingAddress' => $address,
            'shippingAddress' => $address,
        ];
    }

    public function setItems($product): void
    {
        $this->designs['creatorse_design'][] = [
            'amount' => $product->quantity,
            'sku' => $product->fulfill_sku,
            'compositions' => [
                'creatorse_composition' => $this->setCompositions($product->files),
            ]
        ];
        $this->setOrderValues($product->total_amount);
    }

    private function setOrderValues($totalAmount): void
    {
        $this->orderShipping['orderValue'] += $totalAmount; // todo @long: check
    }

    private function setCompositions($files): array
    {
        $arr = [];
        $arrPrintSpace = [];

        foreach ($files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );
            if (!in_array($printSpace, $arrPrintSpace)) {
                $arr[] = [
                    'productArea' => [
                        'assignedView' => [
                            'view' => [
                                'position' => strtoupper($printSpace),
                            ]
                        ]
                    ],
                    'elements' => [
                        'creatorse_designElementMotive' => [
                            'motive' => [
                                'url' => $file->design_url,
                            ],
                            'position' => [
                                'left' => 0,
                                'right' => 0,
                                'top' => 0,
                            ],
                        ]
                    ]
                ];
                $arrPrintSpace[] = $printSpace;
            }
        }

        return $arr;
    }
}
