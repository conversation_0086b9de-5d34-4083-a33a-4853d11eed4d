<?php /** @noinspection PhpParameterNameChangedDuringInheritanceInspection */

/** @noinspection TypeUnsafeComparisonInspection */

namespace App\Providers\FulfillAPI\PrintLogistic\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\PrintLogisticWebhookRepository;

class ResponseModel extends AbstractResponseModel
{
    protected function setErrors($string): void
    {
    }

    protected function checkCreateOrderHasResponse($string): bool
    {
        return true;
    }

    public function mappingCreateOrder($string, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $fulfillOrderId,
            OrderProductFulfillStatus::PENDING
        );
    }

    public function mappingCrawlOrder($string, $fulfillOrderId): array
    {
        return (new PrintLogisticWebhookRepository($string, $this->supplier_id))
            ->setSupplierOrderId($fulfillOrderId)
            ->withCrawlMode()
            ->get();
    }
}
