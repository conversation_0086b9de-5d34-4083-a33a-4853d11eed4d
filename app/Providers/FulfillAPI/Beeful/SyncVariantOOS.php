<?php

namespace App\Providers\FulfillAPI\Beeful;

use App\Providers\FulfillAPI\AbstractSyncVariantOOS;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Throwable;

class SyncVariantOOS extends AbstractSyncVariantOOS
{
    /**
     * @param Collection $variants     *
     *
     * @return Collection
     *
     * @throws Throwable
     */
    public function fetchData(Collection $variants): Collection
    {
        $cfg = $this->getSupplierConfig();
        $responses = Http::get($cfg['api'] . '/products');
        if ($responses->failed()) {
            return collect();
        }
        $responses = $responses->json();
        return collect(data_get($responses, 'data', []))->keyBy('variant_id')->map(fn($response) => $this->parseStockQty($response));
    }

    /**
     * @param $response
     *
     * @return int|null
     */
    public function parseStockQty($response): ?int
    {
        try {
            $qty = data_get($response, 'stock');
            if (!is_numeric($qty)) {
                return null;
            }
            return (int) $qty;
        } catch (\Throwable $e) {
            return null;
        }
    }
}
