<?php

namespace App\Providers\FulfillAPI\FmEasyCustom;

use App\Models\OrderProduct;
use App\Providers\FulfillAPI\ObjectFulfill;
use Modules\OrderService\Models\RegionOrderProducts;
class Provider extends ObjectFulfill
{
    /**
     * @override
     *
     * @param    OrderProduct|RegionOrderProducts    $op
     *
     * @return OrderProduct|RegionOrderProducts
     */
    public function correctOrderProduct(OrderProduct|RegionOrderProducts $op): OrderProduct|RegionOrderProducts
    {
        try {
            $options = json_decode($op->options, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            $options = [];
        }

        $op->options = $options;

        return $op;
    }

    public function createOrder($order, $reOrder): object
    {
        // TODO: Implement createOrder() method.
        return new \stdClass();
    }

    public function cancelOrder($fulfillOrderId, $orderId): bool
    {
        return true;
    }

    public function crawlOrder($fulfillOrderId, $orderId = null, $justCreated = false): array
    {
        // TODO: Implement crawlOrder() method.
    }

    public function crawlProductsAndProductVariants(): void
    {
        // TODO: Implement crawlProductsAndProductVariants() method.
    }

    public function crawlProductsAndProductVariantsJob(): void
    {
        // TODO: Implement crawlProductsAndProductVariantsJob() method.
    }
}
