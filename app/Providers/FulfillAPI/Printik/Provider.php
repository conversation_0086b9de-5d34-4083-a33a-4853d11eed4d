<?php

namespace App\Providers\FulfillAPI\Printik;

use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Support\Arr;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/order/create';
    protected const ENDPOINT_CANCEL_ORDER = '/order/cancel';
    protected const ENDPOINT_CRAWL_ORDER = '/order';


    /**
     * @param $provider
     * @return void
     */
    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $token = Arr::get($provider, $this?->mode ?? 'dev')['token'];
        $this->setHeader('printik-access-token', $token);
    }

    /**
     * @param $params
     * @return void
     */
    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->setApiWithEndpoint();
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url = 'POST';
                break;

            case self::METHOD_CANCEL_ORDER:
                $this->setApiWithEndpoint();
                $this->params = [
                    'payload' => [
                        "orderCode" => $params->id,
                        "sellerOrderCode" => ''
                    ]
                ];
                $this->method_url = 'POST';
                break;

            case self::METHOD_CRAWL_ORDER:
                $this->setApiWithEndpoint();
                $this->api  .= '/' . $params;
                $this->fulfill_order_id = $params;
                $this->method_url = 'GET';
                break;
        }
    }

    /**
     * @param $params
     *
     * @return bool|string
     * @throws \Throwable
     */
    public function withBody($params): bool|string
    {
        $payload = Arr::get($params, 'payload');
        if (isset($payload)) {
            return json_encode($payload, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
        }
        return false;
    }
}
