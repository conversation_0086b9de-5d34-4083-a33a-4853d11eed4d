<?php

namespace App\Providers\FulfillAPI\Bondre\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\BondreWebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    /**
     * @param $arr
     *
     * @return void
     */
    protected function setErrors($arr): void
    {
        $supResponseCode = Arr::get($arr, 'code');
        if (isset($supResponseCode) && intval($supResponseCode) !== 200) {
            $this->errors[] = 'Fulfill order code : ' . intval($supResponseCode) . ' - message : ' . Arr::get($arr, 'message');
        }
    }

    /**
     * @param $arr
     *
     * @return bool
     */
    protected function checkCreateOrderHasResponse($arr): bool
    {
        return data_get($arr, 'response_status_code') !== null;
    }

    /**
     * @param     array     $arr
     * @param               $orderProductIds
     * @param               $fulfillOrderId
     *
     * @return void
     */
    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {

        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $fulfillOrderId,
            $this->handleApiResponsedStatus($arr),
        );
    }

    /**
     * @param     array     $arr
     * @param               $fulfillOrderId
     *
     * @return array
     */
    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        $responseData = Arr::get($arr, 'data');
        $responseCode = Arr::get($arr, 'code');
        if (intval($responseCode) == 200 &&
            !empty($responseData) &&
            isset($responseData[0])) {
            return (new BondreWebhookRepository($responseData[0], $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }

    /**
     * @param array $arr
     * @param $expectedStatusOnSuccess
     * @param $expectedStatusOnFail
     *
     * @return string
     */
    public function handleApiResponsedStatus(array $arr,
                                             $expectedStatusOnSuccess = OrderProductFulfillStatus::PENDING,
                                             $expectedStatusOnFail = OrderProductFulfillStatus::UNFULFILLED): string
    {
        $responseCode = intval(Arr::get($arr, 'code'));
        if ($responseCode === 200) {
            return $expectedStatusOnSuccess;
        }
        return $expectedStatusOnFail;
    }
}
