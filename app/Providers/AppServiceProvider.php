<?php

namespace App\Providers;

use App\Enums\CacheKeys;
use App\Models\Staff;
use App\Models\Supplier;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\SenPrintsAuth;
use App\Services\ProcessLockService;
use Illuminate\Console\Scheduling\Event as ScheduleEvent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

/**
 * @property $model
 */
class AppServiceProvider extends ServiceProvider
{

    public function boot()
    {
        try {
            //init macro for JsonResponse
            JsonResponse::macro('withCached', function ($cacheStatus = false) {
                $status = $cacheStatus ? 'HITS' : 'MISS';
                return $this->header('Be-Cache-Status', $status);
            });

            Cache::macro('hasCached', function ($key, $store = null, $tags = []) {
                $cacheStore = Cache::store($store);

                if (!empty($tags)) {
                    $cacheStore = $cacheStore->tags($tags);
                }
                return $cacheStore->has($key) && $cacheStore->get($key) !== null;
            });

            DB::beforeExecuting(static function (string $sql, array $bindings) {
                $_bindings = $bindings;
                $sql = Str::lower(preg_replace_callback('/\?/', static function () use (&$_bindings) {
                    return DB::connection()->getPdo()->quote(array_shift($_bindings));
                }, $sql));
                $isDeleteQuery = Str::startsWith($sql, 'delete from');
                $existsWhere = preg_match('/(\s*where\s*)/i', $sql);
                $existsWhereCondition = false;
                if ($isDeleteQuery && $existsWhere) {
                    $explode_sql = explode(' where ', $sql);
                    if (count($explode_sql) > 1) {
                        $where = trim($explode_sql[1]);
                        $where = preg_replace('/((and)?\s*1=1(and)?\s*)|((and)?\s*1 = 1(and)?\s*)/', '', $where);
                        $sql = $explode_sql[0] . ' where ' . trim($where);
                    }
                    $explode_sql = explode(' where ', $sql);
                    if ((count($explode_sql) > 1)) {
                        $where = trim($explode_sql[1]);
                        $andWhere = explode(' and ', $where);
                        $orWhere = explode(' or ', $where);
                        if (!empty($andWhere) || !empty($orWhere)) {
                            $existsWhereCondition = true;
                        }
                    }
                }
                if ($isDeleteQuery && empty($bindings) && empty($existsWhereCondition)) {
                    throw new \InvalidArgumentException('Delete query without where clause is not allowed.');
                }
            });
            QueryBuilder::macro('setConnection', function ($connection) {
                $this->connection = $connection;
            });
            Builder::macro('on', function ($connection) {
                $this->model->setConnection($connection);
                $this->query->setConnection($this->model->getConnection());
                $this->query->from = $this->model->getConnection()->getDatabaseName() . '.' . $this->model->getOnlyTable();
                return $this;
            });
            Builder::macro('onSellerConnection', function (SenPrintsAuth|User|Staff|Supplier|null $seller, bool $shardingCompleted = true) {
                $connection = $seller?->getPrivateConnection(shardingCompleted: $shardingCompleted) ?? config('database.default');
                return $this->on($connection);
            });
            DB::listen(function ($query) {
                // Log queries that take longer than 2 seconds
                if ($query->connectionName === 'singlestore' && $query->time > 2000) {
                    $sql = $query->sql;
                    $_bindings = $query->bindings;
                    $sql = Str::lower(preg_replace_callback('/\?/', static function () use (&$_bindings) {
                        return DB::connection()->getPdo()->quote(array_shift($_bindings));
                    }, $sql));
                    $message = sprintf(
                        "[%s] Time (%.2fs): %s",
                        $query->connectionName,
                        $query->time / 1000,
                        $sql
                    );
                    $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 20);
                    $relevantTrace = [];
                    foreach ($trace as $traceLine) {
                        if (isset($traceLine['file']) && !str_contains($traceLine['file'], 'vendor')) {
                            $relevantTrace[] = [
                                'file' => $traceLine['file'],
                                'line' => $traceLine['line'],
                                'function' => $traceLine['function'],
                            ];
                        }
                    }
                    graylogInfo($message, [
                        'category' => 'log_queries_singlestore',
                        'query' => $sql,
                        'time' => $query->time / 1000,
                        'trace' => $relevantTrace,
                    ]);
                }
            });

            ScheduleEvent::macro('logAfter', function () {
                return $this->after(function () {
                    graylogInfo('Command executed: ' . $this->command, [
                        'category' => 'schedule_command',
                        'expression' => $this->expression,
                        'command' => $this->command,
                    ]);
                    try {
                        SystemConfig::setConfig(CacheKeys::LAST_DATETIME_SCHEDULE_RAN, array(
                            'value' => now()->toDateTimeString(),
                            'status' => 1
                        ));
                    } catch (\Throwable $e) {}
                });
            });
            $options = Http::getOptions();
            Arr::forget($options, 'proxy');
            Http::withOptions($options);
        } catch (\Throwable $e) {
            logException($e);
        }
    }
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('process-lock', function () {
            return new ProcessLockService();
        });
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }
}
