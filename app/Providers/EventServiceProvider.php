<?php

namespace App\Providers;

use App\Events\AccountCreated;
use App\Events\CampaignCreated;
use App\Events\CustomerAddressUpdated;
use App\Events\EmailVerifyAccountRequested;
use App\Events\InviteMember;
use App\Events\KlaviyoOrderCancelled;
use App\Events\Login;
use App\Events\OrderCancelledEvent;
use App\Events\OrderChangeDesign;
use App\Events\OrderFulfilled;
use App\Events\OrderPaymentCompleted;
use App\Events\OrderProductFulfilled;
use App\Events\OrderRefundedEvent;
use App\Events\OrderUpdated;
use App\Events\PaymentAccountCreated;
use App\Events\PayoutCreated;
use App\Events\ResetPasswordRequested;
use App\Events\StoreContacted;
use App\Events\SupplierResetPasswordRequested;
use App\Listeners\CheckDesignFiles;
use App\Listeners\CheckDesignFilesFromOrder;
use App\Listeners\HandleOrderCompleted;
use App\Listeners\HandleSendBuyerCrossShipping;
use App\Listeners\LogOrderChangeDesign;
use App\Listeners\OrderRefundedListener;
use App\Listeners\ReassignSupplierAfterOrderCompleted;
use App\Listeners\RegisterTrackingListener;
use App\Listeners\SellerFraudChecking;
use App\Listeners\SendContactCustomerNotification;
use App\Listeners\SendContactSellerNotification;
use App\Listeners\SendEmailVerifyAccount;
use App\Listeners\SendInviteMemberEmail;
use App\Listeners\SendOrderCancelledTelegramNotification;
use App\Listeners\SendOrderConfirmationNotification;
use App\Listeners\SendOrderDiscordNotification;
use App\Listeners\SendOrderFulfillNotification;
use App\Listeners\SendOrderSellerNotification;
use App\Listeners\SendOrderTelegramNotification;
use App\Listeners\SendOrderUpdateNotification;
use App\Listeners\SendOrderWebhookNotification;
use App\Listeners\SendPaymentAccountConfirmNotification;
use App\Listeners\SendPayoutConfirmNotification;
use App\Listeners\SendResetPasswordNotification;
use App\Listeners\SendSupplierResetPasswordNotification;
use App\Listeners\SendWelcomeNotification;
use App\Listeners\UpdateGatewayTrackingListener;
use App\Listeners\VerifyCustomerAddress;
use App\Listeners\VerifyCustomerAddressAndSendMail;
use App\Models\Campaign;
use App\Models\CustomEmailTemplate;
use App\Models\ExpressCampaign;
use App\Models\FulfillProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\Store;
use App\Models\Template;
use App\Models\TemplateCampaign;
use App\Observers\CampaignObserver;
use App\Observers\CustomEmailTemplateObserver;
use App\Observers\ExpressCampaignObserver;
use App\Observers\FulfillProductObserver;
use App\Observers\OrderObserver;
use App\Observers\OrderProductObserver;
use App\Observers\ProductObserver;
use App\Observers\StoreObserver;
use App\Observers\TemplateCampaignObserver;
use App\Observers\TemplateObserver;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoOrderAbandonedListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoOrderCanceledListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoOrderPlacedListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoOrderRefundedListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoSellerAccountCreatedListener;
use App\Events\KlaviyoOrderRefunded;
use App\Events\KlaviyoTrackingUpdated;
use App\Events\OrderAbandoned;
use App\Listeners\SendWooCommerceStatusWebhookNotification;
use App\Listeners\SendWooCommerceTrackingWebhookNotification;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoDeliveredShipmentListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoMarkedOutForDeliveryListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoFulfilledOrderListener;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoFulfilledPartialOrderListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        EmailVerifyAccountRequested::class => [
            SendEmailVerifyAccount::class,
        ],
        ResetPasswordRequested::class => [
            SendResetPasswordNotification::class,
        ],
        SupplierResetPasswordRequested::class => [
            SendSupplierResetPasswordNotification::class,
        ],
        StoreContacted::class => [
            SendContactSellerNotification::class,
            SendContactCustomerNotification::class,
        ],
        AccountCreated::class => [
            SendWelcomeNotification::class,
            SellerFraudChecking::class,
            RegisterTrackingListener::class,
            KlaviyoSellerAccountCreatedListener::class
        ],
        PayoutCreated::class => [
            SendPayoutConfirmNotification::class,
        ],
        PaymentAccountCreated::class => [
            SendPaymentAccountConfirmNotification::class
        ],
        OrderChangeDesign::class => [
            LogOrderChangeDesign::class,
        ],
        OrderUpdated::class => [
            SendOrderUpdateNotification::class
        ],
        OrderFulfilled::class => [
            SendOrderFulfillNotification::class,
            SendWooCommerceStatusWebhookNotification::class,
            KlaviyoFulfilledOrderListener::class,
        ],
        OrderProductFulfilled::class => [
            UpdateGatewayTrackingListener::class,
            SendWooCommerceTrackingWebhookNotification::class,
            KlaviyoFulfilledPartialOrderListener::class,
        ],
        OrderPaymentCompleted::class => [
            HandleOrderCompleted::class,
            VerifyCustomerAddressAndSendMail::class,
            SendOrderSellerNotification::class,
            SendOrderConfirmationNotification::class,
            SendOrderDiscordNotification::class,
            SendOrderTelegramNotification::class,
            SendOrderWebhookNotification::class,
            CheckDesignFilesFromOrder::class,
            HandleSendBuyerCrossShipping::class,
            KlaviyoOrderPlacedListener::class,
            ReassignSupplierAfterOrderCompleted::class,
        ],
        CustomerAddressUpdated::class => [
            VerifyCustomerAddress::class
        ],
        InviteMember::class => [
            SendInviteMemberEmail::class,
        ],
        CampaignCreated::class => [
            CheckDesignFiles::class,
        ],
        Login::class => [
            SellerFraudChecking::class
        ],
        OrderCancelledEvent::class => [
            SendOrderCancelledTelegramNotification::class,
        ],
        OrderRefundedEvent::class => [
            OrderRefundedListener::class,
        ],
        OrderAbandoned::class => [
            KlaviyoOrderAbandonedListener::class,
        ],
        KlaviyoOrderRefunded::class => [
            KlaviyoOrderRefundedListener::class,
        ],
        KlaviyoOrderCancelled::class => [
            KlaviyoOrderCanceledListener::class,
        ],
        KlaviyoTrackingUpdated::class => [
            KlaviyoMarkedOutForDeliveryListener::class,
            KlaviyoDeliveredShipmentListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(): void
    {
        Order::observe(OrderObserver::class);
        Store::observe(StoreObserver::class);
        OrderProduct::observe(OrderProductObserver::class);
        CustomEmailTemplate::observe(CustomEmailTemplateObserver::class);
        Template::observe(TemplateObserver::class);
        Product::observe(ProductObserver::class);
        Campaign::observe(CampaignObserver::class);
        ExpressCampaign::observe(ExpressCampaignObserver::class);
        TemplateCampaign::observe(TemplateCampaignObserver::class);
        FulfillProduct::observe(FulfillProductObserver::class);
    }
}
