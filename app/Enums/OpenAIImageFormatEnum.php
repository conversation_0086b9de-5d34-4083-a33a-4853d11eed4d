<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static PNG()
 * @method static static JPEG()
 * @method static static WEBP()
 */
final class OpenAIImageFormatEnum extends Enum
{
    public const PNG = 'png';
    public const JPEG = 'jpeg';
    public const WEBP = 'webp';

    /**
     * Get all valid image formats for OpenAI's gpt-image-1 model
     *
     * @return array
     */
    public static function getValidFormats(): array
    {
        return [
            self::PNG,
            self::JPEG,
            self::WEBP
        ];
    }

    /**
     * Check if a format is valid for OpenAI's gpt-image-1 model
     *
     * @param string $format
     * @return bool
     */
    public static function isValidFormat(string $format): bool
    {
        return in_array($format, self::getValidFormats());
    }
}
