<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class AccessExternalApiType extends Enum
{
    public const SUPPLIER = 'supplier';
    public const SELLER = 'seller';
    public const STORE = 'store';
    public const PAYMENT_GATEWAY = 'payment_gateway';
    public const PARTNER = 'partner';

    public static function getArrayDecade(): array
    {
        return [
            self::SUPPLIER,
            self::PAYMENT_GATEWAY,
            self::PARTNER,
        ];
    }
}
