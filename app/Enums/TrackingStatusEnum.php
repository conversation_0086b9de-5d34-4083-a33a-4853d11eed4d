<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class TrackingStatusEnum extends Enum
{
    public const NEW = 'new';
    public const PENDING = 'pending';
    public const PICKUP = 'pickup';
    public const TRANSIT = 'transit';
    public const DELIVERED = 'delivered';
    public const UNDELIVERED = 'undelivered';
    public const NOTFOUND = 'notfound';
    public const EXCEPTION = 'exception';
    public const EXPIRED = 'expired';
    public const UNTRACKED = 'untracked';
    public const ALERT = 'alert';
    public const UNDETECTED = 'undetected';
    public const OUT_FOR_DELIVERY = 'out_for_delivery';
    public const INFO_RECEIVED = 'pre_shipment';

    public static function endStatuses(): array
    {
        return [
            self::DELIVERED,
            self::UNDELIVERED,
            self::OUT_FOR_DELIVERY,
            self::EXPIRED,
            self::ALERT,
            self::UNDETECTED
        ];
    }

    public static function trackingLateStatuses(): array
    {
        return [
            self::NEW,
            self::UNDETECTED,
            self::INFO_RECEIVED,
            self::NOTFOUND,
            self::UNTRACKED,
        ];
    }

    public static function inTransitStatuses(): array
    {
        return [
            self::ALERT,
            self::EXPIRED,
            self::PICKUP,
            self::TRANSIT,
            self::EXCEPTION,
            self::OUT_FOR_DELIVERY
        ];
    }
}
