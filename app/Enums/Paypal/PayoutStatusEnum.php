<?php

namespace App\Enums\Paypal;

use BenSampo\Enum\Enum;

final class PayoutStatusEnum extends Enum
{
    public const SUCCESS = 'success';
    public const FAILED = 'failed';
    public const PENDING = 'pending';
    public const UNCLAIMED = 'unclaimed';
    public const RETURNED = 'returned';
    public const ONHOLD = 'onhold';
    public const BLOCKED = 'blocked';
    public const REFUNDED = 'refunded';
    public const REVERSED = 'reversed';
    public const NONE = 'none';
}
