<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class OrderSupportStatusEnum extends Enum
{
    public const NEED_SUPPORT = -2;
    public const ALL = -1;
    public const NORMAL = 0;
    public const CUSTOMER = 1;
    public const FF_ISSUE = 2;
    public const TECH = 3;
    public const OTHERS = 4;

    public const SHIPPING_ISSUE = 5;
    public const SUP_OOS = 6;
    public const TIME_OVER = 7;
    public const WRONG_ADDRESS = 8;
    public const WARNING_LARGE_QUANTITY_AMOUNT = 9;
    public const NEED_REASSIGN_SUPPLIER_MANUALLY = 10;
    public const FULFILL_MANUALLY = 11;
}
