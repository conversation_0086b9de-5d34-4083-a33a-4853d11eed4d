<?php

namespace App\Enums\SaleReport;

use BenSampo\Enum\Enum;

final class DataTypeEnum extends Enum
{
    public const OVERVIEW = 'overview';
    public const COUNTRY = 'country';
    public const DEVICE = 'device';
    public const DEVICE_DETAIL = 'device_detail';
    public const AD_CAMPAIGN = 'ad_campaign';
    public const AD_SOURCE = 'ad_source';
    public const AD_MEDIUM = 'ad_medium';
    public const PRODUCT = 'product';

    public static function getArrCanTypeNA(): array
    {
        return [
            self::AD_CAMPAIGN,
            self::AD_SOURCE,
            self::AD_MEDIUM,
            self::DEVICE,
            self::DEVICE_DETAIL,
            self::DEVICE_DETAIL,
            self::COUNTRY,
        ];
    }

    public static function getArrForTypeCampaign(): array
    {
        return [
            self::DEVICE,
            self::DEVICE_DETAIL,
        ];
    }
}
