<?php

namespace App\Enums;

use BenSamp<PERSON>\Enum\Enum;

final class OrderHistoryActionEnum extends Enum
{
    public const CREATED = 'Created';
    public const PAID_BY_CUSTOMER = 'Paid By Customer';
    public const PAID_BY_ADMIN = 'Paid By Admin';
    public const HOLD_ORDER = 'Hold Order';
    public const HOLD_FULFILL = 'Hold Fulfill';
    public const RESUME_ORDER = 'Resume Order';
    public const RESUME_FULFILL = 'Resume Fulfill';
    public const EDIT_ADDRESS = 'Edit Address';
    public const CUSTOMER_EDIT_ADDRESS = 'Customer Edit Address';
    public const ADMIN_EDIT_ADDRESS = 'Admin Edit Address';

    public const CUSTOMER_CONFIRM_ADDRESS = 'Customer Confirm Address';
    public const REVALIDATE_CUSTOMER_ADDRESS = 'Re-Validate Customer Address';

    public const ADMIN_CONFIRM_ADDRESS = 'Admin Confirm Address';
    public const EDIT_CUSTOMER_NOTE = 'Edit Customer Note';
    public const ADD_ADMIN_NOTE = 'Add Admin Note';
    public const ASSIGNED_SUPPLIER = 'Assigned Supplier';
    public const REASSIGNED_PRIORITY_SUPPLIER = 'Re-assigned Priority Supplier';
    public const REASSIGN_PRIORITY_SUPPLIER_FAILED = 'Re-assign Priority Supplier Failed';

    public const REFUNDED = 'Refunded';
    public const CANCELLED = 'Cancelled';
    public const EMAIL_TO_CUSTOMER = 'Email To Customer';
    public const REJECT_FULFILL = 'Reject Fulfill';
    public const FULFILLED = 'Fulfilled';
    public const FULFILLMENT_CREATED = 'Fulfillment Created';
    public const FULFILLMENT_UPDATED = 'Fulfillment Updated';
    public const FULFILLMENT_COMPLETED = 'Fulfillment Completed';
    public const FULFILLMENT_CANCELLED = 'Fulfillment Cancelled';
    public const VALIDATE = 'Validate';
    public const UPDATED = 'Updated';
    public const CHANGE_SUPPORT_CATEGORY = 'Change Support Category';
    public const ASSIGN_TO_STAFF = 'Assign To Staff';
    public const EXPORT = 'Export';
    public const PAYMENT_SUMMARY = 'Payment Summary';
    public const PAYMENT_FAILED = 'Payment Failed';
    public const FULFILL_UPDATED = 'Fulfill Updated';
    public const UPDATE_PRODUCT = 'Update Order Product';
    public const SUSPENDED = 'Suspended Order';
    public const APPROVE_REFUND = 'Approve Refund';
    public const APPROVED_DESIGN = 'Approved Design';
    public const CHANGE_DESIGN = 'Change design';
    public const CUSTOMER_ACTION = 'Customer Action';
    public const ADMIN_ACTION = 'Admin Action';
    public const CHANGE_FRAUD_STATUS = 'Change Fraud Status';
    public const REQUEST_CANCEL_12H_CREATED = 'Request cancel 12h - Created';
    public const REQUEST_CANCEL_12H_CONFIRMED = 'Request cancel 12h - Confirmed';
    public const NEED_VERIFY_ADDRESS_MANUALLY = 'Need verify address manually';
    public const WOOCOMMERCE_STATUS_SYNC = 'WooCommerce Status Sync';
    public const CREATED_CHARGE_EXTRA_FEE = 'Created Charge Extra Fee';
}
