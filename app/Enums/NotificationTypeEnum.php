<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class NotificationTypeEnum extends Enum
{
    public const ABANDONED_CHECKOUT_EMAIL_1 = 'abandoned_checkout_email_1';
    public const ABANDONED_CHECKOUT_EMAIL_2 = 'abandoned_checkout_email_2';
    public const ABANDONED_CHECKOUT_EMAIL_3 = 'abandoned_checkout_email_3';
    public const ABANDONED_CHECKOUT_SMS_1 = 'abandoned_checkout_sms_1';
    public const ABANDONED_CHECKOUT_SMS_2 = 'abandoned_checkout_sms_2';
    public const ABANDONED_CHECKOUT_SMS_3 = 'abandoned_checkout_sms_3';
    public const NOTHING = 'nothing';
}
