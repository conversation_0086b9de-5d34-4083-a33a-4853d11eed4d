<?php

namespace App\Enums;

use BenSamp<PERSON>\Enum\Enum;

final class DiscordUserIdEnum extends Enum
{
    public const J2TEAMNNL = 787568883074072577;
    public const JAMES = 189808433912872963;
    public const THANGNM = 710083679724240907;
    public const TUNGNT = 452058019858087937;
    public const TUANHM = 341114052514283521;
    public const PHUC_TRINH = 874562069414952972;
    public const LIEN_HQ = 874567421539778591;
    public const DEV = '&874927484997627934';
    public const BIZDEV = '&883373258513186917';
    public const CS = '&883370322181648494';
    public const VINH = 532704756146372629;
    public const SS = '&922516607220207638';
    public const MIMICS = 880389879983403029;
    public const QUANCS = 920513487103459359;
}
