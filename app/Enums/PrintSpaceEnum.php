<?php

namespace App\Enums;

use <PERSON><PERSON><PERSON><PERSON>\Enum\Enum;

final class PrintSpaceEnum extends Enum
{
    public const DEFAULT = 'default';
    public const FRONT = 'front';
    public const BACK = 'back';
    public const ARR = [
        self::DEFAULT,
        'main',
        'back',
        'front',
        'front_1',
        'front_2',
        'front_3',
        'front_4',
        'large',
        'small',
        '9oz',
        '10oz',
        '11oz',
        '12oz',
        '15oz',
        '16oz',
        '18oz',
        '20oz',
        '20oz2',
        '32oz',
        '10x10',
        '10x14',
        '10x8',
        '11x14',
        '12x12',
        '12x16',
        '12x18',
        '12x8',
        '13x19',
        '13x21',
        '14x10',
        '14x11',
        '14x14',
        '14x21',
        '16x12',
        '16x16',
        '16x20',
        '16x24',
        '17x17',
        '18x12',
        '18x18',
        '18x24',
        '20x16',
        '20x20',
        '20x30',
        '21x14',
        '24x16',
        '24x17',
        '24x18',
        '24x24',
        '24x32',
        '24x36',
        '27x40',
        '28x28',
        '30x20',
        '30x30',
        '30x40',
        '30x46',
        '32x24',
        '32x32',
        '32x48',
        '34x21',
        '35x60',
        '36x24',
        '36x54',
        '36x60',
        '40x27',
        '40x40',
        '46x30',
        '48x32',
        '4x6',
        '50x60',
        '54x36',
        '60x80',
        '6x4',
        '6x9',
        '8x10',
        '8x12',
        '8x8',
        'king',
        'queen',
        'twin',
        'twin_xl',
        'standard',
        '120x140',
        '120x150',
        '130x180',
        '150x200',
        's',
        'm',
        'l',
        'xl',
        'right_sleeve',
        'left_sleeve',
        'left_chest',
        'right_chest',
        'glass',
        'decanter',
        'coaster',
        'box',
        'heart',
        'round',
        '2x2 inches',
        '3x3 inches',
        '3-5x3-5 inches',
        '4x4 inches',
        '8 inches',
        '12 inches',
        '14 inches',
        '18 inches',
        '22 inches',
        '26 inches',
        '30 inches',
        '48 inches',
        'one sided',
        'double sided',
    ];

    /**
     * @return array
     */
    public static function getArraySameWithFront(): array
    {
        return [
            self::DEFAULT,
            'main',
            'front',
            'front_1',
            'front_2',
            'front_3',
            'front_4',
        ];
    }

    /**
     * @param $val
     * @return bool
     */
    public static function checkIfSameWithFront($val): bool
    {
        return in_array($val, self::getArraySameWithFront(), true);
    }

    /**
     * @return array
     */
    public static function additionalPrintSpaces(): array
    {
        return [
            'right_sleeve',
            'left_sleeve',
        ];
    }

    public static function getRandomValue(): string
    {
        return self::ARR[array_rand(self::ARR)];
    }
}
