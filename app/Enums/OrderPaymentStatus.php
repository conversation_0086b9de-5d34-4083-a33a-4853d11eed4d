<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class OrderPaymentStatus extends Enum
{
    public const UNPAID = 'unpaid';
    public const PAID = 'paid';
    public const PARTIALLY_PAID = 'partially_paid';
    public const REFUNDED = 'refunded';
    public const PARTIALLY_REFUNDED = 'partially_refunded';
    public const AUTHORIZED = 'authorized';
    public const FAILED = 'failed';
    public const PENDING = 'pending';
}
