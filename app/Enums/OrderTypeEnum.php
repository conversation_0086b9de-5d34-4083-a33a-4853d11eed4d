<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class OrderTypeEnum extends Enum
{
    public const REGULAR = 'regular';
    public const FULFILLMENT = 'fulfillment';
    public const SERVICE = 'service';
    public const CUSTOM = 'custom';
    public const FBA = 'fba';

    public static function fulFill(): array
    {
        return [self::FULFILLMENT, self::FBA];
    }

    public static function platform(): array
    {
        return [self::REGULAR, self::CUSTOM];
    }

    public static function isFulfillOrder($type): bool
    {
        return in_array($type, [self::FULFILLMENT, self::FBA], true);
    }
}
