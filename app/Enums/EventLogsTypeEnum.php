<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class EventLogsTypeEnum extends Enum
{
    public const VISIT = 'visit';
    public const ADD_TO_CART = 'add_to_cart';
    public const INIT_CHECKOUT = 'init_checkout';
    public const INTERACT = 'interact';

    public static function getArrayForAnalytic(): array
    {
        // this for sorting too so don't change the order
        return [
            self::INIT_CHECKOUT,
            self::ADD_TO_CART,
            self::VISIT,
        ];
    }
}
