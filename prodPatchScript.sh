#!/bin/sh
kubectl patch deployment backend-api --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}"
kubectl patch deployment backend-queue --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true
kubectl patch deployment backend-queue-fulfill --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true
kubectl patch deployment backend-queue-render --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true
kubectl patch deployment backend-queue-order --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true
kubectl patch deployment backend-api-logs --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true
kubectl patch deployment backend-queue-check-copyright --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true
kubectl patch deployment backend-queue-social-feed --namespace=production -p "{\"spec\":{\"template\":{\"metadata\":{\"labels\":{\"date\":\"`date +'%s'`\"}}}}}" || true

exit