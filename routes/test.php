<?php

use App\Enums\CacheKeys;
use App\Enums\SystemConfigTypeEnum;
use App\Http\Controllers\Analytic3\SellerController;
use App\Http\Controllers\PayoutController;
use App\Http\Controllers\SmsController;
use App\Http\Controllers\TestController;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\CampaignService;
use App\Services\InactiveService;
use App\Services\LianLian;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\Marketing\Enums\SesSenderStatus;
use Modules\Marketing\Http\Controllers\MarketingController;

Route::get('update-social-feed-command', function () {
    Artisan::call("update-social-feed-command");
    return "command called";
});

Route::get('update_seller_id_upsell', function () {
    Artisan::call("update_seller_id_upsell");
    return "command called";
});

Route::get('update_seller_id_upsell/config', function (Request $request) {
    $config = SystemConfig::firstWhere('key', CacheKeys::UPDATE_SELLER_ID_UPSELL);
    $configQuery = json_decode($config?->json_data, true) ?? [];
    $newConfigQuery = [
        'type' => $request->get('type', data_get($configQuery, 'type')),
        'seller_ids' => $request->get('seller_ids', data_get($configQuery, 'seller_ids')),
    ];
    $newConfig = [
        'value' => $request->get('value', $config?->value),
        'status' => $request->get('status', 1),
        'json_data' => json_encode($newConfigQuery),
        'type' => SystemConfigTypeEnum::BACKEND
    ];
    SystemConfig::setConfig(CacheKeys::UPDATE_SELLER_ID_UPSELL, $newConfig);
    dd($newConfig);
});

Route::prefix('/ses')->group(function () {
    Route::get('/identities', function () {
        $service = new \Modules\Marketing\Services\AmazonSesService();
        $service->listIdentities();
    });

    Route::get('/status', function (Request $request) {
        $identify = $request->get('identity');
        $service = new \Modules\Marketing\Services\AmazonSesService();
        $status = $service->getVerificationStatus($identify);
        $statusName = SesSenderStatus::getKey($status);
        graylogInfo('Get status', [
            'category' => 'ses',
            'identity' => $identify,
            'status' => $status,
            'status_name' => $statusName,
        ]);
    });
});

Route::get('/shopify-test/{id}', [TestController::class, 'deleteShopifySession']);

Route::get('/temp-put-cache', function () {
    cache()->store('altredis')->tags('tag1')->put('test1', 'lmao', 60);
    cache()->tags('tag1')->put('test1', 'haha', 60);
    return 1;
});

if (app()->environment(['local', 'development'])) {
    Route::get('/next-send-at', [MarketingController::class, 'updateSubscriberNextSendAt']);
}

Route::get('/temp-check', function () {
    //check we save by default way
    $check1 = Cache::tags('tag1')->get('test1');
    $check2 = Cache::get('test1');
    $check3 = Cache::store('altredis')->tags(['swayok1âcscasctag'])->get('test1');
    $check4 = Cache::store('altredis')->get('test1');


    $check5 = cache()->store('altredis')->tags(['swayok1âcscasctag'])->get('test1');
    $check6 = cache()->store('altredis')->get('test1');
    dd($check1, $check2, $check3, $check4, $check5, $check6);
});

Route::get('/temp-remove', function () {
    $result = Cache::store('altredis')->tags(['swayok1tag'])->forget('test1');
    dd($result);
});

Route::post('/assign-priority-supplier', [TestController::class, 'testAssignPrioritySupplier']);

Route::prefix('test')->group(function () {
    Route::get('/send_custom_email', function () {
        // call to command
        Artisan::call('app:send-custom-email-template');
        return 'command app:send-custom-email-template called';
    });
    Route::get('/put-alternative-cache', [TestController::class, 'putAlternativeCache']);
    Route::get('/get-alternative-cache', [TestController::class, 'getAlternativeCache']);
    Route::get('/forget-alternative-cache', [TestController::class, 'forgetAlternativeCache']);

    Route::get('count-pending-campaign', function () {
        $limitUsers = CampaignService::listUserLimitCreateCampaign();
        return ImportCampaignsData::query()
            ->where('status', ImportCampaignStatusEnum::PENDING)
            ->where('type', ImportCampaignTypeEnum::REGULAR)
            ->when(!empty($limitUsers), function ($q) use ($limitUsers) {
                $q->whereNotIn('seller_id', $limitUsers);
            })
            ->whereNotNull('campaign_id')
            ->count();
    });
    Route::get('/change-role-seller', function () {
        $processId = InactiveService::generateProcessId();
        $fileName = 'API:' . basename(__FILE__);
        $limit = 1000;
        $type = 'customer_having_order';
        $category = ['category' => $type];

        InactiveService::logToGraylog($processId, $fileName, 'Start scan limit:' . $limit . ' ' . $type, $category);
        $userIds = User::query()
            ->where('role', 'customer')
            ->whereHas('seller_orders')
            ->limit($limit)
            ->pluck('id')
            ->toArray();

        if (empty($userIds)) {
            InactiveService::logToGraylog($processId, $fileName, 'No more sellers to scan', $category);
            return 0;
        }

        $total = count($userIds);
        $exampleUserIdToLog = array_slice($userIds, 0, 20);
        $category["userIds"] = $exampleUserIdToLog;
        InactiveService::logToGraylog($processId, $fileName, 'Total sellers scan: ' . $total, $category, true);

        $result = User::query()
            ->whereIn('id', $userIds)
            ->update(['role' => 'seller']);

        InactiveService::logToGraylog($processId, $fileName, 'Update result: ' . $result, $category, true);
        return 0;
    });
    Route::get('/', [TestController::class, 'test']);
    Route::get('/crawl-order', [TestController::class, 'crawlOrder']);

    Route::get('/kientest', [TestController::class, 'kientest']);
    Route::post('/update-custom-options/{product}', [TestController::class, 'updateCustomOptions']);
    Route::get('/insert-report', [TestController::class, 'insertSaleReport']);
    Route::get('/sync-order', [TestController::class, 'syncOrder']);
    Route::get('/update-fulfill', [TestController::class, 'updateFulfillOrderIdByLog']);

    Route::get('/deploy', function () {
        return 'ok';
    });

    Route::prefix('products')->group(function () {
        Route::get('/total-es-campaigns', [TestController::class, 'getTotalEsCampaigns'])->name('total-es-campaigns-today');
    });

    Route::prefix('admin')->group(function () {
        Route::get('/mail-logs', [TestController::class, 'sendEmailConfirm']);
    });

    Route::get('/refresh-feed/{id}', [TestController::class, 'refreshFeed']);
    Route::get('/cache-status', [TestController::class, 'cacheStatus']);
    Route::get('/clear-cache', [TestController::class, 'clearCache']);
    Route::get('/render-pb', [TestController::class, 'renderPb']);
    Route::get('/update-stripe', [TestController::class, 'updateDomainGatewayStripe']);
    Route::get('/delete-stripe', [TestController::class, 'deleteGatewayStripe']);

    // test sms
    Route::get('/sms-order-confirmation/{order}', [SmsController::class, 'orderConfirmationSms']);

    //test send email
    Route::post('/test-send-mail', [TestController::class, 'testSendMail']);


    Route::get('/juno', function () {
        $developerId = config('services.lianlian.developer_id');
        $accessToken = config('services.lianlian.access_token');
        $region = config('services.lianlian.region');
        $privateKey = config('services.lianlian.private_key');

        $lianlian = new LianLian($developerId, $accessToken, $privateKey, $region);

        return $lianlian->getAllNetworkContacts();
    });

    Route::get('/discord', function () {
        return config('senprints.discord_webhook_url.dev_chat');
    });
    Route::post('/send-test-email', [TestController::class, 'sendTestEmail']);

    Route::get('/tele', function () {
        $sellerId = 122335;

        $request = new Illuminate\Http\Request([
            'date_type' => 'today',
            'seller_id' => $sellerId
        ]);

        return (new SellerController())->dashboard($request);
    });

    // Test route for Ideogram image migration
    Route::prefix('ideogram')->group(function () {
        Route::get('/migrate/{id?}', function ($id = 10) {
            // Dispatch job to migrate images from Ideogram
            \App\Jobs\MigrateIdeogramImagesToLocal::dispatch($id);

            return [
                'success' => true,
                'message' => "Migration job for record ID: {$id} has been dispatched to queue 'ideogram-migration'",
                'timestamp' => now()->toDateTimeString()
            ];
        });

        // Additional test route to see record details
        Route::get('/record/{id}', function ($id) {
            $record = \App\Models\AiImageGeneration::find($id);

            if (!$record) {
                return [
                    'success' => false,
                    'message' => "Record with ID: {$id} not found"
                ];
            }

            return [
                'success' => true,
                'record' => $record,
                'has_ideogram_urls' => collect($record->result_images)->filter(function ($url) {
                        return \Illuminate\Support\Str::contains($url, ['ideogram.ai', 'ideo.ai']);
                    })->count() > 0
            ];
        });
    });

    Route::post('/move-user-to-customer', [TestController::class, 'moveUserToCustomer']);

});

Route::prefix('sys')->group(function () {
    Route::get('/update-sign', [PayoutController::class, 'updateSignature']);
});
