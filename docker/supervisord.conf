[unix_http_server]
file=/var/run/supervisord/supervisord.sock

[supervisord]
nodaemon=true
logfile=/var/run/supervisord/supervisord.log    ; supervisord log file
logfile_maxbytes=100MB                           ; maximum size of logfile before rotation
logfile_backups=10                              ; number of backed up logfiles
pidfile=/var/run/supervisord/supervisord.pid    ; pidfile location
user=root                                       ; default user

[supervisorctl]
serverurl=unix:///var/run/supervisord/supervisord.sock

[include]
files = /etc/supervisor.d/*.conf

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
