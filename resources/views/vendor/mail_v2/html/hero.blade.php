<table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr>
            <td class="new_email_body new_tc">
                <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                    cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="new_content_cell new_content_b new_py new_tc">
                                <table role="presentation" class="new_column" width="100%" border="0" cellspacing="0"
                                    cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="new_column_cell new_py new_tc">
                                                @if (isset($logo))
                                                    <table role="presentation" class="new_ic_h" align="center" width="80"
                                                        border="0" cellspacing="0" cellpadding="0">
                                                        <tbody>
                                                            <tr>
                                                                <td class="new_default_b"><img role="img"
                                                                        src="{{ getFullPathEmailResource($logo) }}"
                                                                        width="48" height="48" alt="Welcome"
                                                                        style="max-width:48px;"></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                @endif
                                                @if (!empty($subject))
                                                    <h1>{{ $subject }}</h1>
                                                @endif
                                                @if (isset($message))
                                                    <p class="new_lead new_tm">
                                                        {{ $message }}
                                                    </p>
                                                @endif
                                                @if (isset($button_url, $button_text))
                                                    @if (isset($template) && $template === 'order_confirmation')
                                                        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
                                                               cellspacing="0" cellpadding="0" style="display: inline-block;">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="new_accent_b">
                                                                        <a href="{{ clickTrackingMail($baseUrl, $button_url, $hashId) }}" target="_blank">
                                                                            <span>{{ $button_text }}</span>
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    @else
                                                        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
                                                            cellspacing="0" cellpadding="0">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="new_accent_b">
                                                                        <a href="{{ clickTrackingMail($baseUrl, $button_url, $hashId) }}" target="_blank">
                                                                            <span>{{ $button_text }}</span>
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    @endif
                                                @endif
                                                @isset($page_contact_us)
                                                    @if (isset($template) && $template === 'order_confirmation')
                                                        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
                                                               cellspacing="0" cellpadding="0" style="margin-left: 0.75rem; display: inline-block;">
                                                            <tbody>
                                                            <tr>
                                                                <td class="new_accent_b" style="background-color: #838383;">
                                                                    <a href="{{ $page_contact_us }}" rel="noopener">
                                                                        Contact Us
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    @else
                                                        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
                                                               cellspacing="0" cellpadding="0" style="margin-top: 15px;">
                                                            <tbody>
                                                            <tr>
                                                                <td class="new_accent_b" style="background-color: #838383;">
                                                                    <a href="{{ $page_contact_us }}" rel="noopener">
                                                                        Contact Us
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    @endif
                                                @endisset
                                                @if (!empty($button_sub_title))
                                                    <table role="presentation" class="new_tc new_pt" align="center" border="0"
                                                           cellspacing="0" cellpadding="0">
                                                        <tbody>
                                                        <tr>
                                                            <td>
                                                                <small>{{ $button_sub_title }}</small>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                @endif
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
    </tbody>
</table>
