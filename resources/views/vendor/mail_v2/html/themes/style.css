/* Reset */
table, td, div {
    box-sizing: border-box; }

  table, td {
    mso-table-lspace: 0pt;
    mso-table-rspace: 0pt; }

  html,
  body {
    width: 100% !important;
    min-width: 100%;
    Margin: 0;
    padding: 0;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%; }

  .new_email_body td, .new_email_body div, .new_email_body a, .new_email_body span {
    line-height: inherit; }

  .new_email_body a {
    text-decoration: none; }

  #outlook a {
    padding: 0; }

  img {
    outline: none;
    border: 0;
    text-decoration: none;
    -ms-interpolation-mode: bicubic;
    clear: both;
    line-height: 100%; }

  table {
    border-spacing: 0;
    mso-table-lspace: 0pt;
    mso-table-rspace: 0pt; }

  td {
    vertical-align: top; }

  /* Grid */
  .new_email_table,
  .new_content_section,
  .new_column,
  .new_col_1,
  .new_col_12,
  .new_col_2,
  .new_col_3,
  .col_thumb,
  .col_description {
    width: 100%;
    min-width: 100%;
    min-width: 0 !important; }

  .new_email_body,
  .new_content_cell,
  .new_col_1,
  .new_col_12,
  .new_col_2,
  .new_col_3,
  .col_thumb,
  .col_description {
    font-size: 0 !important;
    line-height: 100%; }

  .new_col_1,
  .new_col_12,
  .new_col_2,
  .new_col_3,
  .col_thumb,
  .col_description {
    display: inline-block;
    vertical-align: top; }

  .new_content_section {
    max-width: 640px;
    Margin: 0 auto;
    text-align: center; }

  .new_email_body,
  .new_content_cell,
  .new_column_cell {
    padding-left: 8px;
    padding-right: 8px; }

  .new_email_start {
    padding-top: 32px; }

  .new_email_end {
    padding-bottom: 32px; }

  .new_column_cell {
    vertical-align: top; }

  .new_col_1 {
    max-width: 156px; }

  .new_col_12 {
    max-width: 192px; }

  .new_col_2 {
    max-width: 312px; }

  .new_col_3 {
    max-width: 468px; }

  .col_thumb {
    max-width: 80px; }

  .col_description {
    max-width: 372px; }

  .new_column_cell,
  .new_column_cell p,
  .new_ebtn a,
  .new_ebtn span
  .new_column_cell h1,
  .new_column_cell h2,
  .new_column_cell h3,
  .new_column_cell h4 {
    font-family: Arial, Helvetica, sans-serif; }

  .new_ebtn a,
  .new_ebtn span
  .new_column_cell h1,
  .new_column_cell h2,
  .new_column_cell h3,
  .new_column_cell h4 {
    font-weight: bold; }

  .new_column_cell,
  .new_column_cell p {
    font-size: 16px;
    color: #616161; }

  .new_column_cell p {
    line-height: 23px;
    mso-line-height-rule: exactly;
    Margin-top: 0;
    Margin-bottom: 24px; }
    .new_column_cell p.new_lead {
      font-size: 20px;
      line-height: 27px; }

  .new_column_cell h1,
  .new_column_cell h2,
  .new_column_cell h3,
  .new_column_cell h4 {
    padding: 0;
    Margin-left: 0;
    Margin-right: 0;
    Margin-top: 16px;
    Margin-bottom: 8px;
    color: #212121; }
    .new_column_cell h1 a,
    .new_column_cell h1 a span,
    .new_column_cell h2 a,
    .new_column_cell h2 a span,
    .new_column_cell h3 a,
    .new_column_cell h3 a span,
    .new_column_cell h4 a,
    .new_column_cell h4 a span {
      color: #212121; }

  .new_column_cell h1 {
    font-size: 26px;
    line-height: 34px; }

  .new_column_cell h2 {
    font-size: 20px;
    line-height: 26px; }

  .new_column_cell h3 {
    font-size: 18px;
    line-height: 23px; }

  .new_column_cell h4 {
    font-size: 14px;
    line-height: 18px; }

  .footer_b .new_column_cell,
  .footer_b .new_column_cell p,
  .footer_b .new_column_cell a,
  .footer_b .new_column_cell a span {
    color: #ffffff; }

  .new_email_body,
  html,
  body {
    background-color: #d9dcee; }

  .new_column_cell a,
  .new_column_cell a span,
  .new_column_cell.new_tp,
  .new_column_cell .new_tp {
    color: #3f51b5; }

  .new_nav_menu {
    text-align: right;
    padding-top: 24px; }
    .new_nav_menu p {
      line-height: 100%; }

  .hero_image {
    background-repeat: no-repeat;
    background-position: 50% 0; }

  .hdr_menu {
    text-align: right;
    padding-top: 10px; }
    .hdr_menu p {
      line-height: 100%; }

  .hdr_menu a,
  .new_email_body a.blink,
  .new_email_body a.blink:visited {
    text-decoration: none; }

  .new_email_body .new_logo_c {
    line-height: 100%; }

  .new_logo_c img {
    width: auto;
    height: 26px; }

  .new_email_body .new_fsocial {
    line-height: 100%; }

  .new_fsocial img {
    width: 24px;
    height: 24px; }

  .hr_ep {
    font-size: 0;
    line-height: 1px;
    mso-line-height-rule: exactly;
    min-height: 1px;
    overflow: hidden;
    height: 2px;
    background-color: transparent !important; }

  .new_content_b {
    background-color: #ffffff; }

  .new_accent_b {
    background-color: #ff4081; }

  .new_default_b {
    background-color: #3f51b5; }

  .footer_b {
    background-color: #8d8f9a; }

  img {
    max-width: 200px; }

  .new_column_cell .new_imgr,
  .new_column_cell .new_imgr img {
    width: 100%;
    height: auto;
    clear: both;
    font-size: 0;
    line-height: 100%; }

  .new_column_cell .new_imgr a,
  .new_column_cell .new_imgr span {
    line-height: 1; }

  .new_ebtn,
  .new_ebtn_xs,
  .new_ic_h,
  .hr_rl {
    display: table;
    margin-left: auto;
    margin-right: auto; }

  .new_ebtn td {
    font-size: 15px;
    font-weight: bold;
    padding: 9px 18px;
    line-height: 22px;
    text-transform: uppercase;
    mso-line-height-rule: exactly;
    border-radius: 3px;
    text-align: center; }
    .new_ebtn td a {
      text-decoration: none; }
    .new_ebtn td a,
    .new_ebtn td a span {
      color: #ffffff; }

  .new_ic_h td {
    padding: 16px;
    text-align: center;
    vertical-align: middle;
    line-height: 100%;
    mso-line-height-rule: exactly;
    border-radius: 100px; }

  .new_ic_h img {
    line-height: 100%; }

  .email_summary {
    display: none;
    font-size: 1px;
    line-height: 1px;
    max-height: 0px;
    max-width: 0px;
    opacity: 0;
    overflow: hidden; }

  .new_brt {
    border-radius: 3px 3px 0 0; }

  .new_brb {
    border-radius: 0 0 3px 3px; }

  .new_bra {
    border-radius: 3px; }

  .new_braf {
    border-radius: 200px; }

  .new_column_cell.new_tm,
  .new_column_cell .new_tm,
  .new_column_cell .new_tm a,
  .new_column_cell .new_tm span {
    color: #9E9E9E; }

  .new_column_cell.sc,
  .new_column_cell .sc,
  .new_column_cell.sc p,
  .new_column_cell.sc a,
  .new_column_cell.sc a span {
    color: #ffffff; }

  .tdel {
    text-decoration: line-through; }

  .new_tc {
    text-align: center; }

  .new_tc .new_imgr img {
    margin-left: auto;
    margin-right: auto; }

  .new_tl {
    text-align: left; }

  table.new_tl {
    margin-left: 0;
    margin-right: auto; }

  .new_tr {
    text-align: right; }

  table.new_tr {
    margin-left: auto;
    margin-right: 0; }

  .new_py {
    padding-top: 16px;
    padding-bottom: 16px; }

  .new_px {
    padding-left: 16px;
    padding-right: 16px; }

  .new_pxs {
    padding-left: 8px;
    padding-right: 8px; }

  .new_pt {
    padding-top: 16px; }

  .new_pte {
    padding-top: 32px; }

  .new_pb {
    padding-bottom: 16px; }

  .new_pb_xs {
    padding-bottom: 8px; }

  .new_pbe {
    padding-bottom: 24px; }

  .new_pte_lg {
    padding-top: 64px; }

  .pl_0,
  .new_content_cell.pl_0 {
    padding-left: 0; }

  .pr_0,
  .new_content_cell.pr_0 {
    padding-right: 0; }

  .new_column_cell .new_mte {
    margin-top: 32px; }

  .new_column_cell .new_mt {
    margin-top: 16px; }

  .new_column_cell .new_mt_xs {
    margin-top: 8px; }

  .new_column_cell .new_mt_0 {
    margin-top: 0; }

  .new_column_cell .new_mb_0 {
    margin-bottom: 0; }

  .new_column_cell .new_mb_xs {
    margin-bottom: 8px; }

  .new_column_cell .new_mb {
    margin-bottom: 16px; }

  .new_column_cell .new_mbe {
    margin-bottom: 32px; }

  .bt {
    border-top: 1px solid; }

  .new_bb {
    border-bottom: 1px solid; }

  .bt,
  .new_bb {
    border-color: #E0E0E0; }

  .clear {
    content: ' ';
    display: block;
    clear: both;
    height: 1px;
    overflow: hidden;
    font-size: 0; }

  @media only screen {
    /* latin */
    @font-face {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v16/CWB0XYA8bzo0kSThX0UTuA.woff2) format("woff2");
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215; }
    /* latin */
    @font-face {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 700;
      src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v16/d-6IYplOFocCacKzxwXSOFtXRa8TVwTICgirnJhmVJw.woff2) format("woff2");
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215; }
    .new_column_cell,
    .new_column_cell p,
    .new_ebtn a,
    .new_ebtn span
  .new_column_cell h1,
    .new_column_cell h2,
    .new_column_cell h3,
    .new_column_cell h4 {
      font-family: "Roboto", sans-serif !important;
      font-weight: 400 !important; }
    .new_ebtn a,
    .new_ebtn span,
    .new_column_cell strong,
    .new_column_cell h1,
    .new_column_cell h2,
    .new_column_cell h3,
    .new_column_cell h4 {
      font-weight: 700 !important; }
    .new_column_cell a {
      display: inline-block; }
      .new_column_cell a img {
        vertical-align: middle; }
    .new_ebtn td {
      padding: 0 !important; }
    .new_ebtn a {
      display: block !important;
      padding: 7px 18px !important;
      line-height: 26px !important; }
      .new_ebtn a span {
        display: block !important;
        text-align: center !important;
        vertical-align: top !important;
        line-height: inherit !important; } }

  @media (max-width: 657px) {
    .new_col_1,
    .new_col_12,
    .new_col_2,
    .new_col_3,
    .col_thumb,
    .col_description {
      max-width: none !important; }
    .new_nav_menu {
      padding-top: 18px !important; }
    .new_email_start {
      padding-top: 8px !important; }
    .new_email_end {
      padding-bottom: 8px !important; }
    .new_nav_menu,
    .new_logo_c {
      text-align: center !important; }
    .new_email_start .new_content_cell {
      position: relative; }
    .col_nav {
      width: auto !important;
      max-width: none !important;
      position: absolute;
      right: 8px;
      top: 2px; }
    .new_pte_lg,
    .new_py.new_pte_lg {
      padding-top: 32px !important; }
    .switch_xs {
      text-align: left !important; }
    .new_switch_tc {
      text-align: center !important; }
    .new_switch_tc table.new_tl {
      float: none !important;
      margin-left: auto !important;
      margin-right: auto !important; }
    .hide {
      max-height: 0 !important;
      display: none !important;
      mso-hide: all !important;
      overflow: hidden !important;
      font-size: 0 !important; } }

.ts {
    font-size: 11px !important;
}

.tb {
    font-weight: bold;
}

.new_bg-code {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    border: 1px solid #efefef;
}

.new_discount_code {
    border: 1px dotted #ff4081;
    padding: 3px;
    color: #ff4081;
    font-weight: bold;
}
