<table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tbody>
    <tr>
        <td class="new_email_body new_tc">
            <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
            <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                   cellpadding="0">
                <tbody>
                <tr>
                    <td class="new_content_cell new_content_b new_py new_tc">
                        @foreach ($comboSets as $set)
                            <div style="display: flex !important; gap: 20px;">
                                <div style="width: fit-content;">
                                    <img class="new_bra" role="img"
                                         src="{{ sendMailImgUrl($set['thumb_url'], 'thumb') }}"
                                         width="200" height="250" style="max-width:200px;">
                                </div>
                                <div style="flex: 1 1 0;">
                                    <table role="presentation" class="new_column" width="100%" border="0"
                                           cellspacing="0" cellpadding="0">
                                        <tbody>
                                        <tr>
                                            <td class="new_column_cell new_tl new_switch_tc" style="font-size: 0.9rem;">
                                                <h3 class="new_mb_0">
                                                    {{ $set['title'] }}
                                                </h3>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    @foreach ($set['products'] as $product)
                                        <div style="display: flex !important;">
                                            <!--[if (mso)|(IE)]><table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:624px;Margin:0 auto;"><tbody><tr><td width="156" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                            <div>
                                                <img class="new_bra" role="img"
                                                     src="{{ sendMailImgUrl($product['thumb_url'], 'thumb') }}"
                                                     width="60" height="72" style="max-width:60px;">
                                            </div>
                                            <!--[if (mso)|(IE)]></td><td width="484" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                            <div>
                                                <table role="presentation" class="new_column" width="100%" border="0"
                                                       cellspacing="0" cellpadding="0">
                                                    <tbody>
                                                    <tr>
                                                        <td class="new_column_cell new_tl new_switch_tc" style="font-size: 0.9rem;">
                                                            <h4>
                                                                <span class="new_tm product_qty">{{ $product['quantity'] }} x </span>
                                                                @php
                                                                    if (!empty($domain) && !empty($product['product_url']) && strpos($product['product_url'], '/') === 0)
                                                                    {
                                                                        $product['product_url'] = "https://" . $domain . $product['product_url'];
                                                                    }
                                                                @endphp
                                                                @if (!empty($domain) && !empty($product['product_url']))
                                                                    <a href="{{ clickTrackingMail($baseUrl, $product['product_url'], $hashId) }}" class="new_tm" target="_blank">{{ $product['product_name'] }}</a>
                                                                @else
                                                                    <span class="new_tm">{{ $product['product_name'] }}</span>
                                                                @endif
                                                            </h4>
                                                            <p class="new_mb_0 new_tm"  style="font-size: 12px !important;">
                                                                {{ productOptionsParser($product['options']) }}
                                                            </p>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!--[if (mso)|(IE)]></td><td width="484" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                        </div>
                                    @endforeach
                                </div>
                                <div style="float: right; margin-right: 10px; max-width: 145px;">
                                    <table role="presentation" class="new_column" width="100%" border="0"
                                           cellspacing="0" cellpadding="0">
                                        <tbody>
                                        <tr>
                                            <td class="new_column_cell new_py new_tr new_switch_tc">
                                                <p class="new_mb_0 new_tp product_price">
                                                    {{ $set['combo_price'] ?? '' }}</p>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endforeach
                        {{ $slot }}
                    </td>
                </tr>
                </tbody>
            </table>
            <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
        </td>
    </tr>
    </tbody>
</table>
