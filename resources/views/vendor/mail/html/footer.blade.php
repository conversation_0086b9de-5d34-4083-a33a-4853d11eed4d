<table role="presentation" class="email_table" width="100%" border="0" cellspacing="0" cellpadding="0"
    style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;border-spacing: 0;width: 100%;min-width: 0 !important;">
    <tbody>
        <tr>
            <td class="email_body email_end tc"
                style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;line-height: 100%;padding-left: 8px;padding-right: 8px;padding-bottom: 32px;background-color: #d9dcee;text-align: center;font-size: 0 !important;">
                <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="content_section" width="100%" border="0" cellspacing="0"
                    cellpadding="0"
                    style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;border-spacing: 0;width: 100%;max-width: 640px;margin: 0 auto;text-align: center;min-width: 0 !important;">
                    <tbody>
                        <tr>
                            <td class="content_cell content_b brb tc" height="16"
                                style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;line-height: inherit;padding-left: 8px;padding-right: 8px;background-color: #ffffff;border-radius: 0 0 3px 3px;text-align: center;font-size: 0 !important;">
                                &nbsp; </td>
                        </tr>
                        <tr>
                            <td class="content_cell py brb tc"
                                style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;line-height: inherit;padding-left: 8px;padding-right: 8px;border-radius: 0 0 3px 3px;text-align: center;padding-top: 16px;padding-bottom: 16px;font-size: 0 !important;">
                                <!--[if (mso)|(IE)]><table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:624px;Margin:0 auto;"><tbody><tr><td width="468" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                <div class="col_3"
                                    style="box-sizing: border-box;width: 100%;line-height: inherit;display: inline-block;vertical-align: top;max-width: 468px;min-width: 0 !important;font-size: 0 !important;">
                                    <table role="presentation" class="column" width="100%" border="0" cellspacing="0"
                                        cellpadding="0"
                                        style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;border-spacing: 0;width: 100%;min-width: 0 !important;">
                                        <tbody>
                                            <tr>
                                                <td class="column_cell pt tl switch_tc"
                                                    style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;padding-left: 8px;padding-right: 8px;font-family: Arial, Helvetica, sans-serif;font-size: 16px;color: #616161;text-align: left;padding-top: 16px;line-height: inherit;">
                                                    <p class="mb_xs"
                                                        style="font-family: Arial, Helvetica, sans-serif;font-size: 16px;color: #616161;line-height: 23px;mso-line-height-rule: exactly;margin-top: 0;margin-bottom: 8px;">
                                                        ©{{ date('Y') }} {{ $data['store_name'] }} <br>
                                                        <span class="tm"
                                                            style="line-height: inherit;color: #9E9E9E;">{{ $data['store_info']['address'] }}</span>
                                                    </p>
                                                    <p class="mb_0"
                                                        style="font-family: Arial, Helvetica, sans-serif;font-size: 16px;color: #616161;line-height: 23px;mso-line-height-rule: exactly;margin-top: 0;margin-bottom: 0;">
                                                        <a href="{{ $data['base_url'] }}/about"
                                                            style="line-height: inherit;text-decoration: none;color: #3f51b5;"><span
                                                                style="line-height: inherit;color: #3f51b5;">About</span></a>
                                                        &nbsp; · &nbsp; <a href="{{ $data['base_url'] }}/collections"
                                                            style="line-height: inherit;text-decoration: none;color: #3f51b5;"><span
                                                                style="line-height: inherit;color: #3f51b5;">Products</span></a>
                                                        &nbsp; · &nbsp; <a href="{{ $data['base_url'] }}/contact"
                                                            style="line-height: inherit;text-decoration: none;color: #3f51b5;"><span
                                                                style="line-height: inherit;color: #3f51b5;">Support</span></a>
                                                        &nbsp; · &nbsp; <a href="{{ $data['base_url'] }}/unsubscribe"
                                                            style="line-height: inherit;text-decoration: none;color: #3f51b5;"><span
                                                                style="line-height: inherit;color: #3f51b5;">Unsubscribe</span></a>
                                                    </p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if (mso)|(IE)]></td><td width="156" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                <div class="col_1"
                                    style="box-sizing: border-box;width: 100%;line-height: inherit;display: inline-block;vertical-align: top;max-width: 156px;min-width: 0 !important;font-size: 0 !important;">
                                    <table role="presentation" class="column" width="100%" border="0" cellspacing="0"
                                        cellpadding="0"
                                        style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;border-spacing: 0;width: 100%;min-width: 0 !important;">
                                        <tbody>
                                            <tr>
                                                <td class="column_cell pt tr switch_tc"
                                                    style="box-sizing: border-box;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;padding-left: 8px;padding-right: 8px;font-family: Arial, Helvetica, sans-serif;font-size: 16px;color: #616161;text-align: right;padding-top: 16px;line-height: inherit;">
                                                    <p class="fsocial mb_0 tm"
                                                        style="font-family: Arial, Helvetica, sans-serif;font-size: 16px;color: #9E9E9E;line-height: 100%;mso-line-height-rule: exactly;margin-top: 0;margin-bottom: 0;">
                                                        @php
                                                            $accounts = $data['store_info']['social_accounts'];
                                                        @endphp
                                                        @if (isset($accounts['facebook']) && strlen($accounts['facebook']) > 0)
                                                            <a href="{{ $accounts['facebook'] }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-facebook.png"
                                                                    width="24" height="24" alt="Facebook"
                                                                    style="max-width: 24px;outline: none;border: 0;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;line-height: 100%;width: 24px;height: 24px;"></a>
                                                            &nbsp;
                                                        @endif
                                                        @if (isset($accounts['twitter']) && strlen($accounts['twitter']) > 0)
                                                            <a href="{{ $accounts['twitter'] }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-twitter.png"
                                                                    width="24" height="24" alt="Twitter"
                                                                    style="max-width: 24px;outline: none;border: 0;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;line-height: 100%;width: 24px;height: 24px;"></a>
                                                            &nbsp;
                                                        @endif
                                                        @if (isset($accounts['instagram']) && strlen($accounts['instagram']) > 0)
                                                            <a href="{{ $accounts['instagram'] }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-instagram.png"
                                                                    width="24" height="24" alt="Instagram"
                                                                    style="max-width: 24px;outline: none;border: 0;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;line-height: 100%;width: 24px;height: 24px;"></a>
                                                            &nbsp;
                                                        @endif
                                                        @if (isset($accounts['pinterest']) && strlen($accounts['pinterest']) > 0)
                                                            <a href="{{ $accounts['pinterest'] }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-pinterest.png"
                                                                    width="24" height="24" alt="Pinterest"
                                                                    style="max-width: 24px;outline: none;border: 0;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;line-height: 100%;width: 24px;height: 24px;"></a>
                                                        @endif
                                                    </p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
    </tbody>
</table>
