@component('mail::message', ['data' => $data])
    @php
        $orderNumber = Arr::get($data, 'order.order_number');
    @endphp
    @component('mail::hero', [
            'subject' => "Your order #{$orderNumber} was cancelled on your request.",
        ])
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            Please allow 1-2 weeks for the refund to appear on your bank statement
        </p>
    @endcomponent
    @php
        $supportEmail = Arr::get($data, 'store_info.email');
        $storeName = Arr::get($data, 'store_info.name');
    @endphp
    @component('mail::content')
        <p class="new_tc">
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br />
            Thank you for shopping at {{ $storeName }}!<br />
            See you soon,<br />
        </p>
        <p class="new_tc">
            The {{ $storeName }} Team<br />
        </p>
    @endcomponent
@endcomponent
