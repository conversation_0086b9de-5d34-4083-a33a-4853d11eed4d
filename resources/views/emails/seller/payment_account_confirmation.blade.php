@component('mail::message', ['data' => $data])
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::hero', [
        'logo' => 'user_white.png',
        'button_url' => $data['confirm_url'],
        'button_text' => 'Confirm',
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
        @slot('subject')
            Payment Account Confirmation
        @endslot
        @slot('message')
            We would like to confirm that your Payment account have been recently changed<br />
            <p class="bg-code">
                <span style="font-weight: bold;">Payment Info</span><br />
                <span>{{ $data['payment']['payment_type'] }}</span><br />
                <span>{{ $data['payment']['account_id'] }}</span><br />
                <span>{{ $data['payment']['account_name'] }}</span><br />
            </p>
        @endslot
    @endcomponent
    @component('mail::content')
        <p>
            If you initiated this change, there
            is
            nothing to worry about. However, if you do not recall updating anything, please do log into your account at
            www.senprints.com and change your password.
            As ever, do not hesitate to contact <NAME_EMAIL> if you have any questions or concerns.
            Hope to see you back soon at SenPrints!
        </p>

        <p>
            Cheers,<br />
            The SenPrints Team
        </p>
    @endcomponent
@endcomponent
