<?php

namespace DummyNamespace;

use ScoutElastic\Searchable;
use Illuminate\Database\Eloquent\Model;

class DummyClass extends Model
{
    use Searchable;

    /**
     * @var string
     */
    protected $indexConfigurator = DummyIndexConfigurator;

    /**
     * @var array
     */
    protected $searchRules = [
        DummySearchRule
    ];

    /**
     * @var array
     */
    protected $mapping = [
        //
    ];
}