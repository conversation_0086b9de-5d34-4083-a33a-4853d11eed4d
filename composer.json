{"name": "senprints/backend-apis", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-imagick": "*", "ext-intl": "*", "ext-json": "*", "ext-pdo": "*", "ext-redis": "*", "ext-simplexml": "*", "2captcha/2captcha": "^1.2", "awobaz/compoships": "^2.1", "babenkoivan/scout-elasticsearch-driver": "*", "bacon/bacon-qr-code": "^2.0", "bensampo/laravel-enum": "^6.3", "bilfeldt/laravel-http-client-logger": "*", "bschmitt/laravel-amqp": "^2.0", "cache/redis-adapter": "^1.0", "cloudflare/sdk": "^1.3", "cloudinary/cloudinary_php": "^2", "crispchat/php-crisp-api": "^1.7", "darkaonline/l5-swagger": "^8.6", "doctrine/dbal": "^3.0", "google/apiclient": "2.13.1", "google/cloud-vision": "^1.5", "guzzlehttp/guzzle": "^7.0.1", "guzzlehttp/psr7": "^2.0.0", "hashids/hashids": "^5.0", "hedii/laravel-gelf-logger": "^8.1", "intervention/image": "^2.7", "j2team/laravel-rdap": "dev-main", "klaviyo/api": "^10.0", "laravel/framework": "^10.0", "laravel/socialite": "^5.0", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.22", "maatwebsite/excel": "^3.1", "marvinlabs/laravel-discord-logger": "1.4.1", "mavinoo/laravel-batch": "^2.2", "myclabs/deep-copy": "^1.10", "omnipay/paypal": "^3.0", "php-http/guzzle7-adapter": "^1.1", "pragmarx/google2fa-laravel": "^2.1", "predis/predis": "^2.0", "propaganistas/laravel-disposable-email": "^2.2", "propaganistas/laravel-phone": "^5.0", "renoki-co/laravel-php-k8s": "^3.4", "senprints/campaign": "*@dev", "senprints/crisp-bot": "*@dev", "senprints/email-marketing-klaviyo": "*@dev", "senprints/marketing": "*@dev", "senprints/order-service": "*@dev", "senprints/seller-account": "*@dev", "senprints/seller-api": "*@dev", "senprints/seller-tier": "*@dev", "senprints/sharding-table": "*@dev", "senprints/shopify-api": "*@dev", "senprints/shopify-app": "*@dev", "senprints/tiktok-shop": "*@dev", "sentry/sentry-laravel": "^3.7", "spatie/laravel-data": "^4.6", "spatie/laravel-permission": "^5.0", "spatie/laravel-rate-limited-job-middleware": "^2.0", "spatie/laravel-sitemap": "^6.0", "stripe/stripe-php": "^10.10", "swayok/alternative-laravel-cache": "*@dev", "theiconic/name-parser": "^1.2", "twilio/sdk": "^7.7", "tymon/jwt-auth": "^2.0", "vladimir-yuldashev/laravel-queue-rabbitmq": "^13.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.9", "fakerphp/faker": "^1.9.1", "itsgoingd/clockwork": "^5.0", "kitloong/laravel-migrations-generator": "^7.0", "laradumps/laradumps": "^4.0", "laravel/telescope": "^4.15", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^7.0", "nunomaduro/larastan": "^2.5", "phpunit/phpunit": "^10.0", "senprints/dev-tool": "*@dev", "spatie/laravel-ignition": "^2.0", "spatie/laravel-ray": "^1.32"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": false, "dealerdirect/phpcodesniffer-composer-installer": false, "composer/package-versions-deprecated": false, "pestphp/pest-plugin": false}}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}, "extra": {"google/apiclient-services": ["Drive"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/functions.php", "helpers/domain.php", "helpers/Log.php", "helpers/SendMail.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "pwd=$(pwd); \n if [ ! -f \"$pwd/.env\" ]; \n then \n echo 'APP_NAME=SenPrints' > \"$pwd/.env\" \n fi"], "phpcs": "phpcs --standard=phpcs-laravel"}, "repositories": [{"type": "path", "url": "./modules/*"}, {"type": "path", "url": "./packages/*"}, {"type": "vcs", "url": "https://github.com/j2team/laravel-rdap"}]}