<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\ShardingTable\Enums\TableShardingEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $database = config("database.connections.mysql_sub.database");
        foreach (TableShardingEnum::asArray() as $table) {
            $query = DB::select("SHOW CREATE TABLE $table")[0]->{'Create Table'};
            DB::connection("mysql_sub")->statement(str_replace('CREATE TABLE ',
                "CREATE TABLE IF NOT EXISTS `$database`.", $query));
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
