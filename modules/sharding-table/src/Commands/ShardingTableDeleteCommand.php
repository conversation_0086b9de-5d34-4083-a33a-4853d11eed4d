<?php

namespace Modules\ShardingTable\Commands;

use App\Enums\DiscordChannel;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserRoleEnum;
use App\Models\Campaign;
use App\Models\IndexCampaign;
use App\Models\UserInfo;
use Illuminate\Console\Command;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;
use Modules\ShardingTable\Jobs\DeleteCampaignJob;
use Modules\ShardingTable\Jobs\DeleteFileJob;

class ShardingTableDeleteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sharding-table:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete synchronized data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        try {
            UserInfo::query()
                ->with('user')
                ->where('key', UserInfoKeyEnum::OLD_CONNECTION)
                ->whereHas('user', function ($query) {
                    $query->where('role', '!=', UserRoleEnum::CUSTOMER)
                        ->where('sharding_status', UserShardingStatusEnum::COMPLETED);
                })
                ->get()
                ->each(function (UserInfo $userInfo) {
                    DeleteFileJob::dispatch($userInfo->user_id, $userInfo->value);
                    $campaignModel = Campaign::query()->on($userInfo->value);
                    if ($userInfo->value === config('database.default')) {
                        $campaignModel = IndexCampaign::query();
                    }
                    $campaigns = $campaignModel
                        ->withTrashed()
                        ->select('id')
                        ->where('seller_id', $userInfo->user_id)
                        ->where('temp_status', TempStatusEnum::SYNCHRONIZED)
                        ->take(1000)
                        ->get()
                        ->pluck('id')
                        ->toArray();
                    if (count($campaigns)) {
                        DeleteCampaignJob::dispatch($campaigns, $userInfo->user_id, $userInfo->value);
                    } else {
                        UserInfo::query()
                            ->where([
                                'key' => UserInfoKeyEnum::OLD_CONNECTION,
                                'user_id' => $userInfo->user_id,
                            ])
                            ->update([
                                'key' => UserInfoKeyEnum::OLD_CONNECTION_DELETED,
                            ]);
                    }
                });
        } catch (\Throwable $e) {
            logToDiscord("Sharding table: ERROR: " . "{$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}", DiscordChannel::SHARDING_DATABASE);
        }
    }
}
