<?php

namespace Modules\ShardingTable\Jobs;

use App\Enums\CampaignRenderModeEnum;
use App\Enums\DiscordChannel;
use App\Models\Campaign;
use App\Models\File;
use App\Models\IndexCampaign;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Modules\ShardingTable\Enums\TempStatusEnum;

class InsertCampaignJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable;

    private ?Campaign $campaign;

    private User $seller;

    private IndexCampaign|Campaign $indexCampaign;

    private ?Campaign $sellerCampaign;

    public function __construct(IndexCampaign|Campaign $campaign, User $seller)
    {
        $this->campaign = Campaign::query()->on($seller->db_connection)->withTrashed()->find($campaign->id);
        $this->indexCampaign = $campaign;
        $this->seller = $seller;
        $this->onQueue('sharding-table-insert');
    }

    public function uniqueId(): string
    {
        return "sharding-insert-" . $this->indexCampaign->id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $this->initConnection();

            $this->validateCampaign();

            $this->deleteSellerCampaign();

            $this->syncCampaign();

            $this->syncDesigns();

            $this->syncProducts();

            $this->syncProductVariantsAndFiles();

            $this->updateSyncFlag();

            DB::commit();
            graylogInfo("Insert: Seller: {$this->seller->id} Campaign: {$this->indexCampaign?->id} successfully.", [
                'category' => 'sharding',
                'action' => 'insert',
                'campaign_data' => $this->indexCampaign->refresh()->toArray()
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            logToDiscord("Insert: Seller: {$this->seller->id} Campaign: {$this->indexCampaign?->id} error: " . "{$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}", DiscordChannel::SHARDING_DATABASE);
        }
    }

    private function initConnection(): void
    {
        config()->set([
            "database.connections.mysql_{$this->seller->id}" => config("database.connections.mysql_seller"),
            "database.connections.mysql_{$this->seller->id}.database" => config("database.connections.mysql.database") . "_{$this->seller->id}",
        ]);
    }

    private function validateCampaign(): void
    {
        if (!$this->campaign) {
            graylogInfo("Campaign {$this->indexCampaign->id} not found on database master.", [
                'category' => 'sharding',
                'action' => 'insert',
                'campaign_data' => $this->indexCampaign->refresh()->toArray()
            ]);
            $this->indexCampaign->forceDelete();
            DB::commit();
            $this->fail();
        }
    }

    private function deleteSellerCampaign(): void
    {
        Campaign::query()
            ->withTrashed()
            ->onSellerConnection($this->seller, shardingCompleted: false)
            ->where('id', $this->campaign->id)
            ->forceDelete();
    }

    private function syncCampaign(): void
    {
        //Get data campaign to sync
        $originalCampaign = $this->campaign->getOriginal();

        //Fix case render_mode is null
        if (!in_array($originalCampaign['render_mode'], CampaignRenderModeEnum::asArray())) {
            $originalCampaign['render_mode'] = CampaignRenderModeEnum::NATURE;
        }

        //Insert seller campaign
        Campaign::query()->onSellerConnection($this->seller, shardingCompleted: false)->insert($originalCampaign);

        //Query seller campaign inserted
        $this->sellerCampaign = Campaign::query()
            ->withTrashed()
            ->onSellerConnection($this->seller, shardingCompleted: false)
            ->find($this->campaign->id);
    }

    private function syncDesigns(): void
    {
        $this->campaign->load('designs');

        $designs = $this->campaign->designs;

        if ($designs->count()) {
            $this->sellerCampaign->designs()->forceDelete();
            $designChunks = $designs->chunk(500);
            foreach ($designChunks as $chunk) {
                File::query()->onSellerConnection($this->seller, shardingCompleted: false)->insert($chunk->toArray());
            }
        }
    }

    private function syncProducts(): void
    {
        $this->campaign->load('products');

        $products = $this->campaign->products;

        if ($products->count()) {
            // Delete product from seller table
            $this->sellerCampaign->products()->withTrashed()->forceDelete();
            //Fix case render_mode is null
            $products->map(function ($product) {
                if (!in_array($product->render_mode, CampaignRenderModeEnum::asArray())) {
                    $product->render_mode = CampaignRenderModeEnum::NATURE;
                };
            });
            // Insert product
            Product::query()->onSellerConnection($this->seller, shardingCompleted: false)->insert($products->toArray());
        }
    }

    private function syncProductVariantsAndFiles(): void
    {
        $this->campaign->products->load(['variants', 'files' => fn ($query) => $query->withoutGlobalScope('getActive')]);

        $this->campaign->products->each(function (Product $product) {
            $this->syncProductVariants($product);
            $this->syncFiles($product);
        });
    }

    private function syncProductVariants(Product $product): void
    {
        if ($product->variants->count()) {
            // Delete from seller table
            $product->variants->each(function (ProductVariant $variant) {
                ProductVariant::query()
                    ->onSellerConnection($this->seller, shardingCompleted: false)
                    ->where([
                        'product_id' => $variant->product_id,
                        'variant_key' => $variant->variant_key,
                        'location_code' => $variant->location_code,
                    ])
                    ->delete();
            });
            // Insert into seller table
            ProductVariant::query()
                ->onSellerConnection($this->seller, shardingCompleted: false)
                ->insert($product->variants->toArray());
        }
    }

    private function syncFiles(Product $product): void
    {
        if ($product->files->count()) {
            // Delete from seller table
            File::query()
                ->onSellerConnection($this->seller, shardingCompleted: false)
                ->whereIn('id', $product->files->pluck('id')->toArray())
                ->delete();

            // Insert into seller table
            File::query()->onSellerConnection($this->seller, shardingCompleted: false)->insert($product->files->toArray());
        }
    }

    private function updateSyncFlag(): void
    {
        //Update temp_status
        if ($this->indexCampaign instanceof IndexCampaign) {
            $this->indexCampaign->update(['temp_status' => TempStatusEnum::SYNCHRONIZED]);
        }
        $this->campaign->update(['temp_status' => TempStatusEnum::SYNCHRONIZED]);

        //Update sync_status to sync to es
        Product::query()
            ->onSellerConnection($this->seller, shardingCompleted: false)
            ->where('id', $this->campaign->id)
            ->orWhere('campaign_id', $this->campaign->id)
            ->update(['sync_status' => Product::SYNC_DATA_STATS_ENABLED]);
        DB::commit();
    }
}
