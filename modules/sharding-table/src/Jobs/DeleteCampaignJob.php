<?php

namespace Modules\ShardingTable\Jobs;

use App\Enums\DiscordChannel;
use App\Enums\FileTypeEnum;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Collection;
use App\Models\File;
use App\Models\IndexCampaign;
use App\Models\IndexProduct;
use App\Models\Product;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

class DeleteCampaignJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, Queueable;

    public function __construct(readonly private array $campaigns, readonly private int $sellerId, readonly private string $oldConnection)
    {
        $this->onQueue('sharding-table-delete');
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();
            Campaign::query()
                ->withTrashed()
                ->on($this->oldConnection)
                ->select('id')
                ->where('seller_id', $this->sellerId)
                ->whereIn('id', $this->campaigns)
                ->get()
                ->each(function (Campaign $campaign) {
                if ($this->oldConnection === 'mysql') {
                    (new SyncProductsToElasticSearchJob())->elasticDeleteProductsByProductIds($campaign->products->pluck('id')->toArray(), 'products_archived');
                    IndexProduct::query()
                        ->whereIn('id', $campaign->products->pluck('id')->toArray())
                        ->orWhere('id', $campaign->id)
                        ->withTrashed()->forceDelete();
                }
                (new SyncProductsToElasticSearchJob())->elasticDeleteProductsByProductIds($campaign->products->pluck('id')->toArray(), str_replace('mysql', 'products', $this->oldConnection));
                $campaign->products->each(function (Product $product) {
                    $product->variants()->delete();
                    $product->files()->delete();
                });
                $campaign->products()->forceDelete();
                $campaign->designs()->forceDelete();
                $campaign->forceDelete();
            });
            DB::commit();
            graylogInfo("Delete: Seller: {$this->sellerId} campaign successfully.", ['category' => 'sharding', 'action' => 'delete', 'campaign_ids' => $this->campaigns]);
        } catch (\Throwable $e) {
            DB::rollBack();
            logException($e, 'DeleteCampaignJob@handle', DiscordChannel::SHARDING_DATABASE);
        }
    }
}
