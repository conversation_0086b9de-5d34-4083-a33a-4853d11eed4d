<?php

namespace Modules\ShardingTable\Jobs;

use App\Enums\DiscordChannel;
use App\Enums\FileTypeEnum;
use App\Models\File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Traits\InitSellerConnection;

class DeleteFileJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, Queueable, InitSellerConnection;

    public function __construct(readonly protected int $sellerId, readonly string $oldConnection)
    {
        $this->onQueue('sharding-table-delete');
    }

    public function uniqueId(): string
    {
        return "sharding-delete-file-" . $this->sellerId;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();
            File::query()
                ->on($this->oldConnection)
                ->where('temp_status', TempStatusEnum::SYNCHRONIZED)
                ->where('type', FileTypeEnum::FONT)
                ->where('seller_id', $this->sellerId)
                ->delete();
            DB::commit();
            graylogInfo("Delete: Seller: {$this->sellerId} files successfully.", ['category' => 'sharding', 'action' => 'delete']);
        } catch (\Throwable $e) {
            DB::rollBack();
            logException($e, 'DeleteFileJob@handle', DiscordChannel::SHARDING_DATABASE);
        }
    }
}
