<?php
namespace Mo<PERSON>les\ShardingTable\Providers;

use Mo<PERSON>les\ShardingTable\Providers\ShardingTableServiceProvider;

class CommandServiceProvider extends ShardingTableServiceProvider
{
    public function boot()
    {
        $commandDir = dirname(__DIR__) . '/Commands';
        $commands = $this->scanFolder($commandDir);
        $_commands = array();
        foreach ($commands as $command) {
            if(file_exists($commandDir . '/' . $command)) {
                $command = basename($command, ".php");
                $_commands[] = "Modules\\ShardingTable\\Commands\\{$command}";
            }
        }
        $this->commands($_commands);
    }
}
