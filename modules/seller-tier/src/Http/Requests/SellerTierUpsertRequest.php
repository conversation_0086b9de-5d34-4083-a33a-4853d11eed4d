<?php
namespace Modules\SellerTier\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Modules\SellerTier\Models\SellerTier;

class SellerTierUpsertRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    public function rules()
    {
        return [
            'id' => [
                'nullable',
                Rule::exists(SellerTier::class)
            ],
            'name' => 'required|min:2|max:250',
            'required_sen_points' => 'required|numeric|min:0',
            'minimum_sen_points' => 'required|numeric|min:0',
            'seller_profit_rate' => 'required|numeric|min:0|max:100',
            'artist_profit_rate' => 'required|numeric|min:0|max:100',
            'sales_bonus' => 'required|numeric|min:0',
            'create_campaign_limit' => 'required|integer|min:0',
        ];
    }
}
