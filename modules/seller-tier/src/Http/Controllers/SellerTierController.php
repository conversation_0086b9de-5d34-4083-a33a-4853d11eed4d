<?php

namespace Modules\SellerTier\Http\Controllers;

use App\Enums\OrderPaymentStatus;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\ApiResponse;
use App\Traits\Contest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\SellerTier\Http\Requests\SellerTierCashbackRequest;
use Modules\SellerTier\Http\Requests\SellerTierUpsertRequest;
use Modules\SellerTier\Models\SellerTier;

class SellerTierController extends Controller
{
    use ApiResponse, Contest;

    public function index(Request $request): JsonResponse
    {
        $perPage = $request->input('per_page');
        $orderBy = $request->input('sort', 'id');
        $direction = $request->input('direction', 'ASC');
        $keyword = $request->input('keyword');

        $tiers = SellerTier::query()
            ->when(!empty($keyword), function ($query) use ($keyword) {
                $query->where(function ($sQuery) use ($keyword) {
                    $sQuery->where('name', 'LIKE', '%' . $keyword . '%');
                });
            })
            ->orderBy($orderBy, $direction)
            ->paginate($perPage);

        return $this->successResponse($tiers);
    }

    public function upsert(SellerTierUpsertRequest $request): JsonResponse
    {
        $tier = SellerTier::query()
            ->updateOrCreate(
                ['id' => $request->input('id')],
                $request->all()
            );

        return $this->successResponse($tier);
    }

    public function show($id): JsonResponse
    {
        $tier = SellerTier::findOrFail($id);

        return $this->successResponse($tier);
    }

    public function destroy($id): JsonResponse
    {
        SellerTier::where('id', $id)->delete();
        User::where('tier_id', $id)->update(['tier_id' => null]);

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function report(Request $request): JsonResponse
    {
        $q = $request->input('q');
        $tierId = $request->input('tier_id');
        $period = $request->input('period');
        $hourOffset = 7;
        $now = now()->addRealHours($hourOffset)->startOfDay();
        $period = $period ? Carbon::parse($period) : $now;
        $firstOfMonth = $period->clone()->startOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
        $endOfMonth = $period->clone()->endOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
        $query = $this->getTierSellers($firstOfMonth, $endOfMonth)->where('user.confirm_join_contest', 0);
        if ($this->hasActiveContest() && $this->isActiveContestNeedConfirmToJoin()) {
            $query->union($this->getTierSellers($firstOfMonth, $endOfMonth)->where('user.confirm_join_contest', 1)->whereNotBetween('order.paid_at', [$this->contestStartDate(), $this->contestEndDate()]));
        }
        $sellers = $query->orderByDesc('order_products_count')->get();
        $sellers = $sellers->when($q, function ($sellers, $q) {
            return $sellers->filter(function ($seller) use ($q) {
                return $seller->name == $q || $seller->email == $q;
            });
        })
            ->when($tierId, function ($sellers, $tierId) {
                return $sellers->where('tier_id', $tierId);
            });

        $sellers->loadCount([
            'billings' => function ($query) use ($sellers, $period) {
                $transactionKeys = $sellers->pluck('id')
                    ->map(fn($sellerId) => 'cashback-' . $sellerId . '-' . $period->clone()->format('Y-m'));

                $query->where('seller_billing.type', SellerBillingType::CASHBACK)
                    ->whereIn('seller_billing.transaction_key', $transactionKeys);
            }
        ]);
        $sellerTiers = SellerTier::query()->select('id', 'name')->get();
        $sellers->map(function ($seller) use ($period, $sellerTiers) {
            $lastTier = $seller->tierHistory()
                ->where('tier_id', '<>',  $seller->tier_id)
                ->orderByDesc('created_at')
                ->first();
            $seller->last_tier = $sellerTiers->filter(fn($tier) => $tier->id === $lastTier?->tier_id)->first();
            $seller->period = $period->format('Y-m');
            $seller->periodString = $period->format('M Y');
            $seller->cashback = ($seller->tier ? $seller->tier->sales_bonus : 0) * $seller->points;
            $seller->paid = (bool) $seller->billings_count;
            $seller->order_products_count = (int) $seller->order_products_count;
            return $seller;
        });

        $totalCashback = $sellers->sum('cashback');

        return $this->successResponse([
            'total_cashback' => $totalCashback,
            'report' => $sellers
        ]);
    }

    public function tierUpdateForSellers(): JsonResponse
    {
        Artisan::call('module:seller-tier:update-tier');
        return $this->successResponse();
    }

    public function cashback(SellerTierCashbackRequest $request)
    {
        $sellerId = $request->input('seller_id');
        $cashback = Cache::lock('cashback_request_' . $sellerId, 15)->get(function () use ($sellerId) {
            $hourOffset = 7;
            $now = now()->addRealHours($hourOffset)->startOfDay();
            $previousMonth = $now->startOfMonth()->subMonthsWithNoOverflow();
            $previousMonthString = $previousMonth->format('M Y');
            $firstOfMonth = $previousMonth->clone()->startOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
            $endOfMonth = $previousMonth->clone()->endOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');

            if ($previousMonth->format('Y-m') < SellerTier::START_MONTH_TO_APPLY_CASHBACK) {
                return $this->errorResponse('Cashback is applicable starting ' . SellerTier::START_MONTH_TO_APPLY_CASHBACK, 422);
            }

            $seller = $this->sellerTierReport($sellerId, $firstOfMonth, $endOfMonth, true, true);

            if (!$seller) {
                return $this->errorResponse('The seller does not exist or has not been tiered');
            }

            $transactionKey = 'cashback-' . $sellerId . '-' . $previousMonth->format('Y-m');
            $seller->loadCount([
                'billings' => function ($query) use ($transactionKey) {
                    $query->where('seller_billing.type', SellerBillingType::CASHBACK)
                        ->where('seller_billing.transaction_key', $transactionKey);
                }
            ]);

            if ($seller->billings_count) {
                return $this->errorResponse('The cashback of the period ' . $previousMonthString . ' has been paid', 422);
            }

            $totalCashback = $seller->tier->sales_bonus * $seller->points;

            if (!$totalCashback) {
                return $this->errorResponse('The total cashback amount is zero. Can not create cashback transaction', 422);
            }

            DB::beginTransaction();

            try {
                $transactionKey = 'cashback-' . $seller->id . '-' . $previousMonth->format('Y-m');
                $seller->updateBalance(
                    $totalCashback,
                    SellerBillingType::CASHBACK,
                    'Cashback ' . $previousMonthString . ': ' . $seller->points . ' SEN, Tier: ' . $seller->tier->name . ', Sales bonus: ' . $seller->tier->sales_bonus,
                    null,
                    SellerBillingStatus::COMPLETED,
                    null,
                    $transactionKey
                );

                DB::commit();
            } catch (\Exception $exception) {
                DB::rollBack();
                logToDiscord('Seller Tier Cashback: ' . $exception->getMessage());
                return $this->errorResponse('Cashback request failed', 500);
            }

            return $this->successResponse(null, 'Payment success');
        });

        if (!$cashback) {
            return $this->errorResponse('Cashback request is being processed', 419);
        }

        return $cashback;
    }

    /**
     * @return JsonResponse
     * @throws \Exception
     */
    public function sellerTierInfo(): JsonResponse
    {
        $sellerId = currentUser()->getUserId();
        $hourOffset = 7;
        $now = now()->addRealHours($hourOffset)->startOfDay();
        $firstOfMonth = $now->clone()->startOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
        $endOfMonth = $now->clone()->endOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
        $previousMonth = $now->startOfMonth()->subMonthsWithNoOverflow();
        $firstOfPreviousMonth = $previousMonth->clone()->startOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
        $endOfPreviousMonth = $previousMonth->clone()->endOfMonth()->subRealHours($hourOffset)->format('Y-m-d H:i:s');
        $tiers = SellerTier::query()
            ->select([
                'name',
                'required_sen_points',
                'sales_bonus'
            ])
            ->get();

        $currentMonthReport = $this->sellerTierReport($sellerId, $firstOfMonth, $endOfMonth);
        $previousMonthReport = $this->sellerTierReport($sellerId, $firstOfPreviousMonth, $endOfPreviousMonth);

        return $this->successResponse([
            'tier_info' => [
                'tier_name' => $currentMonthReport->tier->name ?? null,
                'current_month_senpoints' => $currentMonthReport->points ?? 0,
                'estimated_current_month_cashback' => !empty($currentMonthReport->tier) ? $currentMonthReport->tier->sales_bonus * $currentMonthReport->points : 0,
                'previous_month_senpoints' => $previousMonthReport->points ?? 0,
                'previous_month_cashback' => !empty($previousMonthReport) && !empty($previousMonthReport->tier) ? $previousMonthReport->tier->sales_bonus * $previousMonthReport->points : 0,
            ],
            'tiers' => $tiers
        ]);
    }

    /**
     * @param $sellerId
     * @param $from
     * @param $to
     * @param $withBalance
     * @param $tierRequired
     * @return mixed
     * @throws \Exception
     */
    public function sellerTierReport($sellerId, $from, $to, $withBalance = false, $tierRequired = false)
    {
        return User::query()
            ->with('tier:id,name,sales_bonus')
            ->leftJoin('order', 'user.id', '=', 'order.seller_id')
            ->select([
                'user.id',
                'user.tier_id',
                'user.name',
                'user.email'
            ])
            ->when($withBalance, function ($query) {
                $query->addSelect(['user.balance']);
            })
            ->selectRaw('sum(order.total_sen_points) as points, sum(order.total_product_amount) as sales, count(order.id) as orders_count, sum(order.total_quantity) as order_products_count')
            ->when($tierRequired, function ($query) {
                $query->whereHas('tier');
            })
            ->where(function ($q) {
                $q->where('user.confirm_join_contest', 0);
                $q->when($this->hasActiveContest() && $this->isActiveContestNeedConfirmToJoin(), function ($q) {
                    $q->orWhere(function ($q) {
                        $q->where('user.confirm_join_contest', 1);
                        $q->whereNotBetween('order.paid_at', [$this->contestStartDate(), $this->contestEndDate()]);
                    });
                });
            })
            ->where('order.payment_status', OrderPaymentStatus::PAID)
            ->whereBetween('order.paid_at', [
                $from,
                $to
            ])
            ->whereNull('order.deleted_at')
            ->groupBy('order.seller_id')
            ->find($sellerId);
    }

    /**
     * @param $firstOfMonth
     * @param $endOfMonth
     * @return User|Builder
     */
    private function getTierSellers($firstOfMonth, $endOfMonth)
    {
        return User::query()
            ->with('tier:id,name,sales_bonus')
            ->leftJoin('order', 'user.id', '=', 'order.seller_id')
            ->select([
                'user.id',
                'user.tier_id',
                'user.name',
                'user.email',
                'user.confirm_join_contest',
            ])
            ->selectRaw('sum(order.total_sen_points) as points, sum(order.total_product_amount) as sales, count(order.id) as orders_count, sum(order.total_quantity) as order_products_count')
            ->groupBy('order.seller_id')
            ->whereHas('tier')
            ->whereBetween('order.paid_at', [$firstOfMonth, $endOfMonth])
            ->where('order.payment_status', OrderPaymentStatus::PAID)
            ->whereNull('order.deleted_at');
    }
}
