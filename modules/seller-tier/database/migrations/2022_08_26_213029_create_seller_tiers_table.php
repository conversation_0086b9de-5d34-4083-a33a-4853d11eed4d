<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSellerTiersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seller_tiers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedDouble('required_sen_points')->default(0);
            $table->unsignedDouble('minimum_sen_points')->default(0);
            $table->unsignedDouble('seller_profit_rate')->default(0);
            $table->unsignedDouble('artist_profit_rate')->default(0);
            $table->unsignedDouble('sales_bonus')->default(0);
            $table->unsignedInteger('create_campaign_limit')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
