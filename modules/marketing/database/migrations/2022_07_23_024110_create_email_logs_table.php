<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('subscriber_id')->index('email_logs_subscriber_id')->comment('Subscriber id');
            $table->foreign('subscriber_id')
                ->references('id')
                ->on('subscribers')
                ->onDelete('cascade');
            $table->unsignedBigInteger('email_campaign_id')->index('email_logs_email_campaign_id')->comment('Email campaign id');
            $table->foreign('email_campaign_id')
                ->references('id')
                ->on('email_campaigns')
                ->onDelete('cascade');
            $table->unsignedBigInteger('order_id')->index('email_logs_order_id')->nullable()->comment('Order id');
            $table->foreign('order_id')
                ->references('id')
                ->on('order')
                ->onDelete('cascade');
            $table->boolean('processing')->index('email_logs_processing')->default(0);
            $table->tinyInteger('retry')->default(0);
            $table->timestamp('retry_at')->nullable();
            $table->boolean('sent')->default(0);
            $table->timestamp('sent_at')->nullable();
            $table->boolean('opened')->default(0);
            $table->timestamp('opened_at')->nullable();
            $table->boolean('ordered')->default(0);
            $table->timestamp('ordered_at')->nullable();
            $table->tinyInteger('clicked')->default(0);
            $table->json('clicked_data')->comment('The json of the clicked links')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
