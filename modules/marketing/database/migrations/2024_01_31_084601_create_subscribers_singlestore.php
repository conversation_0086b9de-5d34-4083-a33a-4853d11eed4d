<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubscribersSinglestore extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        if (!Schema::connection('singlestore')->hasTable('subscribers')) {
            Schema::connection('singlestore')->create('subscribers', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->bigInteger('seller_id')->index('subscribers_seller_id')->comment('Seller id');
                $table->string('name', 250)->comment('Name of the subscriber');
                $table->string('email', 250)->index('subscribers_email')->comment('Email of the subscriber');
                $table->string('phone', 100)->nullable()->comment('Phone number of the subscriber');
                $table->string('country')->nullable()->comment('Country address of the subscriber');
                $table->string('state')->nullable()->comment('State address of the subscriber');
                $table->string('city')->nullable()->comment('City address of the subscriber');
                $table->string('domain', 250)->index('subscribers_domain')->comment('Domain of the email of subscriber');
                $table->string('status')->index('subscribers_status')->default('new');
                $table->boolean('email_subscribed')->index('subscribers_email_subscribed')->default(1);
                $table->boolean('sms_subscribed')->index('subscribers_sms_subscribed')->default(1);
                $table->boolean('is_deleted')->index('subscribers_is_deleted')->default(0);
                $table->softDeletes();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
