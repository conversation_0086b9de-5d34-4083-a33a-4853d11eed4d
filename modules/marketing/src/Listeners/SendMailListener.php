<?php
namespace Modules\Marketing\Listeners;

use Modules\Marketing\Entities\SubMailerConfig;
use Modules\Marketing\Events\SendMailEvent;
use Modules\Marketing\Services\MarketingServices;
use Modules\Marketing\Supports\EmailBuilder;
use Modules\Marketing\Traits\MailConfigTrait;

class SendMailListener
{
    use MailConfigTrait;
    /**
     * Handle the event.
     *
     * @param SendMailEvent $event
     * @return void
     * @throws \Exception
     */
    public function handle(SendMailEvent $event)
    {
        if(!empty($event->triggerEvent)) {
            $triggerEvent = $event->triggerEvent;
        }
        $setting_id = $event->settingId ?? 0;
        $subMailerConfig = new SubMailerConfig();
        if(!empty($event->subMailerConfig)) {
            $subMailerConfig = $event->subMailerConfig;
        }
        $useSystemMailer = $subMailerConfig->useSystemMailer ?? false;
        graylogInfo('[' . currentTime() . '] - SendMailListener -> handle -> User ID: ' . $event->userId . ', Setting ID: #' . $setting_id, [
            'category' => 'email_marketing',
        ]);
        if ($useSystemMailer === false) {
            if(!empty($event->userId) || !empty($setting_id)) {
                $setting_id = $this->changeMailConfig($event->userId, $setting_id, $subMailerConfig);
            }
            if(empty($setting_id)) {
                return;
            }
        } else {
            $setting_id = $this->changeMailConfig($event->userId, $setting_id, $subMailerConfig);
        }
        try {
            app()->make('mailer')->to($event->to)->send(new EmailBuilder($event->content, $event->title, $event->args));
            $updatedRows = MarketingServices::updateSubscriberNextSendAt($event->userId, $event->to);
            if ($updatedRows === false) {
                graylogInfo('[' . currentTime() . '] - SendMailListener -> Update Subscriber Next Send At Failed -> User ID: ' . $event->userId, [
                    'category' => 'ses',
                    'event' => 'update_subscriber_next_send_at',
                    'user_id' => $event->userId,
                    'to' => $event->to,
                ]);
            }
            if(isset($triggerEvent)) {
                event(new $triggerEvent(true, $setting_id, $event->args));
            }
        } catch (\Exception $exception) {
            if(isset($triggerEvent)) {
                event(new $triggerEvent(false, $setting_id, $event->args));
            }
            logToDiscord('[' . currentTime() . '] - SendMailListener -> Exception -> Setting ID: ' . $setting_id . ', Error: ' . $exception->getMessage(), 'email_marketing', true);
            throw $exception;
        }
    }
}
