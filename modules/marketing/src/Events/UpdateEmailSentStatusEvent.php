<?php
namespace Modules\Marketing\Events;

class UpdateEmailSentStatusEvent extends Event
{
    /**
     * @var boolean
     */
    public bool $status;

    /**
     * @var int
     */
    public int $setting_id;

    /**
     * @var array
     */
    public array $args;

    /**
     * UpdateEmailSentStatusEvent constructor.
     * @param bool $status
     * @param array $args
     */
    public function __construct($status, $setting_id, $args = array())
    {
        $this->status = $status;
        $this->setting_id = $setting_id;
        $this->args = $args;
    }
}
