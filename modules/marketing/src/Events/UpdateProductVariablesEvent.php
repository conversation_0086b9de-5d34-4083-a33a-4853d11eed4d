<?php
namespace Modules\Marketing\Events;

class UpdateProductVariablesEvent extends Event
{
    /**
     * @var int
     */
    public int $campaign_id;
    /**
     * @var array
     */
    public array $products;

    /**
     * UpdateProductVariablesEvent constructor.
     * @param int $campaign_id
     * @param array $products
     */
    public function __construct($campaign_id, $products)
    {
        $this->campaign_id = $campaign_id;
        $this->products = $products;
    }
}
