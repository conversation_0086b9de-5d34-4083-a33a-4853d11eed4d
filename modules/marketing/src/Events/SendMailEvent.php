<?php
namespace Modules\Marketing\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Marketing\Entities\SubMailerConfig;

class SendMailEvent extends Event
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var string
     */
    public $content;

    /**
     * @var string
     */
    public $title;

    /**
     * @var string
     */
    public $to;

    /**
     * @var array
     */
    public $args;

    /**
     * @var boolean
     */
    public $triggerEvent;

    /**
     * @var int
     */
    public $userId;

    /**
     * @var int
     */
    public $settingId;

    /**
     * @var SubMailerConfig
     */
    public SubMailerConfig $subMailerConfig;

    /**
     * SendMailEvent constructor.
     * @param string $content
     * @param string $title
     * @param string $to
     * @param array $args
     * @param object $triggerAfterSentEvent
     * @param $userId
     * @param $settingId
     * @param $subMailerConfig
     */
    public function __construct($content, $title, $to, $args = array(), $triggerAfterSentEvent = null, $userId = 0, $settingId = 0, SubMailerConfig $subMailerConfig)
    {
        $this->content = $content;
        $this->title = $title;
        $this->to = $to;
        $this->args = $args;
        $this->triggerEvent = $triggerAfterSentEvent;
        $this->userId = $userId;
        $this->settingId = $settingId;
        $this->subMailerConfig = $subMailerConfig;
    }
}
