<?php

namespace Modules\Marketing\Services;

use App\Enums\StoreDomainStatusEnum;
use App\Enums\TagsEnum;
use App\Models\Order;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Models\SystemConfig;
use App\Models\Tags;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\InvalidCastException;
use Illuminate\Database\Eloquent\MassAssignmentException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use InvalidArgumentException;
use LogicException;
use Modules\Marketing\Data\DomainInfoData;
use Modules\Marketing\Data\SenderInfoData;
use Modules\Marketing\Enums\CampaignStatusEnum;
use Modules\Marketing\Enums\EmailLogEnum;
use Modules\Marketing\Enums\EmailSettingsStatusEnum;
use Modules\Marketing\Enums\SesIdentityType;
use Modules\Marketing\Enums\SesSenderStatus;
use Modules\Marketing\Enums\SubscriberEnum;
use Modules\Marketing\Events\StoreCampaignSubscribersEvent;
use Modules\Marketing\Events\UpdateProductVariablesEvent;
use Modules\Marketing\Jobs\SesAddIdentityJob;
use Modules\Marketing\Jobs\SesUpdateIdentityJob;
use Modules\Marketing\Models\EmailCampaigns;
use Modules\Marketing\Models\EmailLogs;
use Modules\Marketing\Models\EmailSettings;
use Modules\Marketing\Models\IndexSubscribers;
use Modules\Marketing\Models\SubscriberTags;
use Modules\Marketing\Supports\Helper;
use Modules\Marketing\Supports\JsonHelper;
use Throwable;

class MarketingServices
{
    /**
     * @param $seller_id
     * @param $status
     * @param $search
     * @param $page
     * @param $limit
     * @param $orderBy
     * @param $directionBy
     * @return array
     */
    public static function getCampaigns($seller_id, $status, $search, $page = 1, $limit = 15, $orderBy = 'created_at', $directionBy = 'desc')
    {
        $query = EmailCampaigns::query()->with('seller')->where([
            'seller_id' => $seller_id,
            'is_deleted' => SubscriberEnum::IS_NOT_DELETED,
        ]);
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->orWhere('name', 'like', '%' . $search . '%')
                    ->orWhere('slug', 'like', '%' . $search . '%');
            });
        }
        if (!empty($status)) {
            $query->where('status', $status);
        }
        $total = $query->count();
        if (!empty($page) && !empty($limit)) {
            $query->limit($limit)->offset(($page - 1) * $limit);
        }
        $campaigns = $query->orderBy($orderBy, $directionBy)->get()->toArray();
        foreach ($campaigns as &$campaign) {
            $campaign['can_stop_scheduled'] = 0;
            if ($campaign['status'] === CampaignStatusEnum::SCHEDULED) {
                $now = strtotime(currentTime());
                $send_time = strtotime($campaign['send_time']);
                if (($send_time - $now) >= SystemConfig::getConfig('email_marketing_can_stop_scheduled_campaign_in_seconds', 60)) {
                    $campaign['can_stop_scheduled'] = 1;
                }
            }
            $sumActivities = EmailLogs::query()->sumActivitiesOfSent($campaign['id']);
            $campaign['total_subscribers'] = 0;
            $campaign['total_sent'] = 0;
            $campaign['total_opened'] = 0;
            $campaign['total_ordered'] = 0;
            $campaign['total_clicked'] = 0;
            if (!empty($sumActivities)) {
                $campaign['total_subscribers'] = (int)($sumActivities['total_subscribers'] ?? 0);
                $campaign['total_sent'] = (int)($sumActivities['total_sent'] ?? 0);
                $campaign['total_opened'] = (int)($sumActivities['total_opened'] ?? 0);
                $campaign['total_ordered'] = (int)($sumActivities['total_ordered'] ?? 0);
                $campaign['total_clicked'] = (int)($sumActivities['total_clicked'] ?? 0);
            }

            $campaign['sent_cr'] = 0;
            $campaign['opened_cr'] = 0;
            $campaign['ordered_cr'] = 0;
            $campaign['clicked_cr'] = 0;
            if ($campaign['total_subscribers'] > 0) {
                $campaign['sent_cr'] = number_format(($campaign['total_sent'] / $campaign['total_subscribers']) * 100, 1);
                $campaign['opened_cr'] = number_format(($campaign['total_opened'] / $campaign['total_subscribers']) * 100, 1);
                $campaign['ordered_cr'] = number_format(($campaign['total_ordered'] / $campaign['total_subscribers']) * 100, 1);
                $campaign['clicked_cr'] = number_format(($campaign['total_clicked'] / $campaign['total_subscribers']) * 100, 1);
            }
        }
        return [$campaigns, $total];
    }

    /**
     * @param $seller_id
     * @param $search
     * @param int $page
     * @param int $limit
     * @param string $orderBy
     * @param string $directionBy
     * @param bool $asArray
     * @param bool $join_seller
     * @param bool $useSystemMailer
     * @param null|bool $fromSystem
     * @return array
     */
    public static function getSubscribers($seller_id, $search, $page = 1, $limit = 15, $orderBy = 'created_at', $directionBy = 'desc', $asArray = false, $join_seller = true, $useSystemMailer = false, $fromSystem = null)
    {
        $query = IndexSubscribers::query()->where([
            'seller_id' => $seller_id,
            'is_deleted' => SubscriberEnum::IS_NOT_DELETED,
        ]);
        if ($fromSystem !== null) {
            $query->where('from_system', $fromSystem);
        }
        if ($useSystemMailer === true) {
            $query->where(function ($q2) {
                $q2->where('next_send_at', '<=', currentTime())
                    ->orWhereNull('next_send_at');
            });
            $query->where(function ($q2) {
                $q2->where('from_system', SubscriberEnum::IS_FROM_SYSTEM);
            });
        }
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->orWhere('name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%')
                    ->orWhere('phone', 'like', '%' . $search . '%')
                    ->orWhere('city', 'like', '%' . $search . '%')
                    ->orWhere('state', 'like', '%' . $search . '%')
                    ->orWhere('country', 'like', '%' . $search . '%')
                    ->orWhere('status', $search)
                    ->orWhere('city', 'like', '%' . $search . '%');
            });
        }
        $total = $query->count();
        if ($join_seller) {
            $query->with('seller');
        }
        if (!empty($page) && !empty($limit)) {
            $query->limit($limit)->offset(($page - 1) * $limit);
        }
        $subscribers = $query->orderBy($orderBy, $directionBy)->get();
        $subscriber_tags = collect([]);
        $all_tags = collect([]);

        if ($subscribers->isNotEmpty()) {
            $subscriber_ids = Arr::pluck($subscribers, 'id');
            foreach (array_chunk($subscriber_ids, 5000) as $taggable_ids) {
                $taggables = SubscriberTags::query()
                    ->selectRaw('taggable_id, tag_id')
                    ->where([
                        'taggable_type' => IndexSubscribers::class
                    ])
                    ->whereIn('taggable_id', $taggable_ids)
                    ->get();
                $subscriber_tags = $subscriber_tags->merge($taggables);
            }
        }

        if ($subscriber_tags->isNotEmpty()) {
            $tag_ids = $subscriber_tags->pluck('tag_id')->unique()->values()->toArray();
            foreach (array_chunk($tag_ids, 5000) as $tag_id) {
                $tags = Tags::query()->selectRaw('id, name')
                    ->whereIn('id', $tag_id)
                    ->where('status', TagsEnum::STATUS_ACTIVE)
                    ->get();
                $all_tags = $all_tags->merge($tags);
            }
        }

        $subscriberTagIds = [];
        foreach ($subscriber_tags as $taggable) {
            $subscriberTagIds[$taggable->taggable_id][] = $taggable->tag_id;
        }

        $tagDataById = [];

        foreach ($all_tags as $tag) {
            $tagDataById[$tag->id] = $tag;
        }

        foreach ($subscribers as $subscriber) {
            $subscriber->full_tags = [];
            $subscriber->tags = [];

            if (isset($subscriberTagIds[$subscriber->id])) {
                $tags_id = $subscriberTagIds[$subscriber->id];
                $tags = [];

                foreach ($tags_id as $tag_id) {
                    if (isset($tagDataById[$tag_id])) {
                        $tags[] = $tagDataById[$tag_id];
                    }
                }

                $subscriber->full_tags = $tags;
                $subscriber->tags = array_column($tags, 'name');
            }
        }
        if ($asArray) {
            $subscribers = $subscribers->toArray();
        }
        return [$subscribers, $total];
    }

    /**
     * @param $seller_id
     * @param $search
     * @param string $status
     * @param string $driver
     * @param int $page
     * @param int $limit
     * @param string $orderBy
     * @param string $directionBy
     * @param bool $asArray
     * @return array
     * @throws \Exception
     */
    public static function getEmailSettings($seller_id, $search, $status = '', $driver = '', $page = 1, $limit = 15, $orderBy = 'created_at', $directionBy = 'desc', $asArray = false)
    {
        $query = EmailSettings::query()->with('seller')->where([
            'seller_id' => $seller_id,
            'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
        ]);
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->orWhere('driver', 'like', '%' . $search . '%')
                    ->orWhere('sender_name', 'like', '%' . $search . '%')
                    ->orWhere('sender_email', 'like', '%' . $search . '%')
                    ->orWhere('delay', 'like', '%' . $search . '%')
                    ->orWhere('limit_per_send', 'like', '%' . $search . '%');
            });
        }
        if (!empty($driver)) {
            $query->where('driver', '=', $driver);
        }
        if (!empty($status)) {
            $query->where('status', '=', $status);
        }
        $settings = $query->orderBy($orderBy, $directionBy)->get();
        $result = collect([]);
        $crypt = Helper::crypt();
        foreach ($settings as $setting) {
            $item = array(
                'id' => $setting->id,
                'driver' => $setting->driver,
                'sender_name' => $setting->sender_name,
                'sender_email' => $setting->sender_email,
                'delay' => $setting->delay,
                'limit_per_send' => $setting->limit_per_send,
                'status' => $setting->status,
                'state' => !empty($setting->state),
                'last_used_at' => $setting->last_used_at,
                'created_at' => $setting->created_at,
                'updated_at' => $setting->updated_at,
                'logs' => $setting->logs,
            );
            try {
                $configs = !empty($setting->settings) ? $crypt->decryptString($setting->settings) : [];
                $configs = JsonHelper::decode($configs, true);
                foreach ($configs as $key => $value) {
                    $item[$key] = $value;
                }
            } catch (\Throwable $e) {
                $_items = array();
                if ($setting->driver === 'smtp') {
                    $_items = array(
                        'smtp_host' => '',
                        'smtp_port' => '',
                        'smtp_username' => '',
                        'smtp_password' => '',
                        'smtp_encryption' => 'tls',
                    );
                }
                if ($setting->driver === 'ses') {
                    $_items = array(
                        'ses_key' => '',
                        'ses_secret' => '',
                        'ses_region' => ''
                    );
                }
                if ($setting->driver === 'mailgun') {
                    $_items = array(
                        'mailgun_domain' => '',
                        'mailgun_secret' => '',
                        'mailgun_endpoint' => '',
                    );
                }
                foreach ($_items as $key => $value) {
                    $item[$key] = $value;
                }
            }
            $result->add($item);
        }
        $total = $result->count();
        if (!empty($page) && !empty($limit)) {
            $result = $result->take(10)->skip(($page - 1) * $limit);
        }
        if ($asArray) {
            $result = $result->toArray();
        }
        return [$result, $total];
    }

    /**
     * @param $seller_id
     * @param $setting_id
     * @return array
     * @throws \Exception
     */
    public static function getEmailSettingDetail($seller_id, $setting_id)
    {
        $result = array();
        $setting = EmailSettings::query()->with('seller')->where([
            'id' => $setting_id,
            'seller_id' => $seller_id,
            'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
        ])->first();
        if (empty($setting)) {
            return $result;
        }
        $result = array(
            'id' => $setting->id,
            'driver' => $setting->driver,
            'sender_name' => $setting->sender_name,
            'sender_email' => $setting->sender_email,
            'delay' => $setting->delay,
            'limit_per_send' => $setting->limit_per_send,
            'status' => $setting->status,
            'last_used_at' => $setting->last_used_at,
            'created_at' => $setting->created_at,
            'updated_at' => $setting->updated_at,
            'use_system_mailer' => $setting->use_system_mailer,
        );
        $crypt = Helper::crypt();
        try {
            $configs = !empty($setting->settings) ? $crypt->decryptString($setting->settings) : [];
            $configs = JsonHelper::decode($configs, true);
            foreach ($configs as $key => $value) {
                $result[$key] = $value;
            }
        } catch (\Throwable $e) {
            $_items = array();
            if ($setting->driver === 'smtp') {
                $_items = array(
                    'smtp_host' => '',
                    'smtp_port' => '',
                    'smtp_username' => '',
                    'smtp_password' => '',
                    'smtp_encryption' => 'tls',
                );
            }
            if ($setting->driver === 'ses') {
                $_items = array(
                    'ses_key' => '',
                    'ses_secret' => '',
                    'ses_region' => ''
                );
            }
            if ($setting->driver === 'mailgun') {
                $_items = array(
                    'mailgun_domain' => '',
                    'mailgun_secret' => '',
                    'mailgun_endpoint' => '',
                );
            }
            foreach ($_items as $key => $value) {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    /**
     * @param $seller_id
     * @param $ids
     * @param $data
     * @param string $action
     * @return array
     */
    public static function updateEmailSettingsBulk($seller_id, $ids, $data, $action = SubscriberEnum::ACTION_EDIT)
    {
        if ($action === SubscriberEnum::ACTION_DELETE) {
            $data['is_deleted'] = SubscriberEnum::IS_DELETED;
            $data['deleted_at'] = currentTime();
            $data['updated_at'] = currentTime();
        }
        $updated = EmailSettings::query()
            ->where('seller_id', $seller_id)
            ->whereIn('id', $ids);
        $updated = $updated->update($data) > 0;
        if (!$updated) {
            $message = 'Cannot update selected email settings.';
            if ($action === SubscriberEnum::ACTION_DELETE) {
                $message = 'Cannot delete selected email settings.';
            }
        } else {
            $message = 'Updated selected email settings successfully.';
            if ($action === SubscriberEnum::ACTION_DELETE) {
                $message = 'Deleted selected email settings successfully.';
            }
        }
        return array(
            'status' => $updated,
            'message' => $message
        );
    }

    /**
     * @param $seller_id
     * @param $subscriberData
     * @param $tags
     * @param $subscriber_id
     * @return array
     * @throws \Exception
     */
    public static function saveSubscriber($seller_id, $subscriberData, $tags = array(), $subscriber_id = 0): array
    {
        try {
            $is_edit = false;
            $subscriber = null;
            $current_time = currentTime();
            if (!empty($subscriber_id)) {
                $subscriber = IndexSubscribers::query()->findOrFail($subscriber_id);
            }
            $subscriberData['seller_id'] = $seller_id;
            $email = $subscriberData['email'];
            if (empty($subscriber)) {
                $subscriber = IndexSubscribers::query()->where([
                    'email' => $email,
                    'seller_id' => $seller_id,
                    'is_deleted' => SubscriberEnum::IS_NOT_DELETED
                ])->first();
            }
            $explodeEmail = explode('@', $email);
            $subscriberData['domain'] = trim($explodeEmail[1]);
            $subscriberData['updated_at'] = $current_time;
            $subscriberData['email_subscribed'] = SubscriberEnum::IS_EMAIL_SUBSCRIBED;
            [$existsEmails, $notFoundEmails] = self::findEmailsFromOrders([$email]);
            if (!empty($existsEmails)) {
                $subscriberData['from_system'] = SubscriberEnum::IS_FROM_SYSTEM;
            } else {
                $subscriberData['from_system'] = SubscriberEnum::IS_NOT_FROM_SYSTEM;
            }
            if (!empty($subscriber)) {
                $subscriber_id = $subscriber->id;
                $is_edit = true;
                IndexSubscribers::query()
                    ->where([
                        'id' => $subscriber_id,
                        'is_deleted' => SubscriberEnum::IS_NOT_DELETED,
                        'seller_id' => $seller_id
                    ])
                    ->update($subscriberData);
            } else {
                $subscriberData['created_at'] = $current_time;
                $subscriber_id = IndexSubscribers::query()->insertGetId($subscriberData);
            }
            $subscriber_tag = array(
                'taggable_id' => $subscriber_id,
                'taggable_type' => IndexSubscribers::class,
            );
            SubscriberTags::query()->where($subscriber_tag)->delete();
            if (is_array($tags) && !empty($tags)) {
                foreach ($tags as $tag) {
                    $tag_check = Tags::query()
                        ->where([
                            'name' => trim($tag),
                            'status' => TagsEnum::STATUS_ACTIVE,
                        ])
                        ->first();
                    if ($tag_check !== null) {
                        $subscriber_tag['tag_id'] = $tag_check->id;
                    } else {
                        $subscriber_tag['tag_id'] = Tags::query()->insertGetId(array(
                            'name' => trim($tag),
                            'created_at' => $current_time,
                            'updated_at' => $current_time,
                        ));
                    }
                    $subscriber_tag_check = SubscriberTags::query()
                        ->where($subscriber_tag)
                        ->first();

                    if ($subscriber_tag_check !== null) {
                        continue;
                    }
                    $subscriber_tag['created_at'] = $current_time;
                    $subscriber_tag['updated_at'] = $current_time;
                    SubscriberTags::query()->insertGetId($subscriber_tag);
                }
            }
            return [$subscriber_id, $current_time, $is_edit];
        } catch (\Throwable $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    /**
     * Create tag
     *
     * @param mixed $tag
     * @param mixed &$existsTags
     * @return mixed
     * @throws InvalidArgumentException
     * @throws InvalidCastException
     */
    public static function createTagIfNotExists($tag, &$existsTags)
    {
        $tagData = null;
        if (!isset($existsTags[$tag])) {
            $tagData = Tags::query()
                ->where([
                    'name' => trim($tag),
                    'status' => TagsEnum::STATUS_ACTIVE,
                ])
                ->first();

            if ($tagData !== null) {
                $existsTags[$tag] = $tagData;
            }
        }
        $tagData = $existsTags[$tag] ?? null;
        if (!empty($tagData)) {
            return $tagData;
        }
        $tagData = new Tags();
        $tagData->name = trim($tag);
        $tagData->status = TagsEnum::STATUS_ACTIVE;
        if ($tagData->save()) {
            $tagData->fresh();
            $existsTags[$tagData->name] = $tagData;
            return $tagData;
        }
        return null;
    }

    /**
     * Puck tags from subscribers
     *
     * @param mixed $subscribers
     * @return array
     */
    private static function puckTagsFromSubscribers($subscribers)
    {
        $tags = [];
        foreach ($subscribers as $subscriber) {
            if (!empty($subscriber['tags'])) {
                $tags = array_merge($tags, $subscriber['tags']);
            }
        }
        return array_unique($tags);
    }

    /**
     * Update or create subscriber
     *
     * @param mixed $subscriberInfo
     * @return Model|Builder|IndexSubscribers
     * @throws InvalidArgumentException
     * @throws UniqueConstraintViolationException
     * @throws Throwable
     */
    private static function updateOrCreateSubscriber($subscriberInfo)
    {
        $subscriber = IndexSubscribers::query()->updateOrCreate([
            'email' => $subscriberInfo['email'],
            'seller_id' => $subscriberInfo['seller_id'],
            'is_deleted' => SubscriberEnum::IS_NOT_DELETED,
        ], [
            'name' => $subscriberInfo['name'],
            'phone' => $subscriberInfo['phone'],
            'city' => $subscriberInfo['city'],
            'state' => $subscriberInfo['state'],
            'country' => $subscriberInfo['country'],
            'domain' => $subscriberInfo['domain'],
            'email_subscribed' => SubscriberEnum::IS_EMAIL_SUBSCRIBED,
            'from_system' => $subscriberInfo['from_system'],
        ]);
        return $subscriber;
    }

    private static function getSubscriberTags($subscriberId, &$existsTags)
    {
        $subscriberTags = SubscriberTags::query()
            ->where('taggable_id', $subscriberId)
            ->where('taggable_type', IndexSubscribers::class)
            ->get();
        $tags = [];
        /** @var SubscriberTags $subscriberTag */
        foreach ($subscriberTags as $subscriberTag) {
            $tag = $existsTags[$subscriberTag->tag_id] ?? null;
            if (!empty($tag)) {
                $tags[] = $tag->name;
            } else {
                $tag = Tags::query()->find($subscriberTag->tag_id);
                if (!empty($tag)) {
                    $tags[] = $tag->name;
                    $existsTags[$tag->id] = $tag;
                }
            }
        }
        return $tags;
    }

    /**
     * Remove all subscriber tags
     *
     * @param mixed $deleteableIds
     * @return void
     * @throws InvalidArgumentException
     * @throws LogicException
     */
    private static function removeAllSubscriberTags($deleteableIds)
    {
        SubscriberTags::query()->whereIn('taggable_id', $deleteableIds)->delete();
    }

    /**
     * Get all tags
     *
     * @return Collection
     * @throws InvalidArgumentException
     */
    private static function getAllTags()
    {
        $preloadTags = [];
        $tags = Tags::query()
            ->select("tags.*")
            ->from(function ($query) {
                $query->select("tag_id")
                    ->from("taggables")
                    ->where("taggable_type", IndexSubscribers::class)
                    ->groupBy("tag_id")
                    ->get();
            }, "tmp")
            ->join("tags", "tags.id", "=", "tmp.tag_id")
            ->get();
        if ($tags->isNotEmpty()) {
            $preloadTags = $tags->keyBy("id");
        }
        return $preloadTags;
    }

    /**
     * Find emails from orders
     * @param array $emails
     * @return (array<string|int, mixed>|array)[]
     */
    public static function findEmailsFromOrders(array $emails)
    {
        // If emails not found in subscribers table, find in orders table
        $result = Order::query()
            ->select('customer_email')
            ->whereIn('customer_email', $emails)
            ->get();
        if ($result->isEmpty()) {
            return [[], []];
        }
        $emailsExists = $result->pluck('customer_email')->toArray();
        $emailsNotFound = array_diff($emails, $emailsExists);
        return [$emailsExists, $emailsNotFound];
    }

    /**
     * @param $subscribers_none_tags
     * @param $subscribers_has_tags
     * @return bool
     * @throws \Exception
     */
    public static function saveSubscribers($subscribers_none_tags, $subscribers_has_tags)
    {
        try {
            foreach (array_chunk($subscribers_none_tags, 500) as $subscribers) {
                $deletableIds = [];
                foreach ($subscribers as &$subscriber) {
                    $subscriber = self::updateOrCreateSubscriber($subscriber);
                    $deletableIds[] = $subscriber->id;
                }
                self::removeAllSubscriberTags($deletableIds);
            }
            if (!empty($subscribers_has_tags)) {
                $preloadExistsTags = self::getAllTags();
                $currentTime = currentTime();
                $listSubscriberTagName = self::puckTagsFromSubscribers($subscribers_has_tags);
                $subscribersHasNewTag = [];
                foreach ($subscribers_has_tags as $subscriber_has_tag) {
                    $listTagName = $subscriber_has_tag['tags'];
                    unset($subscriber_has_tag['tags']);
                    $subscriber = self::updateOrCreateSubscriber($subscriber_has_tag);
                    $listSubscriberTagName = self::getSubscriberTags($subscriber->id, $preloadExistsTags);
                    $newTags = array_diff($listTagName, $listSubscriberTagName);
                    foreach ($newTags as $tagName) {
                        $tagData = self::createTagIfNotExists($tagName, $preloadExistsTags);
                        if (empty($tagData)) {
                            continue;
                        }
                        $subscriberTagData = array(
                            'taggable_id' => $subscriber->id,
                            'taggable_type' => IndexSubscribers::class,
                            'tag_id' => $tagData->id,
                            'created_at' => $currentTime,
                            'updated_at' => $currentTime,
                        );
                        $alreadyTagged = SubscriberTags::query()->where($subscriberTagData)->exists();
                        if ($alreadyTagged) {
                            continue;
                        }
                        $subscribersHasNewTag[] = $subscriberTagData;
                    }
                    $notFoundTags = array_diff($listSubscriberTagName, $listTagName);
                    foreach ($notFoundTags as $tagName) {
                        $tagData = self::createTagIfNotExists($tagName, $preloadExistsTags);
                        if (empty($tagData)) {
                            continue;
                        }
                        $subscriberTagData = array(
                            'taggable_id' => $subscriber->id,
                            'taggable_type' => IndexSubscribers::class,
                            'tag_id' => $tagData->id,
                        );
                        SubscriberTags::query()->where($subscriberTagData)->delete();
                    }
                }
                $chunkedSubscribersHasNewTag = array_chunk($subscribersHasNewTag, 100);
                foreach ($chunkedSubscribersHasNewTag as $subscribersHasNewTag) {
                    SubscriberTags::query()->insert($subscribersHasNewTag);
                }
            }
            return true;
        } catch (\Throwable $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    /**
     * @param $seller_id
     * @param $ids
     * @param $data
     * @param string $action
     * @return bool
     */
    public static function updateSubscribersBulk($seller_id, $ids, $data, $action = SubscriberEnum::ACTION_EDIT)
    {
        if ($action === SubscriberEnum::ACTION_DELETE) {
            $data['is_deleted'] = SubscriberEnum::IS_DELETED;
            $data['updated_at'] = currentTime();
        }
        $updated = IndexSubscribers::query()
            ->where('seller_id', $seller_id)
            ->whereIn('id', $ids)
            ->update($data);
        return $updated > 0;
    }

    /**
     * @param $seller_id
     * @param $ids
     * @param $data
     * @param string $action
     * @return array
     */
    public static function updateCampaignsBulk($seller_id, $ids, $data, $action = SubscriberEnum::ACTION_EDIT)
    {
        $statusWhere = [];
        if ($action === SubscriberEnum::ACTION_DELETE) {
            $data['is_deleted'] = SubscriberEnum::IS_DELETED;
            $data['deleted_at'] = currentTime();
            $data['updated_at'] = currentTime();
            $statusWhere = [
                CampaignStatusEnum::SENDING,
                CampaignStatusEnum::FINISHED,
                CampaignStatusEnum::SCHEDULED,
                CampaignStatusEnum::CANCELLED,
            ];
        }
        $updated = EmailCampaigns::query()
            ->where('seller_id', $seller_id)
            ->whereIn('id', $ids);
        if (!empty($statusWhere)) {
            $updated->whereNotIn('status', $statusWhere);
        }
        $updated = $updated->update($data) > 0;
        if (!$updated) {
            $message = 'Cannot update selected campaigns.';
            if ($action === SubscriberEnum::ACTION_DELETE) {
                $message = 'Cannot delete selected campaigns.';
            }
        } else {
            $message = 'Updated selected campaigns successfully.';
            if ($action === SubscriberEnum::ACTION_DELETE) {
                $message = 'Deleted selected campaigns successfully.';
            }
        }
        return array(
            'status' => $updated,
            'message' => $message
        );
    }

    /**
     * @param $seller_id
     * @param $store_id
     * @return Store|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public static function getSellerStoreDetail($seller_id, $store_id)
    {
        return Store::query()
            ->select('id',
                'name',
                'domain',
                'sub_domain',
                'email',
                'phone',
                'address',
                'logo_url',
                'favicon',
                'head_line',
                'order_prefix',
                'theme',
                'timezone',
                'foot_line',
                'default_color',
            )
            ->where('seller_id', $seller_id)
            ->where('id', $store_id)
            ->first();
    }

    /**
     * @param $subscribers
     * @param $countries
     * @param $cities
     * @param $states
     * @param $tags
     * @param $exclude_domains
     * @return array
     */
    public static function filterSubscribersByCondititions($subscribers, $countries, $cities, $states, $tags, $exclude_domains)
    {
        $filtered = array();
        if (empty($subscribers)) {
            return $filtered;
        }
        if (!empty($countries)) {
            $countries = array_map('trim', $countries);
            $countries = array_map(static function ($item) {
                return Str::slug($item);
            }, $countries);
        }
        if (!empty($cities)) {
            $cities = array_map('trim', $cities);
            $cities = array_map(static function ($item) {
                return Str::slug($item);
            }, $cities);
        }
        if (!empty($states)) {
            $states = array_map('trim', $states);
            $states = array_map(static function ($item) {
                return Str::slug($item);
            }, $states);
        }
        if (!empty($tags)) {
            $tags = array_map('trim', $tags);
            $tags = array_map('intval', $tags);
        }
        if (!empty($exclude_domains)) {
            $exclude_domains = array_map('trim', $exclude_domains);
            $exclude_domains = array_map(static function ($item) {
                return Str::slug($item);
            }, $exclude_domains);
        }
        foreach ($subscribers as $subscriber) {
            if (!empty($subscriber['country']) && !empty($countries[0]) && !Str::contains(Str::slug($subscriber['country']), $countries)) {
                continue;
            }
            if (!empty($subscriber['city']) && !empty($cities[0]) && !Str::contains(Str::slug($subscriber['city']), $cities)) {
                continue;
            }
            if (!empty($subscriber['state']) && !empty($states[0]) && !Str::contains(Str::slug($subscriber['state']), $states)) {
                continue;
            }
            if (!empty($subscriber['domain']) && !empty($exclude_domains[0]) && Str::contains(Str::slug($subscriber['domain']), $exclude_domains)) {
                continue;
            }
            if (!empty($tags[0])) {
                $has_tags = [];
                if (!empty($subscriber['full_tags'])) {
                    $tags_id = Arr::pluck($subscriber['full_tags'], 'id');
                    $has_tags = array_intersect($tags_id, $tags);
                }
                if (count($has_tags) === 0) {
                    continue;
                }
            }
            $filtered[] = $subscriber;
        }
        return $filtered;
    }

    /**
     * @param $campaign_id
     * @param $seller_id
     * @param $store_id
     * @param $campaign_name
     * @param $status
     * @param $subject
     * @param $schedule
     * @param $btn_text
     * @param $hero_text
     * @param $main_btn_text
     * @param $recommendations_text
     * @param $content_text
     * @param $products
     * @param $send_conditions
     * @param $setting_id
     * @param bool $use_system_mailer
     * @return array
     */
    public static function saveEmailCampaign($campaign_id, $seller_id, $store_id, $campaign_name, $status, $subject, $schedule, $btn_text, $hero_text, $main_btn_text, $recommendations_text, $content_text, $products, $send_conditions, $setting_id, $use_system_mailer = false)
    {
        if (!empty($campaign_id)) {
            $campaign_check = EmailCampaigns::query()->where('seller_id', $seller_id)
                ->where('id', $campaign_id)
                ->where('is_deleted', CampaignStatusEnum::IS_NOT_DELETED)
                ->first();
            if (empty($campaign_check)) {
                return [
                    'status' => false,
                    'message' => 'The campaign that you want to update was not found.',
                ];
            }
        }
        if (in_array($status, [CampaignStatusEnum::DRAFT, CampaignStatusEnum::SCHEDULED], true)) {
            $email_template = 'default';
            $current_time = currentTime();
            if (!empty($schedule)) {
                $schedule = Carbon::parse($schedule);
                $sellerUTCOffset = get_utc_offset_by_user_or_store($seller_id, $store_id);
                $schedule = $schedule->subRealHours($sellerUTCOffset)->format('Y-m-d H:i:s');
            } else {
                $schedule = $current_time;
            }
            if ($status === CampaignStatusEnum::DRAFT) {
                $schedule = null;
            }
            $campaign_data = array(
                'seller_id' => $seller_id,
                'store_id' => $store_id,
                'name' => $campaign_name,
                'email_template' => $email_template,
                'send_time' => $schedule,
                'hold_at' => $schedule,
                'updated_at' => $current_time,
                'setting_id' => $setting_id,
                'use_system_mailer' => $use_system_mailer,
            );
            $campaign_data['status'] = $status;

            if (empty($campaign_id)) {
                $campaign_data['slug'] = Str::slug($campaign_name);
            }
            $variables = array(
                'subject' => $subject,
                'btn_text' => $btn_text,
                'hero_text' => $hero_text,
                'main_btn_text' => $main_btn_text,
                'recommendations_text' => $recommendations_text,
                'content_text' => $content_text,
                'products' => $products,
            );
            $campaign_data['send_conditions'] = JsonHelper::encode($send_conditions);
            $campaign_data['variables'] = JsonHelper::encode($variables);

            $subscribers = [];
            if ($status === CampaignStatusEnum::SCHEDULED) {
                [$subscribers, $total] = self::getSubscribers($seller_id, '', 0, 0, 'created_at', 'desc', true, useSystemMailer: $use_system_mailer);
                if ($total === 0) {
                    $campaign_data['status'] = CampaignStatusEnum::DRAFT;
                    $campaign_data['send_time'] = null;
                    $campaign_data['hold_at'] = null;
                }
            }
            if (empty($campaign_id)) {
                $campaign_data['created_at'] = $current_time;
                $campaign_id = EmailCampaigns::query()->insertGetId($campaign_data);
            } else {
                EmailCampaigns::query()
                    ->where([
                        'id' => $campaign_id,
                        'seller_id' => $seller_id
                    ])
                    ->where('is_deleted', CampaignStatusEnum::IS_NOT_DELETED)
                    ->whereIn('status', [CampaignStatusEnum::DRAFT, CampaignStatusEnum::SCHEDULED])
                    ->update($campaign_data);
            }
            event(new UpdateProductVariablesEvent($campaign_id, $products));
            if ($status === CampaignStatusEnum::SCHEDULED) {
                if (empty($subscribers)) {
                    return [
                        'status' => false,
                        'message' => 'You dont have any subscribers can send the email, please check it again.',
                    ];
                }
                if ($send_conditions['type'] === 'all') {
                    $filtered_subscribers = $subscribers;
                } else {
                    $filter_conditions = $send_conditions['filter'];
                    $filtered_subscribers = self::filterSubscribersByCondititions($subscribers, $filter_conditions['countries'], $filter_conditions['cities'], $filter_conditions['states'], $filter_conditions['tags'], $filter_conditions['domains']);
                }
                if (!empty($filtered_subscribers)) {
                    event(new StoreCampaignSubscribersEvent($campaign_id, $filtered_subscribers));
                }
            }
        }
        return array(
            'status' => true,
            'message' => '',
            'campaign_id' => $campaign_id,
        );
    }

    /**
     * @param $seller_id
     * @param $campaign_id
     * @return array
     */
    public static function getEmailCampaign($seller_id, $campaign_id)
    {
        $result = array();
        $campaign_check = EmailCampaigns::query()->where('seller_id', $seller_id)
            ->where('id', $campaign_id)
            ->where('is_deleted', CampaignStatusEnum::IS_NOT_DELETED)
            ->first();
        if (empty($campaign_check)) {
            return $result;
        }
        $sellerUTCOffset = get_utc_offset_by_user_or_store($seller_id, $campaign_check->store_id);
        $send_time = '';
        if (!empty($campaign_check->send_time)) {
            $send_time = Carbon::parse($campaign_check->send_time);
            $send_time = $send_time->subRealHours($sellerUTCOffset)->format('Y-m-d\TH:i');
        }
        $campaign_data = array(
            'id' => $campaign_check->id,
            'seller_id' => $seller_id,
            'store_id' => $campaign_check->store_id,
            'name' => $campaign_check->name,
            'status' => $campaign_check->status,
            'schedule' => $send_time,
            'setting_id' => $campaign_check->setting_id,
            'use_system_mailer' => $campaign_check->use_system_mailer,
        );
        $variables = JsonHelper::decode($campaign_check->variables, true);
        $campaign_data['recipients'] = JsonHelper::decode($campaign_check->send_conditions, true);
        return array_merge($campaign_data, $variables);
    }

    /**
     * @param $email_id
     * @param $order_id
     * @return bool
     */
    public static function saveTrackingOrder($email_id, $order_id): bool
    {
        try {
            $check = EmailLogs::query()->whereKey($email_id)->where('ordered', '=', EmailLogEnum::NOT_ACTIVATED)->first();
            if ($check === null) {
                logToDiscord('Email log not found. Email ID: ' . $email_id . ' / Order ID: ' . $order_id);
                return false;
            }
            $check->update([
                'order_id' => $order_id,
                'ordered' => EmailLogEnum::ACTIVATED,
                'ordered_at' => currentTime()
            ]);
        } catch (\Throwable $e) {
            logException($e);
        }
    }

    /**
     * Get all senders for a seller
     *
     * @param int $sellerId
     * @param int $perPage
     * @param int $page
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public static function getSenders(int $sellerId, int $perPage = 15, int $page = 1): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        // Get all store domains for the seller and return data paginated
        $storeDomains = StoreDomain::query()
            ->select([
                'id as id',
                'seller_id',
                'domain as domain',
                'sender_name as name',
                'sender_email as email',
                'sender_verified as verified',
                'sender_next_verify_at as next_verify_at',
                'sender_dns_records as dns_records',
            ])
            ->where('seller_id', $sellerId)
            ->paginate($perPage, ['*'], 'page', $page);

        // Return the senders data paginated
        return $storeDomains;
    }

    /**
     * Add a new sender
     *
     * @param int $sellerId
     * @param array $senderData
     * @return array
     * @throws \Exception
     */
    public static function addSender($sellerId, $senderData): array
    {
        // Parse domain from email
        $name = $senderData['name'];
        $username = $senderData['username'];
        $storeDomainId = $senderData['store_domain_id'];
        $storeDomain = StoreDomain::query()
            ->where('id', $storeDomainId)
            ->where('seller_id', $sellerId)
            ->first();
        if (empty($storeDomain)) {
            throw new \RuntimeException('Domain not found in store domains.');
        }
        // Check if the domain is already added
        if ($storeDomain->sender_verified !== SesSenderStatus::NOT_VERIFIED) {
            throw new \RuntimeException('Domain is already added.');
        }
        // Check if the domain is not activated
        if ($storeDomain->status !== StoreDomainStatusEnum::ACTIVATED) {
            throw new \RuntimeException('Domain is not activated.');
        }
        // Create Amazon SES service
        $domain = $storeDomain->domain;
        $email = $username . '@' . $domain;
        // Need queue cause SES limit add identity 1 time per second
        SesAddIdentityJob::dispatch($domain, SesIdentityType::DOMAIN);
        SesAddIdentityJob::dispatch($email, SesIdentityType::EMAIL);
        // Pre update store domain
        $storeDomain->sender_email = $email;
        $storeDomain->sender_name = $name;
        $storeDomain->sender_verified = SesSenderStatus::PENDING;
        $storeDomain->sender_next_verify_at = Carbon::now()->addMinutes(5);
        if (!$storeDomain->save()) {
            throw new \RuntimeException('Failed to save sender data.');
        }
        return [
            'id' => $storeDomain->id,
            'name' => $storeDomain->sender_name,
            'email' => $storeDomain->sender_email,
        ];
    }

    /**
     * Verify a sender
     *
     * @param int $sellerId
     * @param int $storeDomainId
     * @return bool
     * @throws \Exception
     */
    public static function getStatus($sellerId, $storeDomainId)
    {
        $storeDomain = StoreDomain::query()
            ->where('id', $storeDomainId)
            ->where('seller_id', $sellerId)
            ->first();
        if (empty($storeDomain)) {
            throw new \RuntimeException('Domain not found in store domains.');
        }
        $sesService = new AmazonSesService();
        $status = $sesService->getVerificationStatus($storeDomain->domain);
        if ($status === false) {
            throw new \RuntimeException('Failed to get verification status.');
        }
        if ($status === SesSenderStatus::FAILED || $status === SesSenderStatus::PENDING) {
            $storeDomain->sender_next_verify_at = Carbon::now()->addMinutes(5);
        } else {
            $storeDomain->sender_next_verify_at = null;
        }
        $storeDomain->sender_verified = $status;
        $storeDomain->save();
        return $status;
    }

    /**
     * Update a sender
     *
     * @param int $sellerId
     * @param int $storeDomainId
     * @param array $senderData
     * @return array
     * @throws \Exception
     */
    public static function updateSender($sellerId, $storeDomainId, $senderData)
    {
        $username = trim($senderData['username']);
        $name = trim($senderData['name']);
        // Delete identity if exists
        $storeDomain = StoreDomain::query()
            ->where('id', $storeDomainId)
            ->where('seller_id', $sellerId)
            ->first();
        if (empty($storeDomain)) {
            throw new \RuntimeException('Domain not found in store domains.');
        }

        // Check if the domain is already verified
        if ($storeDomain->sender_verified === SesSenderStatus::SUCCESS) {
            throw new \RuntimeException('Domain is already verified.');
        }
        // Check if the domain is not verified
        if ($storeDomain->sender_verified === SesSenderStatus::NOT_VERIFIED) {
            throw new \RuntimeException('Domain is not verified.');
        }

        $newEmail = $username . '@' . $storeDomain->domain;
        $oldEmail = $storeDomain->sender_email;
        SesUpdateIdentityJob::dispatch($oldEmail, $newEmail, $name, SesIdentityType::EMAIL);

        return [
            'id' => $storeDomainId,
            'name' => $name,
            'email' => $newEmail
        ];
    }

    /**
     * Get sender details
     *
     * @param int $sellerId
     * @param int $storeDomainId
     * @return array|null
     */
    public static function getSender($sellerId, $storeDomainId)
    {
        // Get store domain by ID
        $storeDomain = StoreDomain::query()
            ->where('id', $storeDomainId)
            ->where('seller_id', $sellerId)
            ->first();
        if (empty($storeDomain)) {
            return null;
        }

        return [
            'id' => $storeDomainId,
            'name' => $storeDomain->sender_name,
            'domain' => $storeDomain->domain,
            'email' => $storeDomain->sender_email,
            'next_verify_at' => $storeDomain->sender_next_verify_at,
            'dns_records' => $storeDomain->sender_dns_records,
            'verified' => $storeDomain->sender_verified,
            'created_at' => $storeDomain->created_at,
            'updated_at' => $storeDomain->updated_at,
        ];
    }

    /**
     * Get domain from store id
     *
     * @param mixed $store_id
     * @param mixed &$stores
     * @return DomainInfoData
     * @throws InvalidArgumentException
     */
    public static function getDomainInfo($store_id, &$stores): DomainInfoData
    {
        if (!isset($stores[$store_id])) {
            $store = Store::query()->whereKey($store_id)->first();
            if (empty($store)) {
                return DomainInfoData::failed();
            }
            $stores[$store_id] = $store;
        } else {
            $store = $stores[$store_id];
        }
        if (empty($store->domain)) {
            $domain = $store->sub_domain . "." . getStoreBaseDomain();
            return DomainInfoData::success($domain, true, $store);
        }
        return DomainInfoData::success($store->domain, false, $store);
    }

    /**
     * Get sender info by domain
     *
     * @param mixed $domain - Domain
     * @return SenderInfoData
     * @throws InvalidArgumentException
     */
    public static function getSenderInfo($domain)
    {
        $storeDomain = StoreDomain::whereDomain($domain)
            ->whereSenderVerified(SesSenderStatus::SUCCESS)
            ->first();

        if ($storeDomain !== null) {
            return SenderInfoData::success($storeDomain->sender_name, $storeDomain->sender_email);
        }

        return SenderInfoData::failed();
    }

    /**
     * Update subscriber next send at
     *
     * @param mixed $sellerId
     * @param mixed $email
     * @return int|bool
     * @throws InvalidArgumentException
     * @throws MassAssignmentException
     */
    public static function updateSubscriberNextSendAt($sellerId, $email)
    {
        $updated = IndexSubscribers::query()
            ->where('seller_id', $sellerId)
            ->where('email', $email)
            ->update([
                'next_send_at' => now()->addWeek(),
            ]);
        if ($updated === 0) {
            return false;
        }
        return $updated;
    }
}
