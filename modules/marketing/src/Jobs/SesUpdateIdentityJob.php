<?php
namespace Modules\Marketing\Jobs;

use App\Models\StoreDomain;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Marketing\Services\AmazonSesService;
use Modules\Marketing\Enums\SesIdentityType;
use Modules\Marketing\Enums\SesSenderStatus;

class SesUpdateIdentityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var string
     */
    protected $oldIdentity;

    /**
     * @var string
     */
    protected $newIdentity;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $type;

    /**
     * Create a new job instance.
     *
     * @param $oldIdentity
     * @param string $newIdentity
     * @param string $type
     */
    public function __construct($oldIdentity, $newIdentity, $name, $type = SesIdentityType::EMAIL)
    {
        $this->onQueue(config('marketing.config.general.aws.ses.queue'));
        $this->oldIdentity = $oldIdentity;
        $this->newIdentity = $newIdentity;
        $this->name = $name;
        $this->type = $type;
    }

    /**
     * @throws \Throwable
     */
    public function handle()
    {
        $serviceSes = new AmazonSesService();
        if ($serviceSes->deleteIdentity($this->oldIdentity) === false) {
            throw new \Exception('Error when delete identity SES');
        }
        // Sleep 1s to wait for SES to update
        sleep(1);
        if ($this->type === SesIdentityType::EMAIL) {
            $result = $serviceSes->addEmailAddress($this->newIdentity);
            if ($result === false) {
                throw new \Exception('Error when add email address SES');
            }
            // Update new identity
            StoreDomain::where('sender_email', $this->oldIdentity)->update([
                'sender_verified' => SesSenderStatus::PENDING,
                'sender_name' => $this->name,
                'sender_email' => $this->newIdentity,
                'sender_next_verify_at' => now()->addMinutes(5),
            ]);
        } elseif ($this->type === SesIdentityType::DOMAIN) {
            $records = $serviceSes->addDomain($this->newIdentity);
            StoreDomain::where('domain', $this->newIdentity)->update([
                'sender_dns_records' => $records,
            ]);
        } else {
            throw new \Exception('Invalid type');
        }
        return true;
    }

    /**
     * @param $exception
     */
    public function failed($exception)
    {
        $message = $exception->getMessage();
        graylogError('Error when update identity SES', [
            'message' => $message,
            'old_identity' => $this->oldIdentity,
            'new_identity' => $this->newIdentity,
            'category' => 'ses',
        ]);
    }
}
