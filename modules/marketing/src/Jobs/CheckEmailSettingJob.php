<?php
namespace Modules\Marketing\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Marketing\Enums\CampaignStatusEnum;
use Modules\Marketing\Events\UpdateEmailSettingUsedTimeEvent;
use Modules\Marketing\Models\EmailCampaigns;
use Modules\Marketing\Supports\Helper;

class CheckEmailSettingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var int
     */
    protected $seller_id;

    /**
     * @var int
     */
    protected $setting_id;

    public function __construct($seller_id, $setting_id)
    {
        $this->seller_id = $seller_id;
        $this->setting_id = $setting_id;
    }
    /**
     * @throws \Throwable
     */
    public function handle()
    {
        if (empty($this->seller_id) || empty($this->setting_id)) {
            return;
        }
        graylogInfo('[' . currentTime() . '] - START - Check email setting connection - Setting ID: #' . $this->setting_id, [
            'category' => 'email_marketing',
        ]);
        $result = Helper::sendCheckEmailConnection($this->setting_id);
        if($result['status']) {
            Helper::updateStateSettingsEmail($this->seller_id, $this->setting_id);
            EmailCampaigns::query()
                ->where('seller_id', $this->seller_id)
                ->where('is_deleted', CampaignStatusEnum::IS_NOT_DELETED)
                ->where('hold_at', '<=', currentTime())
                ->update(array(
                    'status' => CampaignStatusEnum::SCHEDULED,
                    'send_time' => currentTime(),
                    'updated_at' => currentTime(),
                ));
        } else {
            graylogInfo('[' . currentTime() . '] - Email setting invalid - Setting ID: #' . $this->setting_id, [
                'category' => 'email_marketing',
            ]);
            $result = Helper::sendCheckEmailConnection($this->setting_id);
            if($result['status']) {
                Helper::updateStateSettingsEmail($this->seller_id, $this->setting_id);
                EmailCampaigns::query()
                    ->where('seller_id', $this->seller_id)
                    ->where('is_deleted', CampaignStatusEnum::IS_NOT_DELETED)
                    ->where('hold_at', '<=', currentTime())
                    ->update(array(
                        'status' => CampaignStatusEnum::SCHEDULED,
                        'send_time' => currentTime(),
                        'updated_at' => currentTime(),
                    ));
            } else {
                graylogInfo('[' . currentTime() . '] - Email setting invalid - Setting ID: #' . $this->setting_id, [
                    'category' => 'email_marketing',
                ]);
                Helper::updateStateSettingsEmail($this->seller_id, $this->setting_id, false, $result['message'] ?? null);
            }
            event(new UpdateEmailSettingUsedTimeEvent($this->setting_id));
            graylogInfo('[' . currentTime() . '] - END - Check email setting connection - Setting ID: #' . $this->setting_id, [
                'category' => 'email_marketing',
            ]);
        };
    }
}
