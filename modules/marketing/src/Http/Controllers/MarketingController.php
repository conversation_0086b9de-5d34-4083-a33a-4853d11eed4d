<?php

namespace Modules\Marketing\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Campaign;
use App\Models\StoreDomain;
use App\Models\SystemConfig;
use App\Traits\ApiResponse;
use App\Traits\CryptTrait;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Marketing\Data\SenderInfoData;
use Modules\Marketing\Entities\SubMailerConfig;
use Modules\Marketing\Enums\CampaignStatusEnum;
use Modules\Marketing\Enums\EmailLogEnum;
use Modules\Marketing\Enums\EmailSettingsStatusEnum;
use Modules\Marketing\Enums\SesSenderStatus;
use Modules\Marketing\Enums\SubscriberSortByAllowEnum;
use Modules\Marketing\Http\Requests\BulkActionEmailSettingsRequest;
use Modules\Marketing\Http\Requests\BulkActionRequest;
use Modules\Marketing\Http\Requests\CampaignsIndexRequest;
use Modules\Marketing\Http\Requests\EmailSettingsIndexRequest;
use Modules\Marketing\Http\Requests\ExportSubscribersRequest;
use Modules\Marketing\Http\Requests\ImportExcelRequest;
use Modules\Marketing\Http\Requests\SendTestEmailRequest;
use Modules\Marketing\Http\Requests\SendTestEmailSettingRequest;
use Modules\Marketing\Http\Requests\StoreCampaignRequest;
use Modules\Marketing\Http\Requests\StoreEmailSettingRequest;
use Modules\Marketing\Http\Requests\StoreSubscriberRequest;
use Modules\Marketing\Http\Requests\SubscribersIndexRequest;
use Modules\Marketing\Jobs\SesDeleteIdentityJob;
use Modules\Marketing\Models\EmailCampaigns;
use Modules\Marketing\Models\EmailLogs;
use Modules\Marketing\Models\EmailSettings;
use Modules\Marketing\Models\IndexSubscribers;
use Modules\Marketing\Services\AmazonSesService;
use Modules\Marketing\Services\MarketingServices;
use Modules\Marketing\Supports\EmailHandler;
use Modules\Marketing\Supports\Exports\ExportSubscribersToExcel;
use Modules\Marketing\Supports\Helper;
use Modules\Marketing\Supports\Imports\EmailSettingsImport;
use Modules\Marketing\Supports\Imports\SubscribersImport;
use Modules\Marketing\Supports\JsonHelper;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class MarketingController extends Controller
{
    use ApiResponse, CryptTrait;

    /**
     * Index route
     */
    public function index()
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        return $this->successResponse([], 'Welcome to marketing module');
    }

    /**
     * Campaigns route
     */
    public function campaigns(CampaignsIndexRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');

        $status = $request->get('status');
        $search = $request->get('q');
        $orderBy = $request->get('sort');
        $directionBy = $request->get('direction', 'DESC');
        $currentPage = $request->get('page');
        $currentPage = max($currentPage, 1);
        $limit = (int)$request->query('per_page', 15);

        if (!in_array($orderBy, SubscriberSortByAllowEnum::asArray(), true)) {
            $orderBy = SubscriberSortByAllowEnum::getDefault();
        }
        [$campaigns, $total] = MarketingServices::getCampaigns($user->getUserId(), $status, $search, $currentPage, $limit, $orderBy, $directionBy);
        $uri = preg_replace("/page=\d+/", '', Request::getRequestUri());
        return new Paginator(
            $campaigns,
            $total,
            $limit,
            $currentPage,
            ['path' => $uri]
        );
    }

    /**
     * @param StoreCampaignRequest $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function storeCampaign(StoreCampaignRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');

        $seller_id = $user->getUserId();
        $id = $request->post('id', 0);
        $store_id = $request->post('store_id');
        $name = $request->post('name');
        $status = $request->post('status');
        $subject = $request->post('subject');
        $schedule = $request->post('schedule');
        $btn_text = $request->post('btn_text');
        $hero_text = $request->post('hero_text');
        $main_btn_text = $request->post('main_btn_text');
        $recommendations_text = $request->post('recommendations_text');
        $content_text = $request->post('content_text');
        $products = $request->post('products');
        $recipients = $request->post('recipients');
        $setting_id = $request->post('setting_id', 0);
        $use_system_mailer = $request->post('use_system_mailer', false);
        if ($status === CampaignStatusEnum::SCHEDULED && count($products) !== 5) {
            return $this->errorResponse('Please select 05 campaigns before click to send.');
        }
        graylogInfo('[' . currentTime() . '] - Seller save email campaign. Seller ID: ' . $seller_id . ' Campaign ID: #' . $id, [
            'category' => 'email_marketing',
            'data' => [
                'id' => $id,
                'seller_id' => $seller_id,
                'store_id' => $store_id,
                'name' => $name,
                'status' => $status,
                'subject' => $subject,
                'schedule' => $schedule,
                'btn_text' => $btn_text,
                'hero_text' => $hero_text,
                'main_btn_text' => $main_btn_text,
                'recommendations_text' => $recommendations_text,
                'content_text' => $content_text,
                'products' => $products,
                'recipients' => $recipients,
                'setting_id' => $setting_id,
                'use_system_mailer' => $use_system_mailer
            ]
        ]);
        $result = MarketingServices::saveEmailCampaign($id, $seller_id, $store_id, $name, $status, $subject, $schedule, $btn_text, $hero_text, $main_btn_text, $recommendations_text, $content_text, $products, $recipients, $setting_id, $use_system_mailer);
        if (!$result['status']) {
            return $this->errorResponse($result['message']);
        }
        return $this->successResponse([
            'id' => $result['campaign_id']
        ]);
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function getCampaignDetail($id)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        $seller_id = $user->getUserId();
        $campaign = MarketingServices::getEmailCampaign($seller_id, $id);
        return $this->successResponse($campaign);
    }

    /**
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function duplicateCampaign(HttpRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $campaignId = $request->get('campaign_id');
        $targetCampaign = EmailCampaigns::query()->firstWhere([
            'id' => $campaignId,
            'seller_id' => $user->getUserId()
        ]);

        if (is_null($targetCampaign)) {
            return $this->errorResponse('Campaign not found');
        }
        $newCampaign = $targetCampaign->replicate()->fill([
            'slug' => $targetCampaign->slug . '-' . strtolower(Str::random(6)),
            'status' => CampaignStatusEnum::DRAFT,
            'created_at' => currentTime(),
            'updated_at' => currentTime(),
        ]);
        try {
            $newCampaignId = null;
            // Check slug
            $slug = $newCampaign['slug'];
            $tmpSlug = $slug;
            $count = 1;
            while (true) {
                if (EmailCampaigns::query()->where('slug', $tmpSlug)->exists()) {
                    $tmpSlug = $slug . '-' . $count;
                    $count++;
                    continue;
                }
                $newCampaign['slug'] = $tmpSlug;
                break;
            }

            if ($newCampaign->save()) {
                $newCampaignId = $newCampaign->id;
            }
            if (is_null($newCampaignId)) {
                return $this->errorResponse();
            }
            return $this->successResponse(['id' => $newCampaignId], "Duplicate campaign successfully");
        } catch (\Exception $e) {
            return $this->errorResponse("Can't duplicate campaign.");
        }
    }

    /**
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function stopCampaign(HttpRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $campaignId = $request->get('campaign_id');
        $targetCampaign = EmailCampaigns::query()->firstWhere([
            'id' => $campaignId,
            'status' => CampaignStatusEnum::SCHEDULED,
            'is_deleted' => CampaignStatusEnum::IS_NOT_DELETED,
            'seller_id' => $user->getUserId()
        ]);

        if (is_null($targetCampaign)) {
            return $this->errorResponse('The campaign you want to stop scheduled not found');
        }
        $now = strtotime(currentTime());
        $send_time = strtotime($targetCampaign->send_time);
        if (($send_time - $now) < SystemConfig::getConfig('email_marketing_can_stop_scheduled_campaign_in_seconds', 60)) {
            return $this->errorResponse('The campaign you want can not be stopped.');
        }
        try {
            EmailCampaigns::query()
                ->where(['id' => $campaignId])
                ->update([
                    'status' => CampaignStatusEnum::DRAFT,
                    'send_time' => null,
                    'updated_at' => currentTime()
                ]);
            EmailLogs::query()->where('email_campaign_id', $campaignId)->where('sent', '=', EmailLogEnum::NOT_ACTIVATED)->delete();
            return $this->successResponse([], "Stop scheduled campaign successfully");
        } catch (\Exception $e) {
            return $this->errorResponse("Can't stop scheduled campaign.");
        }
    }

    /**
     * @param BulkActionRequest $request
     * @return JsonResponse
     */
    public function bulkCampaignAction(BulkActionRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $data = [];
        $ids = explode(',', $request->post('ids'));
        $action = $request->post('action');
        $status = $request->post('status');
        if ($status) {
            $data['status'] = $status;
        }
        $updated = MarketingServices::updateCampaignsBulk($user->getUserId(), $ids, $data, $action);
        return !empty($updated['status']) ? $this->successResponse([], $updated['message']) : $this->errorResponse($updated['message']);
    }

    /**
     * @param SubscribersIndexRequest $request
     * @return Paginator
     */
    public function subscribers(SubscribersIndexRequest $request): Paginator
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        $search = $request->get('q');
        $orderBy = $request->get('sort');
        $directionBy = $request->get('direction', 'DESC');
        $currentPage = $request->get('page');
        $currentPage = max($currentPage, 1);
        $limit = (int)$request->query('per_page', 15);
        $fromSystem = $request->get('from_system', null);

        if (!in_array($orderBy, SubscriberSortByAllowEnum::asArray(), true)) {
            $orderBy = SubscriberSortByAllowEnum::getDefault();
        }
        [$subscribers, $total] = MarketingServices::getSubscribers($user->getUserId(), $search, $currentPage, $limit, $orderBy, $directionBy, fromSystem: $fromSystem);
        $uri = preg_replace("/page=\d+/", '', Request::getRequestUri());

        return new Paginator(
            $subscribers,
            $total,
            $limit,
            $currentPage,
            ['path' => $uri]
        );
    }

    /**
     * @param StoreSubscriberRequest $request
     * @return JsonResponse
     */
    public function storeSubscriber(StoreSubscriberRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $subscriber_id = $request->post('id');
        $tags = $request->post('tags');
        $data = [
            'name' => $request->post('name'),
            'email' => $request->post('email'),
            'phone' => $request->post('phone'),
            'city' => $request->post('city'),
            'state' => $request->post('state'),
            'country' => $request->post('country'),
            'status' => $request->post('status'),
        ];
        try {
            [$subscriber_id, $update_at, $is_edit] = MarketingServices::saveSubscriber($user->getUserId(), $data, $tags, $subscriber_id);
            return $this->successResponse([
                'id' => $subscriber_id,
                'updated_at' => $update_at,
                'is_edit' => $is_edit,
            ]);
        } catch (\Exception $e) {
            logToDiscord('[' . currentTime() . '] - Seller save subscribers failed. Error: ' . $e->getMessage(), 'email_marketing', true);
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param BulkActionRequest $request
     * @return JsonResponse
     */
    public function bulkAction(BulkActionRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $data = [];
        $ids = explode(',', $request->post('ids'));
        $action = $request->post('action');
        $status = $request->post('status');
        if ($status) {
            $data['status'] = $status;
        }
        $updated = MarketingServices::updateSubscribersBulk($user->getUserId(), $ids, $data, $action);
        return $updated ? $this->successResponse() : $this->errorResponse();
    }

    /**
     * @param ImportExcelRequest $request
     * @return JsonResponse
     */
    public function importSubscribers(ImportExcelRequest $request)
    {
        $file = $request->file('file');
        try {
            Excel::import(new SubscribersImport(), $file);
            return $this->successResponse();
        } catch (\Exception $e) {
            $code = $e->getCode();

            if ($code === 2002) {
                return $this->errorResponse('Import failed', 403);
            }

            if ($code === 23000) {
                return $this->errorResponse('Duplicate entry', 403);
            }

            return $this->errorResponse($e->getMessage(), $code > 0 ? $code : 403);
        }
    }

    /**
     * @param ExportSubscribersRequest $request
     * @return JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportSubscribers(ExportSubscribersRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        $seller_id = $user->getUserId();
        $option = $request->input('option');
        $search = $request->get('q');
        $orderBy = $request->get('sort');
        $directionBy = $request->get('direction', 'DESC');
        if (!in_array($orderBy, SubscriberSortByAllowEnum::asArray(), true)) {
            $orderBy = SubscriberSortByAllowEnum::getDefault();
        }
        if ($option === 'export_all') {
            $search = '';
        }
        [$subscribers, $total] = MarketingServices::getSubscribers($seller_id, $search, 0, 0, $orderBy, $directionBy, true, false);
        if ($total === 0) {
            return $this->errorResponse('No subscribers available to export', 403);
        }
        $subscribersModified = array_map(static function ($subscriber) {
            $tags = $subscriber['tags'];
            unset($subscriber['id'],
                $subscriber['is_deleted'],
                $subscriber['deleted_at'],
                $subscriber['created_at'],
                $subscriber['tags'],
                $subscriber['email_subscribed'],
                $subscriber['sms_subscribed'],
                $subscriber['seller_id'],
                $subscriber['seller'],
                $subscriber['domain'],
                $subscriber['full_tags']
            );
            $subscriber['tags'] = implode('|', $tags);
            $subscriber['status'] = Str::ucfirst($subscriber['status']);
            $subscriber['updated_at'] = date('m/d/Y', strtotime($subscriber['updated_at']));
            return $subscriber;
        }, $subscribers);
        $headings = array_keys($subscribersModified[0]);
        $fileName = 'Senprints_subscribers_export_' . Carbon::now()->toDateTimeLocalString() . '.csv';
        $export = new ExportSubscribersToExcel($subscribersModified, $headings);
        return Excel::download($export, $fileName);
    }

    /**
     * For testing email
     *
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function updateSubscriberNextSendAt(HttpRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'seller_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $email = $request->get('email');
        $sellerId = $request->get('seller_id');
        $updatedRows = IndexSubscribers::query()
            ->where('email', $email)
            ->where('seller_id', $sellerId)
            ->update([
                'next_send_at' => now()->addMinutes(5)
            ]);
        if ($updatedRows === 0) {
            return $this->errorResponse('Email not found');
        }
        return $this->successResponse([
            'email' => $email,
            'next_send_at' => now()->addMinutes(5)
        ], 'Update next send at successfully');
    }

    /**
     * @param EmailSettingsIndexRequest $request
     * @return Paginator
     */
    public function emailSettings(EmailSettingsIndexRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_marketing');
        $search = $request->get('q');
        $orderBy = $request->get('sort');
        $directionBy = $request->get('direction', 'DESC');
        $currentPage = $request->get('page');
        $currentPage = max($currentPage, 1);
        $limit = (int)$request->query('per_page', 15);

        $status = $request->get('status');
        $driver = $request->get('driver');

        if (!in_array($orderBy, SubscriberSortByAllowEnum::asArray(), true)) {
            $orderBy = SubscriberSortByAllowEnum::getDefault();
        }
        [$settings, $total] = MarketingServices::getEmailSettings($user->getUserId(), $search, $status, $driver, $currentPage, $limit, $orderBy, $directionBy);
        $uri = preg_replace("/page=\d+/", '', Request::getRequestUri());

        return new Paginator(
            $settings,
            $total,
            $limit,
            $currentPage,
            ['path' => $uri]
        );
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function getEmailSettingDetail($id): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_marketing');
        $seller_id = $user->getUserId();
        $setting = MarketingServices::getEmailSettingDetail($seller_id, $id);
        return $this->successResponse($setting);
    }

    /**
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function duplicateEmailSetting(HttpRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_marketing');
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $setting_id = $request->get('id');
        $seller_id = $user->getUserId();
        $setting = EmailSettings::query()->where([
            'id' => $setting_id,
            'seller_id' => $seller_id,
            'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
        ])->first();

        if (is_null($setting)) {
            return $this->errorResponse('Email setting not found');
        }
        $newEmailSetting = $setting->replicate()->fill([
            'status' => EmailSettingsStatusEnum::INVALID,
            'last_used_at' => null,
            'hold_at' => null,
            'deleted_at' => null,
            'created_at' => currentTime(),
            'updated_at' => currentTime(),
        ]);
        try {
            $newSettingId = null;
            if ($newEmailSetting->save()) {
                $newSettingId = $newEmailSetting->id;
            }
            if (is_null($newSettingId)) {
                return $this->errorResponse();
            }
            return $this->successResponse(['id' => $newSettingId], "Duplicate email setting successfully");
        } catch (\Exception $e) {
            return $this->errorResponse("Can't duplicate email setting.");
        }
    }

    /**
     * @param BulkActionEmailSettingsRequest $request
     * @return JsonResponse
     */
    public function bulkEmailSettingsAction(BulkActionEmailSettingsRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_marketing');
        $data = [];
        $ids = explode(',', $request->post('ids'));
        $action = $request->post('action');
        $sender_name = $request->post('sender_name');
        $sender_email = $request->post('sender_email');
        $delay = $request->post('delay');
        $limit_per_send = $request->post('limit_per_send');
        if (!empty($sender_name)) {
            $data['sender_name'] = $sender_name;
        }
        if (!empty($sender_email)) {
            $data['sender_email'] = $sender_email;
        }
        if (!empty($delay)) {
            $data['delay'] = $delay;
        }
        if (!empty($limit_per_send)) {
            $data['limit_per_send'] = $limit_per_send;
        }
        $updated = MarketingServices::updateEmailSettingsBulk($user->getUserId(), $ids, $data, $action);
        return !empty($updated['status']) ? $this->successResponse([], $updated['message']) : $this->errorResponse($updated['message']);
    }

    /**
     * @param StoreEmailSettingRequest $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function saveEmailSettings(StoreEmailSettingRequest $request): JsonResponse
    {
        $currentUser = currentUser();
        $seller_id = $currentUser->getUserId();
        $id = $request->get('id', 0);
        $driver = $request->get('driver');
        $smtp_host = $request->get('smtp_host');
        $smtp_port = $request->get('smtp_port');
        $smtp_username = $request->get('smtp_username');
        $smtp_password = $request->get('smtp_password');
        $smtp_encryption = $request->get('smtp_encryption');
        $mailgun_domain = $request->get('mailgun_domain');
        $mailgun_secret = $request->get('mailgun_secret');
        $mailgun_endpoint = $request->get('mailgun_endpoint');
        $ses_key = $request->get('ses_key');
        $ses_secret = $request->get('ses_secret');
        $ses_region = $request->get('ses_region');
        $sender_name = $request->get('sender_name');
        $sender_email = $request->get('sender_email');
        $email_sent_test = $request->get('email_sent_test', $currentUser->getEmail());
        $delay = (int)$request->get('delay', 0);
        $limit_per_send = (int)$request->get('limit_per_send', SystemConfig::getConfig('email_marketing_limit_send_email_per_time', 500));
        if ($driver === 'smtp') {
            $settings = JsonHelper::encode(array(
                'smtp_host' => $smtp_host,
                'smtp_port' => $smtp_port,
                'smtp_username' => $smtp_username,
                'smtp_password' => $smtp_password,
                'smtp_encryption' => Str::lower($smtp_encryption),
            ));
        }
        if ($driver === 'ses') {
            $settings = JsonHelper::encode(array(
                'ses_key' => $ses_key,
                'ses_secret' => $ses_secret,
                'ses_region' => $ses_region
            ));
        }
        if ($driver === 'mailgun') {
            $settings = JsonHelper::encode(array(
                'mailgun_domain' => $mailgun_domain,
                'mailgun_secret' => $mailgun_secret,
                'mailgun_endpoint' => $mailgun_endpoint,
            ));
        }
        $current_time = currentTime();
        $email_settings = EmailSettings::query()->where([
            'seller_id' => $seller_id,
            'driver' => $driver,
            'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
        ])->get();
        $crypt = $this->crypt();
        if (!empty($email_settings)) {
            foreach ($email_settings as $email_setting) {
                if (empty($email_setting->settings)) {
                    continue;
                }
                $decrypt_settings = $crypt->decryptString($email_setting->settings);
                $decrypt_settings = JsonHelper::decode($decrypt_settings, true);
                if (($driver === 'smtp') && $smtp_username === $decrypt_settings['smtp_username']) {
                    $id = $email_setting->id;
                    break;
                }
                if (($driver === 'ses') && $ses_key === $decrypt_settings['ses_key']) {
                    $id = $email_setting->id;
                    break;
                }
                if (($driver === 'mailgun') && $mailgun_secret === $decrypt_settings['mailgun_secret']) {
                    $id = $email_setting->id;
                    break;
                }
            }
        }
        if (!empty($id)) {
            $setting = EmailSettings::query()->where([
                'seller_id' => $seller_id,
                'is_deleted' => CampaignStatusEnum::IS_NOT_DELETED,
                'id' => $id
            ])->first();
            if (empty($setting)) {
                return $this->errorResponse('Your email setting does not exists.');
            }
            $setting->update([
                'driver' => $driver,
                'delay' => $delay,
                'limit_per_send' => $limit_per_send,
                'settings' => $crypt->encryptString($settings),
                'last_used_at' => $current_time,
                'hold_at' => $current_time,
                'updated_at' => $current_time,
                'sender_name' => $sender_name,
                'sender_email' => $sender_email
            ]);
        } else {
            $id = EmailSettings::query()->insertGetId([
                'seller_id' => $seller_id,
                'driver' => $driver,
                'delay' => $delay,
                'limit_per_send' => $limit_per_send,
                'settings' => $crypt->encryptString($settings),
                'is_deleted' => CampaignStatusEnum::IS_NOT_DELETED,
                'last_used_at' => $current_time,
                'hold_at' => $current_time,
                'deleted_at' => $current_time,
                'created_at' => $current_time,
                'updated_at' => $current_time,
                'sender_name' => $sender_name,
                'sender_email' => $sender_email
            ]);
        }
        Helper::removeCachedEmailSettings($id);
        $result = Helper::sendCheckEmailConnection($id, $email_sent_test);
        if ($result['status']) {
            Helper::updateStateSettingsEmail($seller_id, $id);
            return $this->successResponse([], 'Your settings have been saved. Email sent successfully to email address: ' . $email_sent_test);
        }
        Helper::updateStateSettingsEmail($seller_id, $id, false, $result['message'] ?? null);
        logToDiscord('[' . currentTime() . '] - Seller send a test email failed. Error: ' . $result['message'], 'email_marketing', true);
        return $this->errorResponse('Something wrong. ' . $result['message']);
    }

    /**
     * @param SendTestEmailSettingRequest $request
     * @return JsonResponse
     */
    public function sendTestEmailSettings(SendTestEmailSettingRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_marketing');
        $seller_id = $user->getUserId();
        $setting_id = $request->get('id');
        $email_sent_test = $request->get('email_sent_test');
        $result = Helper::sendCheckEmailConnection($setting_id, $email_sent_test);
        if ($result['status']) {
            Helper::updateStateSettingsEmail($seller_id, $setting_id);
            return $this->successResponse([], 'The email sent successfully to email address: ' . $email_sent_test);
        }
        Helper::updateStateSettingsEmail($seller_id, $setting_id, false, $result['message'] ?? null);
        logToDiscord('[' . currentTime() . '] - Seller send a test email failed. Error: ' . $result['message'], 'email_marketing', true);
        return $this->errorResponse('Something wrong. ' . $result['message']);
    }

    /**
     * @param ImportExcelRequest $request
     * @return JsonResponse
     */
    public function importEmailSettings(ImportExcelRequest $request)
    {
        $file = $request->file('file');
        try {
            Excel::import(new EmailSettingsImport(), $file);
            return $this->successResponse();
        } catch (\Exception $e) {
            $code = $e->getCode();

            if ($code === 2002) {
                return $this->errorResponse('Import failed', 403);
            }

            if ($code === 23000) {
                return $this->errorResponse('Duplicate entry', 403);
            }

            return $this->errorResponse($e->getMessage(), $code > 0 ? $code : 403);
        }
    }

    /**
     * @param SendTestEmailRequest $request
     * @return JsonResponse
     */
    public function sendTestEmailCampaign(SendTestEmailRequest $request)
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required|integer',
            'email' => 'required|string|email',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        try {
            $campaign_id = $request->post('campaign_id');
            $email = $request->post('email');
            $campaign = EmailCampaigns::query()->whereKey($campaign_id)->firstOrFail();
            if ($campaign === null) {
                return $this->errorResponse('Campaign not found');
            }
            $template = MARKETING_MODULE_PATH . '/resources/templates/' . $campaign->email_template . '.tpl';
            if (!File::exists($template)) {
                return $this->errorResponse('The email template not found');
            }
            $useSystemMailer = (bool)$campaign->use_system_mailer ?? false;

            if ($useSystemMailer) {
                $stores = [];
                $domainInfo = MarketingServices::getDomainInfo($campaign->store_id, $stores);
                if ($domainInfo->isFailed()) {
                    return $this->errorResponse('Can not found store');
                }
                if ($domainInfo->is_subdomain) {
                    $senderInfo = SenderInfoData::success($domainInfo->store_info->name, 'store@' . $domainInfo->domain);
                } else {
                    $senderInfo = MarketingServices::getSenderInfo($domainInfo->domain);
                }
                if ($senderInfo->isFailed()) {
                    return $this->errorResponse('Can not found store domain');
                }
            }

            $subMailerConfig = new SubMailerConfig();
            if ($useSystemMailer) {
                $subMailerConfig->setUseSystemMailer($useSystemMailer);
                $subMailerConfig->setSenderEmail($senderInfo->sender_email);
                $subMailerConfig->setSenderName($senderInfo->sender_name);
            }

            $email_id = $campaign_id;
            $subscriber_id = Str::random(6);
            $email_handler = new EmailHandler();
            $email_handler->setUserId($campaign->seller_id);
            $variables = JsonHelper::decode($campaign->variables, true);
            $variables['raw_email_id'] = $email_id;
            $variables['raw_subscriber_id'] = $subscriber_id;
            $variables['email_id'] = $email_id;
            $variables['subscriber_id'] = $subscriber_id;
            $variables['name'] = $campaign->store->name;
            $variables['first_name'] = explode(' ', $campaign->store->name)[0];
            $variables['random_string'] = Str::random(6);
            foreach ($variables['products_variables'] as $idx => $product) {
                $variables['product_name_' . $idx] = $product['product_name'];
                $variables['product_image_' . $idx] = $product['product_image'];
                $variables['product_price_' . $idx] = $product['product_price'];
                $variables['product_url_' . $idx] = str_replace(array('{{product_id}}', '{{email_id}}'), array($product['product_id'], $variables['email_id']), $variables['product_click_url']);
            }
            unset($variables['products_variables'], $variables['product_click_url'], $variables['products']);
            $email_handler->setSubMailerConfig($subMailerConfig);
            $email_handler->addVariables($variables);
            if ($campaign->setting_id) {
                $email_handler->setSettingId($campaign->setting_id);
            }
            $email_handler->send(
                File::get($template),
                $variables['subject'] . ' #' . Str::random(6),
                $email,
                $variables
            );
            return $this->successResponse([], 'Email sent successfully to email address: ' . $request->post('email'));
        } catch (\Throwable $exception) {
            logToDiscord('[' . currentTime() . '] - Seller sent a test email failed. Error: ' . $exception->getMessage(), 'email_marketing', true);
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param $store_id
     * @return JsonResponse
     */
    public function getStore($store_id): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        if (empty($store_id)) {
            return $this->errorResponse('Can not found store');
        }
        $store = MarketingServices::getSellerStoreDetail($user->getUserId(), $store_id);
        return !empty($store) ? $this->successResponse($store) : $this->errorResponse();
    }

    /**
     * @return JsonResponse
     */
    public function prepareCampaignData(HttpRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_campaigns');
        $seller_id = $user->getUserId();
        $result = [];
        $useSystemMailer = (bool)$request->get('use_system_mailer', false);
        [$subscribers, $total] = MarketingServices::getSubscribers($seller_id, '', 0, 0, 'created_at', 'desc', true, false, useSystemMailer: $useSystemMailer);
        if (empty($total)) {
            $result['countries'] = [];
            $result['cities'] = [];
            $result['states'] = [];
            $result['tags'] = [];
            $result['domains'] = [];
            $result['subscribers'] = 0;
            $result['matched_subscribers'] = 0;
            return $this->successResponse($result);
        }
        $query_countries = $request->get('countries');
        $query_cities = $request->get('cities');
        $query_states = $request->get('states');
        $query_tags = $request->get('tags');
        $query_exclude_domains = $request->get('exclude_domains');
        $query_countries = explode('|', $query_countries);
        $query_cities = explode('|', $query_cities);
        $query_states = explode('|', $query_states);
        $query_tags = explode('|', $query_tags);
        $query_exclude_domains = explode('|', $query_exclude_domains);
        $filtered = MarketingServices::filterSubscribersByCondititions($subscribers, $query_countries, $query_cities, $query_states, $query_tags, $query_exclude_domains);
        $countries = [];
        $cities = [];
        $states = [];
        $tags = [];
        $domains = [];
        if (!empty($query_countries)) {
            $query_countries = array_map('trim', $query_countries);
            $query_countries = array_map(static function ($item) {
                return Str::slug($item);
            }, $query_countries);
        }
        foreach ($subscribers as $subscriber) {
            if (!empty($subscriber['country'])) {
                $countries[$subscriber['country']]['id'] = $subscriber['country'];
                $countries[$subscriber['country']]['name'] = $subscriber['country'];
            }
            if (!empty($subscriber['domain'])) {
                $domains[$subscriber['domain']]['id'] = $subscriber['domain'];
                $domains[$subscriber['domain']]['name'] = $subscriber['domain'];
            }
            if (!empty($subscriber['full_tags'])) {
                foreach ($subscriber['full_tags'] as $tag) {
                    $tags[$tag['id']]['id'] = $tag['id'];
                    $tags[$tag['id']]['name'] = $tag['name'];
                }
            }
        }
        foreach ($subscribers as $subscriber) {
            if (!empty($subscriber['city'])) {
                if (!empty($query_countries[0]) && !Str::contains(Str::slug($subscriber['country']), $query_countries)) {
                    continue;
                }
                $cities[$subscriber['city']]['id'] = $subscriber['city'];
                $cities[$subscriber['city']]['name'] = $subscriber['city'];
            }
        }
        foreach ($subscribers as $subscriber) {
            if (!empty($subscriber['state'])) {
                if (!empty($query_countries[0]) && !Str::contains(Str::slug($subscriber['country']), $query_countries)) {
                    continue;
                }
                $states[$subscriber['state']]['id'] = $subscriber['state'];
                $states[$subscriber['state']]['name'] = $subscriber['state'];
            }
        }
        $result['countries'] = array_values($countries);
        $result['cities'] = array_values($cities);
        $result['states'] = array_values($states);
        $result['tags'] = array_values($tags);
        $result['domains'] = array_values($domains);
        $result['subscribers'] = $total;
        $result['matched_subscribers'] = count($filtered);
        return $this->successResponse($result);
    }

    /**
     * @param HttpRequest $request
     * @return Application|JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function unsubscribeEmailByToken(HttpRequest $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'cid' => 'required|string',
                'sid' => 'required|string',
                'slid' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->errors());
            }
            $campaign_id = $request->get('cid');
            $campaign = EmailCampaigns::query()->firstWhere('id', $campaign_id);
            if ($campaign === null) {
                throw new ModelNotFoundException();
            }
            $subscribe_id = $request->get('sid');
            $subscriber = IndexSubscribers::query()->whereKey($subscribe_id)->firstOrFail();
            if ($subscriber !== null) {
                $subscriber->unsubscribe();
            }
            $store_url = Helper::getFullStoreUrl($campaign->store);
            return redirect($store_url . '?utm_campaign=' . addslashes($campaign->name) . '&utm_source=email_marketing&utm_medium=email_unsubscribe');
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param HttpRequest $request
     * @return Application|ResponseFactory|JsonResponse|Response
     */
    public function trackingOpen(HttpRequest $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'seid' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->errors());
            }
            $email_id = $request->get('seid');
            $check = EmailLogs::query()->whereKey($email_id)->where('opened', '=', EmailLogEnum::NOT_ACTIVATED)->first();
            if ($check === null) {
                return response(file_get_contents(MARKETING_MODULE_PATH . '/resources/images/pixel.gif'))->header('content-type', 'image/gif');
            }
            $check->update([
                'opened' => 1,
                'opened_at' => currentTime()
            ]);
            return response(file_get_contents(MARKETING_MODULE_PATH . '/resources/images/pixel.gif'))->header('content-type', 'image/gif');
        } catch (\Throwable $e) {
            return response(file_get_contents(MARKETING_MODULE_PATH . '/resources/images/pixel.gif'))->header('content-type', 'image/gif');
        }
    }

    /**
     * @param HttpRequest $request
     * @return Application|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|JsonResponse
     */
    public function trackingClick(HttpRequest $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'pid' => 'required|string',
                'seid' => 'required|string',
                'slid' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->errors());
            }
            $product_id = $request->get('pid');
            $email_id = $request->get('seid');
            $seller_id = $request->get('slid', 0);
            $seller = currentUser($seller_id)->getInfoAccess();
            $product = Campaign::query()
                ->onSellerConnection($seller)
                ->firstWhere('id', $product_id);
            if ($product === null) {
                throw new ModelNotFoundException();
            }
            $email = EmailLogs::query()->whereKey($email_id)->where('sent', '=', EmailLogEnum::ACTIVATED)->first();
            if ($email !== null) {
                $campaign = EmailCampaigns::query()->firstWhere('id', $email->email_campaign_id);
                $click_data = !empty($email->clicked_data) ? JsonHelper::decode($email->clicked_data, true) : [];
                $is_clicked = Arr::where($click_data, function ($value) use ($product_id) {
                    return (int)$value['product_id'] === (int)$product_id;
                });
                if (empty($is_clicked)) {
                    $click_data[] = array(
                        'product_id' => $product_id,
                        'clicked_at' => currentTime(),
                    );
                    ++$email->clicked;
                } else {
                    // $click_data = array_map(static function (&$clicked) use ($product_id) {
                    //     if((int) $clicked['product_id'] === (int) $product_id) {
                    //         $clicked['clicked_at'] = currentTime();
                    //     }
                    //     return $clicked;
                    // }, $click_data);

                    foreach ($click_data as $id => $click) {
                        if ((int)$click['product_id'] === (int)$product_id) {
                            $click_data[$id]['clicked_at'] = currentTime();
                        }
                    }
                }
                $email->clicked_data = JsonHelper::encode($click_data);
                $email->save();
            } else {
                $campaign = EmailCampaigns::query()->firstWhere('id', $email_id);
            }
            if ($campaign === null) {
                throw new ModelNotFoundException();
            }
            $variables = !empty($campaign->variables) ? JsonHelper::decode($campaign->variables, true) : [];
            $products = $variables['products'] ?? [];
            if (!Str::contains($product_id, $products)) {
                throw new ModelNotFoundException();
            }
            $store_url = Helper::getFullStoreUrl($campaign->store);
            return redirect()->away($store_url . '/' . $product->slug . '?utm_campaign=' . addslashes($campaign->name) . '&utm_source=email_marketing&utm_medium=email&seid=' . $email_id);
        } catch (\Throwable $e) {
            throw new ModelNotFoundException();
        }
    }

    /**
     * @param HttpRequest $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|JsonResponse
     */
    public function previewTemplate(HttpRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $campaign_id = $request->get('campaign_id');
        $campaign = EmailCampaigns::query()->whereKey($campaign_id)->firstOrFail();
        if ($campaign === null) {
            return $this->errorResponse('Campaign not found');
        }
        $data = array();
        $store = $campaign->store;
        $variables = JsonHelper::decode($campaign->variables);
        $store_url = Helper::getFullStoreUrl($store);
        $data['raw_email_id'] = 1;
        $data['raw_subscriber_id'] = 1;
        $data['raw_campaign_id'] = 1;
        $data['email_log_id'] = 1;
        $data['email_id'] = Str::uuid();
        $data['subscriber_id'] = 1;
        $data['campaign_id'] = 1;
        $data['store_url'] = $store_url;
        $data['store_logo'] = imgUrl($store->logo_url, 'logo');
        $data['store_name'] = $store->name;
        $data['store_color'] = $store->default_color ?? 'rgb(41, 199, 71)';
        $data['store_address'] = $store->name . ' | ' . $store->address;
        $data['hero_text'] = $variables->hero_text;
        $data['btn_text'] = $variables->btn_text;
        $data['main_btn_text'] = $variables->main_btn_text;
        $data['recommendations_text'] = $variables->recommendations_text;
        $data['first_name'] = explode(' ', $store->name)[0];
        $data['random_string'] = Str::random(6);
        $data['content_text'] = $variables->content_text;
        $data['faq_url'] = $store_url . '/page/faq';
        $data['contact_url'] = $store_url . '/page/contact-us';
        $data['unsubscribe_url'] = $store_url . '/api/marketing/email/unsubscribe?t={{subscriber_id}}&c={{campaign_id}}';
        $data['image_open_url'] = $store_url . '/api/marketing/email/open?t={{email_id}}';
        $data['product_click_url'] = $store_url . '/api/marketing/email/click?p={{product_id}}&e={{email_id}}';
        foreach ($variables->products_variables as $idx => $product) {
            $data['product_name_' . $idx] = $product->product_name;
            $data['product_image_' . $idx] = $product->product_image;
            $data['product_price_' . $idx] = $product->product_price;
            $data['product_url_' . $idx] = str_replace(array('{{product_id}}', '{{email_id}}'), array($product->product_id, $data['email_id']), $data['product_click_url']);
        }
        return view('marketing::default', $data);
    }

    /**
     * @param BulkActionRequest $request
     * @return JsonResponse
     */
    public function resumeHoldCampaignAction(BulkActionRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $data = [];
        $ids = explode(',', $request->post('ids'));
        $current_time = currentTime();
        $status = $request->post('status');
        if ($status) {
            $data['status'] = $status;
            $data['send_time'] = $current_time;
            $data['hold_at'] = $current_time;
            $data['updated_at'] = $current_time;
        }
        $updated = EmailCampaigns::query()
            ->where('seller_id', $user->getUserId())
            ->whereIn('id', $ids)
            ->where('status', CampaignStatusEnum::ON_HOLD);
        $updated = $updated->update($data) > 0;
        if (!$updated) {
            return $this->errorResponse('Cannot update selected campaigns.');
        }
        return $this->successResponse([], 'Updated selected campaigns successfully.');
    }

    /**
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function stopSendingCampaign(HttpRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $campaignId = $request->get('campaign_id');
        $targetCampaign = EmailCampaigns::query()->firstWhere([
            'id' => $campaignId,
            'status' => CampaignStatusEnum::SENDING,
            'is_deleted' => CampaignStatusEnum::IS_NOT_DELETED,
            'seller_id' => $user->getUserId()
        ]);

        if (is_null($targetCampaign)) {
            return $this->errorResponse('The campaign you want to stop sending not found');
        }
        try {
            EmailCampaigns::query()
                ->where(['id' => $campaignId])
                ->update([
                    'status' => CampaignStatusEnum::ON_HOLD,
                    'hold_at' => currentTime(),
                    'updated_at' => currentTime()
                ]);
            return $this->successResponse([], "Stop sending campaign successfully");
        } catch (\Exception $e) {
            return $this->errorResponse("Can't stop sending campaign.");
        }
    }

    /**
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function cancelHoldCampaignAction(HttpRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_campaigns');
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $campaignId = $request->get('campaign_id');
        $targetCampaign = EmailCampaigns::query()->firstWhere([
            'id' => $campaignId,
            'status' => CampaignStatusEnum::ON_HOLD,
            'is_deleted' => CampaignStatusEnum::IS_NOT_DELETED,
            'seller_id' => $user->getUserId()
        ]);

        if (is_null($targetCampaign)) {
            return $this->errorResponse('The campaign you want to cancel not found');
        }
        try {
            EmailCampaigns::query()
                ->where(['id' => $campaignId])
                ->update([
                    'status' => CampaignStatusEnum::CANCELLED,
                    'updated_at' => currentTime()
                ]);
            return $this->successResponse([], "Cancel hold campaign successfully");
        } catch (\Exception $e) {
            return $this->errorResponse("Can't cancel hold campaign.");
        }
    }

    /**
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function changeStateEmailSettings(HttpRequest $request): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_marketing');
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'state' => 'required|integer|in:0,1',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $id = $request->get('id');
        $state = $request->get('state');
        $targetSetting = EmailSettings::query()->firstWhere([
            'id' => $id,
            'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
            'seller_id' => $user->getUserId()
        ]);

        if (is_null($targetSetting)) {
            return $this->errorResponse('The email setting you want to update not found');
        }
        try {
            EmailSettings::query()
                ->where(['id' => $id])
                ->update([
                    'state' => $state,
                    'updated_at' => currentTime()
                ]);
            return $this->successResponse([], "Change the status of email setting successfully");
        } catch (\Exception $e) {
            return $this->errorResponse("Can't change the status of email setting.");
        }
    }

    /**
     * Get list of senders
     *
     * @return JsonResponse
     */
    public function getSenders(HttpRequest $request)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);
        try {
            $senders = MarketingServices::getSenders($sellerId, $perPage, $page);
            return $this->successResponse($senders);
        } catch (\Exception $e) {
            graylogError('Failed to get senders', [
                'seller_id' => $sellerId,
                'message' => $e->getMessage(),
                'category' => 'ses',
            ]);
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Add a new sender
     *
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function addSender(HttpRequest $request)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:64|regex:/^[a-zA-Z0-9-_\s]+$/',
            'username' => 'required|max:12|regex:/^[a-z0-9-_]+$/',
            'store_domain_id' => 'required|exists:store_domains,id',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }

        $senderData = $request->only(['name', 'username', 'store_domain_id']);
        try {
            $sender = MarketingServices::addSender($sellerId, $senderData);
            return $this->successResponse($sender, 'Sender being queued for verification');
        } catch (\Exception $e) {
            graylogError('Failed to add sender', [
                'seller_id' => $sellerId,
                'message' => $e->getMessage(),
                'category' => 'ses',
            ]);
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Check identity status
     *
     * @param int $storeDomainId
     * @return JsonResponse
     */
    public function getStatus($storeDomainId)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $cacheKey = 'email_marketing_ses_identity_status_' . $storeDomainId;
        $cacheTime = 5 * 60; // 5 minutes
        $status = Cache::remember($cacheKey, $cacheTime, function () use ($sellerId, $storeDomainId) {
            try {
                return MarketingServices::getStatus($sellerId, $storeDomainId);
            } catch (\Exception $e) {
                graylogError('Failed to check status', [
                    'seller_id' => $sellerId,
                    'store_domain_id' => $storeDomainId,
                    'message' => $e->getMessage(),
                    'category' => 'ses',
                ]);
                return null;
            }
        });
        if ($status === null) {
            return $this->errorResponse('Failed to check status', 500);
        }
        if ($status === SesSenderStatus::FAILED) {
            return $this->successResponse([
                'status' => SesSenderStatus::FAILED,
            ], 'Sender is not verified');
        }
        if ($status === SesSenderStatus::PENDING) {
            return $this->successResponse([
                'status' => SesSenderStatus::PENDING,
            ], 'Sender is pending');
        }
        if ($status === SesSenderStatus::SUCCESS) {
            return $this->successResponse([
                'status' => SesSenderStatus::SUCCESS,
            ], 'Sender is verified');
        }
        if ($status === SesSenderStatus::NOT_VERIFIED) {
            return $this->successResponse([
                'status' => SesSenderStatus::NOT_VERIFIED,
            ], 'Sender is not verified');
        }
        return $this->errorResponse('Failed to check status', 500);
    }

    /**
     * Update a sender
     *
     * @param int $storeDomainId
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function updateSender($storeDomainId, HttpRequest $request)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:64|regex:/^[a-zA-Z0-9-_\s]+$/',
            'username' => 'required|max:12|regex:/^[a-z0-9-_]+$/',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }

        $senderData = $request->only(['name', 'username']);
        try {
            $updatedSender = MarketingServices::updateSender($sellerId, $storeDomainId, $senderData);
        } catch (\Exception $e) {
            graylogError('Failed to update sender', [
                'seller_id' => $sellerId,
                'store_domain_id' => $storeDomainId,
                'message' => $e->getMessage(),
                'category' => 'ses',
            ]);
            return $this->errorResponse($e->getMessage(), 500);
        }

        return $updatedSender
            ? $this->successResponse($updatedSender, 'Sender is being queued for update')
            : $this->errorResponse('Failed to update sender', 500);
    }

    /**
     * Get sender details
     *
     * @param int $storeDomainId
     * @return JsonResponse
     */
    public function getSender($storeDomainId)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();

        try {
            $sender = MarketingServices::getSender($sellerId, $storeDomainId);
            return $sender
                ? $this->successResponse($sender)
                : $this->errorResponse('Sender not found', 404);
        } catch (\Exception $e) {
            graylogError('Failed to get sender', [
                'seller_id' => $sellerId,
                'store_domain_id' => $storeDomainId,
                'message' => $e->getMessage(),
                'category' => 'ses',
            ]);
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Delete a sender
     *
     * @param int $storeDomainId
     * @param HttpRequest $request
     * @return JsonResponse
     */
    public function deleteSender($storeDomainId)
    {
        $userId = currentUser()->getUserId();
        $storeDomain = StoreDomain::query()
            ->where('seller_id', $userId)
            ->where('id', $storeDomainId)
            ->first();
        if (empty($storeDomain)) {
            return $this->errorResponse('Sender not found', 404);
        }
        SesDeleteIdentityJob::dispatch($storeDomainId);
        return $this->successResponse([], 'Sender is being queued for deletion');
    }

    /**
     * Delete a sender
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getIdentities()
    {
        $sesService = new AmazonSesService();
        $identities = $sesService->getIdentities();
        return $this->successResponse($identities);
    }

    /**
     * Delete a sender
     * @param HttpRequest $request
     * @return JsonResponse
     * @throws BadRequestException
     * @throws BindingResolutionException
     */
    public function deleteIdentity(HttpRequest $request)
    {
        $identity = $request->get('identity');
        $sesService = new AmazonSesService();
        $result = $sesService->deleteIdentity($identity);
        return $this->successResponse([
            'result' => $result,
        ]);
    }
}
