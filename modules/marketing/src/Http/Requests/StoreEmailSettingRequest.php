<?php
namespace Modules\Marketing\Http\Requests;

use App\Traits\PreventsRedirectWhenFailedTrait;

class StoreEmailSettingRequest extends Request
{
    use PreventsRedirectWhenFailedTrait;
    public function rules(): array
    {
        return [
            'id' => 'nullable',
            'driver' => 'required|string|in:smtp,ses,mailgun',
            'delay' => 'required|integer',
            'limit_per_send' => 'required|integer',
            'smtp_host' => 'required_if:driver,==,smtp',
            'smtp_port' => 'required_if:driver,==,smtp',
            'smtp_username' => 'required_if:driver,==,smtp',
            'smtp_password' => 'required_if:driver,==,smtp',
            'smtp_encryption' => 'required_if:driver,==,smtp',
            'mailgun_domain' => 'required_if:driver,==,mailgun',
            'mailgun_secret' => 'required_if:driver,==,mailgun',
            'mailgun_endpoint' => 'required_if:driver,==,mailgun',
            'ses_key' => 'required_if:driver,==,ses',
            'ses_secret' => 'required_if:driver,==,ses',
            'ses_region' => 'required_if:driver,==,ses',
            'sender_name' => 'required|string',
            'sender_email' => 'required|email',
            'email_sent_test' => 'nullable|email',
        ];
    }
}
