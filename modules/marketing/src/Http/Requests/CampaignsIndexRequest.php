<?php

namespace Modules\Marketing\Http\Requests;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Modules\Marketing\Enums\CampaignStatusEnum;

class CampaignsIndexRequest extends Request
{
    use PreventsRedirectWhenFailedTrait;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => 'nullable|string|in:' . implode(',', CampaignStatusEnum::asArray()),
            'q' => 'nullable',
            'sort' => 'nullable',
            'direction' => 'nullable',
            'page' => 'nullable|integer',
            'per_page' => 'nullable|integer',
        ];
    }

    public function attributes()
    {
        return [
            'status' => 'Campaign Status',
            'q' => 'Search query',
            'sort' => 'Sort by',
            'direction' => 'Direction',
            'page' => 'Page',
            'per_page' => 'Per page',
        ];
    }
}
