<?php
namespace Modules\Marketing\Http\Requests;

use App\Traits\PreventsRedirectWhenFailedTrait;

class SubscribersIndexRequest extends Request
{
    use PreventsRedirectWhenFailedTrait;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => 'nullable',
        ];
    }

    public function attributes()
    {
        return [
            'status' => 'Subscriber Status'
        ];
    }
}
