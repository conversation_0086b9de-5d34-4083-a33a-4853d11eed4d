<?php

namespace Modules\Marketing\Supports\Imports;

use App\Models\SystemConfig;
use App\Traits\CryptTrait;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Marketing\Enums\CampaignStatusEnum;
use Modules\Marketing\Enums\EmailSettingsStatusEnum;
use Modules\Marketing\Jobs\CheckEmailSettingJob;
use Modules\Marketing\Models\EmailSettings;
use Modules\Marketing\Supports\JsonHelper;

class EmailSettingsImport implements ToCollection, WithHeadingRow
{
    use CryptTrait;

    private function parseBeforeMapping(&$row): void
    {
        $row = $row->toArray();
        $row = array_map('trim', $row);
    }

    /**
     * @param Collection $rows
     * @throws \Exception
     */
    public function collection(Collection $rows): void
    {
        $currentUserId = currentUser()->getUserId();
        $added = [];
        $crypt = $this->crypt();
        DB::beginTransaction();
        try {
            foreach ($rows as $row) {
                if (!$row->filter()->isNotEmpty()) {
                    continue;
                }
                $this->parseBeforeMapping($row);
                $driver = 'smtp';
                $valid = true;
                $smtp_host = $row['smtp_host'];
                $smtp_port = $row['smtp_port'];
                $smtp_username = $row['smtp_username'];
                $smtp_password = $row['smtp_password'];
                $smtp_encryption = $row['smtp_encryption'];
                $delay = $row['delay'];
                $limit_per_send = $row['limit_per_send'];
                if (empty($limit_per_send)) {
                    $limit_per_send = SystemConfig::getConfig('email_marketing_limit_send_email_per_time', 500);
                }
                $sender_name = $row['sender_name'];
                $sender_email = $row['sender_email'];
                $validator = Validator::make([
                    'smtp_host' => $smtp_host,
                    'smtp_port' => $smtp_port,
                    'smtp_username' => $smtp_username,
                    'smtp_password' => $smtp_password,
                    'smtp_encryption' => $smtp_encryption,
                    'sender_name' => $sender_name,
                    'sender_email' => $sender_email,
                ], [
                    'smtp_port' => 'required|integer',
                    'smtp_username' => 'required',
                    'smtp_password' => 'required',
                    'smtp_encryption' => 'required',
                    'sender_name' => 'required',
                    'sender_email' => 'required|email',
                ]);
                if (!$validator->passes()) {
                    $valid = false;
                }
                $setting_id = 0;
                if ($valid) {
                    if (!empty($added[$smtp_username])) {
                        $valid = false;
                    } else {
                        $email_settings = EmailSettings::query()
                            ->where([
                                'seller_id' => $currentUserId,
                                'driver' => $driver,
                                'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
                            ])
                            ->get();
                        $added[$smtp_username] = false;
                        if ($email_settings->isNotEmpty()) {
                            foreach ($email_settings as $email_setting) {
                                if (empty($email_setting->settings)) {
                                    continue;
                                }
                                $settings = $crypt->decryptString($email_setting->settings);
                                $settings = JsonHelper::decode($settings, true);
                                if ($smtp_username === $settings['smtp_username']) {
                                    $added[$smtp_username] = true;
                                    $setting_id = $email_setting->id;
                                    break;
                                }
                            }
                        }
                    }
                }
                if (!$valid) {
                    continue;
                }
                $current_time = currentTime();
                $settings = JsonHelper::encode([
                    'smtp_host' => $smtp_host,
                    'smtp_port' => $smtp_port,
                    'smtp_username' => $smtp_username,
                    'smtp_password' => $smtp_password,
                    'smtp_encryption' => Str::lower($smtp_encryption),
                ]);
                if (!empty($setting_id)) {
                    $insert_data = [
                        'delay' => $delay,
                        'limit_per_send' => $limit_per_send,
                        'settings' => $crypt->encryptString($settings),
                        'last_used_at' => $current_time,
                        'hold_at' => $current_time,
                        'updated_at' => $current_time,
                        'sender_name' => $sender_name,
                        'sender_email' => $sender_email
                    ];
                } else {
                    $insert_data = [
                        'seller_id' => $currentUserId,
                        'driver' => $driver,
                        'delay' => $delay,
                        'limit_per_send' => $limit_per_send,
                        'settings' => $crypt->encryptString($settings),
                        'is_deleted' => CampaignStatusEnum::IS_NOT_DELETED,
                        'last_used_at' => $current_time,
                        'hold_at' => $current_time,
                        'deleted_at' => null,
                        'created_at' => $current_time,
                        'updated_at' => $current_time,
                        'sender_name' => $sender_name,
                        'sender_email' => $sender_email
                    ];
                }
                try {
                    if (empty($setting_id)) {
                        $setting_id = EmailSettings::query()->insertGetId($insert_data);
                    } else {
                        EmailSettings::query()->whereKey($setting_id)->update($insert_data);
                    }
                    dispatch(new CheckEmailSettingJob($currentUserId, $setting_id))->onQueue(config('marketing.config.general.queue'));
                    $added[$smtp_username] = true;
                } catch (\Exception $exception) {
                    logToDiscord('[' . currentTime() . '] - Seller import email settings failed. Error: ' . $exception->getMessage(), 'email_marketing', true);
                    continue;
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \RuntimeException($e->getMessage(), 403);
        }
        DB::commit();
    }
}
