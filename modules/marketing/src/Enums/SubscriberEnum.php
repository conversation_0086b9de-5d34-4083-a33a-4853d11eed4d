<?php
namespace Modules\Marketing\Enums;

use BenSampo\Enum\Enum;

final class SubscriberEnum extends Enum
{
    public const IS_NOT_DELETED = 0;
    public const IS_DELETED = 1;

    public const ACTION_EDIT = 'edit';
    public const ACTION_DELETE = 'delete';

    public const IS_EMAIL_SUBSCRIBED = 1;
    public const IS_EMAIL_NOT_SUBSCRIBED = 0;

    public const IS_FROM_SYSTEM = 1;
    public const IS_NOT_FROM_SYSTEM = 0;
}
