<?php
namespace Modules\Marketing\Enums;

use BenSampo\Enum\Enum;

final class SubscriberSortByAllowEnum extends Enum
{
    public const COL_NAME = 'name';
    public const COL_EMAIL = 'email';
    public const COL_PHONE = 'phone';
    public const COL_COUNTRY = 'country';
    public const COL_STATE = 'state';
    public const COL_CITY = 'city';
    public const COL_STATUS = 'status';

    public static function getDefault(): string
    {
        return 'created_at';
    }
}
