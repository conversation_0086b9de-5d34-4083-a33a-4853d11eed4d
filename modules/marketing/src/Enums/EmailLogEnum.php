<?php
namespace Modules\Marketing\Enums;

use BenSampo\Enum\Enum;

class EmailLogEnum extends Enum
{
    public const NOT_ACTIVATED = 0;
    public const ACTIVATED = 1;

    public const ACTION_SENT = 'sent';
    public const ACTION_OPENED = 'opened';
    public const ACTION_ORDERED = 'ordered';
    public const ACTION_FAILED = 'failed';
    public const ACTION_CLICKED = 'clicked';
    public const ACTION_MAX_RETRIED = 'max_retried';
}
