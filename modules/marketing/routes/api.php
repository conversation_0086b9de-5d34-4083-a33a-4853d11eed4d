<?php

use Illuminate\Support\Facades\Route;
use Modules\Marketing\Http\Controllers\MarketingController;

Route::group(['prefix' => 'seller', 'middleware' => 'auth.access.token'], function () {
    Route::get('store/{store_id}/detail', [MarketingController::class, 'getStore']);
    Route::group(['prefix' => 'email-marketing', 'middleware' => ['staff_log', 'seller_log']], function () {
        Route::get('/', [MarketingController::class, 'index']);

        Route::prefix('campaigns')->group(function () {
            Route::get('/', [MarketingController::class, 'campaigns']);
            Route::post('store', [MarketingController::class, 'storeCampaign']);
            Route::post('duplicate', [MarketingController::class, 'duplicateCampaign']);
            Route::put('resume-hold-campaigns', [MarketingController::class, 'resumeHoldCampaignAction']);
            Route::post('stop-scheduled', [MarketingController::class, 'stopCampaign']);
            Route::post('stop-sending', [MarketingController::class, 'stopSendingCampaign']);
            Route::post('cancel-hold-campaign', [MarketingController::class, 'cancelHoldCampaignAction']);
            Route::put('bulk-action', [MarketingController::class, 'bulkCampaignAction']);
            Route::get('prepare', [MarketingController::class, 'prepareCampaignData']);
            Route::get('{campaign_id}/detail', [MarketingController::class, 'getCampaignDetail']);
        });

        //Subscribers route
        Route::prefix('subscribers')->group(function () {
            Route::get('/', [MarketingController::class, 'subscribers']);
            Route::post('store', [MarketingController::class, 'storeSubscriber']);
            Route::post('import', [MarketingController::class, 'importSubscribers']);
            Route::put('bulk-action', [MarketingController::class, 'bulkAction']);
            Route::get('export', [MarketingController::class, 'exportSubscribers']);
        });

        //Email settings route
        Route::prefix('email')->group(function () {
            Route::get('settings', [MarketingController::class, 'emailSettings']);
            Route::get('{id}/detail', [MarketingController::class, 'getEmailSettingDetail']);
            Route::post('duplicate', [MarketingController::class, 'duplicateEmailSetting']);
            Route::post('save-settings', [MarketingController::class, 'saveEmailSettings']);
            Route::post('send-campaign-test', [MarketingController::class, 'sendTestEmailCampaign']);
            Route::post('send-test-email-settings', [MarketingController::class, 'sendTestEmailSettings']);
            Route::post('import', [MarketingController::class, 'importEmailSettings']);
            Route::post('change-state', [MarketingController::class, 'changeStateEmailSettings']);
            Route::put('bulk-action', [MarketingController::class, 'bulkEmailSettingsAction']);
        });

        Route::prefix('senders')->group(function () {
            // Sender routes
            Route::get('/', [MarketingController::class, 'getSenders']);
            Route::get('{id}/status', [MarketingController::class, 'getStatus']);
            Route::get('{id}', [MarketingController::class, 'getSender']);
            Route::post('/', [MarketingController::class, 'addSender']);
            Route::patch('{id}', [MarketingController::class, 'updateSender']);
            Route::delete('{id}', [MarketingController::class, 'deleteSender']);

            // Identity routes (just for staff)
            if (env('APP_ENV') === 'local') {
                Route::prefix('identity')->group(function () {
                    Route::get('/get', [MarketingController::class, 'getIdentities']);
                    Route::post('/delete', [MarketingController::class, 'deleteIdentity']);
                });
            }
        });
    });
});
