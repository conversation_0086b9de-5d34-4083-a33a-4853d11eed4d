<?php
namespace Modules\SellerAccount\Models;

use App\Models\Model;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Modules\SellerAccount\Models\UserBalance
 * @property int $id
 * @property int|null $seller_id
 * @property string $type
 * @property double|null $balance
 * @property \Illuminate\Support\Carbon $expired_at
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static Builder|UserBalance canUse()
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UserBalance query()
 */
class UserBalance extends Model
{
    use HasFactory;
    /**
     * table name
     *
     * @var string
     */
    protected $table = 'user_balance';

    protected $fillable = [
        'seller_id',
        'type',
        'balance',
        'expired_at',
        'created_at',
        'updated_at'
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'nickname', 'email', 'status', 'custom_payment')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown',
                'nickname' => null
            ]);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCanUse(Builder $query): Builder
    {
        return $query->where(function (Builder $q) {
            $q->whereNull('expired_at')->orWhere('expired_at', '>', now());
        })->where('balance', '>', 0);
    }
}
