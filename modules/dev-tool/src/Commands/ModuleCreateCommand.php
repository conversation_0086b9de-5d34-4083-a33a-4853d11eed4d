<?php
namespace Modules\DevTool\Commands;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Modules\DevTool\Commands\Abstracts\BaseCommand;

class ModuleCreateCommand extends BaseCommand {

    /**
     * The console command signature.
     *
     * @var string
     */
    protected $signature = 'module:create {name : The module name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a module in the /modules directory.';

    public function handle() {
        if (!preg_match('/^[a-z0-9\-\_]+$/i', $this->argument('name'))) {
            $this->error('Only alphabetic characters are allowed.');
            return 1;
        }

        $module = strtolower($this->argument('name'));
        $location = module_path(Str::lower($module));

        if (File::isDirectory($location)) {
            $this->error('A module named [' . $module . '] already exists.');
            return 1;
        }

        try {
            $this->publishStubs($this->getStub(), $location);
            $this->renameFiles($module, $location);
            $this->searchAndReplaceInFiles($module, $location);
            $this->line('------------------');
            $this->line('<info>The module</info> <comment>' . Str::lower($module) . '</comment> <info>was created in</info> <comment>' . $location . '</comment><info>, customize it!</info>');
            $this->line('<info>Add</info> <comment>"senprints/'. Str::lower($module) . ': "*@dev"</comment> to composer.json then run <comment>composer update</comment> to use this module!');
            $this->line('------------------');
            $this->call('cache:clear');
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
            return 0;
        }
        return 1;
    }

    /**
     * @param string $replaceText
     * @return array
     */
    public function getReplacements(string $replaceText): array {
        return [
            '{-module}'  => Str::lower($replaceText),
            '{module}'   => Str::snake(str_replace('-', '_', $replaceText)),
            '{+module}'  => Str::camel($replaceText),
            '{modules}'  => Str::plural(Str::snake(str_replace('-', '_', $replaceText))),
            '{Modules}'  => Str::ucfirst(Str::plural(Str::snake(str_replace('-', '_', $replaceText)))),
            '{-modules}' => Str::plural($replaceText),
            '{MODULE}'   => Str::upper(Str::snake(str_replace('-', '_', $replaceText))),
            '{Module}'   => Str::ucfirst(Str::camel($replaceText)),
        ];
    }

    /**
     * @return string
     */
    public function getStub(): string {
        return __DIR__ . '/../../stubs/module';
    }
}
