<?php
namespace Modules\DevTool\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ModuleMakeMigrationCommand  extends Command
{
    /**
     * The console command signature.
     *
     * @var string
     */
    protected $signature = 'module:make:migration {name : The migration name}
                                                {module : The module name}
                                                {--create= : The table to be created}
                                                {--table= : The table to migrate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make a migration file in /modules/{module-name}/database/migrations directory.';

    public function handle() {
        if (!preg_match('/^[a-z0-9\-\_]+$/i', $this->argument('name'))) {
            $this->error('Only alphabetic characters are allowed.');
            return 1;
        }

        if (!preg_match('/^[a-z0-9\-\_]+$/i', $this->argument('module'))) {
            $this->error('Only alphabetic characters are allowed.');
            return 1;
        }

        $name = strtolower($this->argument('name'));
        $module = strtolower($this->argument('module'));
        $create = $this->option('create');
        $table = $this->option('table');
        $module_path = 'modules/' . Str::lower($module) . '/database/migrations';
        $module_full_path = module_path(Str::lower($module) . '/database/migrations');
        $this->line('------------------');
        if (!File::isDirectory($module_full_path)) {
            $this->error('You can not create a migration for a non exits module.');
            $this->line('------------------');
            return 1;
        }
        $this->call('make:migration', array(
            'name' => $name,
            '--path' => $module_path,
            '--table' => $table,
            '--create' => $create,
        ));
        $this->line('<info>File path:</info> <comment>' . $module_full_path . '</comment><info>, customize it!</info>');
        $this->line('<info>Please run command</info> <comment>php artisan migrate</comment> <info>to apply your changed</info>');
        $this->line('------------------');
        return 0;
    }
}
