<?php
namespace Modules\{Module}\Providers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;

class {Module}ServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap {Name} service provider service.
     *
     * @return void
     */
    public function boot()
    {
        if(!defined('{MODULE}_MODULE_PATH')) {
            define('{MODULE}_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->autoload({MODULE}_MODULE_PATH . '/helpers');
        $this->loadMigrationsFrom({MODULE}_MODULE_PATH . '/database/migrations');
        $this->app->booted(function () {
            $this->loadViewsFrom({MODULE}_MODULE_PATH . '/resources/views', '{name}');
            $this->registerConfigs(['{-name}']);
            $this->registerRoutes();
        });
    }

    /**
     * Register the {name}'s configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = {MODULE}_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, '{.name}.config.' . $fileName);
        }
    }

    /**
     * Register the {name}'s routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = {MODULE}_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }

    /**
     * @param string $path
     * @param array $ignoreFiles
     * @return array
     */
    public function scanFolder($path, array $ignoreFiles = [])
    {
        try {
            if (File::isDirectory($path)) {
                $data = array_diff(scandir($path), array_merge(['.', '..', '.DS_Store'], $ignoreFiles));
                natsort($data);
                return array_values($data);
            }
            return [];
        } catch (\Exception $exception) {
            return [];
        }
    }

    /**
     * Load helpers from a directory
     * @param string $directory
     */
    public function autoload(string $directory): void
    {
        $helpers = File::glob($directory . '/*.php');
        if(empty($helpers)) {
            return;
        }
        foreach ($helpers as $helper) {
            File::requireOnce($helper);
        }
    }
}
