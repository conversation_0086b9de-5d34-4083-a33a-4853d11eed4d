<?php

namespace Modules\SellerAPI\Swagger;

/**
 * @OA\Get(
 *     path="/seller-api/me",
 *     summary="Get information about the authenticated user",
 *     description="Retrieve the details of the authenticated user.",
 *     tags={"Me"},
 *     security={{ "bearerAuth": {} }},
 *     @OA\Response(
 *         response=200,
 *         description="User information retrieved successfully",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(
 *                 property="user",
 *                 type="object",
 *                 @OA\Property(property="id", type="integer", example=1),
 *                 @OA\Property(property="email", type="string", example="<EMAIL>"),
 *                 @OA\Property(property="name", type="string", example="John Doe")
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Unauthorized",
 *         @OA\JsonContent(
 *              @OA\Property(property="success", type="boolean", example=false),
 *              @OA\Property(property="message", type="string", example="Unauthorized")
 *         )
 *     )
 * )
 */

class MeSwagger
{
    //
}
