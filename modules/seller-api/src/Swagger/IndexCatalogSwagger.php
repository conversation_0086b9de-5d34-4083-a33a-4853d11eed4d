<?php

namespace Modules\SellerAPI\Swagger;

/**
 * @OA\Get(
 *     path="/seller-api/catalog",
 *     summary="Get the list of catalogs",
 *     description="Retrieve the list of catalogs available in the system.",
 *     tags={"Catalog"},
 *     security={{ "bearerAuth": {} }},
 *     @OA\Response(
 *         response=200,
 *         description="Catalog list retrieved successfully",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(
 *                 property="data",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(property="id", type="integer", example=1),
 *                     @OA\Property(property="name", type="string", example="Category Name"),
 *                     @OA\Property(property="slug", type="string", example="Category Slug"),
 *                     @OA\Property(
 *                         property="products",
 *                         type="array",
 *                         @OA\Items(
 *                             type="object",
 *                             @OA\Property(property="id", type="integer", example=101),
 *                             @OA\Property(property="name", type="string", example="Product Name"),
 *                             @OA\Property(property="sku", type="string", example="PROD123"),
 *                             @OA\Property(property="thumb_url", type="string", example="https://example.com/thumb.jpg"),
 *                             @OA\Property(property="base_cost", type="number", format="float", example=10.5),
 *                             @OA\Property(property="suggested_price", type="number", format="float", example=15.0),
 *                             @OA\Property(
 *                                 property="options",
 *                                 type="object",
 *                                 example={"color": {"white", "black", "sport grey", "wild lime", "kelly green", "charcoal", "classic orange"}, "size": {"s", "m", "l", "xl", "2xl", "3xl", "4xl", "5xl"}}
 *                             ),
 *                             @OA\Property(
 *                                 property="print_spaces",
 *                                 type="array",
 *                                 @OA\Items(
 *                                     type="object",
 *                                     @OA\Property(property="name", type="string", example="front"),
 *                                     @OA\Property(property="width", type="integer", example=3000),
 *                                     @OA\Property(property="height", type="integer", example=3000),
 *                                     @OA\Property(property="price", type="number", format="float", example=1, nullable=true)
 *                                 ),
 *                                 example={"front": {"name": "front", "width": 3000, "height": 3000, "price": 1}, "back": {"name": "back", "width": 4050, "height": 4650, "price": 2}}
 *                             )
 *                         )
 *                     )
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Unauthorized",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=false),
 *             @OA\Property(property="message", type="string", example="Unauthorized")
 *         )
 *     )
 * )
 */

class IndexCatalogSwagger
{
    //
}
