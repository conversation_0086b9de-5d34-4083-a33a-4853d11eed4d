<?php

namespace Modules\SellerAPI\Swagger;

/**
 * @OA\Put(
 *     path="/seller-api/orders/{id}",
 *     summary="Update an order",
 *     description="Only send fields that need updating.",
 *     tags={"Orders"},
 *     security={{ "bearerAuth": {} }},
 *     @OA\Parameter(
 *         name="id",
 *         in="path",
 *         required=true,
 *         description="The unique ID of the order",
 *         @OA\Schema(type="integer", example=1)
 *     ),
 *     @OA\RequestBody(
 *          required=true,
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  type="object",
 *                  @OA\Property(
 *                      property="order_number",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="store_name",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="store_domain",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="customer_name",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="customer_email",
 *                      type="string",
 *                      format="email"
 *                  ),
 *                  @OA\Property(
 *                      property="customer_phone",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="address",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="address_2",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="house_number",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="mailbox_number",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="city",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="state",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="postcode",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="country",
 *                      enum={"US", "CA", "GB", "AU", "DE", "FR", "IT", "ES", "JP"},
 *                      type="string",
 *                      description="Country code (these options are examples, the actual list of countries may vary)",
 *                  ),
 *                  @OA\Property(
 *                      property="shipping_method",
 *                      enum=L5_SWAGGER_CONST_SHIPPING_METHOD,
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="order_note",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="shipping_label",
 *                      type="string",
 *                      format="url",
 *                      nullable=true,
 *                      description="Shipping label URL (required if the order is platform)"
 *                  ),
 *                  @OA\Property(
 *                      property="products",
 *                      type="array",
 *                      @OA\Items(
 *                          type="object",
 *                          required={"quantity", "template_id", "options"},
 *                          @OA\Property(
 *                              property="id",
 *                              type="integer",
 *                              nullable=true,
 *                              description="Order Product ID (nullable, fill only when updating an existing product)",
 *                          ),
 *                          @OA\Property(
 *                              property="campaign_title",
 *                              type="string"
 *                          ),
 *                          @OA\Property(
 *                              property="quantity",
 *                              type="integer"
 *                          ),
 *                          @OA\Property(
 *                              property="template_id",
 *                              type="integer"
 *                          ),
 *                          @OA\Property(
 *                              property="options",
 *                              type="object",
 *                              description="Product options as key-value pairs",
 *                              @OA\AdditionalProperties(type="string")
 *                          ),
 *                          @OA\Property(
 *                              property="custom_options",
 *                              type="array",
 *                              nullable=true,
 *                              description="Custom options for the product",
 *                              @OA\Items(
 *                                  type="object",
 *                                  @OA\Property(property="type", type="string", description="Type of the option"),
 *                                  @OA\Property(property="label", type="string", description="Label of the option"),
 *                                  @OA\Property(property="value", type="string", description="Value of the option")
 *                              )
 *                          ),
 *                          @OA\Property(
 *                              property="barcode",
 *                              type="string",
 *                              nullable=true,
 *                              description="Barcode (required only when fulfill_fba_by is amazon)"
 *                          ),
 *                          @OA\Property(
 *                              property="fulfill_fba_by",
 *                              type="string",
 *                              nullable=true,
 *                              enum=L5_SWAGGER_CONST_FULFILL_FBA_BY,
 *                              description="Fulfillment type (required only when order is platform)"
 *                          ),
 *                          @OA\Property(
 *                              property="design_by_sen",
 *                              type="boolean",
 *                              nullable=true,
 *                              description="If true, the design will be process by SenPrints",
 *                          ),
 *                          @OA\Property(
 *                              property="designs",
 *                              type="array",
 *                              @OA\Items(
 *                                  type="object",
 *                                  @OA\Property(
 *                                      property="id",
 *                                      type="integer",
 *                                      description="ID of the design file",
 *                                  ),
 *                                  @OA\Property(
 *                                      property="file_url",
 *                                      type="string",
 *                                      format="url"
 *                                  ),
 *                                  @OA\Property(
 *                                      property="print_space",
 *                                      type="string",
 *                                      enum=L5_SWAGGER_CONST_PRINT_SPACE,
 *                                  )
 *                              )
 *                          ),
 *                          @OA\Property(
 *                              property="mockups",
 *                              type="array",
 *                              @OA\Items(
 *                                  type="object",
 *                                  @OA\Property(
 *                                      property="id",
 *                                      type="integer",
 *                                      description="ID of the mockup file",
 *                                  ),
 *                                  @OA\Property(
 *                                      property="file_url",
 *                                      type="string",
 *                                      format="url"
 *                                  ),
 *                                  @OA\Property(
 *                                      property="print_space",
 *                                      type="string",
 *                                      enum=L5_SWAGGER_CONST_PRINT_SPACE,
 *                                  )
 *                              )
 *                          )
 *                      )
 *                  )
 *              ),
 *              @OA\Examples(
 *                  example="Example1",
 *                  summary="Example updating order fulfillment",
 *                  value={
 *                      "order_number": "TEST-123456",
 *                      "store_name": "ABC Store",
 *                      "store_domain": "abcstore.com",
 *                      "customer_name": "John Doe",
 *                      "customer_email": "<EMAIL>",
 *                      "customer_phone": "+1234567890",
 *                      "address": "123 ABC Street",
 *                      "address_2": "Apt 4B",
 *                      "house_number": "12",
 *                      "mailbox_number": "34",
 *                      "city": "New York",
 *                      "state": "NY",
 *                      "postcode": "10001",
 *                      "country": "US",
 *                      "shipping_method": "standard",
 *                      "order_note": "Please deliver before weekend.",
 *                      "products": {
 *                          {
 *                              "id": 1,
 *                              "campaign_title": "T-Shirt Campaign",
 *                              "quantity": 2,
 *                              "template_id": 565,
 *                              "options": {
 *                                  "color": "red",
 *                                  "size": "m"
 *                              },
 *                              "designs": {
 *                                  {
 *                                      "id": 1,
 *                                      "file_url": "https://example.com/design1.png",
 *                                      "print_space": "front"
 *                                  }
 *                              },
 *                              "mockups": {
 *                                  {
 *                                      "id": 10,
 *                                      "file_url": "https://example.com/mockup1.png",
 *                                      "print_space": "front"
 *                                  }
 *                              }
 *                          }
 *                      }
 *                  }
 *              ),
 *              @OA\Examples(
 *                  example="Example2",
 *                  summary="Example updating order platform",
 *                  value={
 *                      "order_number": "TEST-789012",
 *                      "store_name": "XYZ Store",
 *                      "store_domain": "xyzstore.com",
 *                      "customer_name": "Jane Doe",
 *                      "customer_email": "<EMAIL>",
 *                      "customer_phone": "+1987654321",
 *                      "address": "456 XYZ Avenue",
 *                      "address_2": "Suite 21",
 *                      "house_number": "45",
 *                      "mailbox_number": "67",
 *                      "city": "Los Angeles",
 *                      "state": "CA",
 *                      "postcode": "90001",
 *                      "country": "US",
 *                      "order_note": "Leave at the front door if not home.",
 *                      "shipping_label": "https://example.com/shipping_label.pdf",
 *                      "products": {
 *                          {
 *                              "id": 2,
 *                              "campaign_title": "Hoodie Campaign",
 *                              "quantity": 30,
 *                              "template_id": 562,
 *                              "options": {
 *                                  "color": "black",
 *                                  "size": "l"
 *                              },
 *                              "barcode": "123456789012",
 *                              "fulfill_fba_by": "amazon",
 *                              "design_by_sen": false,
 *                              "custom_options": {
 *                                  {
 *                                      "type": "Gift Wrap",
 *                                      "label": "Wrap it nicely",
 *                                      "value": "Yes"
 *                                  }
 *                              },
 *                              "designs": {
 *                                  {
 *                                      "id": 2,
 *                                      "file_url": "https://example.com/design2.png",
 *                                      "print_space": "back"
 *                                  }
 *                              },
 *                              "mockups": {
 *                                  {
 *                                      "id": 3,
 *                                      "file_url": "https://example.com/mockup2.png",
 *                                      "print_space": "back"
 *                                  }
 *                              }
 *                          }
 *                      }
 *                  }
 *              )
 *          )
 *      ),
 *     @OA\Response(
 *         response=200,
 *         description="Order updated successfully",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Order updated successfully"),
 *             @OA\Property(property="data", ref="#/components/schemas/Order")
 *         )
 *     ),
 *     @OA\Response(
 *         response=400,
 *         description="Invalid data provided",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=false),
 *             @OA\Property(property="message", type="string", example="Validation error"),
 *             @OA\Property(property="errors", type="object")
 *         )
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Order not found",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=false),
 *             @OA\Property(property="message", type="string", example="Order not found")
 *         )
 *     ),
 *     @OA\Response(
 *         response=403,
 *         description="Unauthorized access",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=false),
 *             @OA\Property(property="message", type="string", example="Forbidden access")
 *         )
 *     )
 * )
 */
class UpdateOrderSwagger
{
    // This class is intentionally left empty.
}
