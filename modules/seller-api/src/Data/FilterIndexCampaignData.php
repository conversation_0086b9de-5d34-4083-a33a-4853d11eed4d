<?php

namespace Modules\SellerAPI\Data;

use App\Enums\SortListingProductEnum;
use Spatie\LaravelData\Data;

class FilterIndexCampaignData extends Data
{
    public function __construct(
        public ?string $s = null,
        public ?string $sort = null, // SortListingProductEnum
        public int $limit = 24,
        public int $page = 1,
        public bool $exclude_personalized = true,
        public ?int $category_id = null,
        public ?int $collection_id = null,
        public ?float $min_price = null,
        public ?float $max_price = null,
        public ?string $color = null,
    ) {
    }
}


