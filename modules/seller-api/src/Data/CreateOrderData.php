<?php

namespace Modules\SellerAPI\Data;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\DataCollection;

class CreateOrderData extends Data
{
    public function __construct(
        public string $order_number_2,
        public string $ip_address,

        // seller
        public int $seller_id,
        public ?int $ref_id = null,
        public ?string $store_name = null,
        public ?string $store_domain = null,

        // customer
        public string $customer_name,
        public ?string $customer_email = null,
        public ?string $customer_phone = null,
        public string $address,
        public ?string $address_2 = null,
        public ?string $house_number = null,
        public ?string $mailbox_number = null,
        public ?string $city = null,
        public ?string $state = null,
        public ?string $postcode = null,
        public string $country,
        public ?string $shipping_method = null,
        public ?string $order_note = null,

        public ?string $shipping_label = null,

        /** @var DataCollection<SaveOrderProductData> $products */
        public DataCollection $products,
    ) {
    }
}
