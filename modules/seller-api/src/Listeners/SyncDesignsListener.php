<?php

namespace Modules\SellerAPI\Listeners;

use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\PrintSpaceEnum;
use App\Models\File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Modules\SellerAPI\Events\OrderProductProcessingDesignsEvent;

class SyncDesignsListener implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, Queueable;

    public function handle(OrderProductProcessingDesignsEvent $event): void
    {
        $orderProduct = $event->getOrderProduct();

        if (!$orderProduct) {
            Log::error(__CLASS__ . ' order product not found', [
                'orderProductId' => $event->orderProductId,
            ]);
            return;
        }

        $batchFileData = [];

        foreach ($event->designs as $design) {
            if (!$design->file_url) {
                continue;
            }

            $printSpace = $design->print_space;
            if ($printSpace === PrintSpaceEnum::DEFAULT) {
                $printSpace = $orderProduct->getDefaultPrintSpace();
            }

            $data = [
                'order_id'         => $orderProduct->order_id,
                'type'             => FileTypeEnum::DESIGN,
                'file_url'         => $design->file_url,
                'file_url_2'       => null,
                'order_product_id' => $orderProduct->id,
                'seller_id'        => $orderProduct->seller_id,
                'option'           => FileRenderType::PRINT,
                'print_space'      => $printSpace,
                'status'           => FileStatusEnum::ACTIVE,
            ];

            $batchFileData[] = $data;
        }

        File::query()
            ->where([
                'order_product_id' => $orderProduct->id,
                'seller_id'        => $orderProduct->seller_id,
                'type'             => FileTypeEnum::DESIGN,
            ])
            ->update([
                'status' => FileStatusEnum::INACTIVE,
            ]);
        if ($batchFileData) {
            File::query()->insert($batchFileData);
        }
    }
}
