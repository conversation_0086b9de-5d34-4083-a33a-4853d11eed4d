<?php

namespace Modules\SellerAPI\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Store;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Request as RequestFacade;
use Modules\SellerAPI\Data\IndexCampaignData;
use Modules\SellerAPI\Data\FilterIndexCampaignData;
use Modules\SellerAPI\Http\Requests\Campaign\FilterIndexCampaignRequest;
use Modules\SellerAPI\Http\Requests\Campaign\IndexCampaignRequest;

class CampaignController extends Controller
{
    use ApiResponse;
    use ElasticClient;

    public function index(IndexCampaignRequest $request): JsonResponse
    {
        $data = IndexCampaignData::from($request->validated());
        $store = Store::find(Store::SENPRINTS_STORE_ID);
        if (empty($store)) {
            return $this->errorResponse('Store not found', 404);
        }

        $response = $this->getListingCampaigns($data, $store);

        $response->data = array_map(function ($item) {
            $item['thumb_url'] = getFullPath($item['thumb_url']);
            
            return $item;
        }, $response->data);

        $uri = preg_replace("/page=\d+/", '', RequestFacade::getRequestUri());
        $result = new LengthAwarePaginator(
            $response->data,
            $response->total,
            $data->per_page,
            $data->page,
            [
                'path' => $uri,
            ]
        );

        return response()->json($result);
    }

    public function filter(FilterIndexCampaignRequest $request): JsonResponse
    {
        $data = FilterIndexCampaignData::from($request->validated());
        $store = Store::find(Store::SENPRINTS_STORE_ID);
        if (empty($store)) {
            return $this->errorResponse('Store not found', 404);
        }
        $filter = array_filter($data->toArray(), function ($value) {
            return $value !== null;
        });
        $response = $this->elasticGetFilterProduct(
            arrFilter: $filter,
            store: $store
        );

        return response()->json($response);
    }
}
