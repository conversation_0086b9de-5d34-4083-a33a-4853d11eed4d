<?php

namespace Modules\SellerAPI\Http\Requests\Campaign;

use App\Enums\SortListingProductEnum;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexCampaignRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function rules(): array
    {
        return [
            's' => [
                'nullable',
                'string',
                'max:255',
            ],
            'sort' => [
                'nullable',
                'string',
                Rule::in(SortListingProductEnum::getValues()),
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:100',
            ],
            'page' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'collection_id' => [
                'nullable',
                'integer',
            ],
            'category_id' => [
                'nullable',
                'integer',
            ],
            'template_ids' => [
                'nullable',
                'array',
            ],
            'min_price' => [
                'nullable',
                'numeric',
            ],
            'max_price' => [
                'nullable',
                'numeric',
            ],
            'color' => [
                'nullable',
                'string',
            ],
        ];
    }
}
