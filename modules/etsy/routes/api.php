<?php

use Illuminate\Support\Facades\Route;
use Modules\Etsy\Http\Controllers\AuthController;
use Modules\Etsy\Http\Controllers\HomeController;

Route::middleware([])
    ->prefix('/api/v1/etsy')
    ->as('api.v1.etsy.')
    ->group(function() {
        Route::get('/', [HomeController::class, 'index'])->name('index');
        Route::get('/ping', [HomeController::class, 'ping'])->name('ping');
        Route::get('/oauth/url', [AuthController::class, 'authUrl'])->name('oauth.url');
        Route::get('/oauth/callback', [AuthController::class, 'authCallback'])->name('oauth.callback');
    });
