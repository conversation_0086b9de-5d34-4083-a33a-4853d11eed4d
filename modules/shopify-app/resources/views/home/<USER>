<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <base target="_top">
    <title>Redirecting...</title>
    <script src="https://unpkg.com/@shopify/app-bridge@3"></script>
    <script src="https://unpkg.com/@shopify/app-bridge-utils@3"></script>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            let AppBridge = window['app-bridge'];
            let createApp = AppBridge.default;
            let Redirect = AppBridge.actions.Redirect;
            const app = createApp({
                apiKey: "{{$apiKey}}",
                shopOrigin: "{{$shop}}",
                host: new URLSearchParams(window.location.search).get("host"),
            });
            const redirect = Redirect.create(app);
            redirect.dispatch(Redirect.Action.REMOTE, '{{$redirect}}');
        });
    </script>
</head>
<body>
</body>
</html>
