<?php
namespace Modules\ShopifyApp\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\ShopifyApp\Events\CopyProductFilesEvent;
use Modules\ShopifyApp\Listeners\CopyProductFilesListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        CopyProductFilesEvent::class => [
            CopyProductFilesListener::class,
        ],
    ];

    /**
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
