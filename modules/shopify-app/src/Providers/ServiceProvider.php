<?php
namespace Modules\ShopifyApp\Providers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider as BaseServiceProvider;

class ServiceProvider extends BaseServiceProvider
{
    /**
     * @param string $path
     * @param array $ignoreFiles
     * @return array
     */
    public function scanFolder($path, array $ignoreFiles = [])
    {
        try {
            if (File::isDirectory($path)) {
                $data = array_diff(scandir($path), array_merge(['.', '..', '.DS_Store'], $ignoreFiles));
                natsort($data);
                return array_values($data);
            }
            return [];
        } catch (\Exception $exception) {
            return [];
        }
    }

    /**
     * Load helpers from a directory
     * @param string $directory
     */
    public function autoload(string $directory): void
    {
        $helpers = File::glob($directory . '/*.php');
        if(empty($helpers)) {
            return;
        }
        foreach ($helpers as $helper) {
            File::requireOnce($helper);
        }
    }
}
