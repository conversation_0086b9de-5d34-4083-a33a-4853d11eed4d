<?php
namespace Modules\ShopifyApp\Providers;
class CommandServiceProvider extends ServiceProvider
{
    public function boot()
    {
        if ($this->app->runningInConsole()) {
            $commandDir = dirname(__DIR__) . '/Commands';
            $commands = scan_folder($commandDir);
            $_commands = array();
            foreach ($commands as $command) {
                if(file_exists($commandDir . '/' . $command)) {
                    $command = basename($command, ".php");
                    $_commands[] = "Modules\\ShopifyApp\\Commands\\{$command}";
                }
            }
            $this->commands($_commands);
        }
    }
}
