<?php
namespace Modules\ShopifyApp\Supports;

use Illuminate\Support\Facades\Config;
use Modules\ShopifyApp\Models\ShopifySession;
use Shopify\Auth\Session;
use Shopify\Auth\SessionStorage as ShopifySessionStorage;

class SessionStorage implements ShopifySessionStorage
{
    /**
     * @param string $sessionId
     * @return Session|null
     */
    public function loadSession(string $sessionId): ?Session
    {
        $dbSession = ShopifySession::query()->where('session_id', $sessionId)->first();
        if (!$dbSession) {
            return null;
        }
        return $this->setSessionFromDatabase($dbSession);
    }

    /**
     * @param Session $session
     * @return bool
     */
    public function storeSession(Session $session): bool
    {
        $dbSession = ShopifySession::query()->where('session_id', $session->getId())->first();
        if (!$dbSession) {
            $dbSession = new ShopifySession();
        }
        $dbSession->session_id = $session->getId();
        $dbSession->shop = $session->getShop();
        $dbSession->state = $session->getState();
        $dbSession->access_token = $session->getAccessToken();
        $dbSession->scope = $session->getScope();
        if (!empty($session->getSellerId())) {
            $dbSession->seller_id = $session->getSellerId();
        }
        if (!empty($session->getStoreId())) {
            $dbSession->store_id = $session->getStoreId();
        }
        try {
            return $dbSession->save();
        } catch (\Exception $err) {
            logException($err, 'storeSession', 'shopify');
            return false;
        }
    }

    /**
     * @param string $sessionId
     * @return bool
     */
    public function deleteSession(string $sessionId): bool
    {
        return ShopifySession::query()->where('session_id', $sessionId)->delete() === 1;
    }

    /**
     * @param int $storeId
     * @return Session|null
     */
    public function loadSessionByStoreId(int $storeId): ?Session
    {
        $dbSession = ShopifySession::query()->where('store_id', $storeId)->first();
        if (!$dbSession) {
            return null;
        }
        return $this->setSessionFromDatabase($dbSession);
    }

    /**
     * @param ShopifySession $dbSession
     * @return Session
     */
    public function setSessionFromDatabase(ShopifySession $dbSession)
    {
        $session = new Session(
            $dbSession->session_id,
            $dbSession->shop,
            Config::get('shopify.app.config.general.online_token', false),
            $dbSession->state
        );
        if ($dbSession->access_token) {
            $session->setAccessToken($dbSession->access_token);
        }
        if ($dbSession->scope) {
            $session->setScope($dbSession->scope);
        }
        if ($dbSession->seller_id) {
            $session->setSellerId($dbSession->seller_id);
        }
        if ($dbSession->store_id) {
            $session->setStoreId($dbSession->store_id);
        }
        return $session;
    }
}
