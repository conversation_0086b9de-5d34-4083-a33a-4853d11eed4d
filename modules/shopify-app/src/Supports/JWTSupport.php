<?php
namespace Modules\ShopifyApp\Supports;

use Exception;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\SignatureInvalidException;

class JWTSupport
{
    public static $leeway = 10;
    /**
     * Generate user access token
     *
     * @param $sellerId
     * @param int $expire
     *
     * @return string
     */
    public static function encode($sellerId, $expire = 86400)
    {
        $payload = array(
            'seller_id' => $sellerId,
            'iss' => env('APP_URL'),
            'sub' => $sellerId,
            'iat' => now()->timestamp,
            'exp' => now()->addSeconds($expire)->timestamp
        );
        return JWT::encode($payload, env('JWT_SECRET'), 'HS256', 'HS256');
    }

    /**
     * decode token
     *
     * @param  $token
     * @return mixed|null
     * @throws Exception
     */
    public static function decode($token)
    {
        $jwtSecret = env('JWT_SECRET');
        try {
            JWT::$leeway = self::$leeway;
            return JWT::decode($token, new Key($jwtSecret, 'HS256'));
        } catch (\InvalidArgumentException | \UnexpectedValueException | SignatureInvalidException | BeforeValidException | ExpiredException $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }
}
