<?php
namespace Modules\ShopifyApp\Listeners;

use App\Enums\FileTypeEnum;
use App\Enums\OrderStatus;
use App\Enums\StorageDisksEnum;
use App\Jobs\ValidateSellerFulfillOrder;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\ShopifyApp\Events\CopyProductFilesEvent;
use Modules\TiktokShop\Services\TiktokShopService;

class CopyProductFilesListener implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param CopyProductFilesEvent $event
     * @return bool
     */
    public function handle(CopyProductFilesEvent $event)
    {
        $order_id = $event->order_id;
        $order_product_id = $event->order_product_id;
        $files = $event->files;
        $isCopyMockup = $event->isCopyMockup;
        try {
            $order = Order::query()->whereKey($order_id)->first();
            $seller = User::query()->find($order->seller_id);
            File::query()
                ->onSellerConnection($seller)
                ->where('order_id', $order_id)->where('order_product_id', $order_product_id)->delete();
            $movedFiles = collect([]);
            $s3Storage = Storage::disk(StorageDisksEnum::DEFAULT);
            $orderProduct = OrderProduct::query()->firstWhere([
                'order_id' => $order_id,
                'id' => $order_product_id,
            ]);
            $update_product_thumb = false;
            foreach ($files as $file) {
                if (!$isCopyMockup && $file->type === FileTypeEnum::IMAGE) { // Khong copy mockup
                    continue;
                }
                $newFile = File::query()->create($file->replicate()->fill([
                    'order_id' => $order_id,
                    'order_product_id' => $order_product_id,
                ])->toArray());
                $file_url = $newFile->file_url_2 ?? $newFile->file_url;
                if(!Str::startsWith($file_url, 'http')) {
                    $oldFilePath = $newFile->file_url_2;
                    $newFilePath = preg_replace("#o/(\d+)/#", "o/{$order_id}/{$order_product_id}/", $oldFilePath);
                    if ($newFilePath && $movedFiles->contains($newFilePath) === false) {
                        if(!$s3Storage->exists($newFilePath)) {
                            $isSuccess = $s3Storage->copy($oldFilePath, $newFilePath);
                            if (!$isSuccess) {
                                logToDiscordNow("[Clone Product Files] Copy from {$oldFilePath} to {$newFilePath} failed.");
                                continue;
                            }
                        }
                        if(empty($newFile->file_url_2) || $newFile->file_url_2 === $newFile->file_url) {
                            $newFile->file_url = $newFilePath;
                        }
                        $newFile->file_url_2 = $newFilePath;
                        $movedFiles->push($newFilePath);
                        if (!$update_product_thumb && $orderProduct && !empty($newFilePath)) {
                            $orderProduct->update(['thumb_url' => $newFilePath]);
                            $update_product_thumb = true;
                        }
                    }
                }
                $success = $newFile->save();
                TiktokShopService::logToDiscord("Clone success - file_id - file_url : {$success} - {$newFile->id} - {$newFile->file_url_2}");
            }
            if ($order && $order->status === OrderStatus::PENDING) {
                $order->status = OrderStatus::DRAFT;
                $order->save();
            }
            ValidateSellerFulfillOrder::dispatch($order);
            return true;
        } catch (\Throwable $e) {
            $string_files = implode(',', $files->pluck('id')->toArray());
            logToDiscord("CopyProductFilesListener: Order Id: {$order_id}, Order Product Id: {$order_product_id}, Design Ids: {$string_files} -> Error: " . $e->getMessage());
            return false;
        }
    }
}
