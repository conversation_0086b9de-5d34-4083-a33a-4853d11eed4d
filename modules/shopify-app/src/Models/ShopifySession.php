<?php
namespace Modules\ShopifyApp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Modules\ShopifyApp\Models\ShopifySession
 *
 * @property int $id
 * @property string $session_id
 * @property string $shop
 * @property string $state
 * @property string $scope
 * @property string $access_token
 * @property int $seller_id
 * @property int $store_id
 * @property string $webhooks
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySession query()
 * @mixin \Eloquent
 */
class ShopifySession extends Model
{
    use HasFactory;

    protected $table = 'shopify_sessions';
}
