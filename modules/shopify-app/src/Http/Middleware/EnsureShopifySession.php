<?php
namespace Modules\ShopifyApp\Http\Middleware;

use App\Enums\UserStatusEnum;
use App\Models\User;
use App\Models\User as Seller;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\ShopifyApp\Models\ShopifySession;
use Shopify\Clients\Graphql;
use Shopify\Context;
use Shopify\Utils;

class EnsureShopifySession
{
    public const REDIRECT_HEADER = 'X-Shopify-API-Request-Failure-Reauthorize';
    public const REDIRECT_URL_HEADER = 'X-Shopify-API-Request-Failure-Reauthorize-Url';

    public const TEST_GRAPHQL_QUERY = <<<QUERY
    {
        shop {
            id,
            name
        }
    }
    QUERY;

    /**
     * Checks if there is currently an active Shopify session.
     *
     * @param Request $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $shop = Utils::sanitizeShopDomain($request->query('shop', ''));
        $session = Utils::loadCurrentSession($request->header(), $request->cookie(), config('shopify.app.config.general.online_token', false));

        if ($session && $shop && $session->getShop() !== $shop) {
            // This request is for a different shop. Go straight to login
            return redirect("/spf/auth/login?shop=$shop");
        }

        if ($session && $session->isValid()) {
            // If the session is valid, check if it's actually active by making a very simple request, and proceed
            $client = new Graphql($session->getShop(), $session->getAccessToken());
            $response = $client->query(self::TEST_GRAPHQL_QUERY);

            if ($response->getStatusCode() === 200) {
                $request->attributes->set('shopifyShop', $response->getBody());
                $request->attributes->set('shopifySession', $session);
                $shopify_session = ShopifySession::query()->firstWhere('shop', $shop);
                if($shopify_session !== null) {
                    $seller = Seller::query()->firstWhere('id', $shopify_session->seller_id);
                    if ($seller !== null && !in_array(Str::lower($seller->status), [UserStatusEnum::HARD_BLOCKED, UserStatusEnum::SOFT_BLOCKED], true)) {
                        $request->attributes->set('seller', $seller);
                        $request->attributes->set('isStoreConnected', !empty($shopify_session->seller_id) && !empty($shopify_session->store_id) ? 1 : 0);
                        $request->session()->put('seller', $seller);
                    }
                }
                return $next($request);
            }
        }

        if ($request->ajax()) {
            // If there is no shop in the URL, we may be able to grab the shop in the session or authentication header
            if (!$shop) {
                if ($session) {
                    $shop = $session->getShop();
                } else if (Context::$IS_EMBEDDED_APP) {
                    $authHeader = $request->header('Authorization', '');
                    if (preg_match('/Bearer (.*)/', $authHeader, $matches) !== false) {
                        $payload = Utils::decodeSessionToken($matches[1]);
                        $shop = parse_url($payload['dest'], PHP_URL_HOST);
                    }
                }
                $shopify_session = ShopifySession::query()->firstWhere('shop', $shop);
                if($shopify_session !== null) {
                    $seller = User::query()->firstWhere('id', $shopify_session->seller_id);
                    if ($seller !== null && !in_array(Str::lower($seller->status), [UserStatusEnum::HARD_BLOCKED, UserStatusEnum::SOFT_BLOCKED], true)) {
                        $request->attributes->set('seller', $seller);
                        $request->attributes->set('isStoreConnected', !empty($shopify_session->seller_id) && !empty($shopify_session->store_id) ? 1 : 0);
                        $request->session()->put('seller', $seller);
                        return $next($request);
                    }
                }
            }

            return response('', 401, [
                self::REDIRECT_HEADER => '1',
                self::REDIRECT_URL_HEADER => "/spf/auth/login?shop=$shop",
            ]);
        }
        return redirect("/spf/auth/login?shop=$shop");
    }
}
