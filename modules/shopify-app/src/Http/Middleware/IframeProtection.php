<?php
namespace Modules\ShopifyApp\Http\Middleware;

use Shopify\Clients\HttpHeaders;
use Shopify\Exception\CookieNotFoundException;
use Shopify\Exception\MissingArgumentException;
use Shopify\Utils;

class IframeProtection {
    /**
     * @throws CookieNotFoundException
     * @throws MissingArgumentException
     */
    public function handle($request, \Closure $next) {
        $rawHeaders = $request->header();
        $headers = new HttpHeaders($rawHeaders);
        $shop = $headers->get(HttpHeaders::X_SHOPIFY_DOMAIN);
        if(empty($shop)) {
            $rawHeaders = $request->header();
            $headers = new HttpHeaders($rawHeaders);
            if ($headers->has('authorization')) {
                $session = Utils::loadCurrentSession($rawHeaders, $request->cookie(), config('shopify.app.config.general.online_token', false));
                if($session) {
                    $shop = $session->getShop();
                }
            }
        }
        if(empty($shop)) {
            return $next($request);
        }
        $headers = [
            "Content-Security-Policy" => "frame-ancestors https://$shop https://admin.shopify.com",
            "Strict-Transport-Security" => "max-age=63072000; includeSubDomains; preload",
            "X-Content-Type-Options" => "nosniff",
            "Referrer-Policy" => "same-origin",
        ];
        $response = $next($request);
        if(method_exists($response, 'header')) {
            foreach($headers as $key => $value) {
                $response->header($key, $value);
            }
            return $response;
        }
        foreach($headers as $key => $value) {
            $response->headers->set($key, $value);
        }
        return $response;
    }
}
