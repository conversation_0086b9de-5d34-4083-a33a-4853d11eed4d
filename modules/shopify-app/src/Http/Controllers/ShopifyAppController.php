<?php
namespace Modules\ShopifyApp\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\ShopifyApp\Models\ShopifySession;
use Modules\ShopifyApp\Services\ShopifyAppService;
use RuntimeException;
use Shopify\Clients\HttpHeaders;
use Shopify\Context;
use Shopify\Webhooks\Registry;

class ShopifyAppController extends Controller {
    use ApiResponse;

    /**
     * @param $headers
     * @param $rawBody
     * @param $session
     * @param $token
     */
    private function validateRequest($headers, $rawBody, $session, $token) {
        $shopify_config = config('shopify.app.config.general');
        if(empty($shopify_config['enable_webhook'])) {
            logToDiscord("Shopify webhook is not enable for running.", 'shopify');
            throw new RuntimeException("Shopify webhook is not enable for running.", 403);
        }
        unset($shopify_config['api_secret']);
        if (empty($rawBody)) {
            logToDiscord("No body was received when processing webhook", 'shopify');
            throw new RuntimeException("No body was received when processing webhook", 400);
        }
        $missingHeaders = $headers->diff([HttpHeaders::X_SHOPIFY_HMAC, HttpHeaders::X_SHOPIFY_TOPIC, HttpHeaders::X_SHOPIFY_DOMAIN], false);
        if (!empty($missingHeaders)) {
            $missingHeaders = implode(', ', $missingHeaders);
            logToDiscord("Missing one or more of the required HTTP headers to process webhooks: [$missingHeaders]", 'shopify');
            throw new RuntimeException("Missing one or more of the required HTTP headers to process webhooks: [$missingHeaders]", 400);
        }
        $shop = $headers->get(HttpHeaders::X_SHOPIFY_DOMAIN);
        if (empty($shop)) {
            logToDiscord("No shop was received when processing webhook", 'shopify');
            throw new RuntimeException("No shop was received when processing webhook", 401);
        }
        $hmac = $headers->get(HttpHeaders::X_SHOPIFY_HMAC);
        if ($hmac !== base64_encode(hash_hmac('sha256', $rawBody, config('shopify.app.config.general.api_secret'), true))) {
            logToDiscord("Could not validate webhook HMAC. Shop: " . $shop, 'shopify');
            throw new RuntimeException("Could not validate webhook HMAC", 401);
        }
        if (empty($session)) {
            throw new RuntimeException("No session when processing webhook", 401);
        }
        if (empty($session->store_id)) {
            logToDiscord("Shop no session. Shop: " . $shop, 'shopify');
            throw new RuntimeException("No session. No shop was received when processing webhook.", 401);
        }
        $store_id = $session->store_id;
        $hash = substr(md5($shop . $store_id), 0, 15);
        if($hash !== $token) {
            logToDiscord("Could not validate webhook token.", 'shopify');
            throw new RuntimeException("Could not validate webhook token.", 401);
        }
        if(!method_exists(Context::$SESSION_STORAGE, 'setSessionFromDatabase')) {
            logToDiscord("Could not found setSessionFromDatabase method. Shop: " . $shop, 'shopify');
            throw new RuntimeException("Could not found setSessionFromDatabase method.", 401);
        }
    }

    /**
     * @param Request $request
     * @param $token
     * @return JsonResponse
     */
    public function createOrders(Request $request, $token)
    {
        return $this->upsertOrders($request, $token, true);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function gdprCustomerRequest(Request $request)
    {
        return $this->gdprUpdate($request, 'gdprCustomerRequest');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function gdprCustomerErasure(Request $request)
    {
        return $this->gdprUpdate($request, 'gdprCustomerErasure');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function gdprShopDataErasure(Request $request)
    {
        return $this->gdprUpdate($request, 'gdprShopDataErasure');
    }

    /**
     * @param Request $request
     * @param $token
     * @return JsonResponse
     */
    public function updateOrders(Request $request, $token)
    {
        return $this->upsertOrders($request, $token, false);
    }

    /**
     * @param Request $request
     * @param $token
     * @param $isCreated
     * @return JsonResponse
     */
    private function upsertOrders(Request $request, $token, $isCreated) {
        try {
            $rawBody = $request->getContent();
            $rawHeaders = $request->header();
            $headers = new HttpHeaders($rawHeaders);
            $shop = $headers->get(HttpHeaders::X_SHOPIFY_DOMAIN);
            $session = ShopifySession::query()->whereNotNull(['store_id', 'seller_id', 'webhooks', 'access_token'])->firstWhere('shop', $shop);
            if (is_null($session)) {
                return $this->errorResponse("No session when processing webhook", 401);
            }
            $this->validateRequest($headers, $rawBody, $session, $token);
            $seller_id = $session->seller_id;
            $store_id = $session->store_id;
            $ref_id = User::whereId($session->seller_id)->value('ref_id');
            $session = Context::$SESSION_STORAGE->setSessionFromDatabase($session);
            $orders = [json_decode($rawBody)];
            $result = ShopifyAppService::createOrUpdateOrders($orders, $session, $seller_id, $ref_id, $store_id, $shop, $isCreated);
            if($result) {
                return $this->successResponse([], 'Created order successfully.');
            }
            $action = $isCreated ? 'create' : 'update';
            graylogInfo("Seller $seller_id with $store_id start to $action orders.", [
                'category' => 'shopify_sync_log',
                'user_type' => 'system',
                'seller_id' => $seller_id,
                'store_id' => $store_id,
                'action'  => "sync",
                'payload_orders' => $orders
            ]);
            logToDiscord("Can not create order from webhook. Shop: $shop - Payload: " . json_encode($orders), 'shopify');
            return $this->errorResponse("Can not create order from webhook.", 400);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * @param Request $request
     * @param $type
     * @return JsonResponse
     */
    private function gdprUpdate(Request $request, $type) {
        try {
            $rawBody = $request->getContent();
            $rawHeaders = $request->header();
            $headers = new HttpHeaders($rawHeaders);
            if (empty($rawBody)) {
                logToDiscord("No body was received when processing webhook gdpr", 'shopify');
                return $this->errorResponse("No body was received when processing webhook gdpr", 400);
            }
            $missingHeaders = $headers->diff([HttpHeaders::X_SHOPIFY_HMAC, HttpHeaders::X_SHOPIFY_DOMAIN], false);
            if (!empty($missingHeaders)) {
                $missingHeaders = implode(', ', $missingHeaders);
                logToDiscord("Missing one or more of the required HTTP headers to process webhooks gdpr: [$missingHeaders]", 'shopify');
                return $this->errorResponse("Missing one or more of the required HTTP headers to process webhooks gdpr: [$missingHeaders]", 400);
            }
            $hmac = $headers->get(HttpHeaders::X_SHOPIFY_HMAC);
            if ($hmac !== base64_encode(hash_hmac('sha256', $rawBody, config('shopify.app.config.general.api_secret'), true))) {
                logToDiscord("Could not validate webhook HMAC gdpr.", 'shopify');
                return $this->errorResponse("Could not validate webhook HMAC gdpr.", 401);
            }
            $shop = $headers->get(HttpHeaders::X_SHOPIFY_DOMAIN);
            $payload = json_decode($rawBody, true);
            graylogInfo("Shopify shop $shop sent request to update GDPR.", [
                'category' => 'shopify_sync_log',
                'user_type' => 'system',
                'type'  => $type,
                'payload' => $payload
            ]);
            return $this->successResponse([], 'Successfully.');
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), 400);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uninstalledWebhook(Request $request)
    {
        $topic = $request->header(HttpHeaders::X_SHOPIFY_TOPIC, '');
        try {
            $response = Registry::process($request->header(), $request->getContent());
            if (!$response->isSuccess()) {
                logToDiscord("Failed to process '$topic' webhook: {$response->getErrorMessage()}", 'shopify');
                return $this->errorResponse("Failed to process '$topic' webhook", 500);
            }
            if($request->hasSession() && $request->session()->has('seller')) {
                $request->session()->forget('seller');
            }
            if (!empty($request->attributes)) {
                if($request->attributes->has('seller')) {
                    $request->attributes->remove('seller');
                }
                if($request->attributes->has('isStoreConnected')) {
                    $request->attributes->remove('isStoreConnected');
                }
            }
            return $this->successResponse([], "Successfully");
        } catch (\Exception $e) {
            logException($e, 'uninstalledWebhook', 'shopify');
            return $this->errorResponse("Got an exception when handling '$topic' webhook", 500);
        }
    }
}
