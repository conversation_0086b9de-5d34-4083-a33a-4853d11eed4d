<?php

namespace Modules\OrderService\Actions\RegionOrder;

use App\Enums\FulfillMappingEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\PricingModeEnum;
use App\Events\CustomerAddressUpdated;
use App\Http\Controllers\SystemConfigController;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\Template;
use App\Services\OrderService;
use App\Services\StoreService;
use Illuminate\Http\Request;
use LogicException;
use Modules\OrderService\Helpers\OrderHelper;
use Modules\OrderService\Helpers\OrderNumberManager;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Traits\UpdatingRegionOrderAttributesTrait;
use UnexpectedValueException;

class UpdateRegionOrder
{
    use UpdatingRegionOrderAttributesTrait;
    private $regionName;

    /**
     * @param Request $request
     * @return array
     */
    public function handle(Request $request): array
    {
        $this->regionName = config('app.region');
        $storeInfo = StoreService::getCurrentStoreInfo();
        if (!$storeInfo) {
            throw new LogicException('Store is not found');
        }
        $orderToken = $request->post('order_token');
        $regionOrder = RegionOrders::onRegion($this->regionName)
            ->updateOrderCondition([
                'store_id' => $storeInfo->id,
                'access_token' => $orderToken
            ])
            ->first();
        if (!$regionOrder) {
            $orderMaster = Order::onRegion('sg')
                ->updateOrderCondition([
                    'store_id' => $storeInfo->id,
                    'access_token' => $orderToken
                ])
                ->first();
            if (!$orderMaster) {
                throw new UnexpectedValueException('Not found order in master');
            }
            $orderMaster->withProducts('sg', ['product']);
            $regionOrder = $orderMaster;
        }

        /** @var Order|RegionOrders $regionOrder */
        $region = $regionOrder->region;
        if (!empty($region) && $region !== $this->regionName) {
            $regionOrder = RegionOrders::onRegion($region)
                ->updateOrderCondition([
                    'store_id' => $storeInfo->id,
                    'access_token' => $orderToken
                ])
                ->first();
            $regionOrder->withProducts($region, ['product']);
        }
        $seller = $storeInfo->seller;
        $regionOrder->products->map(function ($orderProduct) use ($seller) {
            $product = Product::query()->onSellerConnection($seller)->find($orderProduct->product_id);
            $orderProduct->setRelation('product', $product);
        });

        if ($regionOrder->isPaidAfter(24)) {
            throw new UnexpectedValueException('Order can not update.', 403);
        }

        // Nếu đơn hàng đã được thanh toán
        // Thì một số thông tin dưới đây không thể cập nhật
        if ($regionOrder->isPaid()) {
            $advancedInfo = [
                'country',
                'discount_code',
                'currency_code',
                'shipping_method',
                'products',
            ];
            foreach ($advancedInfo as $key) {
                $request->offsetUnset($key);
            }
        }

        if (empty($request->all())) {
            return $this->responseShowOrderData($regionOrder);
        }
        $email = OrderHelper::getValidEmail($request->post('email'));
        $orderNumber = OrderNumberManager::make([
            'region_name'     => $region,
            'region_order_id' => $regionOrder->id,
            'prefix'          => $storeInfo->order_prefix,
        ])->encode();

        try {
            if (!empty($email)) {
                $regionOrder->customer_email = $email;
                $regionOrder->status = OrderStatus::PENDING;
                $regionOrder->order_number = $orderNumber;
            }
            if ($regionOrder->type === OrderTypeEnum::REGULAR && data_get($storeInfo, 'custom_payment', false)) {
                $regionOrder->type = OrderTypeEnum::CUSTOM;
            }
            $userInfo = $request->post('user_info');
            $countryCode = strtoupper($request->post('country'));
            $oldState = $regionOrder->state ?? null;

            if ($userInfo) {
                $keys = [
                    'name',
                    'address',
                    'city',
                    'zipcode',
                    'country',
                ];
                $errorFields = [];
                foreach ($keys as $key) {
                    if (!isset($userInfo[$key])) {
                        $errorFields[] = $key;
                    }
                }
                if (count($errorFields) > 0) {
                    throw new UnexpectedValueException('These fields are required: ' . implode(', ', $errorFields));
                }
                unset($errorFields, $keys);
                $countryCode = strtoupper($userInfo['country']);
                $regionOrder->customer_name = $userInfo['name'];
                $regionOrder->customer_phone = $userInfo['phone'] ?? null;
                $regionOrder->address = $userInfo['address'];
                $regionOrder->address_2 = $userInfo['address_2'] ?? null;
                $regionOrder->city = $userInfo['city'];
                $regionOrder->state = $userInfo['state'] ?? null;
                $regionOrder->postcode = $userInfo['zipcode'];
                $regionOrder->mailbox_number = $userInfo['mailbox_number'] ?? null;
                $regionOrder->house_number = $userInfo['house_number'] ?? null;
            }

            // flag to check if we need to calculate again
            $hasChanges = false;
            $hasChangesState = false;
            $autoApplyCoupon = null;
            $noShipLogs = [];
            $disableCountry = SystemConfig::getConfig('disable_country', '');
            $disableCountry = !empty($disableCountry) ? explode(',', $disableCountry) : [];
            if ($oldState !== $regionOrder->state) {
                $hasChangesState = true;
            }
            if (($countryCode && $countryCode !== $regionOrder->country) || $hasChangesState) {
                $orderLocation = SystemLocation::findByCountryCode($countryCode);
                if (!is_null($orderLocation)) {
                    $regionOrder->country = $countryCode;
                    $regionCodes = $orderLocation->getRegionCodes();
                    $regionOrder->assignSupplier(source: Order::CHECKOUT_ASSIGN_SUPPLIER, isRegion:true, inCreatingOrder:true);
                    // update base cost
                    $regionOrder->fulfill_status = OrderFulfillStatus::UNFULFILLED;
                    $regionOrder->products->map(function ($product) use ($regionOrder, $regionCodes, $orderLocation, $disableCountry, &$noShipLogs, $storeInfo, $seller) {
                        $product->fulfill_status = OrderProductFulfillStatus::UNFULFILLED;
                        $dynamicBaseCostIndex = 0;
                        $productInfo = $product->product;
                        if ($product->options) {
                            $variantKey = getVariantKey($product->options);
                            $productOptionsList = isset($productInfo) ? json_decode($productInfo->options, true) : [];
                            $productColor = json_decode($product->options, true);
                            $productBaseColor = $productColor['color'] ?? null;
                            $variantKeyBaseSize = getBaseVariantFromOptions($productOptionsList, $productBaseColor);
                            $campLocation = getLocationByCode($product?->product?->market_location);
                            $campRegionCodes = $campLocation ? $campLocation->getRegionCodes() : ['*'];
                            $templateProduct = Template::findAndCacheByKey($product->template_id, false);
                            if (!empty($productInfo) && $productInfo->pricing_mode === PricingModeEnum::ADJUST_PRICE && $storeInfo->enable_dynamic_base_cost) {
                                $templateInfoOrderAndCampLocation = StoreService::getBaseVariantOnOrderAndCampLocation($templateProduct->id, $variantKeyBaseSize,  $regionCodes, $campRegionCodes);
                                OrderService::getDynamicBaseCostIndex($templateInfoOrderAndCampLocation['campaign_location'], $templateInfoOrderAndCampLocation['order_location'], $dynamicBaseCostIndex);
                            }
                            $templateVariantByOrder = ProductVariant::findAndCacheByTemplate($product->template_id)
                                ->filter(function ($each) use ($variantKey, $regionCodes) {
                                    return $each->variant_key === $variantKey
                                        && in_array($each->location_code, $regionCodes);
                                })
                                ->sortBy(function ($each) use ($regionCodes) {
                                    return array_search($each['location_code'], $regionCodes);
                                })
                                ->first();
                            $currencyCode = isset($productInfo) ? $productInfo->currency_code : $regionOrder->currency_code;
                            $rate = SystemConfigController::findOrDefaultCurrency($currencyCode)->rate;
                            $extraCustomFee = $product->extra_custom_fee ?? 0;
                            $productPrice = $productInfo->price;
                            if (isset($templateVariantByOrder) && isset($productInfo)) {
                                if ($productInfo->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                                    $productPrice += ceil($templateVariantByOrder->adjust_price * $rate);
                                } else if ($productInfo->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                                    $productVariant = ProductVariant::query()
                                        ->onSellerConnection($seller)
                                        ->select('price')
                                        ->firstWhere([
                                            'variant_key' => $variantKey,
                                            'product_id' => $productInfo->id,
                                        ]);
                                    if (!is_null($productVariant)) {
                                        // set product price = variant price
                                        $productPrice = $productVariant->price;
                                    } else {
                                        $productPrice = $templateVariantByOrder->price * $rate;
                                    }
                                }
                            }
                            $productPrice /= $rate;
                            $productPrice += $extraCustomFee;
                            $product->dynamic_base_cost_index = $dynamicBaseCostIndex;
                            $dynamicBaseCostIndex = $dynamicBaseCostIndex * $rate;
                            $dynamicBaseCostIndex = roundToHalf($dynamicBaseCostIndex);
                            $dynamicBaseCostIndexRounded = $dynamicBaseCostIndex / $rate;
                            $product->price = $productPrice + $dynamicBaseCostIndexRounded;
                            $product->dynamic_base_cost_index_rounded = $dynamicBaseCostIndexRounded;
                            if (!isset($templateVariantByOrder, $productInfo)) {
                                $product->allow_mapping = false;
                            } else {
                                $product->pricing_mode = $productInfo->pricing_mode;
                                $product->adjust_price = $templateVariantByOrder->adjust_price;
                            }
                            $isExcludeShipping = FulfillMapping::checkExcludeShipping(
                                $orderLocation,
                                $product->template_id,
                            );
                            $product->variant_key = $variantKey;
                            $reason = match (true) {
                                is_null($templateVariantByOrder) => 'NOT_FOUND_VARIANT',
                                (bool) $templateVariantByOrder->out_of_stock => 'OUT_OF_STOCK',
                                $isExcludeShipping => 'EXCLUDE_SHIPPING',
                                in_array($regionOrder->country, $disableCountry, true) => 'DISABLE_COUNTRY',
                                default => null,
                            };

                            if ($reason) {
                                $noShipLogs[$product->id] = [$reason];
                                $product->fulfill_status = OrderProductFulfillStatus::NO_SHIP;
                                $regionOrder->fulfill_status = OrderFulfillStatus::NO_SHIP;
                            } else {
                                $product->base_cost = $templateVariantByOrder->base_cost + ($product?->extra_print_cost ?? 0);
                            }
                        }
                    });
                    OrderHelper::mappingCorrectPricing($regionOrder->products, false, $storeInfo->seller_id, (bool)data_get($storeInfo, 'enable_dynamic_base_cost'));
                    $autoApplyCoupon = $storeInfo->auto_apply_coupon;
                    $hasChanges = true;
                }
            }

            if ($request->has('tip_amount')) {
                $tipAmount = (float) $request->post('tip_amount');
                if ($regionOrder->tip_amount !== $tipAmount) {
                    $regionOrder->tip_amount = $tipAmount;
                    $hasChanges = true;
                }
            }

            // update order note
            if ($request->has('order_note')) {
                $regionOrder->order_note = $request->post('order_note');
            }

            //Apply delivery insurance for only $0.98
            if ($request->has('delivery_insurance')) {
                if ($storeInfo->enable_insurance_fee && $request->boolean('delivery_insurance')) {
                    $regionOrder->insurance_fee = $regionOrder->getInsuranceFee();
                } else {
                    $regionOrder->insurance_fee = 0;
                }
                $hasChanges = true;
            }
            // link promotion to order and execute calculation
            $discountCode = $request->post('discount_code');
            $ref = $request->post('ref');
            if ($ref === 'apply_discount_code') {
                $hasChanges = $this->applyDiscount($regionOrder, $discountCode);
                if (!$hasChanges) {
                    throw new UnexpectedValueException('Your discount code is not available.');
                }
            }

            if (!empty($autoApplyCoupon) && (!$regionOrder->getAttribute('discount_code'))) {
                $this->applyDiscount($regionOrder, $autoApplyCoupon);
            }

            $shippingMethod = $request->post('shipping_method');
            if ($shippingMethod && $shippingMethod !== $regionOrder->shipping_method && $regionOrder->isShippingMethodValid($shippingMethod)) {
                $regionOrder->shipping_method = $shippingMethod;
                $hasChanges = true;
            }

            $paymentGatewayId = $request->post('payment_gateway_id');
            if (!empty($paymentGatewayId)) {
                $regionOrder->payment_gateway_id = (int)$paymentGatewayId;
                $gateway = PaymentGateway::query()
                    ->where('id', $paymentGatewayId)
                    ->where('active', 1)
                    ->value('gateway');

                $paymentMethodName = $request->post('payment_method_name');
                if ($gateway === PaymentMethodEnum::STRIPE && is_string($paymentMethodName) && str_contains($paymentMethodName, $gateway)) {
                    $regionOrder->payment_method = $paymentMethodName;
                } else {
                    $regionOrder->payment_method = $gateway;
                }
            }
            $ccDiscount = $request->post('credit_card_discount');
            if (!empty($ccDiscount) && $regionOrder->payment_method === PaymentMethodEnum::STRIPE) {
                $regionOrder->payment_discount = getPaymentDiscountRate();
                $hasChanges = true;
            }
            if ($regionOrder->payment_method !== PaymentMethodEnum::STRIPE && $regionOrder->payment_discount > 0) {
                $regionOrder->payment_discount = 0;
                $hasChanges = true;
            }

            $paymentFailedLog = $request->post('payment_failed_log');

            if (!empty($paymentFailedLog)) {
                $regionOrder->payment_status = OrderPaymentStatus::FAILED;
                $regionOrder->payment_log = $paymentFailedLog;
                if ($regionOrder->status === OrderStatus::PENDING_PAYMENT) {
                    $regionOrder->status = OrderStatus::PENDING;
                }
            }
            $location = SystemLocation::findByCountryCodeThenSetForAssign($regionOrder);
            if (!$hasChanges) {
                // address po box => must recalculate
                if (str_contains(optional($location)->address, 'po box')) {
                    $hasChanges = true;
                }
                // order state changed => must recalculate
                if (!$hasChanges && $oldState !== $regionOrder->state) {
                    $hasChanges = true;
                }

                $isCombo = $regionOrder->products->where('combo_id', '!=', null)->count() > 0;
                $isUpdateCountry = !!$request->post('country');
                if (!$hasChanges && $isCombo && $isUpdateCountry) {
                    $hasChanges = true;
                }
            }
            if ($hasChanges) {
                $regionOrder->calculateOrder();
                $excludeMappings = FulfillMapping::filterByExcludeLocation(
                    FulfillMappingEnum::SHIPPING_EXCLUDE_LOCATION,
                    $location,
                    $regionOrder->shipping_method,
                );
                $noShip = false;
                if(in_array($regionOrder->country, $disableCountry, true)) {
                    $regionOrder->fulfill_status = OrderFulfillStatus::NO_SHIP;
                    $noShip = true;
                }
                $regionOrder->products->map(function ($product) use ($regionOrder, $excludeMappings, $noShip, &$noShipLogs) {
                    if ($noShip) {
                        $noShipLogs[$product->id][] = 'DISABLE_COUNTRY';
                        $product->fulfill_status = OrderProductFulfillStatus::NO_SHIP;
                    } else if ($excludeMappings && $excludeMappings->count() > 0) {
                        $suppliers_id = $excludeMappings->pluck('supplier_id')->filter(fn($v) => !empty($v))->values()->toArray();
                        $isSupplierExcluded = is_null($excludeMappings->first()->supplier_id) || (!empty($product->supplier_id) && !empty($suppliers_id) && in_array($product->supplier_id, $suppliers_id));
                        if ($isSupplierExcluded) {
                            $noShipLogs[$product->id][] = 'EXCLUDE_SHIPPING';
                            $product->fulfill_status = OrderProductFulfillStatus::NO_SHIP;
                            $regionOrder->fulfill_status = OrderFulfillStatus::NO_SHIP;
                        }
                    }
                    unset($product->pricing_mode, $product->adjust_price, $product->allow_mapping, $product->variant_key);
                    $product->save();
                });
                // TODO: save log for no ship
            }

            // update visit info
            $visitInfo = $request->post('visit_info');
            if ($visitInfo) {
                if (isset($visitInfo['ad_source'])) {
                    $regionOrder->ad_source = $visitInfo['ad_source'];
                }

                if (isset($visitInfo['ad_campaign'])) {
                    $regionOrder->ad_campaign = $visitInfo['ad_campaign'];
                }

                if (isset($visitInfo['ad_medium'])) {
                    $regionOrder->ad_medium = $visitInfo['ad_medium'];
                }
                $regionOrder->visit_info = json_encode($visitInfo, JSON_THROW_ON_ERROR);
            }

            $regionOrder->save();
        } catch (\Throwable $e) {
            if (!($e instanceof UnexpectedValueException)) {
                logException($e, __FUNCTION__, 'error', true);
                graylogError('Update region order error', [
                    'category' => 'update_region_order_error',
                    'data' => $e
                ]);

                if ($this->isReadOnlyError($e->getMessage())) {
                    graylogError('DB permission error', [
                        'category' => 'region_db_permission_error',
                        'action' => 'update',
                        'data' => $e,
                        'current_region_config' => config("regions.{$this->regionName}"),
                        'current_region' => $this->regionName,
                        'order_region' => $regionOrder->region,
                        'token' => $regionOrder->access_token,
                    ]);
                }
            }

            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }

        if ($regionOrder->type === OrderTypeEnum::REGULAR && $request->boolean('verify_address')) {
            CustomerAddressUpdated::dispatch($orderNumber, !(!$region || $region === config('app.region_master')));
        }
        return $this->responseShowOrderData($regionOrder);
    }

    /**
     * @param $regionOrder
     * @return array
     */
    private function responseShowOrderData($regionOrder): array
    {
        $shippingMethods = $regionOrder->availableShippingMethods();
        $separateName = $regionOrder->isSeparateName();
        $order = $regionOrder->toArray();
        // remove unused fields
        $fields = [
            'total_amount',
            'tip_amount',
            'total_discount',
            'payment_discount',
            'total_paid',
            'total_product_amount',
            'total_product_cost',
            'total_quantity',
            'total_shipping_amount',
            'total_shipping_cost',
            'total_tax_amount',
            'insurance_fee',
            'discount_code',
            'fulfill_status',
            'products'
        ];
        foreach ($order as $key => $value) {
            if (!in_array($key, $fields, true)) {
                unset($order[$key]);
            }
        }
        return [
            'success' => true,
            'order' => $order,
            'separate_name' => $separateName,
            'shipping_methods' => $shippingMethods,
        ];
    }

    /**
     * @param $message
     * @return bool
     */
    private function isReadOnlyError($message): bool
    {
        $readOnlyErrorMessage = 'General error: 1290 The MariaDB server is running with the --read-only option so it cannot execute this statement';
        $accessViolation = 'SQLSTATE[42000]: Syntax error or access violation';
        return str_contains($message, $readOnlyErrorMessage) || str_contains($message, $accessViolation) ;
    }
}
