<?php

namespace Modules\OrderService\Commands;

use Illuminate\Console\Command;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderJob;

class SyncRegionOrderCommand extends Command
{
    protected $signature = 'region-order:sync {region=us} {--limit=100} {--id=}';

    protected $description = 'Sync order in region database to master database';

    protected $region;

    /**
     * @return mixed
     */
    protected function getOrders()
    {
        $query = RegionOrders::onRegion($this->region)->withTrashed();
        if ($this->option('id')) {
            $query->where('id', $this->option('id'));
        } else {
            $query->where(function ($query) {
                $query->whereColumn('synced_at', '!=', 'updated_at')->orWhereNull('synced_at');
            })
            ->where(function ($q) {
                $q->where(function  ($q) {
                    $q->where('updated_at', '<=', now()->subMinutes(5));
                    $q->where(function ($q) {
                        $q->whereNull('paid_at')
                            ->orWhere('paid_at', '<=', now()->subMinutes(5));
                    });
                });
                $q->where(function  ($q) {
                    $q->where('updated_at', '>=', now()->subDay())
                        ->orWhere('paid_at', '>=', now()->subDay());
                });
            })
            ->orderBy('updated_at')
            ->limit($this->option('limit'));
        }
        return $query->get();
    }

    /**
     * @return void
     */
    public function handle()
    {
        $this->region = $this->argument('region');
        if ($this->region === config('app.region_master')) {
            $this->log('Region is master, no need to sync');
            return;
        }
        $orders = $this->getOrders();
        graylogInfo('scan sync orders, total: ' . $orders->count(), [
            'category' => 'sync_order_start',
            'region' => $this->region,
            'region_order_ids' => $orders->pluck('id')->toJson(),
        ]);
        $success = 0;
        $errorMessages = [];
        $total = $orders->count();
        $failedOrderIds = [];
        foreach ($orders as $order) {
            if (!$order->getRegion()) {
                $this->log('Order has no region', [
                    'order_id' => $order->getId(),
                    'region_name' => $this->argument('region'),
                ]);
                continue;
            }

            try {
                /**
                 * We don't need put this job to queue
                 * So we can use dispatchSync
                 */
                SyncOrderJob::dispatch($order->id, $order->getRegion())->onQueue('sync_order');
                $success++;
            } catch (\Exception $e) {
                $errorMessages[] = $e->getMessage();
                $failedOrderIds[] = $order->getId();
            }
        }

        $this->log('Sync order done', [
            'total' => $total,
            'success' => $success,
            'error' => count($errorMessages),
            'error_messages' => $errorMessages,
            'failed_order_ids' => $failedOrderIds,
            'region_name' => $this->argument('region'),
        ]);
    }

    private function log($message, array $data = [])
    {
        $this->line($message);
        $data['category'] = 'sync_region_order';
        graylogInfo($message, $data);
    }
}
