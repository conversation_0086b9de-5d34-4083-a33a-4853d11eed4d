<?php
namespace Modules\OrderService\Helpers;

class OrderNumberManager
{
    const DEFAULT_PREFIX = 'SP';
    const MIN_ORDER_REGION_ID_LENGTH = 4;

    public function __construct(
        public ?string $regionName,
        public int $regionOrderId,
        public ?string $prefix = null,
    ) {}

    /**
     * @param $att
     * @return static
     */
    public static function make($att)
    {
        return new static(
            $att['region_name'],
            $att['region_order_id'],
            $att['prefix'],
        );
    }

    /**
     * @return string
     */
    public function encode()
    {
        return (
            $this->encodePrefix().
            $this->encodeRegionId().
            '-'.
            $this->encodeRegionOrderId()
        );
    }

    /**
     * @return string|null
     */
    private function encodePrefix()
    {
        $prefix = $this->prefix;
        if (!$prefix) {
            $prefix = static::DEFAULT_PREFIX;
        }

        return $prefix;
    }

    /**
     * @return int|string|null
     */
    private function encodeRegionId()
    {
        if ($this->regionName) {
            return config("region.regions.{$this->regionName}.id");
        }
        return '';
    }

    /**
     * @return string
     */
    public function encodeRegionOrderId()
    {
        $pattern = '%0' . static::MIN_ORDER_REGION_ID_LENGTH .'d';
        return sprintf($pattern, $this->regionOrderId);
    }
}
