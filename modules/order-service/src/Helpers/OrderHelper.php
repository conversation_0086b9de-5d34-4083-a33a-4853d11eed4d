<?php
namespace Modules\OrderService\Helpers;

use App\Enums\CurrencyEnum;
use App\Enums\PricingModeEnum;
use App\Http\Controllers\SystemConfigController;
use App\Services\UserService;
use Illuminate\Support\Str;
use Modules\OrderService\Rules\CheckValidEmailRule;

class OrderHelper
{
    /**
     * @param $ipAddress
     * @return string
     */
    public static function generateAccessToken($ipAddress = null): string
    {
        $regionName = config('app.region', 'sg');
        $regionId = config("region.regions.{$regionName}.id");
        $token = md5(Str::orderedUuid() . $ipAddress . $regionId);
        try {
            setcookie('order_token', $token);
        } catch (\Exception $e) {}
        return $token;
    }

    /**
     * @param $customerEmail
     * @return string|null
     */
    public static function getValidEmail($customerEmail): ?string
    {
        if (CheckValidEmailRule::passed($customerEmail)) {
            return $customerEmail;
        }

        return null;
    }

    /**
     * @param $products
     * @param $applyTestPrice
     * @param $sellerId
     * @param $enableDynamicBaseCost
     * @return void
     */
    public static function mappingCorrectPricing(&$products, $applyTestPrice, $sellerId, $enableDynamicBaseCost = false): void
    {
        $sellerIds = $products->pluck('seller_id')->unique()->toArray();
        $pricing = UserService::getPricingListBySellerIds($sellerIds);

        if ($pricing->isEmpty()) {
            return;
        }

        $products->map(function ($product) use ($pricing, $applyTestPrice, $sellerId, $enableDynamicBaseCost) {
            if (isset($product->allow_mapping) && $product->allow_mapping == false) {
                return $product;
            }
            $productPricing = $pricing->filter(function ($savedPrice) use ($product) {
                $generalConditions = $savedPrice->seller_id == $product->seller_id && $savedPrice->product_id == $product->template_id;
                if (!$generalConditions) {
                    return false;
                }
                if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                    $option = json_decode($product->options, true) ?? [];
                    $colorPosition = getPositionOfKeyInOptions('color', $option);
                    $variantKey = $product->variant_key;
                    $explode = explode('-', $variantKey);
                    if ($explode[$colorPosition] && $explode[$colorPosition] !== 'white') {
                        $explode[$colorPosition] = 'white';
                    }
                    return $savedPrice->variant_key === implode('-', $explode);
                }
                return true;
            })->sortBy('price')->first();
            if ($productPricing) {
                $productPricing = $productPricing->toArray();
                $rate = SystemConfigController::findOrDefaultCurrency($productPricing['currency_code'])->rate;

                if ($product->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                    $productPricing['price'] += ceil($product->adjust_price);
                }

                if ($productPricing['currency_code'] !== CurrencyEnum::USD) {
                    $productPricing['price'] /= $rate;
                    $productPricing['test_price'] /= $rate;
                }

                if ($product->pricing_mode === PricingModeEnum::ADJUST_PRICE && $enableDynamicBaseCost) {
                    $dynamicBaseCostIndexForPrice = $product->dynamic_base_cost_index * $rate;
                    $dynamicBaseCostIndexForPrice = roundToHalf($dynamicBaseCostIndexForPrice);
                    $dynamicBaseCostIndexRounded = $dynamicBaseCostIndexForPrice / $rate;
                    $productPricing['price'] += $dynamicBaseCostIndexRounded;
                    $product->dynamic_base_cost_index_rounded = $dynamicBaseCostIndexRounded;
                }

                $product->price = $productPricing['price'] + $product->extra_custom_fee + data_get($product, 'extra_print_cost', 0);
                if ($sellerId == $productPricing['seller_id'] && $productPricing['test_price'] != 0) {
                    $product->adjust_test_price = 0;

                    if ($applyTestPrice == 'true') {
                        $product->price += $productPricing['test_price'];
                        $product->adjust_test_price = $productPricing['test_price'];
                    }
                }
            }
            return $product;
        });
    }
}
