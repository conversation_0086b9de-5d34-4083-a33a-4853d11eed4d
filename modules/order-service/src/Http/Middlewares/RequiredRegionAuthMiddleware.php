<?php

namespace Modules\OrderService\Http\Middlewares;

use Closure;
use Illuminate\Http\Request;

class RequiredRegionAuthMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if (!$request->region()) {
            return response()->json([
                'success' => false,
                'message' => 'Required region info'
            ], 401);
        }
        return $next($request);
    }
}
