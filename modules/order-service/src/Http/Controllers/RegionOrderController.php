<?php
namespace Modules\OrderService\Http\Controllers;

use App\Http\Requests\OrderUpdateRequest;
use App\Http\Requests\Storefront\Checkout\StoreRequest;
use App\Traits\ApiResponse;
use App\Traits\StripePaymentDescriptors;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use LogicException;
use Modules\OrderService\Actions\RegionOrder\CreateRegionOrder;
use Modules\OrderService\Actions\RegionOrder\GetRegionOrder;
use Modules\OrderService\Actions\RegionOrder\UpdateRegionOrder;
use Modules\OrderService\Actions\RegionOrder\UpdateRegionOrderPaymentLog;
use UnexpectedValueException;
use Throwable;

class RegionOrderController extends Controller
{
    use ApiResponse,StripePaymentDescriptors;

    /**
     * Show order detail
     *
     * @param Request $request
     * @param string $accessToken
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function show(Request $request, string $accessToken): JsonResponse
    {
        try {
            if (empty($accessToken) || !isValidOrderToken($accessToken)) {
                return $this->errorResponse('Access token is invalid');
            }
            $responseData = app(GetRegionOrder::class, ['accessToken' => $accessToken])->handle($request);
        } catch (Throwable $e) {
            if (!($e instanceof UnexpectedValueException)) {
                logException($e, __FUNCTION__ . " -> " . $accessToken, 'error_checkout', true);
            }
            return $this->errorResponse(optional($e)->getMessage());
        }
        return $this->successResponse($responseData);
    }

    /**
     * @param StoreRequest $request
     * @return JsonResponse
     */
    public function create(StoreRequest $request): JsonResponse
    {
        try {
            $accessToken = app(CreateRegionOrder::class)->handle($request);
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'error_checkout', true);
            return $this->errorResponse(optional($e)->getMessage());
        }
        return $this->successResponse($accessToken);
    }

    /**
     * @param OrderUpdateRequest $request
     * @return JsonResponse
     */
    public function update(OrderUpdateRequest $request): JsonResponse
    {
        try {
            $regionOrder = app(UpdateRegionOrder::class)->handle($request);
        } catch (LogicException | Throwable $e) {
            logException($e, __FUNCTION__ . " -> " . $request->post('order_token'), 'error_checkout', true);
            return $this->errorResponse($e->getMessage());
        }
        $success = data_get($regionOrder, 'success', false);
        $message = data_get($regionOrder, 'message');

        if ($success) {
            return $this->successResponse($regionOrder);
        }
        return $this->errorResponse($message);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updatePaymentLog(Request $request): JsonResponse
    {
        try {
            app(UpdateRegionOrderPaymentLog::class)->handle($request);
            return $this->successResponse();
        } catch (Throwable $e) {
            if (!($e instanceof UnexpectedValueException)) {
                logException($e, __FUNCTION__, 'error_checkout', true);
            }
            return $this->errorResponse($e->getMessage());
        }
    }
}
