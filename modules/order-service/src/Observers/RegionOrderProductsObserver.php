<?php

namespace Modules\OrderService\Observers;

use Modules\OrderService\Events\RegionOrderCreatedEvent;
use Modules\OrderService\Models\RegionOrderProducts;

class RegionOrderProductsObserver
{
    public function created(RegionOrderProducts $lineItem)
    {
        RegionOrderCreatedEvent::dispatch($lineItem->order);
    }

    public function updated(RegionOrderProducts $lineItem) {
        graylogInfo('Region order Product Was Changed', [
            'user_id' => optional(currentUser())->getUserId(),
            'category' => 'debug_order_fulfill',
            'order_id' => $lineItem?->order_id,
            'order_product_id' => $lineItem?->id,
            'fulfill_status' => $lineItem?->fulfill_status,
            'fulfill_status_origin' => $lineItem->getOriginal('fulfill_status'),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'at' => now(),
            'attributes' => $lineItem->getAttributes(),
            'origin' => $lineItem->getOriginal(),
        ]);
    }

    public function deleted(RegionOrderProducts $lineItem) {}
}
