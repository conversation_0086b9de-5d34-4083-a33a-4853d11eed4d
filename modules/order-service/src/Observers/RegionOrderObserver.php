<?php

namespace Modules\OrderService\Observers;

use Modules\OrderService\Jobs\DatabaseSync\SyncOrderJob;
use Modules\OrderService\Models\RegionOrders;

class RegionOrderObserver
{
    public function created(RegionOrders $regionOrder)
    {
        graylogInfo('Region order Was Created', [
            'user_id' => optional(currentUser())->getUserId(),
            'category' => 'debug_order_region_fulfill',
            'order_id' => $regionOrder->getId(),
            'function' => 'created',
            'is_region' => true,
            'status' => $regionOrder->getStatus(),
            'fulfill_status' => $regionOrder->getFulfillStatus(),
            'fulfill_status_origin' => $regionOrder->getOriginal('fulfill_status'),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'at' => now(),
            'attributes' => $regionOrder->getAttributes(),
            'origin' => $regionOrder->getOriginal(),
        ]);
    }

    public function updated(RegionOrders $regionOrder)
    {
        graylogInfo('Region order Fulfill Was Changed', [
            'user_id' => optional(currentUser())->getUserId(),
            'category' => 'debug_order_region_fulfill',
            'order_id' => $regionOrder->getId(),
            'function' => 'updated',
            'is_region' => true,
            'status' => $regionOrder->getStatus(),
            'fulfill_status' => $regionOrder->getFulfillStatus(),
            'fulfill_status_origin' => $regionOrder->getOriginal('fulfill_status'),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'at' => now(),
            'attributes' => $regionOrder->getAttributes(),
            'origin' => $regionOrder->getOriginal(),
        ]);
        $this->dispatchJob($regionOrder);
    }

    public function deleted(RegionOrders $regionOrder)
    {
        $this->dispatchJob($regionOrder);
    }

    protected function dispatchJob(RegionOrders $regionOrder)
    {
        /**
         * We only sync order on demand if order has been paid
         */
        if ($regionOrder->isPaid()) {
            SyncOrderJob::dispatch($regionOrder->getId(), $regionOrder->getRegion())->onQueue('sync_order_region');
        }
    }
}
