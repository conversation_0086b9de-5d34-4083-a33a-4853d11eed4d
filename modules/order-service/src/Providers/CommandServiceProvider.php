<?php

namespace Mo<PERSON>les\OrderService\Providers;

use Mo<PERSON>les\OrderService\Providers\EventServiceProvider as ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{

    public function boot(): void
    {
        $commandDir = dirname(__DIR__) . '/Commands';
        $commands = $this->scanFolder($commandDir);
        $_commands = array();
        foreach ($commands as $command) {
            if(file_exists($commandDir . '/' . $command)) {
                $command = basename($command, ".php");
                $_commands[] = "Modules\\OrderService\\Commands\\{$command}";
            }
        }
        $this->commands($_commands);
    }
}
