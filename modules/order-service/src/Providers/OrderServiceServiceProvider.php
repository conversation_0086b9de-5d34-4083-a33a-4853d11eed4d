<?php
namespace Modules\OrderService\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Http\Request;
use Modules\OrderService\Macros\RegionRequestMacro;
use Modules\OrderService\Macros\RequestStoreMacro;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Observers\RegionOrderObserver;

class OrderServiceServiceProvider extends ServiceProvider
{
    /**
     * @throws \ReflectionException
     */
    public function boot()
    {
        RegionOrders::observe(RegionOrderObserver::class);
        if(!defined('ORDER_SERVICE_MODULE_PATH')) {
            define('ORDER_SERVICE_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->app->booted(function () {
            $this->app->register(CommandServiceProvider::class);
            $this->app->register(InitConnectionServiceProvider::class);

            $this->registerConfigs(['general']);
            $this->registerRoutes();
            $this->registerMacros();
            $this->register();
            /** @var Schedule $schedule */
            $schedule = $this->app->make(Schedule::class);
            $schedule->command('region-order:sync us --limit=2000')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('region-order:sync eu --limit=2000')->everyMinute()->withoutOverlapping(5)->logAfter();
        });

        $this->loadMigrationsFrom(ORDER_SERVICE_MODULE_PATH . '/database/migrations');
    }

    /**
     * Register the custom_campaign's configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = ORDER_SERVICE_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, 'order.config.' . $fileName);
        }
    }

    /**
     * Register the custom_campaign's routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = ORDER_SERVICE_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }

    /**
     * @return void
     * @throws \ReflectionException
     */
    protected function registerMacros()
    {
        Request::mixin(new RequestStoreMacro());
        Request::mixin(new RegionRequestMacro());
    }

    /**
     * @return void
     */
    public function register()
    {
        $this->app->register(EventServiceProvider::class);
    }
}
