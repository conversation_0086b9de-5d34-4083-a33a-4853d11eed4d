<?php

namespace Modules\OrderService\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Models\RegionOrderModel;

class RegionOrderUpdatedEvent implements RegionOrderEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public RegionOrderModel $regionOrderModel,
    ) {
        //
    }

    public function regionOrder(): RegionOrderModel
    {
        return $this->regionOrderModel;
    }
}
