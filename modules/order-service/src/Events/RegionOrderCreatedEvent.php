<?php

namespace Modules\OrderService\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Models\RegionOrders;

class RegionOrderCreatedEvent implements RegionOrderEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(public RegionOrders $regionOrderModel) {

    }

    public function regionOrder(): RegionOrders
    {
        return $this->regionOrderModel;
    }
}
