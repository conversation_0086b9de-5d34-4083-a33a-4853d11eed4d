<?php

namespace Modules\OrderService\Data;

use Spatie\LaravelData\Data;

class CreateRegionOrderProductData extends Data
{
    public function __construct(
        public int $order_id,
        public ?int $product_id = null,
        public ?int $campaign_id = null,
        public ?int $seller_id = null,
        public ?int $auth_id = null,
        public ?int $ref_id = null,
        public ?int $template_id = null,
        public ?int $shipping_rule_id = null,
        public ?int $promotion_rule_id = null,
        public ?int $related_product_id = null,
        public ?int $related_campaign_id = null,
        public ?string $campaign_title = null,
        public ?string $product_name = null,
        public ?string $product_url = null,
        public string $thumb_url,
        public ?string $options = null,
        public ?string $custom_options = null,
        public ?string $custom_designs = null,
        public ?string $custom_print_space = null,
        public ?string $color = null,
        public ?string $size = null,
        public ?float $cost = 0,
        public float $base_cost = 0,
        public float $dynamic_base_cost_index_rounded = 0,
        public float $dynamic_base_cost_index = 0,
        public float $price = 0,
        public ?float $adjust_test_price = null,
        public float $extra_custom_fee = 0,
        public float $extra_print_cost = 0,
        public float $weight = 0,
        public int $quantity = 1,
        public float $tax = 0,
        public ?string $barcode = null,
        public ?float $total_amount = 0,
        public ?float $base_shipping_cost = 0,
        public ?float $shipping_cost = 0,
        public ?float $discount_amount = 0,
        public ?float $profit = 0,
        public ?float $seller_profit = 0,
        public ?float $artist_profit = 0.00,
        public ?float $sen_points = 0,
        public ?bool $upsell_status = false,
        public ?int $fulfilled_quantity = 0,
        public ?string $fulfill_status = 'unfulfilled',
        public ?string $sen_fulfill_status = 'yes',
        public ?int $refund_quantity = 0,
        public ?string $sku = null,
        public ?int $supplier_id = null,
        public ?string $supplier_name = null,
        public ?string $assigned_supplier_at = null,
        public ?string $fulfill_sku = null,
        public ?int $fulfill_product_id = null,
        public ?float $fulfill_cost = 0,
        public ?string $fulfill_order_id = '',
        public ?string $fulfill_exception_log = null,
        public ?string $shipping_carrier = null,
        public ?string $tracking_code = null,
        public ?string $tracking_url = null,
        public ?string $tracking_status = 'new',
        public ?string $updated_at,
        public ?string $fulfilled_at = null,
        public ?string $delivered_at = null,
        public ?string $deleted_at = null,
        public ?string $received_at = null,
        public ?float $processing_day = 0.00,
        public ?float $shipping_day = 0.00,
        public ?string $billed_at = null,
        public ?bool $personalized = false,
        public ?bool $full_printed = false,
        public ?string $exported_at = null,
        public ?int $shard_id = 10,
        public ?int $collection_id = null,
        public ?string $tm_status = 'unverified',
        public ?string $external_product_id = null,
        public ?string $external_fulfillment_id = null,
        public ?string $external_id = null,
        public ?int $sync_status = 0,
        public ?string $sync_at = null,
        public ?string $fulfill_fba_by = null,
        public ?int $cross_shipping = 0,
        public ?string $supplier_exported_at = null,
        public ?int $total_reprint = null,
        public ?float $fulfill_base_cost = 0.00,
        public ?float $fulfill_shipping_cost = 0.00,
        public ?float $fulfill_profit = 0.00,
        public ?string $additional_attributes = null,
        public ?string $combo_id = null,
        public ?string $next_scan_tracking_code_at = null,
        public ?int $at_risk = null
    ) {}

    public static function loadDefaultValue(array $data): array {
        if (!isset($data['total_amount'])) {
            $data['total_amount'] = 0;
        }
        if (!isset($data['base_shipping_cost'])) {
            $data['base_shipping_cost'] = 0;
        }
        if (!isset($data['shipping_cost'])) {
            $data['shipping_cost'] = 0;
        }
        if (!isset($data['discount_amount'])) {
            $data['discount_amount'] = 0;
        }
        if (!isset($data['profit'])) {
            $data['profit'] = 0;
        }
        if (!isset($data['seller_profit'])) {
            $data['seller_profit'] = 0;
        }
        if (!isset($data['artist_profit'])) {
            $data['artist_profit'] = 0.00;
        }
        if (!isset($data['sen_points'])) {
            $data['sen_points'] = 0;
        }
        if (!isset($data['fulfilled_quantity'])) {
            $data['fulfilled_quantity'] = 0;
        }
        if (!isset($data['fulfill_status'])) {
            $data['fulfill_status'] = 'unfulfilled';
        }
        if (!isset($data['sen_fulfill_status'])) {
            $data['sen_fulfill_status'] = 'yes';
        }
        if (!isset($data['refund_quantity'])) {
            $data['refund_quantity'] = 0;
        }
        if (!isset($data['fulfill_cost'])) {
            $data['fulfill_cost'] = 0;
        }
        if (!isset($data['fulfill_order_id'])) {
            $data['fulfill_order_id'] = '';
        }
        if (!isset($data['tracking_status'])) {
            $data['tracking_status'] = 'new';
        }
        if (!isset($data['processing_day'])) {
            $data['processing_day'] = 0.00;
        }
        if (!isset($data['tm_status'])) {
            $data['tm_status'] = 'unverified';
        }
        if (!isset($data['sync_status'])) {
            $data['sync_status'] = 0;
        }
        if (!isset($data['cross_shipping'])) {
            $data['cross_shipping'] = 0;
        }
        if (!isset($data['fulfill_base_cost'])) {
            $data['fulfill_base_cost'] = 0.00;
        }
        if (!isset($data['fulfill_shipping_cost'])) {
            $data['fulfill_shipping_cost'] = 0.00;
        }
        if (!isset($data['fulfill_profit'])) {
            $data['fulfill_profit'] = 0.00;
        }
        if (!isset($data['shipping_day'])) {
            $data['shipping_day'] = 0.00;
        }
        if (!isset($data['processing_day'])) {
            $data['processing_day'] = 0.00;
        }

        return $data;
    }

    public static function from(...$payloads): static
    {
        $payload = $payloads[0] ?? [];
        $payload = self::loadDefaultValue($payload);
        return parent::from($payload);
    }
}
