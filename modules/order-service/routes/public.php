<?php

use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Support\Facades\Route;
use Modules\OrderService\Http\Middlewares\RequiredStoreInfoMiddleware;
use Modules\OrderService\Http\Controllers\RegionOrderController;

Route::prefix('v2/public')
    ->middleware([
        SubstituteBindings::class
    ])
    ->group(function () {
        Route::middleware([
            RequiredStoreInfoMiddleware::class,
        ])->group(function(){
            Route::group([
                'prefix' => 'order',
            ], static function () {
                Route::get('{accessToken}', [RegionOrderController::class, 'show'])->name('show');
                Route::post('create', [RegionOrderController::class, 'create'])->name('create')->middleware('throttle:create_order');
                Route::post('update', [RegionOrderController::class, 'update'])->name('update');
                Route::post('log-checkout-exception', [RegionOrderController::class, 'updatePaymentLog']);
            });
        });
    });

Route::prefix('region-checkout-test')->group(function () {
    Route::get('sync-order', [RegionOrderController::class, 'testSyncOrder']);
});
