<?php

use Illuminate\Support\Facades\Route;
use Modules\EmailMarketingKlaviyo\Http\Controllers\EmailMarketingKlaviyoController;
use App\Http\Middleware\StoreBelongToSeller;

Route::group(['prefix' => 'seller', 'middleware' => 'auth.access.token'], function () {
    Route::prefix('store')->group(function () {
        Route::middleware(StoreBelongToSeller::class)->group(function () {
            Route::group(['prefix' => '{store_id}/email-marketing-klaviyo'], function () {
                Route::post('sync-profiles', [EmailMarketingKlaviyoController::class, 'syncProfiles']);
                Route::get('private-key', [EmailMarketingKlaviyoController::class, 'getKeys']);
                Route::put('private-key', [EmailMarketingKlaviyoController::class, 'updatePrivateKey']);
            });
        });
    });
});
