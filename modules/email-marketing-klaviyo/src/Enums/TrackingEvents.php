<?php

namespace Modules\EmailMarketingKlaviyo\Enums;

use Ben<PERSON>ampo\Enum\Enum;

final class TrackingEvents extends Enum
{
     public const CHECKOUT_STARTED = 'Checkout Started';
     public const PLACED_ORDER = 'Placed Order';
     public const ORDERED_PRODUCT = 'Ordered Product';
     public const FULFILLED_ORDER = 'Fulfilled Order';
     public const FULFILLED_PARTIAL_ORDER = 'Fulfilled Partial Order';
     public const CANCELLED_ORDER = 'Cancelled Order';
     public const REFUNDED_ORDER = 'Refunded Order';
     public const CONFIRMED_SHIPMENT = 'Confirmed Shipment';
     public const DELIVERED_SHIPMENT = 'Delivered Shipment';
     public const MARKED_OUT_FOR_DELIVERY = 'Marked Out for Delivery';
     public const ACTIVE_ON_SITE = 'Active on Site';
     public const VIEWED_PRODUCT = 'Viewed Product';
     public const VIEWED_COLLECTION = 'Viewed Collection';
     public const SUBMITTED_SEARCH = 'Submitted Search';
     public const ADDED_TO_CART = 'Added to Cart';
}
