<?php

namespace Modules\EmailMarketingKlaviyo\Supports;

final class ArrayHelper
{
    /**
     * Recursively remove empty values from an array.
     *
     * @param mixed $haystack
     * @return mixed
     */
    public static function removeEmpty($haystack)
    {
        foreach ($haystack as $key => $value) {
            if (is_array($value)) {
                $haystack[$key] = self::removeEmpty($haystack[$key]);
            }

            if ($haystack[$key] === '' || $haystack[$key] === null) {
                unset($haystack[$key]);
            }
        }

        return $haystack;
    }
}
