<?php

namespace Modules\EmailMarketingKlaviyo\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Traits\ApiResponse;
use Cloudinary\Api\HttpStatusCode;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Modules\EmailMarketingKlaviyo\Data\ImportProfilesData;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoImportProfilesJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class EmailMarketingKlaviyoController extends Controller
{
    use ApiResponse;

    /**
     * Sync profiles to Klaviyo
     *
     * @param Request $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function syncProfiles(Request $request, $storeId)
    {
        $store = Store::whereId($storeId)->firstOrFail();
        $storeId = $store->id;
        if (!$store) {
            return $this->errorResponse('Store not found', 404);
        }
        $privateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
        if (empty($privateKey)) {
            return $this->errorResponse('Klaviyo private key not found', 404);
        }
        if (!KlaviyoService::isKeyValid($privateKey)) {
            return $this->errorResponse('Invalid Klaviyo private key', 400);
        }
        $data = new ImportProfilesData(
            store_id: $storeId,
            private_key: $privateKey,
        );
        dispatch(new KlaviyoImportProfilesJob($data));

        return $this->successResponse([], 'Sync profiles to Klaviyo job dispatched');
    }


    /**
     * Get Klaviyo keys
     * @param Request $request
     * @param mixed $storeId
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getKeys(Request $request, $storeId)
    {
        $store = Store::find($storeId);
        if (!$store) {
            return $this->errorResponse('Store not found', 404);
        }
        $privateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
        $listId = $store->klaviyo_list_id ?? $store->seller->klaviyo_list_id;
        return $this->successResponse([
            'private_key' => $privateKey,
            'list_id' => $listId,
        ]);
    }

    /**
     * Update Klaviyo keys
     *
     * @param Request $request
     * @param $storeId
     * @return JsonResponse
     */
    public function updatePrivateKey(Request $request, $storeId)
    {
        $validator = Validator::make($request->all(), [
            'private_key' => 'nullable|starts_with:pk_',
            'list_id' => 'nullable|string|regex:/^[a-zA-Z0-9_-]+$/',
        ]);
        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return $this->errorResponse($firstError, HttpStatusCode::BAD_REQUEST);
        }
        $store = Store::query()->find($storeId);
        if (!$store) {
            return $this->errorResponse('Store not found', 404);
        }
        $privateKey = $request->input('private_key');
        $listId = $request->input('list_id');
        if (!empty($privateKey) && KlaviyoService::isKeyValid($privateKey) === false) {
            return $this->errorResponse('Invalid Klaviyo private key', 400);
        }
        $store->klaviyo_private_key = $privateKey;
        $store->klaviyo_list_id = $listId;
        $store->save();

        try {
            $service = new KlaviyoService([
                'private_key' => $privateKey,
            ]);
            $service->initMetrics($storeId);
        } catch (\Throwable $e) {
            return $this->errorResponse('Invalid Klaviyo private key', 400);
        }
        return $this->successResponse([], 'Klaviyo private key updated');
    }
}
