<?php

namespace Modules\EmailMarketingKlaviyo\Listeners;

use App\Enums\OrderStatus;
use App\Events\KlaviyoOrderRefunded;
use App\Models\Order;
use App\Models\Store;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Carbon;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Modules\EmailMarketingKlaviyo\Services\StoreService;
use Modules\OrderService\Models\RegionOrders;

class KlaviyoOrderRefundedListener implements ShouldQueue
{
    use InteractsWithQueue;

    public Order|RegionOrders $order;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = KlaviyoQueueName::TrackEvents;

    public function handle(KlaviyoOrderRefunded $event): void
    {
        $this->order = $event->order;
        $this->order->refresh();
        $this->order->customer_phone = phone($this->order->customer_phone)->isValid() ? phone($this->order->customer_phone)->formatE164() : null;
        $store = Store::whereId($this->order->store_id)->first();
        if (empty($store)) {
            return;
        }
        $klaviPrivateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
        if (empty($klaviPrivateKey)) {
            return;
        }
        if ($this->order->isCustomServiceOrder() || $this->order->isServiceOrder()) {
            return;
        }
        $domain = $store->domain;
        if (empty($domain)) {
            $domain = $store->sub_domain . "." . getStoreBaseDomain();
        }
        if ($this->order->status !== OrderStatus::REFUNDED) {
            graylogInfo("Order is not refunded", [
                "order_id" => $this->order->id,
                "payment_status" => $this->order->payment_status,
                "order_status" => $this->order->status,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        if (!KlaviyoService::isOrderTypeAllowed($this->order)) {
            graylogInfo("Order type is not regular", [
                "order_id" => $this->order->id,
                "order_type" => $this->order->type,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        $result = StoreService::getOrderItems($this->order);
        if ($result === null) {
            graylogInfo("Order has no products", [
                "order_id" => $this->order->id,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        $orderedItems = $result->items;
        $categories = $result->categories;
        $itemNames = $result->item_names;
        $orderID = $this->order->id;
        $totalAmount = $this->order->total_amount;
        $shippingAddress = StoreService::getShippingAddress($this->order);
        $billingAddress = StoreService::getBillingAddress($this->order);
        $currencyCode = StoreService::getCurrency($this->order);
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => [
                        "OrderId" => $orderID,
                        "Reason" => "Refunded",
                        "Categories" => $categories,
                        "ItemNames" => $itemNames,
                        "DiscountCode" => $this->order->discount_code,
                        "DiscountValue" => $this->order->total_discount,
                        "Brands" => [],
                        "Items" => $orderedItems,
                        "BillingAddress" => $billingAddress,
                        "ShippingAddress" => $shippingAddress,
                    ],
                    "time" => Carbon::now()->toIso8601ZuluString(),
                    "value" => $totalAmount,
                    "value_currency" => $currencyCode,
                    "unique_id" => $orderID,
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Refunded Order",
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => $this->order->customer_email,
                                "phone_number" => $this->order->customer_phone,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $store->name ?? "",
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($klaviPrivateKey, $payload));
    }
}
