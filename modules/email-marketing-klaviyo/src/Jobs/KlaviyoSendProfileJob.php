<?php

namespace Modules\EmailMarketingKlaviyo\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use KlaviyoAPI\KlaviyoAPI;
use Modules\EmailMarketingKlaviyo\Data\ProfileData;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Spatie\RateLimitedMiddleware\RateLimited;
use Throwable;

class KlaviyoSendProfileJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public ProfileData $data;
    public string $privateKey;

    public function __construct(ProfileData $data, string $privateKey = "")
    {
        $this->onQueue(KlaviyoQueueName::ImportProfiles);
        $this->data = $data;
        $this->privateKey = $privateKey;
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    public function middleware(): array
    {
        $steadyRateLimit = (new RateLimited())
            ->allow(700)
            ->everyMinute()
            ->releaseAfterBackoff($this->attempts(), 3);
        $burstRateLimit = (new RateLimited())
            ->allow(75)
            ->everySecond()
            ->releaseAfterBackoff($this->attempts(), 3);
        return [
            $steadyRateLimit,
            $burstRateLimit
        ];
    }

    public function handle()
    {
        if ($this->privateKey === "") {
            $this->privateKey = config('email.marketing.klaviyo.config.general.senprints.private_key', '');
            if ($this->privateKey === "") {
                throw new \Exception("Klaviyo private key is not set");
            }
        }
        $payload = [
            "data" => (object) [
                "type" => "profile",
                "attributes" => (object) [
                    "email" => $this->data->email,
                    "first_name" => $this->data->first_name,
                    "last_name" => $this->data->last_name,
                ]
            ]
        ];
        $klaviyoClient = new KlaviyoAPI($this->privateKey, guzzle_options: [
            'timeout' => 10,
            'verify' => false,
        ]);
        $klaviyoClient->Profiles->createProfile($payload);
    }

    /**
     * Handle a job failure.
     *
     * @param null|Throwable $exception
     * @return void
     */
    public function failed(?Throwable $exception): void
    {
        KlaviyoService::processException($exception, 'KlaviyoSendProfileJob', [
            'data' => $this->data,
            'privateKey' => $this->privateKey,
        ]);
        if ($exception->getCode() === 304) {
            $this->delete();
        }
    }

    /**
     * The number of times the job may be attempted.
     *
     * @return Carbon
     */
    public function retryUntil(): Carbon
    {
        return now()->addSeconds(5);
    }
}
