<?php

namespace Modules\EmailMarketingKlaviyo\Jobs\Demo;

use App\Enums\OrderPaymentStatus;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Order;
use App\Events\OrderPaymentCompleted;
use Modules\OrderService\Models\RegionOrders;
use Modules\Marketing\Supports\Helper;
use TheIconic\NameParser\Parser;
use App\Models\Store;
use Illuminate\Support\Str;
use App\Enums\OrderTypeEnum;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Services\StoreService;
use App\Enums\OrderStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSubscribeProfilesJob;
use Illuminate\Support\Carbon;

class KlaviyoDemoMarkedOutForDeliveryJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?User $seller;
    public $storeId;
    public ?Store $store;

    public function __construct($storeId, $sellerId = null)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        if ($storeId) {
            $this->storeId = $storeId;
            $this->store = Store::whereId($storeId)->first();
        }
        else {
            $this->seller = User::query()->find($sellerId);
        }
    }

    public function handle(): void
    {
        if (empty($this->store) && empty($this->seller)) {
            return;
        }
        $privateKey = $this->store?->klaviyo_private_key ?? $this->seller?->klaviyo_private_key;;
        // $publicKey = $this->store->klaviyo_public_key ?? null;
        if (empty($privateKey)) {
            $this->release();
            return;
        }
        $baseUrl = env('APP_URL', 'https://senprints.com');
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        $storeName = env('APP_NAME', 'SenPrints');
        $properties = [
            '$value' => 100,
            'Collections' => ['T-Shirt'],
            'Customer Locale' => 'Nigeria',
            'Estimated Delivery' => '2021-09-01',
            'Fulfillment Service' => $storeName,
            'Items' => ['T-Shirt'],
            'Item Count' => 1,
            'Transit Hours' => 0,
            'Shipment Status' => 'out_for_delivery',
            'Source Name' => 'web',
            'Tracking Company' => '17Track',
            'Tracking Numbers' => '12345',
            'OptedInToSmsOrderUpdates' => 'True',
        ];
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => $properties,
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Marked Out For Delivery"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => 'noreply@' . $domain,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $store->name ?? "",
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($privateKey, $payload));
    }

    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoDemoMarkedOutForDeliveryJob', [
            'storeId' => $this?->storeId,
            'sellerId' => $this->seller?->id,
        ]);
    }
}
