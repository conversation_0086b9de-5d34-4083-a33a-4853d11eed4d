<?php

namespace Modules\EmailMarketingKlaviyo\Jobs\Demo;

use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Store;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class KlaviyoDemoAbandonedCartJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?User $seller;
    public $storeId;
    public ?Store $store;

    public function __construct($storeId, $sellerId = null)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        if ($storeId) {
            $this->storeId = $storeId;
            $this->store = Store::whereId($storeId)->first();
        }
        else {
            $this->seller = User::query()->find($sellerId);
        }
    }

    public function handle(): void
    {
        if (empty($this->store) && empty($this->seller)) {
            return;
        }
        $privateKey = $this->store?->klaviyo_private_key ?? $this->seller?->klaviyo_private_key;;
        // $publicKey = $this->store->klaviyo_public_key ?? null;
        if (empty($privateKey)) {
            $this->release();
            return;
        }
        $baseUrl = env('APP_URL', 'https://senprints.com');
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        $storeName = env('APP_NAME', 'SenPrints');
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => [
                        "OrderId" => '12345',
                        "Categories" => ['T-Shirt'],
                        "ItemNames" => ['T-Shirt'],
                        "DiscountCode" => 'DISCOUNT',
                        "DiscountValue" => 0,
                        "Brands" => [],
                        "OrderStatusUrl" => $baseUrl . '/order-status/12345',
                        "Items" => [
                            [
                                "ProductID" => '12345',
                                "SKU" => 'SKU123',
                                "ProductName" => 'T-Shirt',
                                "Quantity" => 1,
                                "Price" => 10,
                                "RowTotal" => 10,
                                "ProductURL" => $baseUrl . '/product/12345',
                                "ImageURL" => $baseUrl . '/product/12345/image.jpg',
                            ]
                        ],
                        "BillingAddress" => [
                            "FirstName" => 'John',
                            "LastName" => 'Doe',
                            "Address1" => '123 Main St',
                            "City" => 'San Francisco',
                            "State" => 'CA',
                            "Zip" => '94103',
                            "Country" => 'US',
                            "CountryCode" => 'US',
                            "Phone" => '************',
                        ],
                        "ShippingAddress" => [
                            "FirstName" => 'John',
                            "LastName" => 'Doe',
                            "Address1" => '123 Main St',
                            "City" => 'San Francisco',
                            "State" => 'CA',
                            "Zip" => '94103',
                            "Country" => 'US',
                            "CountryCode" => 'US',
                            "Phone" => '************',
                        ],
                    ],
                    "time" => Carbon::now()->toIso8601ZuluString(),
                    "value" => 10,
                    "value_currency" => 'USD',
                    "unique_id" => '12345',
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Abandoned Cart"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => 'noreply@' . $domain,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $store->name ?? "",
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($privateKey, $payload));
    }

    /**
     * Handle a job failure.
     *
     * @param mixed $exception
     * @return void
     */
    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoDemoAbandonedCartJob', [
            'storeId' => $this?->storeId,
            'sellerId' => $this->seller?->id,
        ]);
    }
}
