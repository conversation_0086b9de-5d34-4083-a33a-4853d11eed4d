<?php

namespace Modules\EmailMarketingKlaviyo\Jobs\Demo;

use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Store;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class KlaviyoDemoActiveOnSiteJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?User $seller;
    public $storeId;
    public ?Store $store;

    public function __construct($storeId, $sellerId = null)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        if ($storeId) {
            $this->storeId = $storeId;
            $this->store = Store::whereId($storeId)->first();
        }
        else {
            $this->seller = User::query()->find($sellerId);
        }
    }

    public function handle(): void
    {
        if (empty($this->store) && empty($this->seller)) {
            return;
        }
        $privateKey = $this->store?->klaviyo_private_key ?? $this->seller?->klaviyo_private_key;;
        // $publicKey = $this->store->klaviyo_public_key ?? null;
        if (empty($privateKey)) {
            $this->release();
            return;
        }
        $baseUrl = env('APP_URL', 'https://senprints.com');
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        $storeName = env('APP_NAME', 'SenPrints');
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => [
                        "URL" => $baseUrl,
                        "Activity ID" => Str::uuid(),
                    ],
                    "time" => Carbon::now()->toIso8601ZuluString(),
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Active on Site",
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => 'noreply@' . $domain,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $storeName,
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($privateKey, $payload));
    }

    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoDemoActiveOnSiteJob', [
            'storeId' => $this?->storeId,
            'sellerId' => $this->seller?->id,
        ]);
    }
}
