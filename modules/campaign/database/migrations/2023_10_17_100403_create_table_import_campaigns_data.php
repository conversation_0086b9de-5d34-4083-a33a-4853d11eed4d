<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateTableImportCampaignsData extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('import_campaigns_data', function (Blueprint $table) {
            $table->id();
            $table->integer('seller_id')->index('seller_id');
            $table->integer('campaign_id')->index('campaign_id')->nullable();
            $table->string('campaign_slug')->index('campaign_slug')->nullable();
            $table->string('campaign_name')->index('campaign_name')->nullable();
            $table->string('campaign_description')->nullable();
            $table->string('collection', 100)->nullable();
            $table->string('store_ids', 100)->index('store_ids')->nullable();
            $table->string('market_location', 20)->index('market_location')->nullable();
            $table->string('currency', 10)->index('currency')->nullable();
            $table->string('pricing_mode', 50)->index('pricing_mode')->nullable();
            $table->string('prefix', 100)->index('prefix')->nullable();
            $table->string('suffix', 100)->index('suffix')->nullable();
            $table->string('print_space', 100)->index('print_space')->nullable();
            $table->boolean('short_url')->default(0);
            $table->text('products')->nullable();
            $table->text('designs')->nullable();
            $table->text('mockups')->nullable();
            $table->enum('status', [
                'pending',
                'processing',
                'completed',
                'failed',
            ])->index('status')->default('pending');
            $table->enum('type', [
                'regular',
                'express',
                'mockup',
                'custom',
                'aop',
            ])->index('type')->default('regular');
            $table->text('logs')->nullable();
            $table->timestamp('created_at')->index('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->index('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
