<?php

use App\Enums\FileStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatusPendingToFileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('file', 'status')) {
            Schema::table('file', function (Blueprint $table) {
                $table->enum('status', FileStatusEnum::asArray())->default(FileStatusEnum::ACTIVE)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
}
