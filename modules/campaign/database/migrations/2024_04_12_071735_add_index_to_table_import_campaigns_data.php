<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('import_campaigns_data', function (Blueprint $table) {
            $table->index(['type', 'status', 'campaign_id', 'created_at'], 'type_status_campaign_id_created_at_index');
            $table->index(['type', 'status', 'seller_id', 'campaign_id', 'created_at'], 'type_status_seller_id_campaign_id_created_at_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
