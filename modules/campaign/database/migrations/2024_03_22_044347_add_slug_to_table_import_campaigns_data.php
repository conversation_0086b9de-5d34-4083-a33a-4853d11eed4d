<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('import_campaigns_data', function (Blueprint $table) {
            $table->text('name_prefix')->nullable()->after('suffix');
            $table->text('name_suffix')->nullable()->after('name_prefix');
            $table->boolean('indefinite_article')->default(false)->after('name_suffix');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
