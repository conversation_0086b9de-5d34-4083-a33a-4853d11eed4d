<?php

namespace Modules\Campaign\Filters;

use App\Filters\AbstractFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

/**
 *
 */
class CampaignFilter extends AbstractFilter
{
    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function id(Builder $q, $value): void
    {
        $q->where('id', (int) $value);
    }
    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function campaignId(Builder $q, $value): void
    {
        $q->where('campaign_id', (int) $value);
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function sellerId(Builder $q, $value): void
    {
        $q->where('seller_id', (int) $value);
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function campaignName(Builder $q, $value): void
    {
        $q->where('campaign_name', 'like', "%$value%");
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function collection(Builder $q, $value): void
    {
        $q->where('collection', 'like', "%$value%");
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function systemType(Builder $q, $value): void
    {
        $q->whereIn('system_type', to_list($value, Str::lower(...)));
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function productType(Builder $q, $value): void
    {
        $q->whereIn('product_type', to_list($value, Str::lower(...)));
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function status(Builder $q, $value): void
    {
        $q->whereIn('status', to_list($value, Str::lower(...)));
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function createdAt(Builder $q, $value): void
    {
        $q->whereDate('created_at', $value);
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function updatedAt(Builder $q, $value): void
    {
        $q->whereDate('updated_at', $value);
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     *
     * @return void
     */
    public function campaignSlug(Builder $q, $value): void
    {
        $q->where('campaign_slug', $value);
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $values
     *
     * @return void
     */
    public function orderBy(Builder $q, $values): void
    {
        foreach (to_list($values) as $value) {
            $v = explode(':', $value);

            $q->orderBy($v[0], $v[1] ?? 'desc');
        }
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                            $value
     *
     * @return void
     */
    public function time(Builder $q, $value): void
    {
        $this->timeFilter($q, $value);
    }
}
