<?php
namespace Modules\Campaign\Providers;

class CommandServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $commandDir = dirname(__DIR__) . '/Commands';
        $commands = $this->scanFolder($commandDir);
        $_commands = array();
        foreach ($commands as $command) {
            if(file_exists($commandDir . '/' . $command)) {
                $command = basename($command, ".php");
                $_commands[] = "Modules\\Campaign\\Commands\\{$command}";
            }
        }
        $this->commands($_commands);
    }
}
