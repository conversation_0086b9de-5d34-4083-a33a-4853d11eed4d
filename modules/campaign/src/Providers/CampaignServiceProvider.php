<?php
namespace Modules\Campaign\Providers;

use Illuminate\Console\Scheduling\Schedule;

class CampaignServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap CustomCampaign service provider service.
     *
     * @return void
     */
    public function boot()
    {
        if(!defined('CUSTOM_CAMPAIGN_MODULE_PATH')) {
            define('CUSTOM_CAMPAIGN_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->autoload(CUSTOM_CAMPAIGN_MODULE_PATH . '/helpers');
        $this->loadMigrationsFrom(CUSTOM_CAMPAIGN_MODULE_PATH . '/database/migrations');
        $this->registerRoutes();
        $this->registerConfigs(['general']);
        $this->app->booted(function () {
            $this->app->register(CommandServiceProvider::class);
            if (config('senprints.schedule_enabled')) {
                /** @var Schedule $schedule */
                $schedule = $this->app->make(Schedule::class);
                $schedule->command('campaign:create-draft')->everyMinute()->withoutOverlapping(5)->logAfter();
                $schedule->command('campaigns:render-psd')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
                $schedule->command('campaign:create-imported')->everyMinute()->withoutOverlapping(10)->logAfter();
                $schedule->command('campaigns:handle-long-processing-camp')->everyMinute()->withoutOverlapping(1)->logAfter();
                $schedule->command('campaigns:handle-save-design-regular-camp')->everyMinute()->withoutOverlapping(5)->logAfter();
                $schedule->command('campaign:handle-processing-imported-row')->everyMinute()->withoutOverlapping()->logAfter();
                $schedule->command('campaign:handle-pending-imported-row --import-design')->everyMinute()->withoutOverlapping()->logAfter();
                $schedule->command('campaign:handle-pending-imported-row --enqueue-design')->everyMinute()->withoutOverlapping()->logAfter();
                $schedule->command('campaign:verify-design-after-render')->everyMinute()->withoutOverlapping(5)->logAfter();
            }
        });
    }

    /**
     * Register the custom_campaign's configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = CUSTOM_CAMPAIGN_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, 'campaign.config.' . $fileName);
        }
    }

    /**
     * Register the custom_campaign's routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = CUSTOM_CAMPAIGN_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }
}
