<?php

namespace Modules\Campaign\Services;

use App\Models\Campaign;
use App\Models\Slug;
use App\Models\User;
use App\Services\CampaignService;
use App\Services\SenPrintsAuth;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use RuntimeException;

class BulkService
{
    use Dispatchable;

    public const ERR_LIMIT_CREATE_CAMPAIGN = 1;

    public const ERR_EXISTS_CAMPAIGN = 2;

    public const ERR_LUNCH_CAMPAIGN_FAILED = 5;

    public function __construct(
        private readonly User       $seller,
        private readonly Campaign   $fromCampaign,
        private readonly ?Campaign  $toCampaign,
        private readonly string     $name,
        private readonly Collection $designs,
        private readonly Collection $designs3D,
        private ?string             $slug = null,
        private ?string             $collection = null,
    ) {}

    /**
     * @throws \Throwable
     */
    public function handle(
        CloneService          $cloneService,
        LaunchService         $launchService
    ): ?Campaign
    {
        $this->slug ??= Str::slug($this->name);

        if ( ! $this->collection && $this->canSellName()) {
            $this->collection = $this->name;
        }

        if ( ! $this->canCreateCampaign()) {
            throw new RuntimeException(
                'Don\'t have permission to create campaign !', self::ERR_LIMIT_CREATE_CAMPAIGN
            );
        }

        if ($exist = $this->findCampaignBySlugOfSeller($this->slug, $this->seller)) {
            if ($exist->isDraft()) {
                $this->removeDraftCampaign($exist, $this->seller);
            }

            elseif ($this->canSellName()) {
                throw new RuntimeException('Exists campaign', self::ERR_EXISTS_CAMPAIGN);
            }
        }

        $new = $cloneService->handle(
            $this->seller,
            $this->fromCampaign,
            $this->toCampaign,
            $this->name,
            Slug::genSlugIfNecessary($this->slug),
            $this->designs,
            $this->designs3D
        );

        if ( ! $launchService->handle($new, $this->seller)) {
            throw new RuntimeException(
                'Lunch campaign failed.', self::ERR_LUNCH_CAMPAIGN_FAILED
            );
        }

        $new->load('campaign_products.designs');

        return $new;
    }

    /**
     * @return \App\Services\SenPrintsAuth
     */
    public function seller(): SenPrintsAuth
    {
        return SenPrintsAuth::instance($this->sellerId());
    }

    /**
     * @return bool
     */
    public function canCreateCampaign(): bool
    {
        return $this->seller()->getNumberCampaignCanCreate() > 0;
    }

    /**
     * @return int
     */
    public function sellerId(): int
    {
        return $this->fromCampaign->seller_id;
    }

    /**
     * @return bool
     */
    public function canSellName(): bool
    {
        return $this->seller()->isSeller() && $this->seller()->getInfo()?->isSellName();
    }

    /**
     * @side-effect
     *
     * @param $slug
     * @param $seller
     * @return Campaign|null
     */
    public function findCampaignBySlugOfSeller($slug, $seller): Campaign|null
    {
        return Campaign::findBySlugOfSeller($slug, $seller);
    }

    /**
     * @param Campaign $campaign
     * @param $seller
     * @return bool
     * @throws \Throwable
     * @side-effect
     *
     */
    public function removeDraftCampaign(Campaign $campaign, $seller): void
    {
        CampaignService::clearCampaignById($campaign->id, $seller);
    }

    /**
     * Group file names
     *
     * @param     array             $contents
     * @param     callable|null     $nameResolver
     * @param     callable|null     $designResolver
     *
     * @return array
     */
    public static function groupFileNames(array $contents, ?callable $nameResolver = null, ?callable $designResolver = null): array
    {
        $nameResolver ??= static fn($content) => $content['name'];
        $designResolver ??= static fn($content) => $content['design'];
        $results = [];

        foreach ($contents as $idx => $content) {
            [$collection, $printSpace, $campaignName] = parse_design_file_name(
                $nameResolver($content)
            );

            if ( ! $campaignName) {
                continue;
            }

            $design_url = $designResolver($content);
            $collection = Str::lower($collection);
            $printSpace = Str::lower($printSpace);

            $key = md5($campaignName . $collection);

            if (! isset($results[$key])) {
                $results[$key] = [
                    'campaign_name' => $campaignName,
                    'collection' => $collection,
                    'designs' => [$printSpace => $design_url]
                ];
            } else {
                $results[$key]['designs'][$printSpace] = $design_url;
            }
        }

        return array_values($results);
    }
}
