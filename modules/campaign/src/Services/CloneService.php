<?php

namespace Modules\Campaign\Services;

use App\Enums\CampaignPublicStatusEnum as PublicStatus;
use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\PricingModeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\CollectionSellerController;
use App\Models\Campaign;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Upsell;
use App\Models\User;
use App\Services\CampaignService;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\Campaign\Jobs\SyncSlugJob;
use RuntimeException;
use Throwable;

class CloneService
{
    public const REPLICATE_CAMPAIGN_FIELDS = [
        'name',
        'slug',
        'default_product_id',
        'tm_status',
        'sync_status',
        'elastic_document_id',
        'created_at',
        'updated_at',
        'sales_score',
        'status',
        'score',
        'time_score',
    ];

    public const ERR_APPLY_DESIGNS_CAMPAIGN_FAILED = 3;

    public const ERR_APPLY_MOCKUPS_CAMPAIGN_FAILED = 4;

    public function __construct(
        private readonly ApplyDesignsToProductService   $applyDesignsService,
        private readonly ApplyDesigns3DToProductService $applyMockupsService
    ) {}

    /**
     * @throws \Throwable
     */
    public function handle(User $seller, Campaign $fromCampaign, ?Campaign $toCampaign, string $name, string $slug, Collection $designs, Collection $designs3D, ?string $collection = null): Campaign
    {
        try {
            $camp = $this->cloneCampaignWithoutRelation($seller, $fromCampaign, $toCampaign, $name, $slug);

            $newProducts = $this->cloneProducts($seller, $fromCampaign, $camp, $designs, $designs3D);

            $camp->setRelation('campaign_products', $newProducts);

            $this->cloneCollections($fromCampaign, $camp);

            $this->cloneStores($fromCampaign, $camp);

            $this->cloneUpsell($fromCampaign, $camp, $seller->id);

            $camp->save();

            if ($collection) {
                $this->attachCampaignToCollection(
                    $camp, $collection, $fromCampaign->seller_id
                );
            }

            return $camp;
        } catch (Throwable $e) {
            logException($e, 'CloneService@handle');
            $camp ??= null;

            silent(function () use ($camp, $seller) {
                $camp && $this->deleteUpsellByCampaignId($camp->id, $seller->id);
                $camp && $this->deleteProductsByCampaignId($camp->id, $seller);
                $camp && $this->deleteVariantsByCampaignId($camp->id, $seller);
            });

            throw $e;
        }
    }

    /**
     * @param User $seller
     * @param Campaign $originCampaign
     * @param Campaign|null $toCampaign
     * @param string $name
     * @param string $slug
     *
     * @return Campaign
     */
    public function cloneCampaignWithoutRelation(User $seller, Campaign $originCampaign, ?Campaign $toCampaign, string $name, string $slug): Campaign
    {
        $clone = $this->replicateCampaign($seller, $originCampaign, $name, $slug, $toCampaign);

        SyncSlugJob::dispatchSync(ids:[$clone->id], seller: $seller);

        return $clone;
    }

    /**
     * @param User $seller
     * @param Campaign $originCampaign
     * @param string $name
     * @param string $slug
     *
     * @return Campaign
     */
    public function replicateCampaign(User $seller, Campaign $originCampaign, string $name, string $slug, ?Campaign $toCampaign): Campaign
    {
        $arrayScore = $this->saleScore();
        $dataNewCampaign = $originCampaign
            ->replicate(self::REPLICATE_CAMPAIGN_FIELDS)
            ->fill([
                'name' => $name,
                'slug' => $slug,
                'tm_status' => CampaignTrademarkStatusEnum::UNVERIFIED,
                'product_type' => ProductType::CAMPAIGN,
                'status' => ProductStatus::DRAFT,
                'score' => $arrayScore['score'],
                'time_score' => $arrayScore['timestamp'],
            ]);
        $dataNewCampaign->public_status = PublicStatus::NO;
        if (in_array($originCampaign->public_status, [PublicStatus::YES, PublicStatus::APPROVED], true)) {
            $dataNewCampaign->public_status = PublicStatus::YES;
        }
        if ($toCampaign) {
            $toCampaign->fill([
                ...Arr::except($dataNewCampaign->toArray(), self::REPLICATE_CAMPAIGN_FIELDS),
                'name' => $name,
                'slug' => $slug,
            ]);
            $toCampaign->save();
        }
        else {
            $toCampaign = Campaign::query()
                ->onSellerConnection($seller)
                ->create($dataNewCampaign->toArray());
        }
        return $toCampaign;
    }

    /**
     * @param User $seller
     * @param Product $originProduct
     * @param Campaign $originCampaign
     * @param Campaign $newCampaign
     *
     * @return Product
     */
    public function replicateProduct(User $seller, Product $originProduct, Campaign $originCampaign, Campaign $newCampaign): Product
    {
        $originProduct->makeHiddenAll();

        $dataNewProduct = $originProduct->replicate([
            'extra_print_cost',
            'tm_status',
            'public_status',
            'sync_status',
            'elastic_document_id',
            'created_at',
            'updated_at',
            'sales_score',
            'score',
            'time_score'
        ])->fill([
            'campaign_id' => $newCampaign->id,
            'status' => ProductStatus::DRAFT,
            'score' => $newCampaign->score + ($originProduct->id === $originCampaign->default_product_id ? 1 : 0),
            'time_score' => $newCampaign->timestamp,
            'public_status' => $newCampaign->public_status,
        ])->toArray();

        return Product::query()->onSellerConnection($seller)->create($dataNewProduct);
    }

    /**
     * @param     \App\Models\Product     $product
     *
     * @return \Illuminate\Support\Collection<ProductVariant>
     */
    public function cloneVariants(User $seller, Product $product): Collection
    {
        if ($product->pricing_mode !== PricingModeEnum::CUSTOM_PRICE) {
            return collect();
        }

        return $product->variants
            ->map(function (ProductVariant $originVariant) use ($seller, $product) {
                return $this->replicateVariant($seller, $originVariant, $product);
            });
    }

    /**
     * @param User $seller
     * @param ProductVariant $fromVariant
     * @param Product $toProduct
     *
     * @return ProductVariant
     */
    public function replicateVariant(User $seller, ProductVariant $fromVariant, Product $toProduct): ProductVariant
    {
        $dataNewVariant = $fromVariant->replicate()
            ->fill([
                'product_id' => $toProduct->id,
                'campaign_id' => $toProduct->campaign_id
            ])
            ->toArray();
        return ProductVariant::query()
            ->onSellerConnection($seller)
            ->create($dataNewVariant);
    }

    /**
     * @param User $seller
     * @param Campaign $originCampaign
     * @param Campaign $newCampaign
     * @param Collection $designs
     * @param Collection $designs3D
     *
     * @return Collection
     * @throws Throwable
     */
    public function cloneProducts(User $seller, Campaign $originCampaign, Campaign $newCampaign, Collection $designs, Collection $designs3D): Collection
    {
        $newProducts = collect();

        /** @var Product $originProduct */
        foreach ($originCampaign->campaign_products as $originProduct) {
            $clone = $this->cloneProductWithoutRelation($seller, $originProduct, $originCampaign, $newCampaign);

            if ($originProduct->id === $originCampaign->default_product_id) {
                $newCampaign->default_product_id = $clone->id;
                $newCampaign->thumb_url = $clone->thumb_url;
                $newCampaign->template_id = $clone->template_id;
            }

            $variants = $this->cloneVariants($seller, $clone);
            $clone->setRelation('variants', $variants);

            $_designs = $designs->filter(fn($d) => $d['product_id'] === $originProduct->id);
            if ( ! $this->applyDesignsService->handle($clone, $_designs, $seller)) {
                throw new RuntimeException(
                    'Apply design campaign failed.', self::ERR_APPLY_DESIGNS_CAMPAIGN_FAILED
                );
            }

            $_designs3D = $designs3D->filter(fn($m) => $m['product_id'] === $originProduct->id);
            if ( ! $this->applyMockupsService->handle($clone, $_designs3D, $seller)) {
                throw new RuntimeException(
                    'Apply mockup campaign failed.', self::ERR_APPLY_MOCKUPS_CAMPAIGN_FAILED
                );
            }

            $newProducts->add($clone);
        }

        return $newProducts;
    }

    /**
     * Chỉ clone sản phẩm của camp ra một sản phẩm của camp mà không có relation
     *
     * @param User $seller
     * @param Product $originProduct
     * @param Campaign $originCampaign
     * @param Campaign $newCampaign
     *
     * @return Product
     */
    public function cloneProductWithoutRelation(User $seller, Product $originProduct, Campaign $originCampaign, Campaign $newCampaign): Product
    {
        $clone = $this->replicateProduct($seller, $originProduct, $originCampaign, $newCampaign);

        $clone->save();

        return $clone;
    }

    /**
     *
     * @side-effect
     *
     * @return array
     */
    public function saleScore(): array
    {
        return getScoreProductBySales();
    }

    /**
     *
     * @side-effect
     *
     * @param     int     $campaignId
     *
     * @return bool
     */
    public function deleteProductsByCampaignId(int $campaignId, $seller): bool
    {
        return Product::query()
            ->onSellerConnection($seller)
            ->where('campaign_id', $campaignId)
            ->delete();
    }

    /**
     * @side-effect
     *
     * @param int $campaignId
     * @param $seller
     * @return bool
     */
    public function deleteVariantsByCampaignId(int $campaignId, $seller): bool
    {
        return ProductVariant::query()
            ->onSellerConnection($seller)
            ->where('campaign_id', $campaignId)
            ->delete();
    }

    /**
     * @side-effect
     *
     * @param     int     $campaignId
     *
     * @return bool
     */
    public function deleteUpsellByCampaignId(int $campaignId, int $sellerId): bool
    {
        return Upsell::deleteByProductId($campaignId, $sellerId);
    }

    /**
     * @param     \App\Models\Campaign     $new
     * @param     string                   $collectionName
     * @param     int                      $sellerId
     *
     * @return bool
     */
    public function attachCampaignToCollection(Campaign $new, string $collectionName, int $sellerId): bool
    {
        $collection = CollectionSellerController::createCollection(
            $collectionName, $sellerId
        );

        return CampaignController::addCampaignToCollection(
            $new->id, $sellerId, $collection['collection_id']
        );
    }

    /**
     * @param     \App\Models\Campaign     $fromCampaign
     * @param     \App\Models\Campaign     $camp
     *
     * @return void
     */
    public function cloneCollections(Campaign $fromCampaign, Campaign $camp): void
    {
        CampaignService::cloneCollections($fromCampaign->id, $camp->id);
    }

    /**
     * @param     \App\Models\Campaign     $fromCampaign
     * @param     \App\Models\Campaign     $camp
     *
     * @return void
     */
    public function cloneStores(Campaign $fromCampaign, Campaign $camp): void
    {
        CampaignService::cloneStores($fromCampaign->id, $camp->id);
    }

    /**
     * @param     \App\Models\Campaign     $fromCampaign
     * @param     \App\Models\Campaign     $camp
     *
     * @return void
     */
    public function cloneUpsell(Campaign $fromCampaign, Campaign $camp, int $sellerId): void
    {
        CampaignService::cloneUpsell($fromCampaign->id, $camp->id, $sellerId);
    }
}
