<?php

namespace Modules\Campaign\Services;

use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\ProductStatus;
use App\Http\Controllers\CampaignController;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Product;
use App\Models\User;

class LaunchService
{
    /**
     * @throws \Throwable
     */
    public function handle(Campaign $draftCampaign, User $seller): bool
    {
        $this->generateImageFilesCampaign($draftCampaign, $seller);

        if ($draftCampaign->tm_status === CampaignTrademarkStatusEnum::TM) {
            $this->blockCampaign($draftCampaign, $seller);
        } else {
            $this->updateCampaignScore($draftCampaign, $seller, $score = $this->saleScore());
            $this->updateCampaignProductScore($draftCampaign, $seller, $score['score']);
        }

        silent(static fn() => (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($draftCampaign->id, sellerId:$seller->id));

        return true;
    }

    /**
     * @param Campaign $draftCampaign
     * @param User $seller
     * @return void
     */
    public function generateImageFilesCampaign(Campaign $draftCampaign, User $seller): void
    {
        CampaignController::generateImageFilesCampaign($draftCampaign, $seller);
    }

    /**
     *
     * @side-effect
     *
     * @return array
     */
    public function saleScore(): array
    {
        return getScoreProductBySales();
    }

    /**
     * @param Campaign $draftCampaign
     * @param User $seller
     * @return void
     */
    public function blockCampaign(Campaign $draftCampaign, User $seller): void
    {
        Product::query()
            ->onSellerConnection($seller)
            ->where('id', $draftCampaign->id)
            ->orWhere('campaign_id', $draftCampaign->id)
            ->update(['status' => ProductStatus::BLOCKED]);
    }

    /**
     * @param     \App\Models\Campaign     $draftCampaign
     * @param     array                    $score
     *
     * @return void
     */
    public function updateCampaignScore(Campaign $draftCampaign, User $seller, array $score): void
    {
        Product::updateCampaignScore(
            $draftCampaign->id, $seller, $score
        );
    }

    /**
     * @param Campaign $draftCampaign
     * @param User $seller
     * @param                              $score1
     *
     * @return void
     */
    public function updateCampaignProductScore(Campaign $draftCampaign, User $seller, $score1): void
    {
        Product::updateScore(
            $draftCampaign->default_product_id, $seller, $score1 + 1
        );
    }
}
