<?php

namespace Modules\Campaign\Services;

use App\Enums\CacheKeys;
use App\Enums\CampaignPublicStatusEnum as PublicStatus;
use App\Enums\DiscordChannel;
use App\Models\Campaign;
use App\Models\Elastic;
use App\Models\User;
use App\Models\UserInfo;
use App\Services\SenPrintsAuth;
use Carbon\Carbon;
use Google\Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Filters\CampaignFilter;
use Modules\Campaign\Imports\BulkCampaignCSVImporter;
use Modules\Campaign\Models\ImportCampaignsData;
use Throwable;

class BulkCampaignV3Service
{
    public function __construct(
        readonly private ListDesignFromGoogleDriverFolderService $listDesignFromGoogleDriverFolderService
    ) {

    }

    /**
     * @param array $payload
     * @param User $seller
     *
     * @return void
     * @throws Exception
     * @throws \Google\Service\Exception
     */
    public function importBulkCampaignViaGoogleDriverFolderUrl(array $payload, User $seller): void
    {
        $data = [...$payload, 'rows' => $this->listDesignFromGoogleDriverFolderService->handle($payload['url'])];
        $this->store($data, $seller);
    }

    /**
     * @param $file
     *
     * @return array
     */
    public function previewBulkCampaignViaCSV($file): array
    {
        $importer = new BulkCampaignCSVImporter();
        Excel::import($importer, $file);

        return [
            'valid' => $importer->valid(),
            'errors' => $importer->errors()
        ];
    }

    public function importBulkCampaignViaCSV(array $payload, User $seller): array
    {
        $importer = new BulkCampaignCSVImporter();
        Excel::import($importer, $payload['file']);

        if ($importer->errors()->isNotEmpty()) {
            return [
                'valid' => $importer->valid(),
                'errors' => $importer->errors()
            ];
        }

        $data = [
            'campaign_id' => $payload['campaign_id'],
            'options' => json_decode($payload['options'], true, 512, JSON_THROW_ON_ERROR),
            'rows' => $importer->valid(),
        ];

        $this->store($data, $seller);

        return [];
    }

    /**
     * @param $origin
     * @param $clone
     *
     * @return mixed
     */
    public function previewBulkCampaignViaDesignFiles($origin, $clone): mixed
    {
        $seller = currentUser()->getInfoAccess();
        $fromCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', data_get($origin, 'campaign_id'))->firstOrFail();

        $service = app(BulkService::class, [
            'fromCampaign' => $fromCampaign,
            'name' => $clone['name'],
            'designs' => collect($clone['designs']),
            'designs3D' => collect($clone['designs3D']),
            'slug' => $clone['slug'],
            'collection' => $clone['collection']
        ]);

        return app()->call([$service, 'handle']);
    }

    /**
     * @param     array        $IDs
     * @param     int|null     $sellerId
     * @param     bool         $force
     *
     * @return \Illuminate\Support\Collection
     */
    public function retry(array $IDs, ?int $sellerId, bool $force = false): Collection
    {
        return ImportCampaignsData::multiSpaceRetryable($sellerId, $force)
            ->whereIn('id', $IDs)
            ->get()
            ->each(static fn(ImportCampaignsData $r) => $r->start())
            ->pluck('status', 'id');
    }

    /**
     * Xoá các dòng dữ liệu import campaign theo ID và seller ID
     *
     * @param     array        $IDs
     * @param     int|null     $sellerId
     *
     * @return int
     */
    public function remove(array $IDs, ?int $sellerId): int
    {
        return ImportCampaignsData::multiSpaceRemovable($sellerId)
            ->whereIn('id', $IDs)
            ->delete();
    }

    /**
     * @param     array        $IDs
     * @param     int|null     $sellerId
     *
     * @return \Illuminate\Support\Collection
     */
    public function start(array $IDs, ?int $sellerId): Collection
    {
        return ImportCampaignsData::multiSpaceStartable($sellerId)
            ->whereIn('id', $IDs)
            ->get()
            ->each(static fn(ImportCampaignsData $r) => $r->start())
            ->pluck('status', 'id');
    }

    /**
     * @param     array        $IDs
     * @param     int|null     $sellerId
     *
     * @return \Illuminate\Support\Collection
     */
    public function stop(array $IDs, ?int $sellerId): Collection
    {
        return ImportCampaignsData::multiSpaceStopable($sellerId)
            ->whereIn('id', $IDs)
            ->get()
            ->each(static fn(ImportCampaignsData $r) => $r->stop())
            ->pluck('status', 'id');
    }

    /**
     * @param     int     $sellerId
     *
     * @return void
     */
    public function startAll(int $sellerId): void
    {
        (new UserInfo())->activeBulkCampaign($sellerId);
    }

    /**
     * @param     int     $sellerId
     *
     * @return void
     */
    public function stopAll(int $sellerId): void
    {
        (new UserInfo())->inactiveBulkCampaign($sellerId);
    }

    /**
     * @param     \Illuminate\Http\Request     $request
     * @param     int|null                     $sellerId
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function searchOnDatabase(Request $request, ?int $sellerId = null): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return Campaign::filter($request->all(), CampaignFilter::class)
            ->where('seller_id', $sellerId)
            ->paginate($request->get('per_page', 15));
    }

    /**
     * @param     \Illuminate\Http\Request     $request
     * @param     int|null                     $sellerId
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     *
     * @throws Throwable
     */
    public function searchOnElastic(Request $request, ?int $sellerId = null): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $select = ['id', 'name', 'slug', 'thumb_url', 'status', 'seller_id', 'tm_status', 'public_status_detail',
            'created_at', 'product_type', 'system_product_type', 'personalized', 'system_type',
            'system_product_type', 'template_id', 'template_name', 'full_printed'
        ];

        $filter = [
            'seller_id' => $sellerId,
            'status' => to_list($request->get('status', ''))
        ];

        if ($productType = $request->get('product_type', [])) {
            $filter['product_types'] = to_list($productType, Str::lower(...));
        }

        [$from, $to] = $request->get('time', [null, null]);
        if ($from && $to) {
            $filter['date_time_range'] = [
                'created_at' => [
                    Carbon::parse($from)->startOfDay()->format('Y-m-d\TH:i:s'),
                    Carbon::parse($to)->endOfDay()->format('Y-m-d\TH:i:s'),
                ]
            ];
        }

        [$campaigns, $total] = (new Elastic())->getCampaign(
            $select,
            array_filter($filter),
            $perPage = $request->get('per_page', 15),
            $page = $request->get('page', 1)
        );

        return new Paginator(
            $campaigns,
            $total,
            $perPage,
            $page
        );
    }

    /**
     * @param     string     $type
     * @param     int        $take
     *
     * @return \Illuminate\Support\Collection
     */
    public function adminGetPendingRecord(string $type, int $take = 1): Collection
    {
        try {
            /** @var Collection|null $cachedIDs */
            $cachedIDs = cacheAlt()->get(CacheKeys::POOL_PENDING_IMPORT_CAMPAIGN_ROWS);
            if (! $cachedIDs || ! $cachedIDs->count()) {
                Artisan::call('campaign:handle-pending-imported-row --enqueue-design');
                $cachedIDs = cacheAlt()->get(CacheKeys::POOL_PENDING_IMPORT_CAMPAIGN_ROWS);
            }

            if (! $cachedIDs || ! $cachedIDs->count()) {
                return collect();
            }

            $results = $cachedIDs
                ->splice(0, $take)
                ->map(fn ($sellerId) => $this->sellerGetPendingRecord($type, currentUser($sellerId), 10))
                ->flatten(1);

            cacheAlt()->forever(CacheKeys::POOL_PENDING_IMPORT_CAMPAIGN_ROWS, $cachedIDs);
        } catch (Throwable $e) {
            logToDiscord([$e->getMessage(), $e->getTraceAsString()], DiscordChannel::ERROR);
        }

        return $results ?? collect();
    }

    /**
     * @param string $type
     * @param SenPrintsAuth|User $seller
     * @param int $take
     *
     * @return Collection
     */
    public function sellerGetPendingRecord(string $type, SenPrintsAuth|User $seller, int $take = 1): Collection
    {
        $sellerId = $seller?->id ?? $seller->getUserId();

        return ImportCampaignsData::takePendingRecord($type, $sellerId, $take)
            ->transform(static function (ImportCampaignsData $row) use ($seller) {
                $row->load([
                    'template_campaign' => function ($query) use ($seller) {
                        $query->select('id', 'slug', 'name', 'full_printed', 'system_type');
                        $query->onSellerConnection($seller);
                        $query->with(['products' => function ($query) use ($seller) {
                            $query->with('designs');
                            $query->with('template_mockups', function ($query) use ($seller) {
                                $query->on('mysql');
                            });
                        }]);
                    },
                ]);

                return $row;
            });
    }

    /**
     * @param     int     $id
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function getOriginalCampaign(int $id): Model|null
    {
        $seller = currentUser()->getInfoAccess();

        return Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', $id)
            ->where('seller_id', $seller->id)
            ->with('products.designs')
            ->first();
    }

    public function optionKeys(): array
    {
        return [
            'slug_suffix', 'slug_prefix', 'name_suffix',
            'name_prefix', 'campaign_id', 'shorten_slug',
            'indefinite_article'
        ];
    }

    /**
     * @param array $all
     * @param User $seller
     *
     * @return bool
     */
    public function store(array $all, User $seller): bool
    {
        $originCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->with('stores:id')
            ->where('id', $all['campaign_id'])
            ->firstOrFail();
        $options = Arr::only($all['options'], $this->optionKeys());
        $data = [
            'ip_address' => $all['ipAddress'] ?? null,
            'device_id' => $all['deviceId'] ?? null,
            'session_id' => $all['sessionId'] ?? null,
            'seller_id' => $seller->id,
            'template_campaign_id' => $originCampaign->id,
            'type' => $originCampaign->system_type . '_multi_space',
            'suffix' => $options['slug_suffix'],
            'prefix' => $options['slug_prefix'],
            'short_url' => $options['shorten_slug'],
            'name_suffix' => $options['name_suffix'],
            'name_prefix' => $options['name_prefix'],
            'indefinite_article' => $options['indefinite_article']
        ];
        $rows = [];

        foreach ($all['rows'] as $row) {
            [$onePrintSpace, $multiPrintSpace] = collect($row['designs'])
                ->map(fn($value, $key) => [
                    'original_url' => $value,
                    'new_url' => null,
                    'print_space' => $key
                ])
                ->partition(fn($value) => !$value['print_space']);

            $data['campaign_name'] = $row['campaign_name'] ?? '';
            $data['collection'] = $row['collection'] ?? '';
            $data['store_ids'] = json_encode($originCampaign->stores->pluck('id')->toArray());

            if ($multiPrintSpace->isNotEmpty()) {
                if ($multiPrintSpace->every(fn($value) => !empty($value['original_url']))) {
                    $_row = [...$data, 'designs' => $multiPrintSpace->values()->toJson()];
                }
            }

            else if ($onePrintSpace->isNotEmpty()) {
                $_row = [...$data, 'designs' => $onePrintSpace->values()->toJson()];
            }

            // create 'draft' campaign
            if (isset($_row)) {
                $_row['campaign_id'] = Campaign::query()
                    ->onSellerConnection($seller)
                    ->create([
                        'product_type' => $originCampaign->product_type,
                        'seller_id' => $seller->id,
                        'public_status' => in_array($originCampaign->public_status, [PublicStatus::YES, PublicStatus::APPROVED], true) ? PublicStatus::YES : PublicStatus::NO,
                    ])?->id;
                $rows[] = $_row;
            }
        }

        return ImportCampaignsData::query()->insert($rows);
    }
}
