<?php
namespace Modules\Campaign\Jobs;

use App\Enums\QueueName;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Campaign\Enums\ImportCampaignSystemStatusEnum;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\Campaign\Services\ImportCampaignDataService;
use Throwable;

class MultiSpaceRegularCampaignImportDesignUrlJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param     \Modules\Campaign\Models\ImportCampaignsData     $rawCampaign
     */
    public function __construct(private readonly ImportCampaignsData $rawCampaign)
    {
        $this->onQueue(QueueName::CRAWL_IMAGE);
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return 'import-design:' . $this->rawCampaign->id;
    }

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        try {
            ImportCampaignDataService::prepareDesignUrl($this->rawCampaign);
            $this->rawCampaign->system_status = ImportCampaignSystemStatusEnum::IMPORTED_DESIGN;
            $this->rawCampaign->save();
        } catch (Throwable $e) {
            logger()?->debug('Import design url failed', [
                'error' => $e->getMessage(),
                'campaign_id' => $this->rawCampaign->id,
            ]);

            throw $e;
        }
    }
}
