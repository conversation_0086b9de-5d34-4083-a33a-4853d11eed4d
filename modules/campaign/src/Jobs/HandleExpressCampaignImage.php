<?php

namespace Modules\Campaign\Jobs;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\FileTypeEnum;
use App\Http\Controllers\ExpressCampaignController;
use App\Http\Controllers\UploadController;
use App\Jobs\ScanCampaignCopyright;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleExpressCampaignImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var int
     */
    protected $campaign_id;

    /**
     * @var string
     */
    protected $file_url;

    /**
     * @var string
     */
    protected $parsedMockupIds;

    /**
     * @var string
     */
    protected $defaultPrintSpace;

    protected $seller;

    public function __construct($campaign_id, $file_url, $parsedMockupIds, $defaultPrintSpace, $sellerId)
    {
        $this->campaign_id = $campaign_id;
        $this->file_url = $file_url;
        $this->parsedMockupIds = $parsedMockupIds;
        $this->defaultPrintSpace = $defaultPrintSpace;
        $this->seller = User::query()->find($sellerId);
    }

    /**
     * @throws \Throwable
     */
    public function handle()
    {
        try {
            if (empty($this->campaign_id) || empty($this->file_url)) {
                $this->logToDiscord('[' . currentTime() . '] - Handle Express Campaign Job -> Not found campaign or file_url.');
                return;
            }

            $action = new ImgDirectLinkAction();
            $directLink = $action->handle($this->file_url);
            $loadImage = UploadController::uploadS3FromDirectLink($directLink, 'p/' . $this->campaign_id);
            if (empty($loadImage)) {
                $this->logToDiscord('[' . currentTime() . '] - Handle Express Campaign Job -> Can not upload file to S3. File url: ' . $this->file_url);
                return;
            }
            $inputUrl = $loadImage['path'];

            File::query()
                ->onSellerConnection($this->seller)
                ->where([
                    'campaign_id' => $this->campaign_id,
                    'type' => FileTypeEnum::DESIGN
                ])
                ->delete();
            $cloudMockupId = ExpressCampaignController::registerIdCloudMockup($inputUrl, $this->parsedMockupIds);
            File::query()
                ->onSellerConnection($this->seller)
                ->insert(
                    [
                        'campaign_id' => $this->campaign_id,
                        'file_url' => $this->file_url,
                        'file_url_2' => $inputUrl,
                        'type' => FileTypeEnum::DESIGN,
                        'type_detail' => $cloudMockupId,
                        'option' => 'print',
                        'print_space' => $this->defaultPrintSpace
                    ]
                );

            ExpressCampaign::query()
                ->onSellerConnection($this->seller)
                ->whereKey($this->campaign_id)
                ->update([
                    'thumb_url' => $inputUrl,
                    'sync_status' => Product::SYNC_DATA_STATS_ENABLED
                ]);

            ScanCampaignCopyright::dispatch($this->campaign_id, sellerId: $this->seller->id);
        } catch (\Exception $e) {
            $this->logToDiscord('[' . currentTime() . '] - Handle Express Campaign Image Job -> Error -> Campaign ID: #' . $this->campaign_id . '. ' . $e->getMessage());
        }
    }

    /**
     * @param $message
     * @return void
     */
    private function logToDiscord($message): void
    {
        logToDiscord($message, 'custom_campaign');
    }
}
