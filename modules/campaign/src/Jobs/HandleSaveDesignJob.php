<?php
namespace Modules\Campaign\Jobs;

use App\Http\Controllers\CampaignController;
use App\Models\Product;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Models\ImportCampaignsData;

class HandleSaveDesignJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $bulkId;

    /**
     * @param $bulkId
     */
    public function __construct($bulkId)
    {
        $this->bulkId = $bulkId;
        $this->onQueue(config('campaign.config.general.queue'));
    }

    /**
     * @return void
     */
    public function handle() {
        try {
            $bulk = ImportCampaignsData::query()->whereKey($this->bulkId)->first();
            if (!$bulk) {
                return;
            }
            $seller = User::query()->find($bulk->seller_id);
            if (empty($bulk)) {
                graylogInfo('[Job] 1. End save design. Bulk Id: ' . $this->bulkId, [
                    'category' => 'bulk_campaigns',
                    'type' => 'job',
                ]);
                return;
            }
            if ($bulk->status === ImportCampaignStatusEnum::COMPLETED) {
                graylogInfo('[Job] 2. End save design. Bulk Id: ' . $this->bulkId, [
                    'category' => 'bulk_campaigns',
                    'type' => 'job',
                ]);
                return;
            }
            $campaign = Product::query()
                ->onSellerConnection($seller)
                ->whereKey($bulk->campaign_id)->first();
            if (empty($campaign)) {
                $bulk->update([
                    'status' => ImportCampaignStatusEnum::FAILED,
                    'logs' => 'Campaign not found'
                ]);
                graylogInfo('[Job] Campaign not found. Bulk Id: ' . $bulk->id, [
                    'category' => 'bulk_campaigns',
                    'campaign_id' => $bulk->campaign_id,
                    'type' => 'job',
                ]);
                return;
            }
            if (!Str::isJson($bulk->mockups)) {
                $bulk->update([
                    'status' => ImportCampaignStatusEnum::FAILED,
                    'logs' => 'Mockups is invalid'
                ]);
                graylogInfo('[Job] Mockups is invalid. Bulk Id: ' . $bulk->id, [
                    'category' => 'bulk_campaigns',
                    'campaign_id' => $bulk->campaign_id,
                    'type' => 'job',
                ]);
                return;
            }
            $mockups = json_decode($bulk->mockups, true, 512, JSON_THROW_ON_ERROR);
            graylogInfo('[Job] Start save design. Bulk Id: ' . $bulk->id, [
                'category' => 'bulk_campaigns',
                'campaign_id' => $bulk->campaign_id,
                'type' => 'job',
            ]);
            /** @var CampaignController $controller */
            $controller = app(CampaignController::class);
            $request = new Request([
                'bulk_id' => $bulk->id,
                'seller_id' => $bulk->seller_id,
                'campaignId' => $bulk->campaign_id,
                'data' => $mockups,
                'storage' => 's3',
            ]);
            $response = $controller->saveCampaignDesignsBulkV2($request, true);
            graylogInfo('[Job] 4. End save design. Bulk Id: ' . $bulk->id, [
                'category' => 'bulk_campaigns',
                'campaign_id' => $bulk->campaign_id,
                'bulk_id' => $bulk->id,
                'type' => 'job',
                'request' => $request->toArray(),
                'response' => $response,
                'time' => now(),
            ]);
            return;
        } catch (\Throwable $exception) {
            logException($exception, 'HandleSaveDesignJob::handle:bulkId -> ' . $this->bulkId);
        }
    }
}
