<?php

namespace Modules\Campaign\Http\Controllers;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\CacheKeys;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\DesignStatusEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\DiscordChannel;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Events\OrderChangeDesign;
use App\Http\Controllers\Controller;
use App\Http\Controllers\UploadController;
use App\Http\Requests\SaveProductThumbnailRequest;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Collection;
use App\Models\Design;
use App\Models\File;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\Slug;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserInfo;
use App\Rules\IsSlugRule;
use App\Rules\ValidFilePath;
use App\Services\CampaignService;
use App\Services\Link;
use App\Services\SenPrintsAuth;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignSystemStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Campaign\Filters\ImportCampaignsDataFilter;
use Modules\Campaign\Http\Requests\BulkCampaign\GroupFileNameRequest;
use Modules\Campaign\Http\Requests\BulkCampaign\ImportCSVRequest;
use Modules\Campaign\Http\Requests\BulkCampaign\ImportViaGoogleDriverFolderRequest;
use Modules\Campaign\Http\Requests\BulkCampaign\ImportViaJSONRequest;
use Modules\Campaign\Http\Requests\BulkCampaign\MarkRowInvalidRequest;
use Modules\Campaign\Http\Requests\BulkCampaign\StoreProcessedRowRequest;
use Modules\Campaign\Http\Requests\BatchMockupUploadRequest;
use Modules\Campaign\Http\Requests\CustomMockupUploadRequest;
use Modules\Campaign\Http\Requests\ImportCampaignsRequest;
use Modules\Campaign\Http\Requests\UploadProductAopDesignRequest;
use Modules\Campaign\Http\Requests\UploadProductDesignRequest;
use Modules\Campaign\Jobs\DownloadCampaignImagesJob;
use Modules\Campaign\Jobs\HandleSaveDesignJob;
use Modules\Campaign\Jobs\SyncSlugJob;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\Campaign\Services\BulkCampaignV3Service;
use Modules\Campaign\Services\BulkService;
use Modules\Campaign\Services\CustomCampaignService;
use Modules\Campaign\Services\ImportCampaignDataService;
use Modules\Campaign\Services\ListDesignFromGoogleDriverFolderService;
use Modules\Campaign\Services\StoreBulkService;
use Modules\Campaign\Supports\Exports\ExportBulkCampaignsToExcel;
use Modules\Campaign\Supports\Imports\CampaignRegularDesignImport;
use Modules\Campaign\Supports\Imports\CustomCampaignImport;
use Ramsey\Uuid\Uuid as UuidValidator;
use RuntimeException;
use Throwable;

class CampaignController extends Controller
{
    use ApiResponse;

    public function __construct(private readonly CampaignService $campaignService)
    {

    }

    public function customMockupUpload($campaignId, CustomMockupUploadRequest $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $productId = $request->input('product_id');
        $filePath = $request->input('file_path');
        $printSpace = $request->input('print_space');
        $seller = User::query()->find($user->getUserId());
        //Delete default mockups of design
        File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $user->getUserId(),
                'product_id' => $productId,
                'status' => FileStatusEnum::ACTIVE
            ])
            ->whereNotNull('mockup_id')->delete();

        $query = File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $user->getUserId(),
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
                'status' => FileStatusEnum::ACTIVE
            ]);

        // Check if this is an AI campaign request and get prompt data
        $isAiCampaign = $request->input('is_ai_campaign', false);
        $promptData = $request->input('prompt_data');
        $designJson = null;

        // Get current mockup count
        $mockupNumbers = $query->clone()->where('product_id', $productId)->count();

        // Only check limit for non-AI campaigns
        if (!$isAiCampaign && $mockupNumbers > 5) {
            return $this->errorResponse('The maximum number of mockups is 5');
        }

        // If this is an AI campaign and we have prompt data, prepare the design_json
        if ($isAiCampaign && $promptData) {
            $designJson = json_encode($promptData);
        }

        DB::beginTransaction();
        try {
            // If this is an AI campaign, we need to delete the old mockups
            if ($isAiCampaign) {
                File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'type' => FileTypeEnum::IMAGE,
                        'type_detail' => FileRenderType::CUSTOM,
                        'seller_id' => $user->getUserId(),
                        'campaign_id' => $campaignId,
                        'product_id' => $productId,
                    ])
                    ->delete();
            }
            $newPath = 'p/' . $campaignId;
            if (is_array($filePath)) {
                // Only check limit for non-AI campaigns
                if (!$isAiCampaign && $mockupNumbers + count($filePath) > 5) {
                    return $this->errorResponse('The maximum number of mockups is 5');
                }
                foreach ($filePath as $path) {
                    $newThumbUrl = saveTempFileAws($path, $newPath);
                    $fileData = [
                        'file_url' => $newThumbUrl,
                        'type' => FileTypeEnum::IMAGE,
                        'type_detail' => FileRenderType::CUSTOM,
                        'seller_id' => $user->getUserId(),
                        'campaign_id' => $campaignId,
                        'product_id' => $productId,
                        'position' => $mockupNumbers + 1,
                        'is_default' => $mockupNumbers === 0 ? 1 : 0
                    ];

                    if ($printSpace !== null) {
                        $fileData['print_space'] = $printSpace;
                    }

                    // Add design_json if available
                    if ($designJson) {
                        $fileData['design_json'] = $designJson;
                    }

                    File::query()
                        ->onSellerConnection($seller)
                        ->insert($fileData);
                }
            } else {
                $newThumbUrl = saveTempFileAws($filePath, $newPath);
                $fileData = [
                    'file_url' => $newThumbUrl,
                    'type' => FileTypeEnum::IMAGE,
                    'type_detail' => FileRenderType::CUSTOM,
                    'seller_id' => $user->getUserId(),
                    'campaign_id' => $campaignId,
                    'product_id' => $productId,
                    'position' => $mockupNumbers + 1,
                    'is_default' => $mockupNumbers === 0 ? 1 : 0
                ];

                if ($printSpace !== null) {
                    $fileData['print_space'] = $printSpace;
                }

                // Add design_json if available
                if ($designJson) {
                    $fileData['design_json'] = $designJson;
                }

                File::query()
                    ->onSellerConnection($seller)
                    ->insert($fileData);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Upload mockup failed' . $exception->getMessage());
        }

        $mockups = $query->addSelect([
            'id',
            'product_id',
            'file_url',
            'design_json',
            'option',
            'type',
            'type_detail',
            'color_fillable',
            'print_space',
            'is_default',
            'position',
        ])->orderBy('position')
            ->where('product_id', $productId)
            ->get()
            ->map(function ($item) {
                $item->selected = false;
                return $item;
            });
        return $this->successResponse($mockups);
    }

    /**
     * Get mockup list
     * @param int $campaignId
     * @param Request $request
     * @return JsonResponse
     */
    public function getMockupList(int $campaignId, Request $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $seller = User::query()->find($user->getUserId());
        $productId = $request->get('product_id');
        $query = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'file_url',
                'design_json',
                'option',
                'type_detail',
                'color_fillable',
                'print_space',
                'position',
                'is_default'
            ])
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $user->getUserId(),
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
                'product_id' => $productId,
                'status' => FileStatusEnum::ACTIVE,
            ])
            ->orderBy('position');

        $mockups = $query->get();

        if ($mockups->count() === 0) {
            return $this->errorResponse();
        }

        // add default state for Vue data
        $mockups->each(fn($item) => $item->selected = false);

        return $this->successResponse($mockups);
    }

    public function uploadCustomMockupForPrintSpace(CustomMockupUploadRequest $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }

        $campaignId = $request->input('campaign_id');
        $productId = $request->input('product_id');
        $printSpace = $request->input('print_space');
        $filePath = $request->input('file_path');
        $sellerId = $request->input('seller_id');

        File::query()
            ->where('product_id', $productId)
            ->where('print_space', $printSpace)
            ->where('status', FileStatusEnum::ACTIVE)
            ->where('type', FileTypeEnum::IMAGE)
            ->update([
                'status' => FileStatusEnum::INACTIVE,
                'updated_at' => now(),
            ]);

        DB::beginTransaction();
        try {
            $newPath = 'p/' . $campaignId;
            $newThumbUrl = saveTempFileAws($filePath, $newPath);

            $fileId = File::query()->insertGetId([
                'seller_id' => $sellerId,
                'product_id' => $productId,
                'type_detail' => 'custom',
                'campaign_id' => $campaignId,
                'type' => FileTypeEnum::IMAGE,
                'print_space' => $printSpace,
                'file_url' => $newThumbUrl,
                'status' => FileStatusEnum::ACTIVE,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            DB::commit();
            return $this->successResponse('Upload mockup success', [
                'file_id' => $fileId,
                'url' => $newThumbUrl,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Upload mockup failed' . $e->getMessage());
        }
    }

    public function deleteMockup(Request $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }

        $campaignId = $request->input('campaign_id');
        $productId = $request->input('product_id');
        $printSpace = $request->input('print_space');

        try {
            $updated = File::query()
                ->where(function ($q) use ($campaignId, $productId) {
                    $q->where('campaign_id', $campaignId)
                        ->orWhere('product_id', $productId);
                })
                ->where('print_space', $printSpace)
                ->where('status', FileStatusEnum::ACTIVE)
                ->where('type', FileTypeEnum::IMAGE)
                ->update([
                    'status' => FileStatusEnum::INACTIVE,
                    'updated_at' => now(),
                ]);

            if ($updated === 0) {
                return $this->errorResponse('Delete mockup failed');
            }

            return $this->successResponse(null, 'Delete mockup success');
        } catch (\Exception $e) {
            return $this->errorResponse('Delete mockup failed' . $e->getMessage());
        }
    }

    /**
     * @param $campaignId
     * @param Request $request
     * @return JsonResponse
     */
    /**
     * Upload multiple mockups in a single batch operation
     *
     * @param int $campaignId
     * @param BatchMockupUploadRequest $request
     * @return JsonResponse
     */
    public function batchMockupUpload($campaignId, BatchMockupUploadRequest $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }

        $sellerId = $user->getUserId();
        $seller = User::query()->find($sellerId);
        $mockups = $request->input('mockups', []);
        $isAiCampaign = $request->boolean('is_ai_campaign', false);

        if (empty($mockups)) {
            return $this->errorResponse('No mockups provided');
        }

        // Group mockups by product_id for more efficient processing
        $mockupsByProduct = [];
        foreach ($mockups as $mockup) {
            $productId = $mockup['product_id'];
            if (!isset($mockupsByProduct[$productId])) {
                $mockupsByProduct[$productId] = [];
            }
            $mockupsByProduct[$productId][] = $mockup;
        }

        $results = [];

        DB::beginTransaction();
        try {
            // Process each product's mockups
            foreach ($mockupsByProduct as $productId => $productMockups) {
                // Delete existing mockups for this product if this is an AI campaign
                if ($isAiCampaign) {
                    File::query()
                        ->onSellerConnection($seller)
                        ->where([
                            'type' => FileTypeEnum::IMAGE,
                            'type_detail' => FileRenderType::CUSTOM,
                            'seller_id' => $sellerId,
                            'campaign_id' => $campaignId,
                            'product_id' => $productId,
                        ])
                        ->delete();
                }

                // Get the current position for new mockups
                $position = 1;
                if (!$isAiCampaign) {
                    // Count existing mockups only for non-AI campaigns
                    $position = File::query()
                        ->onSellerConnection($seller)
                        ->where([
                            'campaign_id' => $campaignId,
                            'seller_id' => $sellerId,
                            'product_id' => $productId,
                            'type' => FileTypeEnum::IMAGE,
                            'type_detail' => FileRenderType::CUSTOM,
                        ])
                        ->count() + 1;
                }

                // Process each mockup for this product
                $productResults = [];

                foreach ($productMockups as $mockup) {
                    $filePath = $mockup['file_path'];
                    $printSpace = $mockup['print_space'] ?? null;
                    $mockupType = $mockup['mockup_type'] ?? null;
                    $promptData = $mockup['prompt_data'] ?? null;

                    // Convert the S3 key to a full URL
                    $newPath = 'p/' . $campaignId;
                    $newThumbUrl = saveTempFileAws($filePath, $newPath);

                    // Prepare file data
                    $fileData = [
                        'file_url' => $newThumbUrl,
                        'type' => FileTypeEnum::IMAGE,
                        'type_detail' => FileRenderType::CUSTOM,
                        'seller_id' => $sellerId,
                        'campaign_id' => $campaignId,
                        'product_id' => $productId,
                        'position' => $position,
                        'is_default' => $position === 1 ? 1 : 0,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    if ($printSpace !== null) {
                        $fileData['print_space'] = $printSpace;
                    }

                    if ($mockupType !== null) {
                        $fileData['render_type'] = $mockupType;
                    }

                    // Add design_json if available
                    if ($promptData) {
                        $fileData['design_json'] = json_encode($promptData);
                    }

                    // Insert the file record
                    $fileId = File::query()
                        ->onSellerConnection($seller)
                        ->insertGetId($fileData);

                    // Add to results
                    $fileData['id'] = $fileId;
                    $productResults[] = $fileData;

                    // Increment position for next mockup
                    $position++;
                }

                $results[$productId] = $productResults;
            }

            DB::commit();
            return $this->successResponse($results);
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Upload mockups failed: ' . $exception->getMessage());
        }
    }

    public function customMockupRemove($campaignId, Request $request): JsonResponse
    {
        $user = currentUser();

        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $sellerId = $user->getUserId();
        $seller = currentUser()->getInfoAccess();
        $mockupId = $request->input('mockup_id');
        $productId = $request->input('product_id');
        $productIds = $request->input('product_ids', []);
        $deleteAll = $request->boolean('delete_all', false);

        $query = File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaignId,
                'seller_id' => $sellerId,
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
            ]);

        DB::beginTransaction();
        try {
            if ($deleteAll) {
                if (!empty($productIds) && is_array($productIds)) {
                    // Delete all mockups for multiple products
                    $query->clone()
                        ->whereIn('product_id', $productIds)
                        ->delete();
                } else if ($productId) {
                    // Delete all mockups for a single product
                    $query->clone()
                        ->where('product_id', $productId)
                        ->delete();
                } else {
                    return $this->errorResponse('Either product_id or product_ids must be provided with delete_all=true');
                }
            } else if ($mockupId) {
                // Delete a specific mockup
                $mockup = $query->clone()->findOrFail($mockupId);
                $mockup->delete();
            } else {
                return $this->errorResponse('Either mockup_id or delete_all with product_id/product_ids must be provided');
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Remove mockup failed');
        }

        $mockups = $query->addSelect([
            'id',
            'product_id',
            'file_url',
            'design_json',
            'option',
            'type',
            'type_detail',
            'color_fillable',
            'print_space',
            'position',
            'is_default',
        ])->where(['product_id' => $productId])
            ->orderBy('position')
            ->get()
            ->map(function ($item) {
                $item->selected = false;
                return $item;
            });
        return $this->successResponse($mockups);
    }

    /**
     * @param $campaignId
     * @param Request $request
     * @return JsonResponse
     */
    public function getProductImagesFromCampaign($campaignId, Request $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $seller = currentUser()->getInfoAccess();
        $campaignQuery = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $seller->id,
            ]);

        if (!$campaignQuery->exists()) {
            return $this->errorResponse('Invalid campaign');
        }
        $system_type = $request->get('system_type', ProductSystemTypeEnum::CUSTOM);
        $images = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'file_url',
                'file_url_2',
                'option',
                'design_id',
                'campaign_id',
                'product_id',
                'token',
                'type_detail'
            ])
            ->where([
                'type' => FileTypeEnum::IMAGE,
                'campaign_id' => $campaignId
            ])
            ->when(in_array($system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::AI_MOCKUP]), function ($query) {
                $query->where('type_detail', FileRenderType::CUSTOM);
            })
            ->when($system_type === ProductSystemTypeEnum::AOP, function ($query) {
                $query->where('print_space', 'default');
            })
            ->whereNull('option')
            ->orderByDesc('is_default')
            ->get()
            ->map(function ($image) use ($system_type) {
                $image->mapped_file_url = $image->file_url;
                if (in_array($system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::AI_MOCKUP])) {
                    $image->mapped_file_url = $image->type_detail === FileRenderType::CUSTOM ? $image->file_url : $image->file_url_2;
                }
                return $image;
            });

        return $this->successResponse($images);
    }

    /**
     * @param $campaignId
     * @param SaveProductThumbnailRequest $request
     * @return JsonResponse
     */
    public function saveProductsThumbnail($campaignId, SaveProductThumbnailRequest $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $data = $request->input('data');
        $default_thumb_url = $request->input('default_thumb_url');
        $data = collect($data)->map(function ($item) {
            $newData = [];
            if (!empty($item['product_id'])) {
                $newData['id'] = $item['product_id'];
            }
            if (!empty($item['campaign_id'])) {
                $newData['campaign_id'] = $item['campaign_id'];
            }
            if (!empty($item['thumb_url'])) {
                $newData['thumb_url'] = $item['thumb_url'];
            }
            if (!empty($item['default_color'])) {
                $newData['default_option'] = $item['default_color'];
            }
            return $newData;
        });

        try {
            DB::beginTransaction();
            $countUpdatedProducts = batch()->updateWithTwoIndex(new Product([], true), $data->toArray(), 'id', 'campaign_id');
            if (!empty($default_thumb_url)) {
                Product::query()->where('id', '=', $campaignId)->update(array(
                    'thumb_url' => $default_thumb_url
                ));
            }
            DB::commit();
            return $this->successResponse($countUpdatedProducts);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), 403);
        }
    }

    /**
     * @param ImportCampaignsRequest $request
     * @return JsonResponse
     */
    public function uploadImportFile(ImportCampaignsRequest $request)
    {
        $LIMIT_MOCKUP_PRODUCT_PER_CAMPAIGN = 10;
        $LIMIT_EXPRESS_PRODUCT_PER_CAMPAIGN = 15;
        $user = currentUser();
        $sellerId = $user->getUserId();
        $seller = User::find($sellerId);
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $file = $request->file('file');
        $system_type = $request->get('type');
        try {
            $rules = [];
            if ($system_type === ProductSystemTypeEnum::REGULAR) {
                $rules = [
                    'campaign_id' => 'required',
                    'prefix' => [
                        'nullable',
                        'string',
                        new IsSlugRule(),
                    ],
                    'suffix' => [
                        'nullable',
                        'string',
                        new IsSlugRule(),
                    ],
                ];
            } else if ($system_type === ProductSystemTypeEnum::CUSTOM || $system_type === ProductSystemTypeEnum::AOP) {
                $rules = [
                    'currency_code' => 'required',
                    'pricing_mode' => 'required',
                    'market_location' => 'required',
                    'prefix' => [
                        'nullable',
                        'string',
                        new IsSlugRule(),
                    ],
                    'suffix' => [
                        'nullable',
                        'string',
                        new IsSlugRule(),
                    ],
                ];
            } else if ($system_type === ProductSystemTypeEnum::EXPRESS || $system_type === ProductSystemTypeEnum::MOCKUP) {
                $rules = [
                    'campaign_id' => 'required',
                    'currency_code' => 'required',
                    'pricing_mode' => 'required',
                    'market_location' => 'required',
                    'prefix' => [
                        'nullable',
                        'string',
                        new IsSlugRule(),
                    ],
                    'suffix' => [
                        'nullable',
                        'string',
                        new IsSlugRule(),
                    ],
                ];
                if ($system_type === ProductSystemTypeEnum::EXPRESS) {
                    $rules['print_space'] = 'required|in:front,back';
                }
            }
            if (!empty($rules)) {
                $validator = Validator::make($request->all(), $rules);
                if ($validator->fails()) {
                    $message_errors = Arr::flatten($validator->messages()->get('*'));
                    return $this->errorResponse(implode("\n", $message_errors));
                }
            }
            $currency = $request->get('currency_code');
            $market_location = $request->get('market_location');
            $pricing_mode = $request->get('pricing_mode');
            $campaign_id = $request->get('campaign_id', 0);
            $products_template = null;
            $campaign_template = null;
            if ($system_type === ProductSystemTypeEnum::AOP) {
                Excel::import(new CustomCampaignImport(is_aop_campaign: true), $file);
                $campaigns = Excel::toArray(new CustomCampaignImport(is_aop_campaign: true), $file);
            } else {
                if (!empty($campaign_id)) {
                    $campaign_template = Campaign::query()
                        ->onSellerConnection($seller)
                        ->selectRaw('id, default_product_id')
                        ->where([
                            'id' => $campaign_id,
                            'seller_id' => $sellerId,
                            'product_type' => ProductType::CAMPAIGN_TEMPLATE
                        ])
                        ->whereIn('status', [ProductStatus::ACTIVE, ProductStatus::ARCHIVED])
                        ->first();
                    $products_template = CustomCampaignService::getListProductsTemplateOfCampaignTemplate($seller, $campaign_id);
                }

                Excel::import(new CustomCampaignImport(is_campaign_template: !empty($campaign_template)), $file);
                $campaigns = Excel::toArray(new CustomCampaignImport(is_campaign_template: !empty($campaign_template)), $file);

            }
            $campaigns = !empty($campaigns) ? $campaigns[0] : [];
            $result = [];
            if (empty($campaigns)) {
                return $this->successResponse($result);
            }
            $products = array();
            $limit_products = array();
            $message = array();
            if ($system_type === ProductSystemTypeEnum::EXPRESS && !$this->validateTotalProduct($campaigns, $LIMIT_EXPRESS_PRODUCT_PER_CAMPAIGN)) {
                return $this->errorResponse('Express campaign can only have ' . $LIMIT_EXPRESS_PRODUCT_PER_CAMPAIGN . ' products');
            }
            if ($system_type === ProductSystemTypeEnum::MOCKUP && !$this->validateTotalProduct($campaigns, $LIMIT_MOCKUP_PRODUCT_PER_CAMPAIGN)) {
                return $this->errorResponse('Mockup campaign can only have ' . $LIMIT_MOCKUP_PRODUCT_PER_CAMPAIGN . ' products');
            }

            foreach ($campaigns as $idx => $campaign) {
                if (empty($campaign['campaign_name'])) {
                    $rowCount = ((int)$idx + 2);
                    return $this->errorResponse('Row #' . $rowCount . ': Campaign name is empty or invalid.', 403);
                }
                if (empty($campaign['product_sku'])) {
                    $rowCount = ((int)$idx + 2);
                    return $this->errorResponse('Row #' . $rowCount . ': Product SKU is empty or invalid.', 403);
                }
                $slug = Str::slug($campaign['campaign_name']);
                $limit_products[$slug]['campaign_name'] = $campaign['campaign_name'];
                $limit_products[$slug]['total'] = ($limit_products[$slug]['total'] ?? 0) + 1;
                $index = $slug . '-' . Str::slug($campaign['product_sku']);
                $result[$index]['product_template'] = null;
                $result[$index]['selectedColors'] = [];
                $result[$index]['custom_mockups'] = [];
                $result[$index]['design_urls'] = [];
                $result[$index]['colors'] = [];
                if (!isset($products[$campaign['product_sku']])) {
                    $query = Product::query()->productBySku($campaign['product_sku']);
                    if (!empty($campaign_template)) {
                        $query->onSellerConnection($seller);
                        $query->where('campaign_id', $campaign_template->id);
                        $query->where('product_type', ProductType::PRODUCT_TEMPLATE);
                    } else {
                        $query->where('product_type', ProductType::TEMPLATE);
                    }
                    $product = $query->first();
                    $products[$campaign['product_sku']] = $product;
                } else {
                    $product = $products[$campaign['product_sku']];
                }
                if (empty($product) && empty($campaign_template)) {
                    $message[] = 'Product SKU "' . $campaign['product_sku'] . '" is not found';
                    continue;
                }
                $selectedColors = !empty($campaign['colors']) ? $campaign['colors'] : [];
                if (empty($campaign_template) && $system_type !== ProductSystemTypeEnum::AOP) {
                    if (!empty($selectedColors)) {
                        $selectedColors = explode(',', $selectedColors);
                    }
                }
                if (!empty($product)) {
                    $options = json_decode($product->options);
                    $colors = [];
                    // check if we have color option
                    $default_option = '';
                    if (isset($options->color) && count($options->color) > 0) {
                        foreach ($options->color as $color) {
                            $colors[] = [
                                'name' => $color,
                                'hex_code' => color2hex($color)
                            ];
                        }
                        if (!empty($selectedColors)) {
                            $i = 0;
                            foreach ($selectedColors as $selectedColor) {
                                $selectedColor = Str::lower(trim($selectedColor));
                                if (!Str::contains($selectedColor, $options->color)) {
                                    continue;
                                }
                                if (empty($default_option)) {
                                    $default_option = $selectedColor;
                                }
                                $result[$index]['selectedColors'][$i] = [
                                    'name' => $selectedColor,
                                    'hex_code' => color2hex($selectedColor)
                                ];
                                $i++;
                            }
                        } else {
                            $result[$index]['selectedColors'] = $colors;
                        }
                    }
                    $result[$index]['colors'] = $colors;
                    $result[$index]['product_template'] = $product;
                    $result[$index]['default_option'] = $default_option;
                    $result[$index]['base_cost'] = $product->base_cost;
                    $result[$index]['base_costs'] = $product->base_costs;
                    $result[$index]['extra_print_cost'] = $product->extra_print_cost;
                    $result[$index]['price'] = $campaign['price'] ?? $product->price;
                    $result[$index]['print_spaces'] = !empty($product->print_spaces) ? json_decode($product->print_spaces, true) : [];
                } else if (!empty($campaign_template) && !empty($products_template)) {
                    $product = $products_template->where('id', $campaign_template->default_product_id)->first()->toArray();
                    $options = json_decode($product['options']);
                    $colors = [];
                    // check if we have color option
                    $default_option = '';
                    if (isset($options->color) && count($options->color) > 0) {
                        foreach ($options->color as $color) {
                            if (empty($default_option)) {
                                $default_option = $color;
                            }
                            $colors[] = [
                                'name' => $color,
                                'hex_code' => color2hex($color)
                            ];
                        }
                    }
                    $result[$index]['selectedColors'] = $colors;
                    $result[$index]['colors'] = $colors;
                    $result[$index]['product_template'] = $product;
                    $result[$index]['default_option'] = $default_option;
                    $result[$index]['base_cost'] = $product['base_cost'];
                    $result[$index]['base_costs'] = $product['base_costs'];
                    $result[$index]['extra_print_cost'] = $product['extra_print_cost'];
                    $result[$index]['price'] = $product['price'];
                    $result[$index]['print_spaces'] = !empty($product['print_spaces']) ? json_decode($product['print_spaces'], true) : [];
                }
                $print_spaces = [];
                if (!empty($result[$index]['print_spaces'])) {
                    array_map(static function ($item) use (&$print_spaces) {
                        if (!empty($item['name'])) {
                            $print_spaces[$item['name']] = Str::lower($item['name']);
                        }
                        return $item;
                    }, $result[$index]['print_spaces']);
                    $print_spaces = array_values($print_spaces);
                }
                $mockup_urls = array_filter($campaign, function ($value, $key) {
                    return (Str::startsWith($key, 'mockup:') || Str::startsWith($key, 'mockup_')) && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);
                $j = 0;
                $thumb_url = '';
                if ($system_type === ProductSystemTypeEnum::AOP) {
                    $thumb_url = imgUrl($product->thumb_url, 'thumb');
                } else {
                    foreach ($mockup_urls as $mockup_url) {
                        if (str_contains($mockup_url, 'drive.google.com') || str_contains($mockup_url, 'dropbox.com')) {
                            $directLink = new Link($mockup_url);
                            $directLink = $directLink->toDirectLink();
                        } else {
                            $directLink = $mockup_url;
                        }
                        $result[$index]['custom_mockups'][$j] = $directLink;
                        if (empty($thumb_url)) {
                            $thumb_url = $directLink;
                        }
                        $j++;
                    }
                }
                $design_urls = array_filter($campaign, function ($value, $key) {
                    return (Str::startsWith($key, 'design:') || Str::startsWith($key, 'design_')) && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);
                $valid_designs = [];
                foreach ($design_urls as $key => $design_url) {
                    //hot fix
                    if (Str::contains($key, ':')) {
                        $exp_key = explode(':', $key);
                    } else {
                        $exp_key = explode('_', $key);
                    }

                    if (count($exp_key) !== 2) {
                        continue;
                    }
                    $print_name = Str::lower(trim($exp_key[1]));
                    if (str_contains($design_url, 'drive.google.com') || str_contains($design_url, 'dropbox.com')) {
                        $directLink = new Link($design_url);
                        $directLink = $directLink->toDirectLink();
                    } else {
                        $directLink = $design_url;
                    }
                    $valid_designs[$print_name]['name'] = $print_name;
                    $valid_designs[$print_name]['url'] = $directLink;
                }
                $result[$index]['design_urls'] = array_values($valid_designs);
                $result[$index]['thumb_url'] = $thumb_url;
                $result[$index]['name'] = $campaign['campaign_name'];
                $result[$index]['description'] = $campaign['campaign_desc'] ?? '';
                $result[$index]['collection'] = $campaign['collection'] ?? '';
                $result[$index]['currency'] = $currency;
                $result[$index]['market_location'] = $market_location;
                $result[$index]['pricing_mode'] = $pricing_mode;
            }
            $total_import_campaigns = count(array_keys($limit_products));
            if (!$this->checkCanCreate($user, $total_import_campaigns)) {
                $message[] = $this->getErrorMessageLimitCreating($user);
                return $this->errorResponse(implode(';', $message), 403);
            }
            foreach ($limit_products as $limit_product) {
                if ($limit_product['total'] > 10 && $system_type === ProductSystemTypeEnum::AOP) {
                    $message[] = 'Campaign name "' . $limit_product['campaign_name'] . '" has ' . $limit_product['total'] . ' products. The maximum number of products is 10.';
                }
            }
            if (!empty($message)) {
                return $this->errorResponse(implode(';', $message), 403);
            }
            return $this->successResponse(array_values($result));
        } catch (\Throwable $e) {
            $this->logToDiscord($e->getCode() . ': Seller upload ' . $system_type . ' campaign failed: ' . $e->getMessage() . ', File: ' . $e->getFile() . ', Line: ' . $e->getLine());

            if ($e->getCode() === 2002) {
                return $this->errorResponse('Import failed', 403);
            }

            return $this->errorResponse($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 403);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function importCustomCampaigns(Request $request)
    {
        $user = currentUser();
        $seller_id = $user->getUserId();
        $authId = $user->getAuthorizedAccountId();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $validator = Validator::make($request->all(), [
            'campaigns' => 'required',
            'currency' => 'required',
            'pricing_mode' => 'required',
            'market_location' => 'required',
            'storefronts' => ['nullable', 'array', 'max:50'],
            'prefix' => [
                'nullable',
                'string',
                new IsSlugRule(),
            ],
            'suffix' => [
                'nullable',
                'string',
                new IsSlugRule(),
            ],
            'system_type' => ['required', 'string', 'in:' . ProductSystemTypeEnum::CUSTOM . ',' . ProductSystemTypeEnum::AOP],
        ]);
        if ($validator->fails()) {
            $message_errors = Arr::flatten($validator->messages()->get('*'));
            return $this->errorResponse(implode("\n", $message_errors));
        }
        $system_type = $request->get('system_type');
        if ($system_type === ProductSystemTypeEnum::AOP) {
            $validator = Validator::make($request->all(), [
                'campaigns.*.product_template.sku' => ['required', 'string'],
                'campaigns.*.thumb_url' => ['required', 'string'],
                'campaigns.*.name' => ['required', 'string'],
                'campaigns.*.product_template' => ['required'],
                'campaigns.*.price' => ['required', 'min:1'],
                'campaigns.*.design_urls' => ['required', 'array', 'max:1'],
            ], [
                'campaigns.*.product_template.sku.required' => 'The product sku field is required.',
                'campaigns.*.thumb_url.required' => 'The product thumb field is required.',
                'campaigns.*.name.required' => 'The campaign name field is required.',
                'campaigns.*.product_template.required' => 'The product template field is required.',
                'campaigns.*.price.required' => 'The product price field is required.',
                'campaigns.*.price.min' => 'The product price must be at least :min.',
                'campaigns.*.design_urls.required' => 'The design urls field is required.',
                'campaigns.*.design_urls.max' => 'The design urls may not be greater than :max urls.'
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'campaigns.*.product_template.sku' => ['required', 'string'],
                'campaigns.*.thumb_url' => ['required', 'string'],
                'campaigns.*.name' => ['required', 'string'],
                'campaigns.*.product_template' => ['required'],
                'campaigns.*.price' => ['required', 'min:1'],
                'campaigns.*.custom_mockups' => ['required', 'array', 'max:50'],
                'campaigns.*.design_urls' => ['nullable', 'array', 'max:10'],
            ], [
                'campaigns.*.product_template.sku.required' => 'The product sku field is required.',
                'campaigns.*.thumb_url.required' => 'The product thumb field is required.',
                'campaigns.*.name.required' => 'The campaign name field is required.',
                'campaigns.*.product_template.required' => 'The product template field is required.',
                'campaigns.*.price.required' => 'The product price field is required.',
                'campaigns.*.custom_mockups.required' => 'The product custom mockups field is required.',
                'campaigns.*.price.min' => 'The product price must be at least :min.',
                'campaigns.*.custom_mockups.max' => 'The custom mockups may not be greater than :max mockups.',
                'campaigns.*.design_urls.max' => 'The design urls may not be greater than :max urls.'
            ]);
        }

        if ($validator->fails()) {
            $message_errors = Arr::flatten($validator->messages()->get('*'));
            return $this->errorResponse(implode("\n", $message_errors));
        }
        $seller = User::query()->find($seller_id);
        $campaigns = $request->get('campaigns');
        $prefix = $request->get('prefix', '');
        $suffix = $request->get('suffix', '');
        $currency_code = $request->get('currency');
        $pricing_mode = $request->get('pricing_mode');
        $market_location = $request->get('market_location');
        $storefronts = $request->get('storefronts');
        $campaigns_group = collect($campaigns)->groupBy('name');
        $imported = true;
        $imported_campaigns = [];
        $prefix = trim($prefix);
        $suffix = trim($suffix);
        $collections = array();
        $products_template = array();
        try {
            foreach ($campaigns_group as $campaign_name => $campaigns) {
                //Create campaign
                $campaign = $campaigns->first();
                $campaign_data = array();
                if (!empty($prefix)) {
                    $campaign_data['prefix'] = $prefix;
                }
                if (!empty($suffix)) {
                    $campaign_data['suffix'] = $suffix;
                }
                $product_template = $campaign['product_template'];
                if ($system_type !== ProductSystemTypeEnum::AOP) {
                    $product_template_options = json_decode($product_template['options']);
                    if (isset($product_template_options->color) && count($product_template_options->color) > 0) {
                        if (empty($campaign['default_option'])) {
                            return $this->errorResponse('The product default option field is required');
                        }
                        if (empty($campaign['selectedColors'])) {
                            return $this->errorResponse('The product colors field is required');
                        }
                    }
                }
                $main_product_id = $product_template['id'] ?? 0;
                $main_product_template = Product::query()->findOrFail($main_product_id);
                // Don't process product template if product is not active
                if (!empty($main_product_template) && $main_product_template->status !== ProductStatus::ACTIVE) {
                    continue;
                }
                $status = ProductStatus::DRAFT;
                if ($system_type === ProductSystemTypeEnum::AOP) {
                    $status = ProductStatus::PENDING;
                }
                $campaign_data['name'] = $campaign_name;
                $campaign_data['description'] = !empty($campaign['description']) ? $campaign['description'] : $campaign_name;
                $campaign_data['thumb_url'] = $campaign['thumb_url'];
                $campaign_data['currency_code'] = $currency_code;
                $campaign_data['pricing_mode'] = $pricing_mode;
                $campaign_data['market_location'] = $market_location;
                $campaign_data['status'] = $status;
                $campaign_data['system_type'] = $system_type;
                $campaign_data['public_status'] = CampaignPublicStatusEnum::NO;
                $campaign_id = CampaignService::createDraftCampaign($campaign_data);
                if (empty($campaign_id)) {
                    $imported = false;
                    continue;
                }
                $collection_name = trim($campaign['collection']);
                if (!empty($collection_name)) {
                    if (empty($collections[Str::slug($collection_name)])) {
                        $collection_id = 0;
                        $exists = Collection::query()
                            ->select(['id', 'name'])
                            ->firstWhere('name', $collection_name);

                        if ($exists) {
                            $collection_id = $exists['id'];
                        } else {
                            $newCollection = CampaignService::addCollection($collection_name);
                            if ($newCollection) {
                                $collection_id = $newCollection->id;
                            }
                        }
                        $collections[Str::slug($collection_name)] = $collection_id;
                    } else {
                        $collection_id = $collections[Str::slug($collection_name)];
                    }
                    CampaignService::addCampaignToCollection($campaign_id, $seller_id, $collection_id);
                }
                // Create products in campaign
                $default_product_id = 0;
                foreach ($campaigns as $product) {
                    $price = (float)$product['price'];
                    $default_option = $product['default_option'];
                    $product_id = $product['product_template']['id'];
                    if (empty($products_template[$product_id])) {
                        $product_template = Product::query()->find($product_id);
                    } else {
                        $product_template = $products_template[$product_id];
                    }
                    // Dont save product template if product is not active
                    if (empty($product_template) || $product_template->status !== ProductStatus::ACTIVE) {
                        continue;
                    }
                    $products_template[$product_id] = $product_template;
                    $colors = [];
                    if (isset($product['selectedColors'])) {
                        foreach ($product['selectedColors'] as $color) {
                            $colors[] = $color['name'];
                        }
                    }
                    $options = json_decode($product_template->options, true);
                    if (count($colors) > 0) {
                        $options['color'] = $colors;
                    }
                    $options = json_encode($options);
                    $sku = $product_template->sku;

                    $pricing = get_base_min_max_price_of_product($product_template, $market_location, $currency_code);
                    $min_price = $pricing['min_price'];
                    $max_price = $pricing['max_price'];

                    $status = ProductStatus::DRAFT;
                    if (empty($price) || $price < $min_price || $price > $max_price) {
                        $status = ProductStatus::ERROR;
                    }
                    if ($system_type === ProductSystemTypeEnum::AOP) {
                        $status = ProductStatus::PENDING;
                    }
                    $insertProduct = [
                        'name' => $product_template->name,
                        'default_option' => $default_option,
                        'campaign_id' => $campaign_id,
                        'seller_id' => $seller_id,
                        'auth_id' => $authId ?? $seller_id,
                        'template_id' => $product_template->id,
                        'product_type' => ProductType::PRODUCT,
                        'thumb_url' => $campaign['thumb_url'],
                        'price' => $price,
                        'currency_code' => $currency_code,
                        'pricing_mode' => $pricing_mode,
                        'market_location' => $market_location,
                        'status' => $status,
                        'options' => $options,
                        'sku' => $sku,
                        'base_cost' => $product_template->base_cost,
                        'shipping_cost' => $product_template->shipping_cost,
                        'description' => $product_template->description,
                        'description_ts' => $product_template->description_ts,
                        'priority' => (int)$product_template->priority,
                        'full_printed' => $product_template->full_printed,
                        'system_type' => $system_type,
                        'public_status' => CampaignPublicStatusEnum::NO,
                    ];
                    $product_id = Product::query()->onSellerConnection($seller)->insertGetId($insertProduct);
                    if (empty($product_id)) {
                        $imported = false;
                        continue;
                    }
                    if (empty($default_product_id)) {
                        $default_product_id = $product_id;
                        Campaign::query()->onSellerConnection($seller)->where([
                            'id' => $campaign_id,
                            'seller_id' => $seller_id,
                        ])->update([
                            'default_product_id' => $product_id,
                            'default_option' => $default_option,
                            'template_id' => $product_template->id,
                            'price' => $price,
                            'status' => $status,
                            'base_cost' => $product_template->base_cost,
                            'shipping_cost' => $product_template->shipping_cost,
                            'options' => $options,
                            'sku' => $sku,
                        ]);
                    }
                    // Create mockups of product
                    if (!empty($product['custom_mockups'])) {
                        foreach ($product['custom_mockups'] as $mockup) {
                            File::query()->onSellerConnection($seller)->insertGetId([
                                'type' => FileTypeEnum::IMAGE,
                                'product_id' => $product_id,
                                'campaign_id' => $campaign_id,
                                'file_url' => $mockup,
                                'type_detail' => FileRenderType::CUSTOM,
                                'seller_id' => $seller_id,
                            ]);
                        }
                    }
                    // Create designs of product
                    if (!empty($product['design_urls'])) {
                        if ($system_type === ProductSystemTypeEnum::AOP) {
                            $design_data = $product['design_urls'][0];
                            $array_where = [
                                'seller_id' => $seller_id,
                                'campaign_id' => $campaign_id,
                                'product_id' => $product_id,
                                'option' => 'print',
                                'print_space' => $design_data['name'],
                                'type' => FileTypeEnum::DESIGN,
                            ];
                            $design = File::query()->onSellerConnection($seller)->where($array_where)->first();
                            if (!empty($design)) {
                                $fileId = $design->id;
                                File::query()->onSellerConnection($seller)->whereKey($fileId)->update(array(
                                    'file_url' => null,
                                    'file_url_2' => $design_data['url'],
                                ));
                            } else {
                                File::query()->onSellerConnection($seller)->insertGetId(array_merge($array_where, [
                                    'file_url_2' => $design_data['url'],
                                    'campaign_id' => $campaign_id,
                                ]));
                            }
                        } else {
                            $print_spaces = [];
                            array_map(static function ($item) use (&$print_spaces) {
                                if (!empty($item['name'])) {
                                    $print_spaces[$item['name']] = Str::lower($item['name']);
                                }
                                return $item;
                            }, !empty($product_template->print_spaces) ? json_decode($product_template->print_spaces, true) : []);
                            $print_spaces = array_values($print_spaces);
                            $designs = [];
                            $full_printed = (int)$product_template->full_printed;
                            foreach ($product['design_urls'] as $design) {
                                if ($design['name'] === 'default') {
                                    if ($full_printed === ProductPrintType::PRINT_2D) {
                                        $designs[$print_spaces[0]] = [
                                            'type' => FileTypeEnum::DESIGN,
                                            'product_id' => $product_id,
                                            'campaign_id' => $campaign_id,
                                            'file_url' => $design['url'],
                                            'option' => 'print',
                                            'print_space' => $print_spaces[0],
                                            'seller_id' => $seller_id,
                                        ];
                                    } else {
                                        foreach ($print_spaces as $print_space) {
                                            $designs[$print_space] = [
                                                'type' => FileTypeEnum::DESIGN,
                                                'product_id' => $product_id,
                                                'campaign_id' => $campaign_id,
                                                'file_url' => $design['url'],
                                                'option' => 'print',
                                                'print_space' => $print_space,
                                                'seller_id' => $seller_id,
                                            ];
                                        }
                                    }
                                } else {
                                    foreach ($print_spaces as $print_space) {
                                        if (Str::contains($design['name'], $print_space)) {
                                            $designs[$print_space] = [
                                                'type' => FileTypeEnum::DESIGN,
                                                'product_id' => $product_id,
                                                'campaign_id' => $campaign_id,
                                                'file_url' => $design['url'],
                                                'option' => 'print',
                                                'print_space' => $print_space,
                                                'seller_id' => $seller_id,
                                            ];
                                            break;
                                        }
                                    }
                                }
                            }
                            File::query()->onSellerConnection($seller)->insert(array_values($designs));
                        }
                    }
                    if (empty($imported_campaigns[$campaign_id])) {
                        $imported_campaigns[$campaign_id] = !empty($product['design_urls']);
                    }
                }
            }
            if (!$imported) {
                return $this->errorResponse('Can not import your campaigns to our system. Please check your data again.');
            }
            if (!empty($imported_campaigns)) {
                // Delete all stores of campaign from store campaign
                $campaign_ids = array_keys($imported_campaigns);
                StoreProduct::query()->whereIn('product_id', $campaign_ids)->delete();
                $totalIds = 0;
                $totalRows = 0;
                // Add store id to campaign id
                if (!empty($storefronts)) {
                    $totalIds = count($storefronts);
                    // Get total found by list id
                    $totalRows = Store::query()
                        ->whereIn('id', $storefronts)
                        ->where('seller_id', $seller_id)
                        ->count();
                }
                dispatch(new SyncProductsToElasticSearchJob($campaign_ids));
                foreach ($imported_campaigns as $campaign_id => $has_design) {
                    if ($totalIds > 0 && $totalRows > 0 && $totalIds === $totalRows) {
                        $data = [];
                        foreach ($storefronts as $storeId) {
                            $data[$storeId] = [
                                'store_id' => $storeId,
                                'product_id' => $campaign_id
                            ];
                        }
                        StoreProduct::query()->insertOrIgnore($data);
                    }
                    dispatch(new DownloadCampaignImagesJob($campaign_id, $seller_id, $has_design))->onQueue(config('campaign.config.general.queue'));
                }
            }
            return $this->successResponse([], 'Your campaigns was imported successfully.');
        } catch (\Exception $exception) {
            $this->logToDiscord('Import custom campaigns failed. Error: ' . $exception->getMessage());
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function importMockupsExpressCampaigns(Request $request)
    {
        $user = currentUser();
        $seller_id = $user->getUserId();
        $seller = $user->getInfoAccess();
        $authId = $user->getAuthorizedAccountId();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $validator = Validator::make($request->all(), [
            'campaigns' => 'required',
            'currency' => 'required',
            'pricing_mode' => 'required',
            'market_location' => 'required',
            'prefix' => [
                'nullable',
                'string',
                new IsSlugRule(),
            ],
            'suffix' => [
                'nullable',
                'string',
                new IsSlugRule(),
            ],
            'storefronts' => ['nullable', 'array', 'max:50'],
        ]);
        if ($validator->fails()) {
            $message_errors = Arr::flatten($validator->messages()->get('*'));
            return $this->errorResponse(implode("\n", $message_errors));
        }
        $validator = Validator::make($request->all(), [
            'campaigns.*.product_template.sku' => ['required', 'string'],
            'campaigns.*.thumb_url' => ['required', 'string'],
            'campaigns.*.name' => ['required', 'string'],
            'campaigns.*.product_template' => ['required'],
            'campaigns.*.custom_mockups' => ['required', 'array', 'max:50'],
        ], [
            'campaigns.*.product_template.sku.required' => 'The product sku field is required.',
            'campaigns.*.thumb_url.required' => 'The product thumb field is required.',
            'campaigns.*.name.required' => 'The campaign name field is required.',
            'campaigns.*.product_template.required' => 'The product template field is required.',
            'campaigns.*.custom_mockups.required' => 'The product custom mockups field is required.',
            'campaigns.*.custom_mockups.max' => 'The custom mockups may not be greater than :max mockups.'
        ]);

        if ($validator->fails()) {
            $message_errors = Arr::flatten($validator->messages()->get('*'));
            return $this->errorResponse(implode("\n", $message_errors));
        }
        $campaigns = $request->get('campaigns');
        $prefix = $request->get('prefix', '');
        $suffix = $request->get('suffix', '');
        $currency_code = $request->get('currency');
        $pricing_mode = $request->get('pricing_mode');
        $market_location = $request->get('market_location');
        $storefronts = $request->get('storefronts');
        $templateCampaignId = $request->get('template_campaign_id', null);
        $imported = true;
        $imported_campaigns = [];
        $prefix = trim($prefix);
        $suffix = trim($suffix);
        try {
            foreach ($campaigns as $campaign) {
                $campaign_data = array();
                if (!empty($prefix)) {
                    $campaign_data['prefix'] = $prefix;
                }
                if (!empty($suffix)) {
                    $campaign_data['suffix'] = $suffix;
                }
                $main_product_id = $campaign['product_template']['id'] ?? 0;
                $main_product_template = Product::query()
                    ->onSellerConnection($seller)
                    ->whereKey($main_product_id)->first();
                if (!$main_product_template) {
                    $main_product_template = Product::query()->whereKey($main_product_id)->first();
                }
                // Don't process product template if product is not active
                if (empty($main_product_template) || $main_product_template->status !== ProductStatus::ACTIVE) {
                    continue;
                }

                $campaign_data['name'] = $campaign['name'];
                $campaign_data['description'] = !empty($campaign['description']) ? $campaign['description'] : $campaign['name'];
                $campaign_data['thumb_url'] = $campaign['thumb_url'];
                $campaign_data['currency_code'] = $currency_code;
                $campaign_data['pricing_mode'] = $pricing_mode;
                $campaign_data['market_location'] = $market_location;
                $campaign_data['template_id'] = $main_product_template->campaign_id;
                $campaign_data['default_product_id'] = $main_product_template->id;
                $campaign_data['default_option'] = $main_product_template->default_option;
                $campaign_data['price'] = $main_product_template->price;
                $campaign_data['old_price'] = $main_product_template->old_price;
                $campaign_data['base_cost'] = $main_product_template->base_cost;
                $campaign_data['shipping_cost'] = $main_product_template->shipping_cost;
                $campaign_data['options'] = $main_product_template->options;
                $campaign_data['sku'] = $main_product_template->sku;
                $campaign_data['system_type'] = ProductSystemTypeEnum::MOCKUP;
                $campaign_data['product_type'] = ProductType::CAMPAIGN;
                $campaign_data['public_status'] = CampaignPublicStatusEnum::NO;
                $campaign_data['template_campaign_id'] = $templateCampaignId ?? null;
                $campaign_id = CampaignService::createDraftCampaign($campaign_data);
                if (empty($campaign_id)) {
                    $imported = false;
                    continue;
                }
                $collection_name = trim($campaign['collection']);
                if (!empty($collection_name)) {
                    $collection_id = 0;
                    $exists = Collection::query()
                        ->select(['id', 'name'])
                        ->firstWhere('name', $collection_name);

                    if ($exists) {
                        $collection_id = $exists['id'];
                    } else {
                        $newCollection = CampaignService::addCollection($collection_name);
                        if ($newCollection) {
                            $collection_id = $newCollection->id;
                        }
                    }
                    CampaignService::addCampaignToCollection($campaign_id, $seller_id, $collection_id);
                }
                // Create mockups of campaign
                foreach ($campaign['custom_mockups'] as $mockup) {
                    File::query()
                        ->onSellerConnection($seller)
                        ->create([
                            'type' => FileTypeEnum::IMAGE,
                            'campaign_id' => $campaign_id,
                            'file_url' => $mockup,
                            'type_detail' => FileRenderType::CUSTOM,
                            'seller_id' => $seller_id,
                        ]);
                }
                $imported_campaigns[] = $campaign_id;
            }
            if (!$imported) {
                return $this->errorResponse('Can not import your campaigns to our system. Please check your data again.');
            }
            if (!empty($imported_campaigns)) {
                SyncSlugJob::dispatchSync($imported_campaigns, $seller);
                // Delete all stores of campaign from store campaign
                StoreProduct::query()->whereIn('product_id', $imported_campaigns)->delete();
                $totalIds = 0;
                $totalRows = 0;
                // Add store id to campaign id
                if (!empty($storefronts)) {
                    $totalIds = count($storefronts);
                    // Get total found by list id
                    $totalRows = Store::query()
                        ->whereIn('id', $storefronts)
                        ->where('seller_id', $seller_id)
                        ->count();
                }
                foreach ($imported_campaigns as $campaign_id) {
                    (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaign_id, sellerId: $seller?->id);
                    if ($totalIds > 0 && $totalRows > 0 && $totalIds === $totalRows) {
                        $data = [];
                        foreach ($storefronts as $storeId) {
                            $data[$storeId] = [
                                'store_id' => $storeId,
                                'product_id' => $campaign_id
                            ];
                        }
                        StoreProduct::query()->insertOrIgnore($data);
                    }
                    dispatch(new DownloadCampaignImagesJob($campaign_id, $seller_id))->onQueue(config('campaign.config.general.queue'));
                }
            }
            return $this->successResponse([], 'Your campaigns was imported successfully.');
        } catch (\Exception $exception) {
            $this->logToDiscord('Import custom campaigns failed. Error: ' . $exception->getMessage());
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function listTemplateProducts(Request $request)
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $seller = User::find($sellerId);
        $validator = Validator::make($request->all(), [
            'campaign_id' => 'required',
        ]);
        if ($validator->fails()) {
            $message_errors = Arr::flatten($validator->messages()->get('*'));
            return $this->errorResponse(implode("\n", $message_errors));
        }
        $campaign_id = $request->get('campaign_id');
        $products = CustomCampaignService::getListProductsTemplateOfCampaignTemplate($seller, $campaign_id);
        return $this->successResponse($products);
    }

    /**
     * @param int $campaignId
     * @return JsonResponse
     */
    public function campaignDetail(int $campaignId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $mockupCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', $campaignId)
            ->with([
                'designs',
                'collections' => fn ($q) => $q->where('product_collection.seller_id', $seller->id),
                'stores'])
            ->firstOrFail();

        $queryFields = [
            'id',
            'market_location',
            'currency_code',
            'pricing_mode',
            'pre_discounted_price',
            'old_price',
        ];

        $templateCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select($queryFields)
            ->where('id', $mockupCampaign->template_id)
            ->with(['template_products'])
            ->first();

        if (!empty($templateCampaign)) {
            $templateCampaign->template_products->map(function ($product) {
                $options = json_decode($product->options);
                $colors = [];

                // check if we have color option
                if (isset($options->color) && count($options->color) > 0) {
                    foreach ($options->color as $color) {
                        $colors[] = [
                            'name' => $color,
                            'hex_code' => color2hex($color)
                        ];
                    }
                }
                $product->colors = $colors;
                $product->selected = false;
                return $product;
            });
        }
        return $this->successResponse([
            'campaign' => $mockupCampaign,
            'template' => $templateCampaign
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateMockupCampaign(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $authId = $user->getAuthorizedAccountId();

        $campaign_id = $request->input('campaign_id');
        $campaignTitle = $request->input('name');
        $campaignDescription = $request->input('description');
        $collections = $request->input('collections');
        $stores = $request->input('storefronts');
        $endTime = $request->input('end_time');
        $showCountdown = $request->input('show_countdown');
        $trackingCode = $request->input('tracking_code');
        $default_thumbnail_url = $request->input('default_thumbnail_url');
        $default_product_id = $request->input('default_product_id');

        $seller = User::query()->find($userId);
        $query = Campaign::query()
            ->onSellerConnection($seller)
            ->whereKey($campaign_id);
        if (!$query->exists()) {
            return $this->errorResponse('Not found', 404);
        }
        try {
            $campaignData = [
                'seller_id' => $userId,
                'auth_id' => $authId,
                'name' => $campaignTitle,
                'description' => $campaignDescription,
                'price' => '',
                'show_countdown' => $showCountdown,
                'end_time' => $endTime,
                'public_status' => CampaignPublicStatusEnum::NO,
                'tracking_code' => json_encode($trackingCode),
            ];

            if (!empty($default_product_id) && $this->isValidDefaultProductId($campaign_id, $default_product_id, $seller)) {
                $campaignData['default_product_id'] = $default_product_id;
            }

            $query->update($campaignData);
            $campaign = $query->with(['template_products'])->first();

            if ($campaign === null) {
                return $this->errorResponse('Not found', 404);
            }

            // remove old collection
            ProductCollection::query()
                ->where([
                    'product_id' => $campaign_id,
                    'seller_id' => $userId
                ])
                ->forceDelete();

            if (!empty($collections)) {
                $collectionIds = [];
                foreach ($collections as $collection) {
                    if (isset($collection['id'])) {
                        if (Collection::query()->where('id', $collection['id'])->exists()) {
                            $collectionIds[] = $collection['id'];
                        }
                    } elseif (isset($collection['name'])) {
                        $exists = Collection::query()
                            ->select(['id', 'name'])
                            ->firstWhere('name', $collection['name']);

                        if ($exists) {
                            $collectionIds[] = $exists['id'];
                        } else {
                            $newCollection = Collection::addCollection($collection['name']);

                            if ($newCollection) {
                                $collectionIds[] = $newCollection->id;
                            }
                        }
                    }
                }
                foreach ($collectionIds as $collectionId) {
                    CampaignService::addCampaignToCollection($campaign_id, $userId, $collectionId);
                }
            }

            StoreProduct::query()->where([
                'product_id' => $campaign_id
            ])->delete();

            if (!empty($stores)) {
                $stores = array_map('intval', $stores);
                $totalIds = count($stores);
                $totalRows = Store::query()
                    ->whereIn('id', $stores)
                    ->where('seller_id', $userId)
                    ->count();

                if ($totalIds === $totalRows) {
                    $data = [];
                    foreach ($stores as $storeId) {
                        $data[$storeId] = [
                            'store_id' => $storeId,
                            'product_id' => $campaign_id
                        ];
                    }
                    StoreProduct::query()->insertOrIgnore($data);
                }
            }

            if (!empty($default_thumbnail_url) && $default_thumbnail_url !== $campaign->thumb_url) {
                $campaign->thumb_url = $default_thumbnail_url;
            }

            $campaign->sync_status = 0;
            $campaign->save();
            return $this->successResponse([
                'campaign_id' => $campaign_id
            ]);
        } catch (\Throwable $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteProductDesign(Request $request)
    {
        $currentUser = currentUser();
        $currentUserId = $currentUser->getUserId();
        $seller = User::query()->find($currentUserId);
        $fileId = $request->get('file_id');
        $productId = $request->get('product_id');
        $orderProductId = $request->get('order_product_id');
        if (empty($fileId) || empty($productId)) {
            return $this->errorResponse('Data invalid');
        }
        if ($orderProductId && UuidValidator::isValid($fileId)) {
            $deleted = Design::query()->where([
                'id' => $fileId,
                'product_id' => $productId,
                'seller_id' => $currentUserId,
                'type' => DesignTypeEnum::PRINT,
                'order_product_id' => $orderProductId,
            ])->update(['status' => DesignStatusEnum::INACTIVE]);
        } else {
            $array_where = [
                'id' => $fileId,
                'type' => FileTypeEnum::DESIGN,
                'option' => 'print',
                'product_id' => $productId,
                'seller_id' => $currentUserId,
                'status' => FileStatusEnum::ACTIVE
            ];
            if ($currentUser->isAdmin()) {
                unset($array_where['seller_id']);
            }
            $deleted = File::query()
                ->onSellerConnection($seller)
                ->where($array_where)
                ->update([
                    'status' => FileStatusEnum::INACTIVE
                ]);
        }
        if (!$deleted) {
            $this->errorResponse('Can not delete your selected design.');
        }
        return $this->successResponse();
    }

    /**
     * @param $campaignId
     * @param UploadProductDesignRequest $request
     * @return JsonResponse
     */
    public function uploadProductDesign($campaignId, UploadProductDesignRequest $request): JsonResponse
    {
        $currentUser = currentUser();
        if (!$currentUser->isAdmin() && !$currentUser->isSeller()) {
            abort(401);
        }
        $currentUserId = $currentUser->getUserId();
        $seller = null;
        if ($currentUser->isSeller()) {
            $seller = User::query()->find($currentUserId);
        }
        $fileId = $request->input('file_id', 0);
        $productId = $request->input('product_id');
        $printSpace = $request->input('print_space');
        $filePath = $request->input('file_path');
        $orderProductId = $request->input('order_product_id');
        $orderProduct = null;
        if (!empty($orderProductId)) {
            $orderProduct = OrderProduct::query()
                ->when($currentUser->isSeller(), function ($query) use ($currentUserId) {
                    $query->where('seller_id', $currentUserId);
                })
                ->whereKey($orderProductId)
                ->first();
            if ($orderProduct) {
                $seller = User::query()->find($orderProduct->seller_id);
                if (!$orderProduct->campaign || $orderProduct->campaign->seller_id !== $seller->id) {
                    $campaign = Campaign::query()
                        ->onSellerConnection($seller)
                        ->select('id', 'slug', 'name', 'product_type', 'template_id', 'system_type')
                        ->find($orderProduct->campaign_id);
                    $orderProduct->setRelation('campaign', $campaign);
                }
                $isCampaignMockupCustom = $orderProduct->campaign && in_array($orderProduct->campaign->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
                $hasCustomOptions = $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION && ($orderProduct->isPrintTypeEmbroidery() || $isCampaignMockupCustom);
                if ($hasCustomOptions) {
                    $newPath = 'o/' . $orderProduct->order_id;
                    $newThumbUrl = moveFileAws($filePath, $newPath);
                    $fields = [
                        'product_id' => $orderProduct->product_id,
                        'order_id' => $orderProduct->order_id,
                        'order_product_id' => $orderProduct->id,
                        'seller_id' => $orderProduct->seller_id,
                        'print_space' => $printSpace,
                    ];
                    Design::query()
                        ->where($fields)
                        ->update(['status' => DesignStatusEnum::INACTIVE]);

                    $design = Design::query()->create([
                        'product_id' => $orderProduct->product_id,
                        'order_id' => $orderProduct->order_id,
                        'order_product_id' => $orderProduct->id,
                        'seller_id' => $orderProduct->seller_id,
                        'print_space' => $printSpace,
                        'file_url' => $newThumbUrl,
                        'type' => DesignTypeEnum::PRINT,
                        'status' => DesignStatusEnum::ACTIVE
                    ]);
                    OrderChangeDesign::dispatch($orderProduct->order_id, ($currentUser->getName() ?? $currentUser->getEmail()) . ' has uploaded a new design. Url: ' . imgUrl($newThumbUrl));
                    return $this->successResponse([
                        'url' => $newThumbUrl,
                        'file_id' => $design->id
                    ]);
                }
            }
        }
        if (!$seller) {
            $slugFromCampaign = Slug::query()->where('campaign_id', $campaignId)->first();
            if (!$slugFromCampaign) {
                return $this->errorResponse('Seller not found');
            }
            $seller = $slugFromCampaign->seller;
        }
        $array_where = [
            'product_id' => $productId,
            'option' => 'print',
            'print_space' => $printSpace,
            'type' => FileTypeEnum::DESIGN,
            'status' => FileStatusEnum::ACTIVE
        ];
        $design = File::query()
            ->onSellerConnection($seller)
            ->where($array_where)
            ->when(!empty($fileId), function ($q) use ($fileId) {
                $q->whereKey($fileId);
            })
            ->first();

        DB::beginTransaction();
        try {
            $newPath = 'p/' . $campaignId;
            $newThumbUrl = saveTempFileAws($filePath, $newPath);
            if (!empty($design)) {
                $fileId = $design->id;
                File::query()
                    ->onSellerConnection($seller)
                    ->whereKey($fileId)->update(array(
                        'file_url' => $newThumbUrl,
                        'file_url_2' => null,
                    ));
            } else {
                $fileId = File::query()
                    ->onSellerConnection($seller)
                    ->insertGetId(array_merge($array_where, [
                        'file_url' => $newThumbUrl,
                        'campaign_id' => $campaignId,
                        'seller_id' => $seller->id,
                    ]));
            }
            DB::commit();
            if ($orderProduct) {
                OrderChangeDesign::dispatch($orderProduct->order_id, ($currentUser->getName() ?? $currentUser->getEmail()) . ' has uploaded a new design. Url: ' . imgUrl($newThumbUrl));
            }
            return $this->successResponse([
                'url' => $newThumbUrl,
                'file_id' => $fileId
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Upload design failed' . $exception->getMessage());
        }
    }

    /**
     * @param $campaignId
     * @param UploadProductAopDesignRequest $request
     * @return JsonResponse
     */
    public function uploadProductAopDesign($campaignId, UploadProductAopDesignRequest $request): JsonResponse
    {
        $user = currentUser();
        if (!$user->isAdmin() && !$user->isSeller()) {
            abort(401);
        }
        $currentUser = currentUser();
        $currentUserId = $currentUser->getUserId();
        $seller = User::query()->find($currentUserId);
        $fileId = $request->input('file_id', 0);
        $productId = $request->input('product_id');
        $filePath = $request->input('file_path');
        $array_where = [
            'seller_id' => $currentUserId,
            'campaign_id' => $campaignId,
            'product_id' => $productId,
            'option' => 'print',
            'print_space' => PrintSpaceEnum::DEFAULT,
            'type' => FileTypeEnum::DESIGN,
        ];
        if ($currentUser->isAdmin()) {
            unset($array_where['seller_id']);
        }
        $design = File::query()
            ->onSellerConnection($seller)
            ->where($array_where)->when(!empty($fileId), function ($q) use ($fileId) {
                $q->whereKey($fileId);
            })->first();

        DB::beginTransaction();
        try {
            if (str_starts_with($filePath, 'tmp/')) {
                $newPath = 'p/' . $campaignId;
                $newThumbUrl = saveTempFileAws($filePath, $newPath);
                if (!empty($design)) {
                    File::query()
                        ->onSellerConnection($seller)
                        ->whereKey($design->id)->delete();
                }
                $fileId = File::query()
                    ->onSellerConnection($seller)
                    ->insertGetId(array_merge($array_where, [
                        'file_url' => $newThumbUrl,
                        'campaign_id' => $campaignId,
                    ]));
                DB::commit();
                return $this->successResponse([
                    [
                        'url' => $newThumbUrl,
                        'id' => $fileId
                    ]
                ]);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Upload design failed' . $exception->getMessage());
        }
        return $this->errorResponse('Upload design failed');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getDesignsFromFile(Request $request): JsonResponse
    {
        $file = $request->file('file');
        Excel::import(new CampaignRegularDesignImport(), $file);
        return $this->successResponse(Excel::toArray(new CampaignRegularDesignImport(), $file)[0], $file);
    }

    public function getDesignsFromFolder(Request $request): JsonResponse
    {
        $url = $request->get('folder_url');
        if (empty($url)) {
            return $this->errorResponse('Folder url is required');
        }

        $folderId = $this->extractFolderIdFromGoogleDriveUrl($url);
        if (empty($folderId)) {
            return $this->errorResponse('Invalid folder url');
        }
        $client = new \Google_Client();
        $client->setAuthConfig(storage_path('google-service-account-credentials.json'));
        $client->addScope(\Google_Service_Drive::DRIVE);
        $service = new \Google_Service_Drive($client);
        $files = [];
        $pageToken = null;
        do {
            $optParams = array(
                'pageSize' => 100,
                'fields' => 'nextPageToken, files(id, name)',
                'pageToken' => $pageToken,
                'q' => "'$folderId' in parents"
            );
            $response = $service->files->listFiles($optParams);
            foreach ($response->getFiles() as $file) {
                $file_id = $file->getId();
                [$collectionName, $campaignName] = $this->extractNameAndCollection($file->getName());
                $files[] = array(
                    'design_url' => "https://drive.google.com/file/d/$file_id/view?usp=drive_link",
                    'name' => $campaignName,
                    'collection' => $collectionName,
                );
            }
            $pageToken = $response->getNextPageToken();
        } while ($pageToken !== null);
        return $this->successResponse($files);
    }

    private function extractFolderIdFromGoogleDriveUrl($url)
    {
        $pattern = '/drive\.google\.com\/drive\/folders\/([a-zA-Z0-9_-]+)/';

        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        } else {
            return null;
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function listImportCampaign(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        if (currentUser()->isAdmin()) {
            $sellerId = $request->get('seller_id');
        } else {
            $sellerId = currentUser()->getUserId();
        }
        $seller = User::query()->find($sellerId);
        $importCampaigns = $this->campaignService->getListImportCampaign($request->all());
        $campaigns = [];
        $campaignTemplates = [];
        $importCampaignsWithPagination = $importCampaigns
            ->paginate($perPage)
            ->through(function ($importCampaign) use ($seller, &$campaigns, &$campaignTemplates) {
                return $this->campaignService->mapCampaignAndTemplateForImportCampaign($seller, $importCampaign, $campaigns, $campaignTemplates);
            });
        return $this->successResponse($importCampaignsWithPagination);
    }

    public function exportListImportCampaign(Request $request)
    {
        if (currentUser()->isAdmin()) {
            $sellerId = $request->get('seller_id');
        } else {
            $sellerId = currentUser()->getUserId();
        }
        $seller = User::query()->find($sellerId);
        $campaigns = [];
        $campaignTemplates = [];
        $importCampaigns = $this->campaignService->getListImportCampaign($request->all())
            ->get()
            ->map(function ($importCampaign) use ($seller, &$campaigns, &$campaignTemplates) {
                return $this->campaignService->mapCampaignAndTemplateForImportCampaign($seller, $importCampaign, $campaigns, $campaignTemplates);
            });
        $fileName = 'Senprints_bulk_campagins_export_' . Carbon::now()->toDateTimeLocalString() . '.csv';
        return Excel::download(new ExportBulkCampaignsToExcel($importCampaigns), $fileName);
    }

    public function getPendingRegularImport(Request $request): JsonResponse
    {
        $user = currentUser();
        $MESSAGE_LIMIT = 'You have reached the maximum number of campaigns you can create.';
        $limitUsers = CampaignService::listUserLimitCreateCampaign();
        if ($user->isSeller() && in_array($user->getUserId(), $limitUsers)) {
            return $this->errorResponse($MESSAGE_LIMIT);
        }
        $processId = $request->get('processId');
        $debug = $request->boolean('debug');
        $reload = cache()->has($request->get('sessionId'));
        try {
            $userInfo = UserInfo::query()->select('user_id')->where('key', UserInfo::KEY_BULK_CAMPAIGN_STATUS);
            $query = ImportCampaignsData::query()
                ->select('id', 'seller_id')
                ->when(!empty($processId), function ($q) use ($processId) {
                    $q->whereKey($processId);
                    $q->whereIn('status', [ImportCampaignStatusEnum::PROCESSING, ImportCampaignStatusEnum::PENDING]);
                }, function ($q) {
                    $q->where('status', ImportCampaignStatusEnum::PENDING);
                })
                ->when($user->isSeller(), function ($q) use ($user) {
                    $q->where('seller_id', $user->getUserId());
                })
                ->leftJoinSub($userInfo, 'user_info', function ($join) {
                    $join->on('import_campaigns_data.seller_id', '=', 'user_info.user_id');
                })
                ->where('type', ImportCampaignTypeEnum::REGULAR)
                ->when(!empty($limitUsers), function ($q) use ($limitUsers) {
                    $q->whereNotIn('seller_id', $limitUsers);
                })
                ->whereNull('user_info.user_id')
                ->orderBy('created_at')
                ->when($debug, function ($q) {
                    $q->limit(1)->ddRawSql();
                });
            $pendingQuery = clone $query;
            $pendingCampaignQuery = clone $query;
            $pendingCampaignQuery = $pendingCampaignQuery->whereNotNull('campaign_id');
            $pending = $pendingCampaignQuery->first();
            $total = $pendingCampaignQuery->count();
            if (!$pending) {
                $pendingQuery = $pendingQuery->whereNull('campaign_id');
                $pending = $pendingQuery->limit(20)->get()->shuffle()->first();
                $total = $pendingQuery->count();
                if (!$pending) {
                    return $this->successResponse([
                        'pending' => null,
                        'total' => 0,
                        'reason' => 'No pending process',
                        'reload' => $reload
                    ]);
                }
            }
            $seller = currentUser($pending->seller_id);
            if ($seller->getNumberCampaignCanCreate() <= 0) {
                User::query()->whereKey($seller->getUserId())->update(['hold_launch_campaign_at' => now()]);
                if ($user->isSeller()) {
                    throw new RuntimeException($MESSAGE_LIMIT);
                }
                $pending->status = ImportCampaignStatusEnum::PENDING;
                $pending->save();
                return $this->successResponse([
                    'pending' => null,
                    'total' => $total - 1,
                    'reason' => $MESSAGE_LIMIT,
                    'reload' => $reload
                ]);
            }
            $isPassed = $this->isPassedGetPendingRegularImport($user);
            if (!$isPassed) {
                return $this->successResponse([
                    'pending' => null,
                    'total' => 0,
                    'reason' => 'You have reached the maximum number of draft campaigns you can create in a minute.',
                    'reload' => $reload
                ]);
            }
            $result = $this->processPendingImportData($pending->id, $total, $seller, $request);
            return $this->successResponse([...$result, 'reload' => $reload]);
        } catch (Throwable $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param $user
     * @return bool
     * @throws \Throwable
     */
    private function isPassedGetPendingRegularImport($user)
    {
        $timeStartJobObject = SystemConfig::getCustomConfig(CacheKeys::CREATE_BULK_DRAFT_CAMPAIGNS);
        if ($timeStartJobObject) {
            $setting = optional($timeStartJobObject)->json_data;
            if (Str::isJson($setting)) {
                $setting = json_decode($setting, true, 512, JSON_THROW_ON_ERROR);
                $slowdown = (bool)data_get($setting, 'slowdown', false);
                $limitOnSeller = (int)data_get($setting, 'on_seller', 30);
                $limitOnTotal = (int)data_get($setting, 'on_total', 80);
                if ($slowdown) {
                    $countCampaignPendingDraft = ImportCampaignsData::query()
                        ->select('id')
                        ->when($user->isSeller(), function ($q) use ($user) {
                            $q->where('seller_id', $user->getUserId());
                        })
                        ->where('status', ImportCampaignStatusEnum::PENDING)
                        ->whereNotNull('campaign_id')
                        ->where('updated_at', '>=', now()->subMinute())
                        ->count();
                    if ($countCampaignPendingDraft >= $limitOnSeller) {
                        return false;
                    }
                    $countTotalCampaignPendingDraft = ImportCampaignsData::query()
                        ->select('id')
                        ->where('status', ImportCampaignStatusEnum::PENDING)
                        ->whereNotNull('campaign_id')
                        ->where('updated_at', '>=', now()->subMinute())
                        ->count();
                    if ($countTotalCampaignPendingDraft >= $limitOnTotal) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private function processPendingImportData($processId, $total, $seller, $request): array
    {
        $pending = ImportCampaignsData::query()->whereKey($processId)->first();
        if (!$pending) {
            return [
                'pending' => null,
                'total' => 0,
            ];
        }
        $deviceId = $request->get('deviceId');
        $sessionId = $request->get('sessionId');
        $ipAddress = $request->get('ipAddress');
        $userAgent = $request->header('user-agent');
        $pending->status = ImportCampaignStatusEnum::PROCESSING;
        $pending->device_id = $deviceId;
        $pending->session_id = $sessionId;
        $pending->ip_address = $ipAddress;
        $pending->user_agent = $userAgent;
        $pending->save();
        $design = json_decode($pending->designs, false);
        if (empty($design->original_url)) {
            $pending->logs = 'Missing design url';
            $pending->status = ImportCampaignStatusEnum::FAILED;
            $pending->save();
            return [
                'pending' => null,
                'total' => $total - 1,
            ];
        }
        if (empty($design->new_url)) {
            try {
                if (str_contains($design->original_url, 'drive.google.com')) {
                    //handle campaign name and collection
                    $client = new \Google_Client();
                    $client->setAuthConfig(storage_path('google-service-account-credentials.json'));
                    $client->addScope(\Google_Service_Drive::DRIVE);
                    $service = new \Google_Service_Drive($client);
                    $fileId = $this->extractFileIdFromGoogleDriveUrl($design->original_url);
                    $file = $service->files->get($fileId, array(
                        'fields' => 'name'
                    ));
                    [$collectionName, $campaignName] = $this->extractNameAndCollection($file->getName());

                    if (empty($pending->campaign_name)) {
                        if (empty($campaignName)) {
                            $pending->logs = 'Invalid file name';
                            $pending->status = ImportCampaignStatusEnum::FAILED;
                            $pending->save();
                            return [
                                'pending' => null,
                                'total' => $total - 1,
                            ];
                        }
                        $pending->campaign_name = $campaignName;
                    }
                    if (empty($pending->collection) && !empty($collectionName)) {
                        $pending->collection = $collectionName;
                    }

                    $action = new ImgDirectLinkAction();
                    $directLink = $action->handle($design->original_url);
                    $loadImage = UploadController::uploadS3FromDirectLink($directLink, 'tmp/');
                    if (empty($loadImage)) {
                        $pending->logs = 'File invalid ' . $design->original_url;
                        $pending->status = ImportCampaignStatusEnum::FAILED;
                        $pending->save();
                        return [
                            'pending' => null,
                            'total' => $total - 1,
                        ];
                    }
                    $design->new_url = $loadImage['path'];
                } else {
                    $design->new_url = $design->original_url;
                }
                $pending->designs = json_encode($design);
                $pending->save();
            } catch (\Exception $e) {
                $pending->logs = 'Handle file ' . $design->original_url . ' error: ' . $e->getMessage();
                $pending->status = ImportCampaignStatusEnum::FAILED;
                $pending->save();
                return [
                    'pending' => null,
                    'total' => $total - 1,
                ];
            }
        }
        $createdCampaign = null;
        if ($pending->campaign_id) {
            $newCampaign = Campaign::query()
                ->onSellerConnection($seller)
                ->whereKey($pending->campaign_id)
                ->with('products')
                ->first();
            if (!empty($newCampaign)) {
                $design = json_decode($pending->designs, false);
                $products = $this->correctPrintSpaceForMug($seller, $newCampaign->id, $newCampaign->products);
                $createdCampaign = [
                    'id' => $newCampaign->id,
                    'name' => $newCampaign->name,
                    'slug' => $newCampaign->slug,
                    'thumb_url' => $newCampaign->thumb_url,
                    'artwork_url' => !empty($design->artwork_url) ? $design->artwork_url : $design->new_url,
                    'new_products' => $products,
                ];
            } else {
                $pending->campaign_id = null;
                $pending->save();
            }
        }
        if ($pending->template_campaign_id) {
            $templateCampaign = Campaign::query()
                ->onSellerConnection($seller)
                ->whereKey($pending->template_campaign_id)
                ->with('products')
                ->first();
            if ($templateCampaign) {
                $this->correctPrintSpaceForMug($seller, $templateCampaign->id, $templateCampaign->products);
            }
            unset($templateCampaign);
        }
        return [
            'pending' => $pending,
            'total' => $total,
            'created' => $createdCampaign,
            'routine' => [],
        ];
    }

    /**
     * @param $seller
     * @param $campaignId
     * @param $products
     * @return mixed
     */
    private function correctPrintSpaceForMug($seller, $campaignId, $products)
    {
        $print_space = 'default';
        foreach ($products as $product) {
            /** @var Product $product */
            if ($product->sku !== 'ACBM15') {
                continue;
            }
            if (!empty($product->print_spaces) && str_contains($product->print_spaces, $print_space)) {
                $print_spaces = str_replace($print_space, '15oz', $product->print_spaces);
                $product->print_spaces = $print_spaces;
                Product::query()->onSellerConnection($seller)
                    ->where([
                        'id' => $product->id,
                        'campaign_id' => $campaignId,
                    ])
                    ->update([
                        'print_spaces' => $print_spaces,
                        'sync_status' => 0,
                    ]);
                File::query()->onSellerConnection($seller)
                    ->where([
                        'product_id' => $product->id,
                        'campaign_id' => $campaignId,
                        'type' => FileTypeEnum::DESIGN,
                        'print_space' => $print_space,
                    ])->update([
                        'print_space' => '15oz',
                    ]);
            }
        }
        return $products;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkRegularLog(Request $request)
    {
        $log = $request->get('log');
        $bulkId = $request->get('bulk_id');
        $user = currentUser();

        $bulk = ImportCampaignsData::query()
            ->whereKey($bulkId)
            ->when($user->isSeller(), function ($q) use ($user) {
                $q->where('seller_id', $user->getUserId());
            })
            ->where('type', ImportCampaignTypeEnum::REGULAR)
            ->firstOrFail();

        $bulk->status = ImportCampaignStatusEnum::FAILED;
        $bulk->logs = $log;
        $bulk->save();

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @param BulkCampaignV3Service $service
     * @return JsonResponse
     */
    public function bulkRegularRerun(Request $request, BulkCampaignV3Service $service)
    {
        $rules = [
            'bulk_id' => [
                Rule::requiredIf(function () use ($request) {
                    return empty($request->get('campaign_id'));
                })
            ],
            'campaign_id' => [
                Rule::requiredIf(function () use ($request) {
                    return empty($request->get('bulk_id'));
                })
            ]
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        $bulkId = $request->get('bulk_id');
        if (!empty($bulkId) && !is_array($bulkId)) {
            $bulkId = [$bulkId];
        }
        $campaignId = $request->get('campaign_id');
        if (!empty($campaignId) && !is_array($campaignId)) {
            $campaignId = [$campaignId];
        }
        $user = currentUser();
        $bulk = ImportCampaignsData::query()
            ->when(!empty($bulkId), function ($q) use ($bulkId) {
                $q->whereIn('id', $bulkId);
            })
            ->when(!empty($campaignId), function ($q) use ($campaignId) {
                $q->whereIn('campaign_id', $campaignId);
            })
            ->when($user->isSeller(), function ($q) use ($user) {
                $q->where('seller_id', $user->getUserId());
            })
            ->whereIn('type', [ImportCampaignTypeEnum::REGULAR, ImportCampaignTypeEnum::REGULAR_MULTI_SPACE])
            ->get();
        if ($bulk->isEmpty()) {
            return $this->errorResponse('Not found bulk campaign');
        }
        $multiSpaceId = [];
        $regularBulkId = [];
        $campaignId = [];
        foreach ($bulk as $item) {
            if ($item->type === ImportCampaignTypeEnum::REGULAR_MULTI_SPACE) {
                $multiSpaceId[] = $item->id;
                continue;
            }
            if ($item->status === ImportCampaignStatusEnum::PROCESSING) {
                if (now()->diffInMinutes($item->updated_at) < 5) {
                    continue;
                }

                if ($item->system_status === ImportCampaignSystemStatusEnum::SAVE_DESIGN) {
                    continue;
                }
            }
            $regularBulkId[] = $item->id;
            if (!empty($item->campaign_id)) {
                $campaignId[] = $item->campaign_id;
            }
        }

        if (empty($regularBulkId) && empty($multiSpaceId)) {
            return $this->errorResponse('Rerun failed.');
        }

        if (!empty($multiSpaceId)) {
            $service->retry($bulkId, $user->getUserId());
        }

        $log = 'Seller request rerun';
        if ($user->isAdmin()) {
            $log = 'Admin request rerun';
        }

        ImportCampaignsData::query()
            ->whereIn('id', $regularBulkId)
            ->update([
                'status' => ImportCampaignStatusEnum::PENDING,
                'system_status' => null,
                'mockups' => null,
                'logs' => $log,
            ]);

        $campaignId = Campaign::query()
            ->select('id')
            ->whereIn('id', $campaignId)
            ->where('status', CampaignStatusEnum::ACTIVE)
            ->get()
            ->pluck('id')
            ->toArray();

        if (!empty($campaignId)) {
            Product::query()
                ->filterByProductOrCampaignIds($campaignId)
                ->where('status', CampaignStatusEnum::ACTIVE)
                ->update([
                    'sync_status' => 0,
                    'status' => CampaignStatusEnum::DRAFT,
                ]);
            File::query()
                ->whereIn('campaign_id', $campaignId)
                ->update([
                    'status' => FileStatusEnum::INACTIVE,
                ]);
        }
        return $this->successResponse();
    }

    private function extractNameAndCollection(string $fileName): array
    {
        $pattern = '/^(?:\[(.*?)]\s*)?(.+?)\.([a-z0-9]+)$/i';
        if (preg_match($pattern, $fileName, $matches)) {
            $collectionName = $matches[1] ?? null;
            if (!empty($collectionName)) {
                $collectionName = trim($collectionName);
            }
            return [$collectionName, trim($matches[2])];
        }
        return [null, null];
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function storeImportCampaign(Request $request): JsonResponse
    {
        $bulkType = $request->get('system_type');
        $storeIds = $request->get('storefronts', []);
        $rules = $this->getValidationRulesForType($bulkType);
        $sellerId = null;
        if (currentUser()->isSeller()) {
            $sellerId = currentUser()->getUserId();
        }
        $seller = User::query()->find($sellerId);

        if (!empty($rules)) {
            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                $message_errors = Arr::flatten($validator->messages()->get('*'));
                return $this->errorResponse(implode("\n", $message_errors));
            }

            $data = $request->only(array_keys($rules));
            $data['seller_id'] = $sellerId;
            $data['type'] = $bulkType;

            if (empty($storeIds) && !empty($data['template_campaign_id'])) {
                $campaign = Campaign::query()->onSellerConnection($seller)->with(['stores:id'])->find($data['template_campaign_id']);
                if ($campaign) {
                    $storeIds = $campaign->stores->pluck('id')->toArray();
                    $data['market_location'] ??= $campaign->market_location;
                    $data['pricing_mode'] ??= $campaign->pricing_mode;
                    $data['currency'] ??= $campaign->currency_code;
                }
            }

            if ($bulkType === ProductSystemTypeEnum::REGULAR) {
                try {
                    $bulkInsertData = [];
                    $templateCampaignId = $data['template_campaign_id'];

                    if (!CampaignService::isCampaignBelongSeller($templateCampaignId, $seller)) {
                        return $this->errorResponse('You do not have permission to access this campaign');
                    }

                    $hasArchived = ImportCampaignDataService::checkArchivedProducts($templateCampaignId, $seller);
                    if ($hasArchived) {
                        return $this->errorResponse('One or more products in your campaign have been archived. Please use another campaign to proceed with the bulk action.');
                    }
                    foreach ($request->get('designs', []) as $design) {
                        unset($data['designs']);
                        $designUrl = $design['design_url'];
                        $data['designs'] = json_encode([
                            'original_url' => $designUrl,
                            'new_url' => null,
                        ], JSON_THROW_ON_ERROR);
                        if (!empty($design['campaign_name'])) {
                            $data['campaign_name'] = $design['campaign_name'];
                        }
                        if (!empty($design['collection'])) {
                            $data['collection'] = $design['collection'];
                        }
                        if (!empty($storeIds)) {
                            $data['store_ids'] = json_encode($storeIds);
                        }
                        $bulkInsertData[] = $data;
                    }
                    ImportCampaignsData::query()->insert($bulkInsertData);
                } catch (\Exception $e) {
                    return $this->errorResponse($e->getMessage());
                }
            } else if ($bulkType === ProductSystemTypeEnum::EXPRESS) {
                $bulkInsertData = [];
                foreach ($request->get('designs', []) as $design) {
                    unset($data['designs']);
                    $data['designs'] = $design['design_url'];
                    $data['campaign_name'] = $design['campaign_name'];
                    if (!empty($design['collection'])) {
                        $data['collection'] = $design['collection'];
                    }
                    if (!empty($storeIds)) {
                        $data['store_ids'] = json_encode($storeIds);
                    }
                    $bulkInsertData[] = $data;
                }
                ImportCampaignsData::query()->insert($bulkInsertData);
            } else if ($bulkType === ImportCampaignTypeEnum::MOCKUP) {
                try {
                    $bulkInsertData = [];
                    unset($data['campaigns']);
                    $campaigns = $request->get('campaigns', []);
                    foreach ($campaigns as $campaign) {
                        $data['campaign_description'] = $campaign['description'];
                        $data['campaign_name'] = $campaign['name'];
                        $data['collection'] = $campaign['collection'];
                        $data['mockups'] = json_encode($campaign['custom_mockups']);
                        $data['products'] = json_encode([
                            'template_id' => $campaign['template_id'],
                            'thumb_url' => $campaign['thumb_url'],
                        ], JSON_THROW_ON_ERROR);
                        if (!empty($storeIds)) {
                            $data['store_ids'] = json_encode($storeIds);
                        }
                        $bulkInsertData[] = $data;
                    }
                    ImportCampaignsData::query()->insert($bulkInsertData);
                } catch (\Exception $e) {
                    return $this->errorResponse($e->getMessage());
                }
            } else if (in_array($bulkType, [ImportCampaignTypeEnum::CUSTOM, ImportCampaignTypeEnum::AOP], true)) {
                try {
                    $bulkInsertData = [];
                    unset($data['campaigns']);
                    $products = $request->get('campaigns', []);
                    $campaigns = [];
                    foreach ($products as $product) {
                        $name = $product['name'];
                        $description = $product['description'];
                        $collection = $product['collection'];
                        unset($product['name'], $product['description'], $product['collection']);
                        if (!isset($campaigns[$name])) {
                            $campaigns[$name] = [
                                'description' => $description,
                                'collection' => $collection,
                            ];
                        }
                        $campaigns[$name]['products'][] = $product;
                    }

                    foreach ($campaigns as $key => $campaign) {
                        $data['campaign_description'] = $campaign['description'];
                        $data['campaign_name'] = $key;
                        $data['collection'] = $campaign['collection'];
                        $data['products'] = json_encode($campaign['products']);
                        if (!empty($storeIds)) {
                            $data['store_ids'] = json_encode($storeIds);
                        }
                        $bulkInsertData[] = $data;
                    }
                    ImportCampaignsData::query()->insert($bulkInsertData);
                } catch (\Exception $e) {
                    return $this->errorResponse($e->getMessage());
                }
            }
            return $this->successResponse();
        }

        return $this->errorResponse('Invalid systems type');
    }

    /**
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateImportCampaignForRegularCampaign(Request $request, int $id): JsonResponse
    {
        $importCampaign = ImportCampaignsData::query()
            ->whereKey($id)
            ->when(currentUser()->isSeller(), function ($q) {
                $q->where('seller_id', currentUser()->getUserId());
            })
            ->where('type', ImportCampaignTypeEnum::REGULAR)
            ->firstOrFail();

        $status = $request->get('status');
        if ($status === ImportCampaignStatusEnum::COMPLETED) {
            $campaignId = $request->get('campaign_id');
        } else {
            $campaignId = $importCampaign->campaign_id;
        }
        $log = $request->get('log');

        if ($status === ImportCampaignStatusEnum::PENDING && $importCampaign->status === ImportCampaignStatusEnum::PROCESSING && now()->diffInMinutes($importCampaign->updated_at) < 5) {
            return $this->errorResponse('Too soon to rerun');
        }

        $importCampaign->update([
            'status' => $status,
            'campaign_id' => $campaignId,
            'logs' => $log
        ]);
        return $this->successResponse();
    }

    private function getValidationRulesForType(string $bulkType): array
    {
        $baseRules = [
            'prefix' => ['nullable', 'string', new IsSlugRule()],
            'suffix' => ['nullable', 'string', new IsSlugRule()],
        ];

        return match ($bulkType) {
            ImportCampaignTypeEnum::REGULAR => array_merge($baseRules, [
                'template_campaign_id' => 'required',
                'designs' => 'required|array',
                'designs.*.design_url' => 'required',
                'designs.*.campaign_name' => 'nullable|string',
                'designs.*.collection' => 'nullable|string',
                'short_url' => 'boolean',
                'name_prefix' => 'nullable|string',
                'name_suffix' => 'nullable|string',
                'indefinite_article' => 'boolean',
            ]),
            ImportCampaignTypeEnum::EXPRESS => array_merge($baseRules, [
                'campaign_id' => 'required',
                'designs' => 'required|array',
                'designs.*.design_url' => 'required|url',
                'designs.*.campaign_name' => 'nullable|string',
                'designs.*.collection' => 'nullable|string',
                'short_url' => 'boolean',
                'print_space' => 'required|string',
            ]),
            ImportCampaignTypeEnum::CUSTOM, ImportCampaignTypeEnum::AOP => array_merge($baseRules, [
                'currency' => 'required',
                'pricing_mode' => 'required',
                'market_location' => 'required',
                'campaigns' => 'required|array',
            ]),
            ImportCampaignTypeEnum::MOCKUP => array_merge($baseRules, [
                'campaign_id' => 'required',
                'currency' => 'required',
                'pricing_mode' => 'required',
                'market_location' => 'required',
            ]),
            default => [],
        };
    }

    /**
     * @param $message
     * @return void
     */
    private function logToDiscord($message): void
    {
        logToDiscord($message, 'custom_campaign');
    }

    /**
     * @param int $importCampaigns
     * @return bool
     * @throws \Throwable
     */
    private function checkCanCreate(SenPrintsAuth $user, $importCampaigns = 0)
    {
        try {
            return ($user->getNumberCampaignCanCreate() - $importCampaigns) > 0;
        } catch (\Exception $exception) {
            $this->logToDiscord('Check can create campaign failed. Error: ' . $exception->getMessage());
            return false;
        }
    }

    /**
     * @param $user
     * @return string
     */
    private function getErrorMessageLimitCreating($user)
    {
        return 'You can only create ' . $user->getInfo()->campaign_limit . ' campaigns in 24 hours. Contact support to increase the limit.';
    }

    private function isValidDefaultProductId(int $campaignId, int $defaultProductId, $seller)
    {
        $mockupCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', $campaignId)
            ->firstOrFail();
        $templateProducts = Product::query()
            ->onSellerConnection($seller)
            ->where('campaign_id', $mockupCampaign->template_id)
            ->where('product_type', ProductType::PRODUCT_TEMPLATE)
            ->pluck('id');
        return $templateProducts->contains($defaultProductId);
    }

    private function validateTotalProduct($campaigns, int $limit): bool
    {
        $campaignCount = [];

        foreach ($campaigns as $item) {
            $campaignName = $item['campaign_name'];
            if (!isset($campaignCount[$campaignName])) {
                $campaignCount[$campaignName] = 0;
            }
            $campaignCount[$campaignName]++;
            if ($campaignCount[$campaignName] > $limit) {
                return false;
            }
        }

        return true;
    }

    public function readImage(Request $request)
    {
        $request->validate([
            'url' => [
                'required',
                'string',
                new ValidFilePath(true)
            ],
        ]);

        $url = $request->get('url');
        $isThumbnail = $request->boolean('is_thumbnail');
        $contentType = 'image/png';

        try {
            if (str_contains($url, 'drive.google.com')) {
                $fileId = $this->extractFileIdFromGoogleDriveUrl($url);

                $client = new \Google_Client();
                $client->setAuthConfig(storage_path('google-service-account-credentials.json'));
                $client->addScope(\Google_Service_Drive::DRIVE);
                $service = new \Google_Service_Drive($client);

                if ($isThumbnail) {
                    $file = $service->files->get($fileId, ['fields' => 'thumbnailLink']);
                    $thumbnailUrl = $file->getThumbnailLink();
                    $thumbnailUrl = str_replace('=s220', '=s100', $thumbnailUrl);
                    $response = Http::get($thumbnailUrl);
                    if ($response->successful()) {
                        $content = $response->body();
                        $contentType = $response->header('Content-Type');
                    } else {
                        throw new \Exception('Unable to fetch thumbnail image');
                    }
                } else {
                    $response = $service->files->get($fileId, ['alt' => 'media']);
                    $content = $response->getBody()->getContents();
                }
            } else {
                $response = Http::get($url);
                if ($response->successful()) {
                    $content = $response->body();
                    $contentType = $response->header('Content-Type');
                } else {
                    throw new \Exception('Unable to fetch image');
                }
            }

            if ($content) {
                return response($content, 200)->header('Content-Type', $contentType);
            }
            throw new \Exception('Unable to fetch image');
        } catch (\Exception $e) {
            return response($e->getMessage(), 404);
        }
    }

    public function extractFileIdFromGoogleDriveUrl($url): string
    {
        if (preg_match('/\?id=([^&]+)/', $url, $matches)) {
            return $matches[1];
        }

        if (preg_match('%drive\.google\.com/file/[a-z]/([^/]+)/(edit|view|preview)%', $url, $matches)) {
            return $matches[1];
        }

        throw new \Exception('Invalid Google Drive file URL');
    }

    public function deleteImportCampaign(Request $request): JsonResponse
    {
        $query = ImportCampaignsData::query()
            ->whereIn('id', $request->get('ids'))
            ->whereIn('status', [ImportCampaignStatusEnum::PENDING, ImportCampaignStatusEnum::FAILED])
            ->where('seller_id', currentUser()->getUserId());
        $cloneQuery = clone $query;
        $total = $cloneQuery->count();
        $query->delete();
        if ($total === 0) {
            return $this->errorResponse('Cannot delete the selected campaigns.');
        }

        return $this->successResponse($total);
    }

    public function bulkUpdateImportCampaignStatus(Request $request): JsonResponse
    {
        $ids = $request->get('ids');
        $status = $request->get('status');
        $query = ImportCampaignsData::query()
            ->whereIn('id', $ids)
            ->where('seller_id', currentUser()->getUserId());

        if ($status === ImportCampaignStatusEnum::PENDING) { // continue
            $query->whereIn('status', [ImportCampaignStatusEnum::STOPPED]);
        } else if ($status === ImportCampaignStatusEnum::STOPPED) { // stop
            $query->whereIn('status', [ImportCampaignStatusEnum::PENDING]);
        }

        $cloneQuery = clone $query;
        $total = $cloneQuery->count();
        $query->update(['status' => $status]);
        if ($total === 0) {
            return $this->errorResponse('Cannot update the selected campaigns.');
        }

        return $this->successResponse($total);
    }

    /**
     * @param Request $request
     * @param ListDesignFromGoogleDriverFolderService $service
     * @return JsonResponse
     */
    public function previewBulkCampaignViaGoogleDriverFolderUrl(Request $request, ListDesignFromGoogleDriverFolderService $service): JsonResponse
    {
        try {
            return $this->successResponse(
                $service->handle($request->get('url'))
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param ImportViaGoogleDriverFolderRequest $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function importBulkCampaignViaGoogleDriverFolderUrl(
        ImportViaGoogleDriverFolderRequest $request,
        BulkCampaignV3Service              $service
    ): JsonResponse
    {
        try {
            $payload = $request->validated();
            $seller = currentUser()->getInfoAccess();
            $hasArchived = ImportCampaignDataService::checkArchivedProducts($payload['campaign_id'], $seller);
            if ($hasArchived) {
                throw new Exception(ImportCampaignDataService::ARCHIVED_PRODUCTS_MESSAGE);
            }
            $service->importBulkCampaignViaGoogleDriverFolderUrl(
                payload: $payload,
                seller: $seller,
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function previewBulkCampaignViaCSV(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $result = $service->previewBulkCampaignViaCSV(
                file: $request->file('file')
            );

            return $this->successResponse($result);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param ImportCSVRequest $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function importBulkCampaignViaCSV(
        ImportCSVRequest      $request,
        BulkCampaignV3Service $service
    ): JsonResponse
    {
        $payload = $request->validated();
        $seller = currentUser()->getInfoAccess();
        try {
            $hasArchived = ImportCampaignDataService::checkArchivedProducts($payload['campaign_id'], $seller);
            if ($hasArchived) {
                throw new Exception(ImportCampaignDataService::ARCHIVED_PRODUCTS_MESSAGE);
            }
            $result = $service->importBulkCampaignViaCSV(payload: $payload, seller: $seller);

            return $this->successResponse($result);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param BulkCampaignV3Service $service
     * @return JsonResponse
     */
    public function previewBulkCampaignViaDesignFiles(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $result = $service->previewBulkCampaignViaDesignFiles(
                origin: $request->get('origin', []),
                clone: $request->get('clone', []),
            );

            return $this->successResponse($result);
        } catch (Throwable $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                match ($e->getCode()) {
                    BulkService::ERR_LIMIT_CREATE_CAMPAIGN => 'ERR_LIMIT_CREATE_CAMPAIGN',
                    BulkService::ERR_EXISTS_CAMPAIGN => 'ERR_EXISTS_CAMPAIGN',
                    BulkService::ERR_LUNCH_CAMPAIGN_FAILED => 'ERR_LUNCH_CAMPAIGN_FAILED',
                    default => 'ERR_UNKNOWN'
                }
            );
        }
    }

    /**
     * Import bulk campaign via JSON
     *
     * @param ImportViaJSONRequest $request
     *
     * @return JsonResponse
     */
    public function importBulkCampaignViaJson(ImportViaJSONRequest $request, StoreBulkService $service): JsonResponse
    {
        try {
            $payload = $request->validated();
            $seller = currentUser()->getInfoAccess();
            $hasArchived = ImportCampaignDataService::checkArchivedProducts($payload['campaign_id'], $seller);
            if ($hasArchived) {
                throw new Exception(ImportCampaignDataService::ARCHIVED_PRODUCTS_MESSAGE);
            }
            $service->handle($payload, $seller);

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getBulkCampaignRows(Request $request): JsonResponse
    {
        try {
            $query = ImportCampaignsData::filter(
                $request->all(), ImportCampaignsDataFilter::class
            )
                ->with([
                    'seller_record:id,name,nickname,email,avatar',
                    'template_campaign:id,slug,name,full_printed,system_type,thumb_url',
                    'template_campaign.designs:campaign_id,file_url,file_url_2,option,type,print_space',
                    'campaign:id,slug,name,full_printed,system_type,thumb_url',
                    'campaign.designs:campaign_id,file_url,file_url_2,option,type,print_space',
                ])
                ->when(currentUser()->isSeller(), fn($q) => $q->where('seller_id', currentUser()->getUserId()));

            return $this->successResponse(
                tap($query->paginate($request->get('per_page', 15)), static function (LengthAwarePaginator $result) use ($request) {
                    $result->withPath($request->get('path', '/'));
                    $result->onEachSide(1);
                })
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param StoreProcessedRowRequest $request
     *
     * @return JsonResponse
     */
    public function saveProcessedRow(StoreProcessedRowRequest $request): JsonResponse
    {
        try {
            $payload = $request->validated();
            $saved = ImportCampaignsData::query()
                ->where('id', $payload['id'])
                ->where('status', ImportCampaignStatusEnum::PENDING)
                ->first();
            if ($saved) {
                $saved->mockups = json_encode(Arr::only($payload, ['designs', 'designs3D']), JSON_THROW_ON_ERROR);
                $saved->status = ImportCampaignStatusEnum::PROCESSING;
                $saved->system_status = ImportCampaignSystemStatusEnum::SAVE_DESIGN;
                $saved->ip_address = data_get($payload,'ipAddress', $saved->ip_address);
                $saved->session_id = data_get($payload,'sessionId', $saved->session_id);
                $saved->device_id = data_get($payload,'deviceId', $saved->device_id);
                $saved->save();
            }

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function markRowInvalid(MarkRowInvalidRequest $request): JsonResponse
    {
        try {
            $payload = $request->validated();
            $dataUpdate = [
                'status' => ImportCampaignStatusEnum::FAILED,
                'logs' => json_encode(['message' => $payload['message']], JSON_THROW_ON_ERROR)
            ];
            if ($ipAddress = data_get($payload, 'ipAddress')) {
                $dataUpdate['ip_address'] = $ipAddress;
            }
            if ($ipAddress = data_get($payload, 'sessionId')) {
                $dataUpdate['session_id'] = $ipAddress;
            }
            if ($ipAddress = data_get($payload, 'deviceId')) {
                $dataUpdate['device_id'] = $ipAddress;
            }
            ImportCampaignsData::query()->where('id', $payload['id'])
                ->unless(
                    currentUser()->isAdmin(),
                    fn($q) => $q->where('seller_id', currentUser()->getUserId())
                )
                ->where('status', ImportCampaignStatusEnum::PENDING)
                ->update($dataUpdate);
            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param BulkCampaignV3Service $service
     * @return JsonResponse
     */
    public function getPendingRecord(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $types = [ImportCampaignTypeEnum::REGULAR_MULTI_SPACE, ImportCampaignTypeEnum::EXPRESS_MULTI_SPACE];
            $type = $request->get('type');
            if (!in_array($type, $types, true)) {
                throw new Exception('Invalid type');
            }

            $take = $request->get('take', 1);
            $user = currentUser();
            $results = match (true) {
                $user->isSeller() => $service->sellerGetPendingRecord($type, $user->getInfoAccess(), $take),
                $user->isAdmin() => $service->adminGetPendingRecord($type, $take),
                default => throw new Exception('Only seller can use this feature')
            };

            return $this->successResponse($results);
        } catch (Throwable $e) {
            logToDiscord($e->getMessage(), DiscordChannel::ERROR, true);

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param BulkCampaignV3Service $service
     * @return JsonResponse
     */
    public function list(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            return $this->successResponse(
                $service->searchOnElastic($request, currentUser()->getUserId())
                    ->withPath($request->get('path', '/'))
                    ->onEachSide(1)
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param \Modules\Campaign\Http\Requests\BulkCampaign\GroupFileNameRequest $request
     *
     * @return JsonResponse
     */
    public function groupFileNames(GroupFileNameRequest $request): JsonResponse
    {
        try {
            $campaigns = BulkService::groupFileNames(
                $request->get('contents'),
                static fn($row) => $row['content'],
                static fn($row) => $row['idx']
            );

            return $this->successResponse($campaigns);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Retry bulk campaign
     *
     * @param Request $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function retry(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $IDs = $request->get('ids', []);
            $force = $request->boolean('force');

            if ($force && !currentUser()->isAdmin()) {
                return $this->errorResponse('Only admin can use force retry');
            }

            $res = $service->retry(
                IDs: to_list($IDs),
                sellerId: currentUser()->isAdmin() ? null : currentUser()->getUserId(),
                force: $force
            );

            return $this->successResponse($res);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Remove bulk campaign
     *
     * @param Request $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function remove(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $IDs = $request->get('ids', []);

            $service->remove(
                to_list($IDs), currentUser()->isAdmin() ? null : currentUser()->getUserId()
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Start bulk campaign
     *
     * @param Request $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function start(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $IDs = $request->get('ids', []);

            $res = $service->start(
                to_list($IDs), currentUser()->isAdmin() ? null : currentUser()->getUserId()
            );

            return $this->successResponse($res);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Stop bulk campaign
     *
     * @param Request $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function stop(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            $IDs = $request->get('ids', []);

            $res = $service->stop(
                to_list($IDs), currentUser()->isAdmin() ? null : currentUser()->getUserId()
            );

            return $this->successResponse($res);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Start bulk campaign
     *
     * @param Request $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function startAll(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            if (!currentUser()->isSeller()) {
                return $this->errorResponse('Only seller can use this feature');
            }

            $service->startAll(
                currentUser()->getUserId()
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Stop bulk campaign
     *
     * @param Request $request
     * @param BulkCampaignV3Service $service
     *
     * @return JsonResponse
     */
    public function stopAll(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            if (!currentUser()->isSeller()) {
                return $this->errorResponse('Only seller can use this feature');
            }

            $service->stopAll(
                currentUser()->getUserId()
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param BulkCampaignV3Service $service
     * @return JsonResponse
     */
    public function originCampaign(Request $request, BulkCampaignV3Service $service): JsonResponse
    {
        try {
            if (!$id = (int)$request->id) {
                throw new RuntimeException('Invalid campaign ID');
            }

            return $this->successResponse(
                $service->getOriginalCampaign($id)
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function testBulkCampaign(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);
        $id = $request->get('id');
        $type = $request->get('type', 1);
        $bulk = ImportCampaignsData::query()->whereKey($id)->first();
        if (empty($bulk)) {
            return $this->errorResponse('Not found');
        }
        if ((int)$type === 2) {
            $mockups = json_decode($bulk->mockups, true);
            /** @var \App\Http\Controllers\CampaignController $controller */
            $controller = app(\App\Http\Controllers\CampaignController::class);
            $request = new Request([
                'bulk_id' => $bulk->id,
                'campaignId' => $bulk->campaign_id,
                'data' => $mockups,
                'storage' => 's3',
            ]);
            $response = $controller->saveCampaignDesignsBulkV2($request, true);
            return $this->successResponse($response);
        }
        HandleSaveDesignJob::dispatchSync($bulk->id);
        return $this->successResponse();
    }
}
