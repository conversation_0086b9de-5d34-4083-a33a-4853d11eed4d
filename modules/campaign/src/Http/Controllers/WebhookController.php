<?php
namespace Modules\Campaign\Http\Controllers;

use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductStatus;
use App\Http\Controllers\Controller;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WebhookController extends Controller
{
    use ApiResponse;

    /**
     * @param $mockupId
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function saveMockupForFile($mockupId, Request $request) {
        $seller = User::query()->find($request->get('seller_id'));
        $file = File::query()
            ->onSellerConnection($seller)
            ->whereKey($mockupId)
            ->withoutGlobalScope('getActive')
            ->where([
                'type' => FileTypeEnum::IMAGE,
                'render_type' => FileRenderType::NONE,
                'print_space' => PrintSpaceEnum::DEFAULT,
            ])
            ->first();
        if (!$file) {
            graylogInfo('[Webhook] File mockup not found', [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'mockupId' => $mockupId,
                'response_data' => json_encode($request->all()),
            ]);
            return $this->errorResponse('File mockup not found', 400);
        }
        $success = $request->boolean('success');
        $message = $request->input('message');
        $data = $request->input('data');
        if (empty($success) || empty($data)) {
            graylogInfo(!empty($message) ? '[Webhook] ' . $message : '[Webhook] File path not found', [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'mockupId' => $mockupId,
                'response_data' => json_encode($request->all()),
            ]);
            return $this->errorResponse('File path not found', 400);
        }
        $filePath = is_object($data) ? $data->result_path : $data['result_path'];
        $campaign_id = $file->campaign_id;
        if (empty($filePath)) {
            graylogInfo('[Webhook] File path not found', [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'mockupId' => $mockupId,
                'response_data' => json_encode($request->all()),
            ]);
            Product::query()
                ->onSellerConnection($seller)
                ->where(function ($q) use ($campaign_id) {
                $q->where('id', $campaign_id);
                $q->orWhere('campaign_id', $campaign_id);
                })
                ->update([
                    'status' => ProductStatus::ERROR,
                    'sync_status' => Product::SYNC_DATA_STATS_ENABLED,
                ]);
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaign_id, 1, sellerId: $seller->id);
            return $this->errorResponse('File path not found', 400);
        }
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->whereKey($campaign_id)
            ->first();
        if (!$campaign) {
            graylogInfo('[Webhook] Campaign not found', [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'campaign_id' => $campaign_id,
                'response_data' => json_encode($request->all()),
            ]);
            return $this->errorResponse('Campaign not found', 404);
        }
        $file->update([
            'status' => FileStatusEnum::ACTIVE,
            'file_url' => $filePath
        ]);
        if ($file->isDefault()) {
            Product::query()
                ->onSellerConnection($seller)
                ->whereKey($file->product_id)
                ->where('campaign_id', $campaign_id)
                ->update([
                    'thumb_url' => $filePath,
                    'sync_status' => Product::SYNC_DATA_STATS_ENABLED
                ]);

            if ($campaign->default_product_id === $file->product_id) {
                $campaign->update([
                    'thumb_url' => $filePath,
                    'sync_status' => Product::SYNC_DATA_STATS_ENABLED
                ]);
            }
        }
        $countPendingMockup = File::query()
            ->onSellerConnection($seller)
            ->selectRaw('COUNT(id) as total')
            ->withoutGlobalScope('getActive')
            ->where([
                'campaign_id' => $campaign_id,
                'seller_id' => $file->seller_id,
                'type' => FileTypeEnum::IMAGE,
                'render_type' => FileRenderType::NONE,
                'status' => FileStatusEnum::PENDING,
                'print_space' => PrintSpaceEnum::DEFAULT,
            ])
            ->value('total');
        $countActiveMockup = File::query()
            ->onSellerConnection($seller)
            ->selectRaw('COUNT(id) as total')
            ->where([
                'campaign_id' => $campaign_id,
                'seller_id' => $file->seller_id,
                'type' => FileTypeEnum::IMAGE,
                'render_type' => FileRenderType::NONE,
                'print_space' => PrintSpaceEnum::DEFAULT,
            ])
            ->value('total');
        graylogInfo('[Webhook] Updated file path', [
            'category' => 'campaign_aop',
            'type' => 'webhook',
            'mockupId' => $mockupId,
            'campaign_id' => $campaign_id,
            'response_data' => json_encode($request->all()),
        ]);
        if ((int) $countPendingMockup === 0 && $countActiveMockup > 0 && in_array($campaign->status, [ProductStatus::PENDING, ProductStatus::ERROR, ProductStatus::INACTIVE])) {
            Product::query()
                ->onSellerConnection($seller)
                ->where(function ($q) use ($campaign_id) {
                    $q->where('id', $campaign_id);
                    $q->orWhere('campaign_id', $campaign_id);
                })
                ->update([
                    'status' => ProductStatus::ACTIVE,
                    'sync_status' => Product::SYNC_DATA_STATS_ENABLED,
                ]);
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaign_id, 1, sellerId: $seller->id);
            graylogInfo('[Webhook] Render completed. Activated campaign.', [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'mockupId' => $mockupId,
                'campaign_id' => $campaign_id,
            ]);
        }
        return $this->successResponse();
    }
}
