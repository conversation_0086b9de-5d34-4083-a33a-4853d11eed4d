<?php
namespace Modules\Campaign\Http\Requests;

class BatchMockupUploadRequest extends Request
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'mockups' => 'required|array',
            'mockups.*.product_id' => 'required',
            'mockups.*.file_path' => 'required|string',
            'mockups.*.print_space' => 'nullable|string|in:front,back',
            'mockups.*.mockup_type' => 'string|nullable',
            'mockups.*.prompt_data' => 'array|nullable',
            'is_ai_campaign' => 'boolean|nullable',
        ];
    }
}
