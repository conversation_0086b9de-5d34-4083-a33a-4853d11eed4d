<?php
namespace Modules\Campaign\Http\Requests;

use <PERSON><PERSON><PERSON>\Campaign\Enums\ProductSystemTypeEnum;

class ImportCampaignsRequest extends Request
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'file' => [
                'file',
                'required',
                'max:10240',
                'mimetypes:text/plain,text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'type' => ['required', 'string', 'in:' . ProductSystemTypeEnum::CUSTOM . ',' . ProductSystemTypeEnum::REGULAR . ',' . ProductSystemTypeEnum::EXPRESS . ',' . ProductSystemTypeEnum::MOCKUP . ',' . ProductSystemTypeEnum::AOP],
        ];
    }
}
