<?php
namespace Modules\Campaign\Http\Requests;

class CustomMockupUploadRequest extends Request
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_id' => 'required',
            'file_path' => 'required',
            'is_ai_campaign' => 'boolean|nullable',
            'prompt_data' => 'array|nullable',
            'print_space' => 'nullable|string|in:front,back',
            'mockup_type' => 'string|nullable'
        ];
    }
}
