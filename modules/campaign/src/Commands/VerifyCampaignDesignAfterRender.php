<?php
namespace Modules\Campaign\Commands;

use App\Enums\QueueName;
use App\Models\User;
use Illuminate\Console\Command;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Jobs\VerifyCampaignDesignAfterRenderJob;
use Modules\Campaign\Models\ImportCampaignsData;

class VerifyCampaignDesignAfterRender extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaign:verify-design-after-render';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify campaign design after render';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $now = now();
        $start = $now->subMinutes(25)->toDateTimeString();
        $end = $now->clone()->subMinutes(5)->toDateTimeString();
        $temp_status = 2;
        $bulks = ImportCampaignsData::query()
            ->select('id', 'seller_id')
            ->where('type', 'regular')
            ->where('status', ImportCampaignStatusEnum::COMPLETED)
            ->where('temp_status', '!=', $temp_status)
            ->whereNotNull('campaign_id')
            ->where('updated_at', '>=', $end)
            ->where('updated_at', '<=', $start)
            ->orderByDesc('id')
            ->get();
        if ($bulks->isEmpty()) {
            return;
        }
        $seller_ids = $bulks->pluck('seller_id')->unique()->toArray();
        $sellers = User::query()->where('id', $seller_ids)->get();
        try {
            foreach ($bulks as $bulk_data) {
                $seller = $sellers->where('id', $bulk_data->seller_id)->first();
                if (!$seller) {
                    continue;
                }
                VerifyCampaignDesignAfterRenderJob::dispatch($bulk_data->id, $seller, $temp_status)->onQueue(QueueName::BULK_CAMPAIGN);
            }
        } catch (\Throwable $e) {
            logException($e, 'VerifyCampaignDesignAfterRender', 'bulk_campaign');
        }
    }
}
