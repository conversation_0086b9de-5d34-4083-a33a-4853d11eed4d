<?php
namespace Modules\Campaign\Commands;

use App\Enums\CacheKeys;
use App\Models\SystemConfig;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\Campaign\Jobs\CreateRegularDraftCampaignsJob;

class CreateDraftCampaignCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaign:create-draft {--seller_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create draft campaign';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Starting process...');
            $sellerId = $this->option('seller_id');
            if (!empty($sellerId)) {
                dispatch(new CreateRegularDraftCampaignsJob($sellerId));
                return;
            }
            $timeStartJobObject = SystemConfig::getCustomConfig(CacheKeys::CREATE_BULK_DRAFT_CAMPAIGNS);
            if (isset($timeStartJobObject) && !empty($timeStartJobObject->value) && now()->lte(Carbon::parse($timeStartJobObject->value))) {
                return;
            }
            dispatch(new CreateRegularDraftCampaignsJob());
            $this->info('Done.');
        } catch (\Throwable $exception) {
            logException($exception, 'CreateDraftCampaignCommand::handle', 'bulk_campaign', true);
        }
    }
}
