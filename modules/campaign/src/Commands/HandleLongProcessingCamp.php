<?php
namespace Modules\Campaign\Commands;

use App\Facades\ProcessLock;
use App\Models\Campaign;
use App\Services\CampaignService;
use Illuminate\Console\Command;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignSystemStatusEnum;
use Modules\Campaign\Models\ImportCampaignsData;

class HandleLongProcessingCamp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaigns:handle-long-processing-camp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle long processing campaign';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ProcessLock::handle($this->signature, callback: function () {
            try {
                $limitUsers = CampaignService::listUserLimitCreateCampaign();
                ImportCampaignsData::query()->where('status', ImportCampaignStatusEnum::PROCESSING)
                    ->when(!empty($limitUsers), function ($query) use ($limitUsers) {
                        $query->whereNotIn('seller_id', $limitUsers);
                    })
                    ->where('updated_at', '<', now()->subMinutes(Campaign::PENDING_THRESHOLD))
                    ->where(function ($query) {
                        $query->where('system_status', '!=', ImportCampaignSystemStatusEnum::SAVE_DESIGN)->orWhereNull('system_status');
                    })
                    ->update([
                        'status' => ImportCampaignStatusEnum::PENDING,
                        'system_status' => null,
                        'logs' => 'Campaign is processing too long, change status to pending'
                    ]);
            } catch (\Throwable $exception) {
                logException($exception, 'HandleLongProcessingCamp::handle');
            }
        });
        return self::SUCCESS;
    }
}
