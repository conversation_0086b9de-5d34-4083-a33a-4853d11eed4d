<?php

namespace Modules\Campaign\Traits;


use App\Enums\CampaignArchivedStatus;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignRenderModeEnum;
use App\Enums\DiscordChannel;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\Campaign;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductCollection;
use App\Models\StoreProduct;
use App\Models\User;
use App\Services\ExpressMockupRenderService;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

trait SyncProductsToElasticSearch
{
    /**
     * @param $pending_products
     * @param $wait
     * @param User|null $seller
     * @return mixed
     */
    public function handle_sync_to_elasticsearch($pending_products, $wait, User $seller = null) {
        $startTime = round(microtime(true) * 1000);
        $campaigns = [];
        $campaignIds = [];
        $products = [];
        $templateIds = [];
        $allIds = [];
        $expressCampaignIds = [];
        $allExpressCampaignIds = [];
        $expressMockupCampaignIds = [];
        $deletedIds = [];
        $storeIds = [];
        $total = $pending_products->count();
        $processId = time();
        $unsetUnusedFields = ['tracking_code', 'supplier_id', 'supplier_name', 'archived', 'show_countdown', 'start_time', 'end_time'];
        $elasticSearchIndex = $seller?->getElasticSearchIndex() ?? get_env('ELATICSEARCH_INDEX', 'products');
        if ($this->enabledDebug) {
            $this->log("Start sync $total products to elasticsearch", [
                'category' => $this->queueName,
                'user_type' => 'system',
                'action'  => "sync",
                'run_time' => now()->toDateTimeString(),
                'type' => $this->isJob ? 'Cronjob' : 'Manual',
                'process_id' => $processId,
            ]);
        }
        try {
            foreach ($pending_products as $productIndex => $productItem) {
                $allIds[$productItem->seller_id][] = (int) $productItem->id;
                if($productItem->product_type === ProductType::FULFILL_PRODUCT) {
                    continue;
                }
                $attrOption = !is_null($productItem->options) ? json_decode($productItem->options, true) : [];
                $this->convertUnRequiredToBool($attrOption);
                $attrTrackingCode = !is_null($productItem->tracking_code) ? json_decode($productItem->tracking_code, true) : [];
                if ((int) $productItem->is_deleted === 1) {
                    $deletedIds[] = (int) $productItem->id;
                    continue;
                }

                if ($productItem->product_type === ProductType::CAMPAIGN_EXPRESS) {
                    $expressCampaignIds[$productItem->seller_id][] = (int) $productItem->id;
                    $allExpressCampaignIds[] = (int) $productItem->id;
                    continue;
                }
                $campaignId = empty($productItem->campaign_id) ? $productItem->id : $productItem->campaign_id;
                if(!isset($storeIds[$campaignId])) {
                    $storeIds[$campaignId] = $productItem->store_campaign->pluck('store_id')->toArray();
                }

                $productData = [
                    'id' => (int) $productItem->id,
                    'campaign_id' => $productItem->campaign_id ? (int)$productItem->campaign_id : 0,
                    'design_id' => $productItem->campaign_id ? (int) $productItem->campaign_id : (int) $productItem->id,
                    'seller_id' => (int) $productItem->seller_id,
                    'template_id' => !is_null($productItem->template_id) && (int) $productItem->template_id > 0 ? (int) $productItem->template_id : 0,
                    'name' => $productItem->name,
                    'slug' => $productItem->slug,
                    'thumb_url' => $productItem->thumb_url,
                    'description' => (string) $productItem->description,
                    'options' => $attrOption,
                    'default_option' => (string) $productItem->default_option,
                    'base_cost' => (float) $productItem->base_cost,
                    'price' => (float) $productItem->price,
                    'old_price' => (float) $productItem->old_price,
                    'shipping_cost' => (float) $productItem->shipping_cost,
                    'start_time' => !is_null($productItem->start_time) ? $productItem->start_time->toDateTimeString() : null,
                    'end_time' => !is_null($productItem->end_time) ? $productItem->end_time->toDateTimeString() : null,
                    'show_countdown' => (bool) $productItem->show_countdown,
                    'default_product_id' => (int) $productItem->default_product_id,
                    'tracking_code' => !is_null($attrTrackingCode) ? $attrTrackingCode : $productItem->tracking_code,
                    'store_ids' => $storeIds[$campaignId] ?? [],
                    'product_type' => $productItem->product_type,
                    'system_type' => $productItem->system_type,
                    'created_at' => $productItem->created_at,
                    'status' => $productItem->status,
                    'public_status' => CampaignPublicStatusEnum::getPublicStatusElastic($productItem->public_status, $productItem->sellers),
                    'personalized' => $productItem->personalized,
                    'supplier_id' => $productItem->supplier_id,
                    'supplier_name' => $productItem->supplier_name ?? '',
                    'currency_code' => $productItem->currency_code,
                    'score' => $productItem->score,
                    'tm_status' => $productItem->tm_status,
                    'public_status_detail' => $productItem->public_status,
                    'full_printed' => $productItem->full_printed,
                    'google_category_id' => (int) $productItem->google_category_id,
                    'pricing_mode' => $productItem->pricing_mode,
                    'market_location' => $productItem->market_location,
                    'archived' => $productItem->archived,
                ];
                unset($attrOption, $attrTrackingCode);
                if ($productItem->system_type === 'mockup') {
                    $expressMockupCampaignIds[] = $productData['id'];
                }
                switch ($productItem->product_type) {
                    case ProductType::CAMPAIGN:
                    case ProductType::CAMPAIGN_TEMPLATE:
                        $campaignIds[$productItem->seller_id][] = $productData['id'];
                        $campaigns[] = $productData;
                        break;
                    case ProductType::PRODUCT_TEMPLATE:
                    case ProductType::TEMPLATE:
                    case ProductType::PRODUCT:
                        $campaignIds[$productItem->seller_id][] = $productData['campaign_id'];
                        $templateIds[] = $productData['template_id'] > 0 ? $productData['template_id'] : $productData['id'];
                        $products[] = $productData;
                        break;
                    default:
                }
                unset($pending_products[$productIndex]);
            }

            if (!empty($expressMockupCampaignIds)) {
                $campaigns_with_default = Campaign::query()
                    ->with(['defaultProduct', 'seller'])
                    ->whereIn('id', $expressMockupCampaignIds)
                    ->get();
                if ($campaigns_with_default->isNotEmpty()) {
                    foreach ($campaigns_with_default as $campaign_with_default) {
                        $attrOption = !is_null($campaign_with_default->options) ? json_decode($campaign_with_default->options, true) : [];
                        $this->convertUnRequiredToBool($attrOption);
                        $attrTrackingCode = !is_null($campaign_with_default->tracking_code) ? json_decode($campaign_with_default->tracking_code, true) : [];
                        $products[] = [
                            'id' => 0,
                            'express_product_id' => Str::uuid(),
                            'campaign_id' => $campaign_with_default->id ? (int) $campaign_with_default->id : 0,
                            'design_id' => !empty($campaign_with_default->campaign_id) ? (int) $campaign_with_default->campaign_id : (int) $campaign_with_default->id,
                            'seller_id' => (int) $campaign_with_default->seller_id,
                            'template_id' => !empty($campaign_with_default->defaultProduct) ? (int) $campaign_with_default->defaultProduct->template_id : 0,
                            'name' => $campaign_with_default->defaultProduct->name ?? $campaign_with_default->name,
                            'slug' => $campaign_with_default->slug,
                            'thumb_url' => $campaign_with_default->thumb_url,
                            'description' => (string) $campaign_with_default->description,
                            'options' => $attrOption,
                            'default_option' => (string) $campaign_with_default->default_option,
                            'base_cost' => (float) $campaign_with_default->base_cost,
                            'price' => (float) $campaign_with_default->price,
                            'old_price' => (float) $campaign_with_default->old_price,
                            'shipping_cost' => (float) $campaign_with_default->shipping_cost,
                            'start_time' => !is_null($campaign_with_default->start_time) ? $campaign_with_default->start_time->toDateTimeString() : null,
                            'end_time' => !is_null($campaign_with_default->end_time) ? $campaign_with_default->end_time->toDateTimeString() : null,
                            'show_countdown' => (bool) $campaign_with_default->show_countdown,
                            'default_product_id' => (int) $campaign_with_default->default_product_id,
                            'tracking_code' => !is_null($attrTrackingCode) ? $attrTrackingCode : $campaign_with_default->tracking_code,
                            'store_ids' => $storeIds[$campaign_with_default->id] ?? [],
                            'product_type' => ProductType::PRODUCT,
                            'system_type' => $campaign_with_default->system_type,
                            'created_at' => $campaign_with_default->created_at,
                            'status' => $campaign_with_default->status,
                            'public_status' => CampaignPublicStatusEnum::getPublicStatusElastic($campaign_with_default->public_status, $campaign_with_default->seller),
                            'personalized' => $campaign_with_default->personalized,
                            'supplier_id' => $campaign_with_default->supplier_id,
                            'supplier_name' => $campaign_with_default->supplier_name ?? '',
                            'currency_code' => $campaign_with_default->currency_code,
                            'score' => $campaign_with_default->score,
                            'tm_status' => $campaign_with_default->tm_status,
                            'public_status_detail' => $campaign_with_default->public_status,
                            'full_printed' => $campaign_with_default->full_printed,
                            'google_category_id' => (int) $campaign_with_default->google_category_id,
                            'pricing_mode' => $campaign_with_default->pricing_mode,
                            'market_location' => $campaign_with_default->market_location,
                            'campaign_name' => $campaign_with_default->name,
                            'archived' => $campaign_with_default->archived,
                        ];
                    }
                }
            }

            if(!empty($campaignIds)) {
                [$campaigns, $products, $campaignCollections, $campaignInfo] = $this->process_campaign_products_collection($campaignIds, $campaigns, $products, false, $processId);
                $products = $this->process_products_template($templateIds, $products, $campaignCollections, $campaignInfo, false, $processId);
            }

            if(!empty($expressCampaignIds)) {
                $startExpressTime = round(microtime(true) * 1000);
                $expressCamps = collect();
                $products_templates = [];
                foreach ($expressCampaignIds as $sellerId => $eCampaignIds) {
                    $eSeller = User::query()->find($sellerId);
                    $expressCampaigns = ExpressCampaign::query()
                        ->onSellerConnection($eSeller)
                        ->select(['id', 'campaign_id', 'seller_id', 'template_id', 'name', 'render_mode', 'options', 'slug', 'thumb_url', 'description', 'default_option', 'base_cost', 'price', 'old_price', 'shipping_cost', 'start_time', 'end_time', 'show_countdown', 'default_product_id', 'tracking_code', 'product_type', 'created_at', 'status', 'personalized', 'supplier_id', 'currency_code', 'score', 'tm_status', 'public_status', 'full_printed', 'google_category_id', 'pricing_mode', 'archived'])
                        ->where('product_type', ProductType::CAMPAIGN_EXPRESS)
                        ->whereIn('id', array_unique($eCampaignIds))
                        ->with([
                            'template_campaign:id,name,template_id,campaign_id',
                            'seller:id,name,email,custom_payment',
                            'design' => function ($query) use ($eSeller) {
                                $query->onSellerConnection($eSeller);
                            }
                        ])
                        ->get();
                    $products_templates[$sellerId] = Product::query()
                        ->onSellerConnection($eSeller)
                        ->select(['id', 'template_id', 'campaign_id',  'name', 'thumb_url', 'description', 'default_option', 'options', 'base_cost', 'price', 'old_price', 'shipping_cost', 'product_type', 'supplier_id', 'score', 'public_status', 'full_printed', 'google_category_id', 'pricing_mode', 'archived'])
                        ->with([
                            'template:id,name,sku,render_mode',
                        ])
                        ->whereIn('campaign_id', $expressCampaigns->pluck('template_id')->unique()->toArray())
                        ->orderBy('priority')
                        ->get()
                        ->groupBy('campaign_id');
                    $expressCamps = $expressCamps->merge($expressCampaigns);
                }
                $totalExpressCamps = $expressCamps->count();
                if($totalExpressCamps > 0) {
                    $campaignStoreIds = $expressCamps->map(function($expressCampaign) {
                        return data_get($expressCampaign, 'campaign_id', data_get($expressCampaign, 'id'));
                    })->unique()->values()->toArray();
                    $storeIdsOfCampaigns = [];
                    if (!empty($campaignStoreIds)) {
                        $storeIdsOfCampaigns = StoreProduct::query()->select(['store_id', 'product_id'])->whereIn('product_id', $campaignStoreIds)->get()->groupBy('product_id')->toArray();
                    }
                    $template_mockup_files = File::query()
                        ->select(['id', 'file_url', 'file_url_2', 'product_id', 'option', 'type_detail', 'position', 'print_space'])
                        ->where([
                            'type' => FileTypeEnum::MOCKUP,
                            'render_type' => FileRenderType::EXPRESS,
                        ])
                        ->get()
                        ->groupBy('product_id')
                        ->sortBy('position');
                    foreach($expressCamps as $expressCampaign) {
                        $campaignOptions = !is_null($expressCampaign->options) ? json_decode($expressCampaign->options, true) : [];
                        $this->convertUnRequiredToBool($campaignOptions);
                        $campaignTrackingCode = !is_null($expressCampaign->tracking_code) ? (is_string($expressCampaign->tracking_code) ? json_decode($expressCampaign->tracking_code, true) : $expressCampaign->tracking_code) : [];
                        $campaignId = $expressCampaign->campaign_id ?: $expressCampaign->id;
                        if(!isset($storeIds[$campaignId]) && isset($storeIdsOfCampaigns[$campaignId])) {
                            $storeIds[$campaignId] = array_unique(array_column($storeIdsOfCampaigns[$campaignId], 'store_id'));
                        }
                        $campaignDesign = $expressCampaign->design ?? null;
                        $campaignDefaultPrintSpace = $expressCampaign->default_option ?? 'front';
                        $cache_template_products = $products_templates[$expressCampaign->seller_id] ?? [];
                        // generate campaign data
                        $dataCampaign = [
                            'id' => (int) $expressCampaign->id,
                            'campaign_id' => $expressCampaign->campaign_id ? (int) $expressCampaign->campaign_id : 0,
                            'design_id' => $expressCampaign->campaign_id ? (int) $expressCampaign->campaign_id : (int) $expressCampaign->id,
                            'seller_id' => (int) $expressCampaign->seller_id,
                            'template_id' => !is_null($expressCampaign->template_id) && (int) $expressCampaign->template_id > 0 ? (int) $expressCampaign->template_id : 0,
                            'template_name' => $expressCampaign->template_campaign?->name ? $expressCampaign->template_campaign->name : null,
                            'name' => $expressCampaign->name,
                            'slug' => $expressCampaign->slug,
                            'thumb_url' => $expressCampaign->thumb_url,
                            'description' => (string)$expressCampaign->description,
                            'options' => $campaignOptions,
                            'default_option' => (string)$expressCampaign->default_option,
                            'base_cost' => (float)$expressCampaign->base_cost,
                            'price' => (float)$expressCampaign->price,
                            'old_price' => (float)$expressCampaign->old_price,
                            'shipping_cost' => (float)$expressCampaign->shipping_cost,
                            'start_time' => !is_null($expressCampaign->start_time) ? $expressCampaign->start_time->toDateTimeString() : null,
                            'end_time' => !is_null($expressCampaign->end_time) ? $expressCampaign->end_time->toDateTimeString() : null,
                            'show_countdown' => (bool)$expressCampaign->show_countdown,
                            'default_product_id' => (int) $expressCampaign->default_product_id,
                            'tracking_code' => !is_null($campaignTrackingCode) ? $campaignTrackingCode : $expressCampaign->tracking_code,
                            'store_ids' => $storeIds[$campaignId] ?? [],
                            'product_type' => ProductType::CAMPAIGN,
                            'system_product_type' => $expressCampaign->product_type,
                            'created_at' => $expressCampaign->created_at,
                            'status' => $expressCampaign->status,
                            'public_status' => CampaignPublicStatusEnum::getPublicStatusElastic($expressCampaign->public_status, $expressCampaign->seller),
                            'personalized' => $expressCampaign->personalized,
                            'supplier_id' => $expressCampaign->supplier_id,
                            'supplier_name' => $expressCampaign->supplier_name ?? '',
                            'currency_code' => $expressCampaign->currency_code,
                            'score' => $expressCampaign->score,
                            'tm_status' => $expressCampaign->tm_status,
                            'public_status_detail' => $expressCampaign->public_status,
                            'full_printed' => $expressCampaign->full_printed,
                            'google_category_id' => (int) $expressCampaign->google_category_id,
                            'pricing_mode' => $expressCampaign->pricing_mode,
                            'archived' => $expressCampaign->archived,
                        ];

                        $campaignIds[$expressCampaign->seller_id][] = $dataCampaign['id'];
                        if (empty($cache_template_products[$expressCampaign->template_id])) {
                            continue;
                        }
                        $template_products = $cache_template_products[$expressCampaign->template_id];
                        if(empty($template_products) || $template_products->isEmpty()) {
                            continue;
                        }
                        foreach($template_products as $product) {
                            $productOptions = !is_null($product->options) ? json_decode($product->options, true) : [];
                            $this->convertUnRequiredToBool($productOptions);
                            $defaultOption = $product->default_option;
                            $hexColor = $product->default_option ? color2hex($defaultOption) : '000000';
                            if(str_starts_with($hexColor, '#')) {
                                $hexColor = substr($hexColor, 1);
                            }
                            $productTrackingCode = $campaignTrackingCode;
                            $productUniqueId = uniqid('express_');
                            //generate product data
                            $productData = [
                                'id' => 0,
                                'express_product_id' => $productUniqueId,
                                'campaign_id' => (int) $expressCampaign->id,
                                'design_id' => (int) $expressCampaign->id,
                                'seller_id' => (int) $expressCampaign->seller_id,
                                'template_id' => $product->template_id,
                                'name' => $product->name,
                                'slug' => $expressCampaign->slug,
                                'thumb_url' => $product->thumb_url,
                                'description' => (string) $product->description,
                                'options' => $productOptions,
                                'default_option' => (string) $product->default_option,
                                'base_cost' => (float) $product->base_cost,
                                'price' => (float) $product->price,
                                'old_price' => (float) $product->old_price,
                                'shipping_cost' => (float) $product->shipping_cost,
                                'start_time' => !is_null($expressCampaign->start_time) ? $expressCampaign->start_time->toDateTimeString() : null,
                                'end_time' => !is_null($expressCampaign->end_time) ? $expressCampaign->end_time->toDateTimeString() : null,
                                'show_countdown' => (bool) $expressCampaign->show_countdown,
                                'default_product_id' => 0,
                                'tracking_code' => !is_null($productTrackingCode) ? $productTrackingCode : $expressCampaign->tracking_code,
                                'store_ids' => $storeIds[$campaignId] ?? [],
                                'product_type' => ProductType::PRODUCT,
                                'system_product_type' => $product->product_type,
                                'created_at' => $expressCampaign->created_at,
                                'status' => ProductStatus::ACTIVE,
                                'public_status' => CampaignPublicStatusEnum::getPublicStatusElastic($expressCampaign->public_status, $expressCampaign->seller),
                                'personalized' => $expressCampaign->personalized,
                                'supplier_id' => $product->supplier_id,
                                'supplier_name' => $product->supplier_name ?? '',
                                'currency_code' => $expressCampaign->currency_code,
                                'score' => $product->score,
                                'tm_status' => $expressCampaign->tm_status,
                                'public_status_detail' => $product->public_status,
                                'full_printed' => $product->full_printed,
                                'google_category_id' => (int) $product->google_category_id,
                                'pricing_mode' => $product->pricing_mode,
                                'campaign_name' => $expressCampaign->name,
                                'archived' => $product->archived,
                            ];
                            if (!$template_mockup_files->has($product->template_id)) {
                                continue;
                            }
                            $productMockups = $template_mockup_files->get($product->template_id, collect())->toArray();
                            if (count($productMockups) === 0) {
                                continue;
                            }
                            $defaultMockupIndex = array_search($campaignDefaultPrintSpace, array_column($productMockups, 'print_space'));
                            $defaultMockup = null;
                            if($defaultMockupIndex) {
                                $defaultMockup = $productMockups[$defaultMockupIndex];
                            } else {
                                $defaultMockup = count($productMockups) > 0 ? $productMockups[0] : null;
                            }

                            $thumbUrl = $productData['thumb_url'];
                            if(!empty($campaignDesign) && !empty($defaultMockup)) {
                                $renderMode = ($expressCampaign->render_mode === $product->template->render_mode) ? $expressCampaign->render_mode : CampaignRenderModeEnum::NATURE;
                                $thumbUrl = app(ExpressMockupRenderService::class)->prepareOptionsForGenerateImageUrl(new File($defaultMockup), $campaignDesign, $hexColor, $renderMode);
                                $productData['thumb_url'] = $thumbUrl;
                            }
                            //check & correct default product id
                            if($product->id === $dataCampaign['default_product_id']) {
                                $dataCampaign['thumb_url'] = $thumbUrl;
                                $dataCampaign['default_product_id'] = $product->template_id;
                                $productData['default_product_id'] = $product->template_id;
                            }

                            $templateIds[] = $product->template_id;
                            $products[] = $productData;
                        }
                        $campaigns[] = $dataCampaign;
                    }
                    if ($this->enabledDebug) {
                        $endExpressTime = round(microtime(true) * 1000);
                        $esTime = $endExpressTime - $startExpressTime;
                        $this->log("Call process list express campaigns in $esTime ms", [
                            'category' => $this->queueName,
                            'user_type' => 'system',
                            'user_id' => null,
                            'action'  => "sync",
                            'run_time' => now()->toDateTimeString(),
                            'time' => $esTime,
                            'type' => $this->isJob ? 'Cronjob' : 'Manual',
                            'process_id' => $processId,
                        ]);
                    }
                    [$campaigns, $products, $campaignCollections, $campaignInfo] = $this->process_campaign_products_collection($campaignIds, $campaigns, $products, true, $processId);
                    $products = $this->process_products_template($templateIds, $products, $campaignCollections, $campaignInfo, true, $processId);
                }
                unset($expressCamps, $products_templates);
            }
            unset($campaignIds, $templateIds);
            if ($this->enabledDebug) {
                $this->log("Start merge campaigns and products", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $startEsTime = round(microtime(true) * 1000);
            // Merge campaigns and products
            $mergedDataSync = array_merge($campaigns, $products);
            $deletedIds = collect($deletedIds);
            $dataProductsSync = collect($mergedDataSync)->groupBy('archived')->toArray();
            if ($this->enabledDebug) {
                $endEsTime = round(microtime(true) * 1000);
                $esTime = $endEsTime - $startEsTime;
                $this->log("End merged campaigns and products", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'time' => $esTime,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $productsElasticParams = [];
            $activeIds = [];
            $archivedIds = [];
            if (!empty($allExpressCampaignIds) || !empty($expressMockupCampaignIds)) {
                if ($this->enabledDebug) {
                    $this->log("Start delete express campaigns on elasticsearch", [
                        'category' => $this->queueName,
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => "sync",
                        'run_time' => now()->toDateTimeString(),
                        'type' => $this->isJob ? 'Cronjob' : 'Manual',
                        'process_id' => $processId,
                    ]);
                }
                $startEsTime = round(microtime(true) * 1000);
                if ($seller && $seller->sharding_status == UserShardingStatusEnum::SYNCING) {
                    $this->elasticDeleteProductsByProductIds(array_unique(array_merge($allExpressCampaignIds, $expressMockupCampaignIds)), 'products_' . $seller->id);
                }
                $this->elasticDeleteProductsByProductIds(array_unique(array_merge($allExpressCampaignIds, $expressMockupCampaignIds)), $elasticSearchIndex);
                $this->elasticDeleteProductsByProductIds(array_unique(array_merge($allExpressCampaignIds, $expressMockupCampaignIds)), 'products_archived');
                if ($this->enabledDebug) {
                    $endEsTime = round(microtime(true) * 1000);
                    $esTime = $endEsTime - $startEsTime;
                    $this->log("Call delete express campaigns on elasticsearch in $esTime ms", [
                        'category' => $this->queueName,
                        'user_type' => 'system',
                        'user_id' => null,
                        'action'  => "sync",
                        'run_time' => now()->toDateTimeString(),
                        'time' => $esTime,
                        'type' => $this->isJob ? 'Cronjob' : 'Manual',
                        'process_id' => $processId,
                    ]);
                }
            }
            if ($this->enabledDebug) {
                $this->log("Start sync elasticsearch", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $startEsTime = round(microtime(true) * 1000);
            $synced = true;
            foreach ($dataProductsSync as $archived => $dataSync) {
                if (!empty($dataSync) && is_array($dataSync)) {
                    $totalItem = count($dataSync);
                    if ((int) $archived === CampaignArchivedStatus::NOT_ARCHIVED) {
                        $activeIds = collect($dataSync)->pluck('id')->toArray();
                        for ($i = 0; $i < $totalItem; $i++) {
                            foreach ($unsetUnusedFields as $field) {
                                if (isset($dataSync[$i][$field])) {
                                    unset($dataSync[$i][$field]);
                                }
                            }
                            $productsElasticParams[] = [
                                'index' => [
                                    '_index' => $elasticSearchIndex,
                                    '_type' => 'product',
                                    '_id' => !empty($dataSync[$i]['express_product_id']) ? $dataSync[$i]['express_product_id'] : $dataSync[$i]['id'],
                                ]
                            ];
                            $productsElasticParams[] = $dataSync[$i];
                            if($seller && $seller->sharding_status == UserShardingStatusEnum::SYNCING){
                                $productsElasticParams[] = [
                                    'index' => [
                                        '_index' => 'products_' . $seller->id,
                                        '_type' => 'product',
                                        '_id' => !empty($dataSync[$i]['express_product_id']) ? $dataSync[$i]['express_product_id'] : $dataSync[$i]['id'],
                                    ]
                                ];
                                $productsElasticParams[] = $dataSync[$i];
                            }
                        }
                    } else {
                        $archivedIds = collect($dataSync)->pluck('id')->toArray();
                        for ($j = 0; $j < $totalItem; $j++) {
                            foreach ($unsetUnusedFields as $field) {
                                if (isset($dataSync[$j][$field])) {
                                    unset($dataSync[$j][$field]);
                                }
                            }
                            if ($seller && in_array($seller->sharding_status, [UserShardingStatusEnum::SYNCING, UserShardingStatusEnum::COMPLETED])) {
                                $productsElasticParams[] = [
                                    'index' => [
                                        '_index' => 'products_' . $seller->id,
                                        '_type' => 'product',
                                        '_id' => !empty($dataSync[$j]['express_product_id']) ? $dataSync[$j]['express_product_id'] : $dataSync[$j]['id'],
                                    ]
                                ];
                            } else {
                                $productsElasticParams[] = [
                                    'index' => [
                                        '_index' => 'products_archived',
                                        '_type' => 'product',
                                        '_id' => !empty($dataSync[$j]['express_product_id']) ? $dataSync[$j]['express_product_id'] : $dataSync[$j]['id'],
                                    ]
                                ];
                            }
                            $productsElasticParams[] = $dataSync[$j];
                        }
                    }
                    $data = array();
                    if ($wait) {
                        $data['refresh'] = true;
                    }
                    $this->log("Total products sync to elasticsearch.", [
                        'category' => $this->queueName,
                        'user_type' => 'system',
                        'action'  => "sync",
                        'run_time' => now()->toDateTimeString(),
                        'total_data' => count($productsElasticParams),
                        'type' => $this->isJob ? 'Cronjob' : 'Manual',
                        'process_id' => $processId,
                    ]);
                    $totalChunk = 500;
                    foreach (array_chunk($productsElasticParams, $totalChunk) as $chunk) {
                        $data['body'] = $chunk;
                        $startEsBulkTime = round(microtime(true) * 1000);
                        $result = $this->elastic('bulk', $data);
                        if ($this->enabledDebug) {
                            $endEsBulkTime = round(microtime(true) * 1000);
                            $esBulkTime = $endEsBulkTime - $startEsBulkTime;
                            $this->log("Call bulk function with $totalChunk products on elasticsearch in $esBulkTime ms", [
                                'category' => $this->queueName,
                                'user_type' => 'system',
                                'user_id' => null,
                                'action' => "sync",
                                'run_time' => now()->toDateTimeString(),
                                'time' => $esBulkTime,
                                'type' => $this->isJob ? 'Cronjob' : 'Manual',
                                'process_id' => $processId,
                            ]);
                        }
                        if (empty($result) || !empty($result['errors'])) {
                            $synced = false;
                        }
                    }
                }
            }
            if (!$synced) {
                $this->log("Call sync elasticsearch failed.", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'all_ids' => json_encode($allIds),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
                return 0;
            }
            if ($this->enabledDebug) {
                $endEsTime = round(microtime(true) * 1000);
                $esTime = $endEsTime - $startEsTime;
                $this->log("Call sync elasticsearch in $esTime ms", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'time' => $esTime,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
                $this->log("Start call delete campaigns and products on elasticsearch.", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $startEsTime = round(microtime(true) * 1000);
            if ($seller && $seller->sharding_status == UserShardingStatusEnum::SYNCING) {
                $this->elasticDeleteProductsByProductIds(array_unique(array_merge($allExpressCampaignIds, $expressMockupCampaignIds)), 'products_' . $seller->id);
            }
            $this->elasticDeleteProductsByProductIds($deletedIds->merge($archivedIds)->unique()->toArray(), $elasticSearchIndex);
            $this->elasticDeleteProductsByProductIds($deletedIds->merge($activeIds)->unique()->toArray(), 'products_archived');
            if ($this->enabledDebug) {
                $endEsTime = round(microtime(true) * 1000);
                $esTime = $endEsTime - $startEsTime;
                $this->log("Call delete campaigns and products on elasticsearch in $esTime ms", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'time' => $esTime,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $startUpdateTime = round(microtime(true) * 1000);
            Product::updateSyncStatusByIds($allIds, Product::SYNC_DATA_TO_SINGLE_STORE, ['temp_status' => TempStatusEnum::DEFAULT]);
            if ($this->enabledDebug) {
                $endUpdateTime = round(microtime(true) * 1000);
                $updateTime = $endUpdateTime - $startUpdateTime;
                $this->log("Call update sync products status in $updateTime ms", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'time' => $updateTime,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
                $endTime = round(microtime(true) * 1000);
                $time = $endTime - $startTime;
                $count = array_sum(array_map('count', $allIds));
                $this->log("Sync-ed $total / $count products to elasticsearch in $time ms", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action'  => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'total' => $total,
                    'time' => $time,
                    'synced_product_id' => json_encode($allIds),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            return $total;
        } catch (\Throwable $e) {
            logException($e);
            return $total;
        }
    }

    /**
     * @param $campaignIds
     * @param $campaigns
     * @param $products
     * @param $express_campaign
     * @param $processId
     * @return array
     * @throws \RuntimeException
     */
    private function process_campaign_products_collection($campaignIds, $campaigns, $products, $express_campaign = false, $processId = 0) {
        try {
            if ($this->enabledDebug) {
                $this->log("Start process express campaign products collection", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $startTime = round(microtime(true) * 1000);
            $campaignCollections = null;
            $campaignInfo = null;
            if(empty($campaignIds)) {
                return [
                    $campaigns,
                    $products,
                    $campaignCollections,
                    $campaignInfo
                ];
            }
            $campaignCollections = ProductCollection::getCollectionsForSyncByCampaignIds($campaignIds);
            foreach ($campaigns as &$campaign) {
                $key = $campaign['seller_id'] . '_' . $campaign['id'];
                if (!isset($campaignCollections[$key])) {
                    $campaign['collection_slugs'] = [];
                    $campaign['collection_ids'] = [];
                    continue;
                }

                $campaign['collection_slugs'] = $campaignCollections[$key]['slugs'];
                $campaign['collection_ids'] = $campaignCollections[$key]['ids'];
            }
            unset($campaign);
            if($express_campaign) {
                foreach ($products as &$product) {
                    $key = $product['seller_id'] . '_' . $product['campaign_id'];
                    if (!isset($campaignCollections[$key])) {
                        $product['collection_slugs'] = [];
                        $product['collection_ids'] = [];
                        continue;
                    }
                    $product['collection_slugs'] = $campaignCollections[$key]['slugs'];
                    $product['collection_ids'] = $campaignCollections[$key]['ids'];
                }
                unset($product);
                if ($this->enabledDebug) {
                    $endTime = round(microtime(true) * 1000);
                    $time = $endTime - $startTime;
                    $this->log("Process express campaign products collection in $time ms", [
                        'category' => $this->queueName,
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => "sync",
                        'run_time' => now()->toDateTimeString(),
                        'time' => $time,
                        'type' => $this->isJob ? 'Cronjob' : 'Manual',
                        'process_id' => $processId,
                    ]);
                }
                return [
                    $campaigns,
                    $products,
                    $campaignCollections,
                    $campaignInfo
                ];
            }
            $campaignInfo = [];
            foreach ($campaignIds as $sellerId => $productIds) {
                $seller = User::query()->find($sellerId);
                $productIds = array_unique($productIds);
                foreach (array_chunk($productIds, 1000) as $ids) {
                    Campaign::query()
                        ->onSellerConnection($seller)
                        ->select([
                            'id',
                            'name',
                            'slug',
                            'description',
                            'default_product_id',
                        ])
                        ->whereIn('id', $ids)
                        ->get()
                        ->each(function($campaign) use (&$campaignInfo){
                            $campaignInfo[$campaign->id] = [
                                'name' => $campaign->name,
                                'slug' => $campaign->slug,
                                'description' => $campaign->description,
                                'default_product_id' => $campaign->default_product_id,
                            ];
                        });
                }
            }
            foreach ($products as &$product) {
                $currentCampaignId = $product['campaign_id'];
                $key = $product['seller_id'] . '_' . $currentCampaignId;
                $currentCampaign = Arr::first($campaignInfo, static function ($campaign, $campaignId) use ($currentCampaignId) {
                    return $campaignId === $currentCampaignId;
                });
                $product['default_product_id'] = (!empty($currentCampaign['default_product_id']) && $product['id'] === $currentCampaign['default_product_id']) ? $product['id'] : 0;
                if (!isset($campaignCollections[$key])) {
                    $product['collection_slugs'] = [];
                    $product['collection_ids'] = [];
                    continue;
                }
                $product['collection_slugs'] = $campaignCollections[$key]['slugs'];
                $product['collection_ids'] = $campaignCollections[$key]['ids'];
            }
            unset($product);
            if ($this->enabledDebug) {
                $endTime = round(microtime(true) * 1000);
                $time = $endTime - $startTime;
                $this->log("Process campaign products collection in $time ms", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'time' => $time,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            return [
                $campaigns,
                $products,
                $campaignCollections,
                $campaignInfo,
            ];
        } catch (\Throwable $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    /**
     * @param $templateIds
     * @param $products
     * @param $campaignCollections
     * @param $campaignInfo
     * @param $express_campaign
     * @param $processId
     * @return array
     * @throws \RuntimeException
     */
    private function process_products_template($templateIds, $products, $campaignCollections, $campaignInfo, $express_campaign = false, $processId = 0) {
        try {
            if ($this->enabledDebug) {
                $this->log("Start process products express template", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            $startTime = round(microtime(true) * 1000);
            $templateIds = array_unique($templateIds);
            $productCategories = ProductCategory::getCategoriesForSyncByProductIds($templateIds);
            if($express_campaign) {
                foreach ($products as &$product) {
                    $product['category_slugs'] = [];
                    $product['category_ids'] = [];
                    $listCategoryName = '';
                    $listCollectionName = '';

                    if (isset($productCategories[$product['template_id']])) {
                        $product['category_slugs'] = $productCategories[$product['template_id']]['slugs'];
                        $product['category_ids'] = $productCategories[$product['template_id']]['ids'];
                        $listCategoryName = implode(', ', $productCategories[$product['template_id']]['name']);
                    }
                    $key = $product['seller_id'] . '_' . $product['campaign_id'];
                    if (!is_null($campaignCollections) && isset($campaignCollections[$key]) && !empty($campaignCollections[$key]['name'])) {
                        $listCollectionName = implode(', ', $campaignCollections[$key]['name']);
                    }
                    $product['search_text'] = '';
                    if (!empty($product['campaign_name'])) {
                        $product['search_text'] = $product['campaign_name'];
                    }
                    $product['search_text'] .= !empty($product['search_text']) ? ', ' . $product['name'] : $product['name'];
                    if (!empty($listCollectionName)) {
                        $product['search_text'] .= ', ' . $listCollectionName;
                    }
                    if (!empty($listCategoryName)) {
                        $product['search_text'] .= ', ' . $listCategoryName;
                    }
                }
                unset($product, $productCategories);
                if ($this->enabledDebug) {
                    $endTime = round(microtime(true) * 1000);
                    $time = $endTime - $startTime;
                    $this->log("Process products express template in $time ms", [
                        'category' => $this->queueName,
                        'user_type' => 'system',
                        'user_id' => null,
                        'action' => "sync",
                        'run_time' => now()->toDateTimeString(),
                        'time' => $time,
                        'type' => $this->isJob ? 'Cronjob' : 'Manual',
                        'process_id' => $processId,
                    ]);
                }
                return $products;
            }
            foreach ($products as &$product) {
                $product['category_slugs'] = [];
                $product['category_ids'] = [];
                $listCategoryName = '';
                $listCollectionName = '';
                if ($product['template_id'] > 0) {
                    if (isset($productCategories[$product['template_id']])) {
                        $product['category_slugs'] = $productCategories[$product['template_id']]['slugs'];
                        $product['category_ids'] = $productCategories[$product['template_id']]['ids'];
                        $listCategoryName = implode(', ', $productCategories[$product['template_id']]['name']);
                    }
                } elseif (isset($productCategories[$product['id']])) {
                    $product['category_slugs'] = $productCategories[$product['id']]['slugs'];
                    $product['category_ids'] = $productCategories[$product['id']]['ids'];
                    $listCategoryName = implode(', ', $productCategories[$product['id']]['name']);
                }

                $key = $product['seller_id'] . '_' . $product['campaign_id'];
                if (!is_null($campaignCollections) && isset($campaignCollections[$key]) && !empty($campaignCollections[$key]['name'])) {
                    $listCollectionName = implode(', ', $campaignCollections[$key]['name']);
                }

                if ($product['product_type'] === ProductType::PRODUCT) {
                    $product['search_text'] = $product['name'];
                    if (!empty($listCollectionName)) {
                        $product['search_text'] .= ', ' . $listCollectionName;
                    }
                    if (!empty($listCategoryName)) {
                        $product['search_text'] .= ', ' . $listCategoryName;
                    }
                }
            }
            unset($product, $productCategories);
            if (!empty($campaignInfo)) {
                foreach ($products as &$product) {
                    $item = $campaignInfo[$product['campaign_id']] ?? null;
                    if (is_null($item)) {
                        continue;
                    }
                    if (is_null($product['slug'])) {
                        $product['slug'] = $item['slug'];
                    }

                    if (($product['product_type'] === ProductType::PRODUCT)) {
                        $name = !empty($item['name']) ? $item['name'] . ', ' : '';
                        if (!empty($item['name'])) {
                            $product['campaign_name'] = $item['name'];
                        }
                        $description = !empty($item['description']) ? $item['description'] : '';
                        if ($product['default_product_id']) {
                            $description .= ', ' . $name; // increase search score for default product
                        }
                        $product['search_text'] = $name . ', ' . $product['search_text'] . ', ' . $description;
                        getSearchTextElastic($product['search_text']);
                    }
                }
                unset($product);
            }
            if ($this->enabledDebug) {
                $endTime = round(microtime(true) * 1000);
                $time = $endTime - $startTime;
                $this->log("Process products template in $time ms", [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => "sync",
                    'run_time' => now()->toDateTimeString(),
                    'time' => $time,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual',
                    'process_id' => $processId,
                ]);
            }
            return $products;
        } catch (\Throwable $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    /**
     * @param $data
     * @return void
     */
    private function convertUnRequiredToBool(&$data): void
    {
        if (is_array($data) && app()->isProduction()) {
            foreach ($data as &$value) {
                if (is_array($value)) {
                    $this->convertUnRequiredToBool($value);
                }
                if (isset($value['unrequired'])) {
                    $value['unrequired'] = (bool) $value['unrequired'];
                }
            }
        }
    }

    /**
     * @param $message
     * @param array $payload
     * @return void
     */
    private function log($message, array $payload = []): void
    {
        graylogInfo($message, $payload);
        if ($this->debugDiscord) {
            $embedDesc = [
                [
                    'description' => collect($payload)->map(function ($value, $key) {
                        if ($key === 'synced_product_id') {
                            $value = Str::isJson($value) ? json_decode($value, true, 512, JSON_THROW_ON_ERROR) : $value;
                            $value = array_slice($value, 0, 100);
                        }
                        return Str::headline($key) . ': ' . (is_array($value) ? json_encode($value, JSON_THROW_ON_ERROR) : $value);
                    })->join("\n"),
                    'color' => 1752220
                ]
            ];
            logToDiscord($message, DiscordChannel::DEV_DEBUG_LOGS, embeds: $embedDesc, threadId: 1379746184398245909);
        }
    }
}
