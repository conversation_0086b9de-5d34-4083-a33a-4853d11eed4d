<?php

namespace Modules\TiktokShop\Services;

use App\Enums\StoreStatusEnum;
use App\Models\Store;
use Exception;
use Illuminate\Support\Arr;
use Modules\TiktokShop\Models\TiktokShopInfo;
use Modules\TiktokShop\Models\TiktokShopSession;

class ConnectService
{
    public static function saveSession($accessToken, $refreshToken, $scopes, $subId, $sellerId): TiktokShopSession
    {
        return TiktokShopSession::query()
            ->updateOrCreate(
                ['seller_id' => $sellerId, 'sub_id' => $subId],
                [
                    'access_token' => $accessToken,
                    'refresh_token' => $refreshToken,
                    'scopes' => implode(',', $scopes),
                    'last_refreshed_at' => now(),
                ]
            );
    }

    /**
     * @throws Exception
     */
    public static function saveShopInfo($info, TiktokShopSession $session, $sellerId): TiktokShopInfo
    {
        $cipher = Arr::get($info, 'cipher');
        $name = Arr::get($info, 'name');
        $code = Arr::get($info, 'code');
        $type = Arr::get($info, 'seller_type');
        $region = Arr::get($info, 'region');
        $shopId = Arr::get($info, 'id');
        if (!$cipher || !$name || !$code || !$type || !$region) {
            throw new Exception('Invalid shop info: ' . json_encode($info));
        }

        if (self::hasConnectedByAnotherSeller($sellerId, $shopId)) {
            throw new Exception('Shop has been connected by another seller');
        }

        $shopInfo = TiktokShopInfo::query()
            ->where('seller_id', currentUser()->getUserId())
            ->where('shop_id', $shopId)
            ->where('session_id', $session->id)
            ->first();

        if ($shopInfo) {
            $shopInfo->update([
                'code' => $code,
                'type' => $type,
                'region' => $region,
                'cipher' => $cipher,
            ]);
            Store::query()
                ->where('id', $shopInfo->store_id)
                ->update([
                    'name' => 'TTS-'. $name,
                    'status' => StoreStatusEnum::ACTIVE,
                ]);
        } else {
            $store = Store::query()
                ->create([
                    'seller_id' => $sellerId,
                    'name' => 'TTS-'. $name,
                    'status' => StoreStatusEnum::ACTIVE,
                ]);
            $shopInfo = TiktokShopInfo::query()->create([
                'session_id' => $session->id,
                'store_id' => $store->id,
                'seller_id' => $sellerId,
                'shop_id' => $shopId,
                'code' => $code,
                'name' => $name,
                'type' => $type,
                'region' => $region,
                'cipher' => $cipher,
            ]);
        }
        return $shopInfo;
    }

    public static function clearInactiveShopInfo(array $activeShopIds, int $sellerId, int $sessionId): void
    {
        $query = TiktokShopInfo::query()
            ->where('seller_id', $sellerId)
            ->where('session_id', $sessionId)
            ->whereNotIn('id', $activeShopIds);
        $clone = clone $query;
        $storeIds = $clone->pluck('store_id')->toArray();
        Store::query()
            ->where('seller_id', $sellerId)
            ->whereIn('id', $storeIds)
            ->update(['status' => StoreStatusEnum::INACTIVE]);
        $query->delete();
    }

    private static function hasConnectedByAnotherSeller($sellerId, $shopId): bool
    {
        return TiktokShopInfo::query()
            ->where('seller_id', '!=', $sellerId)
            ->where('shop_id', $shopId)
            ->exists();
    }
}
