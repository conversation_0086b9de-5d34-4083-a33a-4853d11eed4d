<?php

namespace Modules\TiktokShop\Services;

use App\Enums\CacheKeys;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ShippingMethodEnum;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\User;
use App\Services\SyncStoreService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\TiktokShop\Models\TiktokShopInfo;

class SyncOrderService
{
    public static function parseCustomerAddress($order): array
    {
        $customerEmail = Arr::get($order, 'buyer_email');
        $customerPhone = Arr::get($order, 'recipient_address.phone_number');
        $customerName  = Arr::get($order, 'recipient_address.name');
        $orderNote     = Arr::get($order, 'buyer_message');
        $postcode      = Arr::get($order, 'recipient_address.postal_code');
        $country       = Arr::get($order, 'recipient_address.region_code');
        $address       = Arr::get($order, 'recipient_address.address_detail');

        $extraAddress  = Arr::get($order, 'recipient_address.district_info', []);
        foreach ($extraAddress as $detail) {
            $level = Arr::get($detail, 'address_level_name');
            $value = Arr::get($detail, 'address_name');
            switch ($level) {
                case 'City':
                    $city = $value;
                    break;
                case 'State':
                    $state = $value;
                    break;
                case 'County':
                    $county = $value;
                    break;
            }
        }
        if (isset($county)) {
            if (isset($city)) {
                $city = $city . ', ' . $county;
            } else {
                $city = $county;
            }
        }

        return [
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_phone' => $customerPhone,
            'address' => $address,
            'address_2' => '',
            'city' => $city ?? null,
            'state' => $state ?? null,
            'postcode' => $postcode,
            'country' => $country,
            'order_note' => $orderNote,
            'shipping_method' => ShippingMethodEnum::STANDARD,
        ];
    }

    public static function createOrder($externalOrderId, $sellerId, $orderData): ?Order
    {
        self::validateOrderData($orderData);
        $shardId = config('senprints.shard_id');
        $fixedData = [
            'shard_id' => $shardId,
            'seller_id' => $sellerId,
            'external_order_id' => $externalOrderId,
            'type' => OrderTypeEnum::FULFILLMENT,
            'status' => OrderStatus::DRAFT,
        ];
        return self::findOrderByExternalId($externalOrderId, $sellerId) ?? Order::query()->create(array_merge($orderData, $fixedData));
    }

    public static function findOrderByExternalId($externalOrderId, $sellerId): ?Order
    {
        return Order::query()->where('external_order_id', $externalOrderId)
            ->where('seller_id', $sellerId)
            ->first();
    }

    /**
     * Cung san pham co cung product_id
     * Variant phan biet boi sku_id
     */
    public static function processOrderProducts($ttsOrder, Order $order, $sellerId, &$logs): array
    {
        $orderProducts = self::formatOrderProducts($ttsOrder);
        $handled = [];
        TiktokShopService::logToDiscord('====processing ' . count($orderProducts) . ' order products...');
        foreach ($orderProducts as $externalId => $product) {
            $external_product_id = Arr::get($product, 'product_id');
            TiktokShopService::logToDiscord('======TTS Template ' . $external_product_id);
            $oldOrderProduct = self::getLastMappedOrderProduct($external_product_id, $sellerId);
            $template_id = null;
            $custom_options = Arr::get($product, 'sku_name');
            TiktokShopService::logToDiscord('======TTS Variant ' . $custom_options);
            $default_template_data = [];
            $oldOrderProductId = null;
            $productTemplate = null;
            if ($oldOrderProduct) {
                TiktokShopService::logToDiscord('======Found Template ' . $oldOrderProduct->template_id);
                $template_id = $oldOrderProduct->template_id ?? null;
                $oldOrderProductId = $oldOrderProduct->id;
                $productTemplate = Product::query()->whereKey($template_id)->first();
                if ($oldOrderProduct->custom_options !== $custom_options) {
                    $default_template_data = self::mappingOldOption($productTemplate, $custom_options, $logs);
                } else {
                    $default_template_data = [
                        'color' => $oldOrderProduct->color,
                        'size' => $oldOrderProduct->size,
                        'sku' => $oldOrderProduct->sku,
                        'options' => $oldOrderProduct->options,
                    ];
                }
            } else {
                $logs['Product' . $external_product_id] = 'Product ' . $external_product_id . ' not valid';
                $logs['Options' . $external_product_id] = 'Product ' . $external_product_id . ' options invalid';
            }
            $title = Arr::get($product, 'product_name') . ' / ' . $custom_options;
            $newOrderProduct = self::createOrUpdateOrderProduct($externalId, $external_product_id, $sellerId, $order->id, array_merge($default_template_data, [
                'ref_id' => User::query()->whereKey($sellerId)->value('ref_id'),
                'template_id' => $template_id,
                'external_fulfillment_id' => (string) Arr::get($ttsOrder, 'delivery_option_id'),
                'campaign_title' => $title,
                'product_name' => $title,
                'custom_options' => $custom_options,
                'quantity' => Arr::get($product, 'quantity'),
            ]));
            TiktokShopService::logToDiscord('======Created item ' . $newOrderProduct->id);
            if ($oldOrderProductId && $productTemplate) {
                try {
                    TiktokShopService::logToDiscord('======Copying design from ' . $oldOrderProductId . ' to ' . $newOrderProduct->id);
                    $hasDesign = self::copyDesigns($oldOrderProductId, $newOrderProduct);
                } catch (\Exception $e) {
                    $hasDesign = false;
                    TiktokShopService::logToDiscord('Error ' . $e->getMessage(), true);
                }
                if (!$hasDesign) {
                    $productTemplateName = $productTemplate->name;
                    TiktokShopService::logToDiscord('Product ' . $productTemplateName . ' has no design', 'tiktok_shop');
                    $logs['Product ' . $productTemplateName . ' has no design'] = 'Product ' . $productTemplateName . ' has no design';
                }
            }

            $handled[] = $externalId;
        }
        TiktokShopService::logToDiscord('======Finish create' . count($handled) . ' product(s)');
        return $handled;
    }

    public static function copyDesigns($lastOrderProductId, OrderProduct $newOrderProduct): bool
    {
        return SyncStoreService::copyDesigns($lastOrderProductId, $newOrderProduct);
    }

    // Lay lai order product cuoi cung seller da mapping voi product id ben thu 3
    public static function getLastMappedOrderProduct($externalProductId, $sellerId): ?OrderProduct
    {
        return OrderProduct::query()
            ->select([
                'id',
                'template_id',
            ])
            ->whereHas('designs')
            ->where('seller_id', $sellerId)
            ->whereNotNull('template_id')
            ->where(function (Builder $query) {
                $query->WhereNotNull('color')
                    ->orWhereNotNull('size');
            })
            ->where('external_product_id', $externalProductId)
            ->orderByDesc('updated_at')
            ->first();
    }

    private static function mappingOldOption(?Product $productTemplate, $custom_options, &$logs): array
    {
        if (!$productTemplate) {
            return [];
        }
        $variant_options = explode(',', $custom_options);
        $variant_options = array_map('trim', $variant_options);
        $variant_options = array_map('strtolower', $variant_options);

        $template_options = !empty($productTemplate->options) ? json_decode($productTemplate->options, true) : [];
        $options = [];
        if(!empty($template_options)) {
            foreach ($template_options as $option => $option_values) {
                foreach ($variant_options as $variant_option) {
                    $variant_option = trim($variant_option);
                    if(!isset($options[$option])) {
                        $options[$option] = null;
                    }
                    if (in_array($variant_option, $option_values, true)) {
                        $options[$option] = $variant_option;
                        unset($logs[$option]);
                        break;
                    }

                    $logs[$option] = 'Custom Option for ' . $option . ' not valid';
                }
            }
        }

        return [
            'color' => $options['color'] ?? null,
            'size' =>  $options['size'] ?? null,
            'sku' => $productTemplate->sku,
            'options' => !empty($options) ? json_encode($options) : null,
        ];
    }

    /**
     * Gop nhom cac san pham.
     * Vi San pham cung 1 loai nhung tiktok tra ve nhieu lan thay vi tra ve quantity
     * @param $order
     * @return array
     */
    private static function formatOrderProducts($order): array
    {
        $orderProducts = Arr::get($order, 'line_items', []);
        $orderId = Arr::get($order, 'id');
        $mergeProducts = [];
        foreach ($orderProducts as $product) {
            $external_product_id = Arr::get($product, 'product_id');
            $skuId = Arr::get($product, 'sku_id');
            $external_id = md5($orderId . $external_product_id . $skuId);
            if (isset($mergeProducts[$external_id])) {
                $mergeProducts[$external_id]['quantity'] += 1;
            } else {
                $mergeProducts[$external_id] = $product;
                $mergeProducts[$external_id]['quantity'] = 1;
            }
        }

        return $mergeProducts;
    }

    private static function validateOrderData($orderData): void
    {
        $requiredFields = [
            'order_number_2',
            'store_domain',
            'store_name',
            'customer_name',
            'customer_email',
            'customer_phone',
            'address',
            'city',
            'state',
            'postcode',
            'country',
            'order_note',
            'fulfill_status',
            'store_id',
            'shipping_method',
            'ref_id'
        ];
        foreach ($requiredFields as $field) {
            if (!Arr::has($orderData, $field)) {
                throw new \InvalidArgumentException("Field $field is required");
            }
        }
    }

    private static function createOrUpdateOrderProduct($external_id, $external_product_id, $sellerId, $orderId, $orderProductData): OrderProduct
    {
        $fixedData = [
            'shard_id' => config('senprints.shard_id'),
        ];
        $uniqueData = [
            'external_id' => $external_id,
            'external_product_id' => $external_product_id,
            'seller_id' => $sellerId,
            'order_id' => $orderId,
        ];
        // handle unexpected case
        OrderProduct::query()->where([
            'order_id' => 0,
        ])->forceDelete();
        $orderProduct = OrderProduct::query()->where($uniqueData)->first();
        if ($orderProduct) {
            $orderProduct->update(array_merge($orderProductData, $fixedData));
            $orderProduct->refresh();
            return $orderProduct;
        }
        return OrderProduct::create(array_merge($orderProductData, $fixedData, $uniqueData));
    }

    public static function isHasShippingCarrier($ttsOrder, TiktokShopInfo $shop): bool
    {
        $accessToken = $shop->session->access_token;
        $cipher = $shop->cipher;

        $id = (string) Arr::get($ttsOrder, 'delivery_option_id');
        $data = TiktokShopClient::getShippingProvider($accessToken, $cipher, $id);
        $ttsAcceptShippingCarrier = array_column($data, 'name');
        $availableShippingCarrier = self::getAvailableShippingCarrier();
        foreach ($ttsAcceptShippingCarrier as $carrier) {
            foreach ($availableShippingCarrier as $availableCarrier) {
                if (Str::contains(strtolower($carrier), strtolower($availableCarrier))) {
                    return true;
                }
            }
        }

        return false;
    }

    private static function getAvailableShippingCarrier() {
        return cache()->remember(CacheKeys::SHIPPING_CARRIERS, CacheKeys::CACHE_30D, static function () {
            return OrderProduct::selectRaw('shipping_carrier, COUNT(*) as total')
                ->whereNotNull('shipping_carrier')
                ->where('shipping_carrier', '<>', '')
                ->having('total', '>', 1000)
                ->groupBy('shipping_carrier')
                ->orderBy('total', 'DESC')
                ->get()
                ->pluck('shipping_carrier')
                ->toArray();
        });
    }
}
