<?php

namespace Mo<PERSON>les\TiktokShop\Providers;
class CommandServiceProvider extends TiktokShopServiceProvider
{
    public function boot()
    {
        $commandDir = dirname(__DIR__) . '/Commands';
        $commands = $this->scanFolder($commandDir);
        $_commands = array();
        foreach ($commands as $command) {
            if (file_exists($commandDir . '/' . $command)) {
                $command = basename($command, ".php");
                $_commands[] = "Modules\\TiktokShop\\Commands\\{$command}";
            }
        }
        $this->commands($_commands);
    }
}
