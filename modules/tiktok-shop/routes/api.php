<?php

use Illuminate\Support\Facades\Route;
use Modules\TiktokShop\Http\Controllers\TiktokShopController;

Route::group(['prefix' => 'seller', 'middleware' => 'auth.access.token'], function () {
    // TiktokShop Router
    Route::group(['prefix' => 'tiktok-shop'], function () {
        Route::get('/', [TiktokShopController::class, 'index']);
        Route::group(['prefix' => 'auth'], function () {
            Route::get('connect', [TiktokShopController::class, 'connectStore']);
            Route::get('callback', [TiktokShopController::class, 'authCallback']);
        });
        Route::post('/webhook', [TiktokShopController::class, 'handleWebhook'])->withoutMiddleware('auth.access.token');
        Route::post('/disconnect', [TiktokShopController::class, 'disconnectStore']);
    });
});
