<?php

namespace Tests\Unit;

use App\Contracts\ProductTemplateServiceContract;
use App\Enums\ProductStatus;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductVariant;
use App\Services\ProductTemplateService;
use Database\Factories\ProductFactory;
use InvalidArgumentException;
use League\Flysystem\FilesystemException;
use League\Flysystem\UnableToCheckExistence;
use Mockery\MockInterface;
use Tests\TestCase;
use Mockery;
use Illuminate\Database\Eloquent\Builder;
use App\Data\Product\PendingFileData;
use App\Enums\StorageDisksEnum;

class ProductTemplateServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_copyPendingFiles()
    {
        // Create a mock of the Storage class
        $storageMock = Mockery::mock('alias:Illuminate\Support\Facades\Storage');

        // Set up the expectations for the mock
        $storageMock->shouldReceive('disk')->with(StorageDisksEnum::DEFAULT)->andReturnSelf();
        $storageMock->shouldReceive('exists')->andReturn(true);
        $storageMock->shouldReceive('copy')->andReturn(true);

        // Create an instance of the ProductTemplateHelper class
        $productTemplateHelper = new ProductTemplateService();

        // Add some pending files to the helper
        $pendingFile1 = PendingFileData::from(['old_path' => 'old/path1', 'new_path' => 'new/path1']);
        $pendingFile2 = PendingFileData::from(['old_path' => 'old/path2', 'new_path' => 'new/path2']);
        $productTemplateHelper->enqueuePendingFile($pendingFile1);
        $productTemplateHelper->enqueuePendingFile($pendingFile2);

        // Call the copyPendingFiles method
        $result = $productTemplateHelper->copyPendingFiles();

        // Assert that the files were copied successfully
        $this->assertTrue($result);
    }

    public function test_cloneMockups_success()
    {
        $mockupMock = $this->partialMock(File::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('file_url')->andReturn('p/1/file.jpg');
            $mock->shouldReceive('getAttribute')->with('file_url_2')->andReturn('p/1/file2.jpg');
            $mock->shouldReceive('getAttribute')->with('design_json')->andReturn('{"url": "p/1/file.jpg"}');
        });
        $productTemplateMock = $this->partialMock(Product::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('id')->andReturn(1);
        });
        $newProductTemplateMock = $this->partialMock(Product::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('id')->andReturn(2);
        });
        $serviceMock = $this->partialMock(ProductTemplateService::class);
        $serviceMock->pendingFiles = collect();
        $serviceMock->productTemplate = $productTemplateMock;
        $serviceMock->newProductTemplate = $newProductTemplateMock;
        $result = $serviceMock->cloneMockup($mockupMock);
        $expected = [
            'product_id' => 2,
            'file_url' => 'p/2/file.jpg',
            'file_url_2' => 'p/2/file2.jpg',
            'design_json' => '{"url":"p/2/file.jpg"}'
        ];
        $this->assertEquals(array_diff_assoc($expected, $result), []);
        $this->assertArrayHasKey('product_id', $result);
    }

    public function test_cloneVideos_success()
    {
        $videoMock = $this->partialMock(File::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('file_url')->andReturn('p/1/video/6a6b89cf782ce083215b8d8798ffd767.mp4');
            $mock->shouldReceive('getAttribute')->with('option')->andReturn('{"thumbnail":"p\/1\/video_thumbnail\/a26fa5d193eb8a54efe7a9249ec04a27.png"}');
        });
        $productTemplateMock = $this->partialMock(Product::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('id')->andReturn(1);
        });
        $newProductTemplateMock = $this->partialMock(Product::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('id')->andReturn(2);
        });
        $serviceMock = $this->partialMock(ProductTemplateService::class);
        $serviceMock->pendingFiles = collect();
        $serviceMock->productTemplate = $productTemplateMock;
        $serviceMock->newProductTemplate = $newProductTemplateMock;
        $result = $serviceMock->cloneVideo($videoMock);
        $expected = [
            'product_id' => 2,
            'file_url' => 'p/2/video/6a6b89cf782ce083215b8d8798ffd767.mp4',
            'option' => '{"thumbnail":"p/2/video_thumbnail/a26fa5d193eb8a54efe7a9249ec04a27.png"}'
        ];
        $this->assertEquals(array_diff_assoc($expected, $result), []);
        $this->assertArrayHasKey('product_id', $result);
    }
}
