<?php

namespace Tests\Unit;

use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;
use Elasticsearch\Common\Exceptions\RuntimeException;
use Http\Client\HttpAsyncClient;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Http\Client\ClientInterface;
use Psr\Log\LoggerInterface;
use Tests\TestCase;

class ElasticSearchTest extends TestCase
{
    protected ClientInterface $httpClient;
    protected LoggerInterface $logger;
    protected HttpAsyncClient $asyncHttpClient;
    protected ClientBuilder $builder;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->httpClient = $this->createStub(ClientInterface::class);
        $this->asyncHttpClient = $this->createStub(HttpAsyncClient::class);
        $this->logger = $this->createStub(LoggerInterface::class);
        $this->builder = ClientBuilder::create();
    }

    public function testCreate()
    {
        $this->assertInstanceOf(ClientBuilder::class, $this->builder);
    }

    public function testFromConfigWithInvalidDataQuietFalseThrowsException()
    {
        $config = [
            'httpClient' => $this->httpClient,
            'logger' => $this->logger,
            'foo' => 'bar'
        ];
        $this->expectException(RuntimeException::class);
        ClientBuilder::fromConfig($config);
    }

    public function testFromConfigWithInvalidDataQuietTrue()
    {
        $config = [
            'httpClient' => $this->httpClient,
            'logger' => $this->logger,
            'foo' => 'bar'
        ];
        $client = ClientBuilder::fromConfig($config, true);
        $this->assertInstanceOf(Client::class, $client);
    }

    public function testBuild()
    {
        $this->assertInstanceOf(Client::class, $this->builder->build());
    }

    public function testSetHosts()
    {
        $result = $this->builder->setHosts(['localhost:9200']);
        $this->assertEquals($this->builder, $result);
    }

    /**
     * @throws \Throwable
     */
    public function testConnectionCanCount()
    {
        $url = route('total-es-campaigns-today');
        $response = $this->get($url);
        $response->assertStatus(200)
        ->assertJsonStructure([
            'result'
        ]);
    }
}
