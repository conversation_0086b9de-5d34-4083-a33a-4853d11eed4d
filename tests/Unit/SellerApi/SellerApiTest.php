<?php

namespace Tests\Unit\SellerApi;

class SellerApiTest extends SellerApiTrait
{
    /**
     * @test
     */
    public function seller_can_get_his_profile(): void
    {
        $response = $this->getJson(route('seller-api.me'));

        $response
            // ->dump()
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'user' => [
                    'id',
                    'email',
                    'name',
                ],
            ]);

        $this->assertEquals($this->seller->id, $response->json('user.id'));
    }

    /**
     * @test
     */
    public function seller_can_get_catalog_list(): void
    {
        $response = $this->getJson(route('seller-api.catalog.index'));

        $response
            // ->dd()
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'slug',
                        'products' => [
                            '*' => [
                                'id',
                                'name',
                                'sku',
                                'thumb_url',
                                'base_cost',
                                'suggested_price',
                                'options',
                                'print_spaces',
                            ]
                        ]
                    ]
                ]
            ]);
    }

    /**
     * @test
     */
    public function seller_can_get_catalog_info(): void
    {
        $template = $this->createTemplateProduct();
        $response = $this->getJson(route('seller-api.catalog.show', [
            'id' => $template->id
        ]));

        $response
            // ->dd()
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'data' => [
                    'variants'       => [
                        '*' => [
                            'variant_key',
                            'base_cost',
                            'location_code',
                            'price',
                            'quantity',
                            'out_of_stock',
                        ]
                    ],
                    'shipping_rules' => [
                        '*' => [
                            'shipping_method',
                            'location_code',
                            'extra_cost',
                            'shipping_cost',
                        ]
                    ],
                    'description',
                    'size_guides'    => [
                        '*' => [
                            'size',
                            'length',
                            'width',
                            'sleeve',
                            'height',
                            'weight',
                        ]
                    ],
                    'shipping_times' => [
                        'standard' => [
                            '*' => [
                                'min',
                                'max',
                            ]
                        ],
                        'express'  => [
                            '*' => [
                                'min',
                                'max',
                            ]
                        ],
                    ],
                    'processing_time',
                ]
            ]);
    }
}
