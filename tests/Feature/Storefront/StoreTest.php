<?php

namespace Tests\Feature\Storefront;

use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Models\Order;
use Tests\TestCase;

class StoreTest extends TestCase
{
    public function test_get_store_info(): void
    {
        // get store from the latest order
        $newOrder = Order::query()
            ->select('store_id', 'store_domain')
            ->where([
                'type' => OrderTypeEnum::REGULAR,
                'status' => OrderStatus::PROCESSING,
                'payment_status' => OrderPaymentStatus::PAID
            ])
            ->whereNotNull(['store_id', 'store_domain'])
            ->latest()
            ->first();

        if (!$newOrder) {
            $this->fail('No order found');
        }

        $response = $this->withHeader('x-domain', $newOrder->store_domain)
            ->get(route('public.store.info'));

        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => ['storeInfo', 'generalSettings'],
                'message'
            ]);

        echo 'All tests have been run.';
    }
}
