<?php

namespace Tests\Feature\Checkout;

use App\Enums\OrderStatus;
use App\Enums\PaymentMethodEnum;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Traits\Encrypter;
use Tests\TestCase;

/**
 * To run checkout tests, run the following command:
 * php artisan test --filter "Tests\\Feature\\Checkout"
 */
class CheckoutStripeTest extends TestCase
{
    use Encrypter;

    private const ENDPOINT = '/public/order/stripe/get-intent-order';

    public function test_load_stripe_config(): void
    {
        $stripeConfig = self::loadStripe('config');
        $this->assertNotNull($stripeConfig);
    }

    public function test_create_payment_intent(): void
    {
        $paymentGatewayId = self::loadStripe('id');

        if (!$paymentGatewayId) {
            $this->fail('Payment gateway is not exist');
        }

        $accessToken = Order::query()
            ->where([
                'status' => OrderStatus::PENDING,
                'payment_method' => PaymentMethodEnum::STRIPE
            ])
            ->when(!isOnDevDatabase(), fn ($query) => $query->where('region', 'us'))
            // fix: Amount must be no more than $999,999.99
            ->where('total_amount', '<', 1000)
            ->where('total_amount', '>', 1)
            ->where('created_at', '<', now()->subDays(7))
            ->orderByDesc('created_at')
            ->value('access_token');

        $url = self::ENDPOINT . '?order_token=' . $accessToken . '&gateway_id=' . $paymentGatewayId;
        $response = $this->get($url);

        $response
            ->assertStatus(200)
            ->assertJsonStructure(['success', 'data', 'message'])
            ->assertJson(['success' => true]);
    }

    private static function loadStripe($column)
    {
        $fields = [$column];

        if ($column === 'config') {
            $fields[] = 'encrypted';
        }

        $gateway = PaymentGateway::query()
            ->where([
                'gateway' => PaymentMethodEnum::STRIPE,
                'active' => 1
            ])
            ->first($fields);

        if (!$gateway) {
            return null;
        }

        return $column === 'config'
            ? self::safeLoadConfig($gateway)
            : $gateway->$column;
    }
}
