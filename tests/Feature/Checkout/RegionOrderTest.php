<?php

namespace Tests\Feature\Checkout;

use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Models\Campaign;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\Store;
use App\Models\User;
use Illuminate\Testing\TestResponse;
use Tests\TestCase;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Models\RegionOrderProducts;

class RegionOrderTest extends TestCase
{
    private ?string $domain = null;

    private const REGION = 'us';
    public function setUp(): void
    {
        parent::setUp();
        $this->domain = Store::query()->whereId(1)->value('domain');
    }

    public function test_create_order(): void
    {
        // make sure we don't create a duplicate order
        self::cleanUp();

        // test with last order product
        $lastOrderProduct = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->where([
                'order.type' => OrderTypeEnum::REGULAR,
                'order.payment_status' => OrderPaymentStatus::PAID
            ])
            ->orderByDesc('order.paid_at')
            ->first();
        if (!$lastOrderProduct) {
            self::fail('No order product found');
        }
        $seller = User::query()->find($lastOrderProduct->seller_id);
        // get campaign, product must have options
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->find($lastOrderProduct->campaign_id);

        if (!$campaign) {
            self::fail('Campaign not found');
        }

        // get a product from DB
        $product = Product::query()
            ->onSellerConnection($seller)
            ->find($lastOrderProduct->product_id);
        if (!$product) {
            self::fail('Product not found');
        }
        // get options
        $options = json_decode($lastOrderProduct->options, true);

        $order = [
            'products' => [
                [
                    'campaign_id' => $campaign->id,
                    'campaign_slug' => $campaign->slug,
                    'campaign_title' => $campaign->name,
                    'currency_code' => 'USD',
                    'isCheckQuantity' => true,
                    'options' => $options,
                    'price' => $product->price,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_url' => '/' . $campaign->slug,
                    'quantity' => 1,
                    'thumb_url' => $product->thumb_url ?? 'default.png',
                    'seller_id' => $campaign->seller_id,
                    'via' => str_replace(['mysql_', 'mysql'], ['m', ''], $campaign->seller->getPrivateConnection()),
                ]
            ]
        ];
        $response = $this
            ->withHeader('x-domain', $this->domain)
            ->post('v2/public/order/create', $order);

        $response
            ->assertStatus(200)
            ->assertJson(['success' => true]);
    }

    public function test_update_order(): void
    {
        $response = $this->withHeader('x-domain', $this->domain)
            ->post('v2/public/order/update', [
                'order_token' => OrderTest::ORDER_TOKEN,
                'email' => '<EMAIL>'
            ]);

        $response
            ->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonStructure([
                'success',
                'data' => ['order', 'shipping_methods'],
                'message'
            ]);
    }

    public function test_update_tip(): void
    {
        $response = $this->updateTip(0.6);
        $this->assertEquals(0.6, $response->json('data.order.tip_amount'), "The tip_amount update failed.");

        $response = $this->updateTip('xyz');
        $response->assertStatus(200)
            ->assertJson(['success' => false])
            ->assertJsonStructure([
                'message' => ['tip_amount']
            ]);

        $response = $this->updateTip(-1);
        $response->assertStatus(200)
            ->assertJson(['success' => false])
            ->assertJsonStructure([
                'message' => ['tip_amount']
            ]);

        $response = $this->updateTip(9999);
        $totalProductAmount = $response->json('data.order.total_product_amount');
        $tipAmount = $response->json('data.order.tip_amount');
        $this->assertEquals(round($totalProductAmount * 0.5, 2), $tipAmount, "The tip_amount should be 50% of total_product_amount.");
    }

    public function test_update_insurance_fee(): void
    {
        $response = $this->updateInsurance(false);
        $this->assertEquals(0, $response->json('data.order.insurance_fee'), "The insurance_fee should be 0 when insurance is false.");

        $response = $this->updateInsurance(true);
        $this->assertGreaterThan(0, $response->json('data.order.insurance_fee'), "The insurance_fee should be greater than 0 when insurance is true.");

        $response = $this->updateInsurance('xyz');
        $this->assertEquals(0, $response->json('data.order.insurance_fee'), "The insurance_fee should be 0 when insurance is invalid.");

        $response = $this->updateInsurance("-1");
        $this->assertEquals(0, $response->json('data.order.insurance_fee'), "The insurance_fee should be 0 when insurance is negative.");

        $response = $this->updateInsurance("1");
        $this->assertGreaterThan(0, $response->json('data.order.insurance_fee'), "The insurance_fee should be greater than 0 when insurance is true.");
    }

    public function test_update_shipping_method(): void
    {
        $this->updateShippingMethod('standard');
        $response = $this->getTestOrder();
        $availableShippingMethod = $response->json('data.shipping_methods.name');
        $this->assertEquals('standard', $response->json('data.order.shipping_method'));

        $this->updateShippingMethod('express');
        if ($availableShippingMethod === 'express') {
            $this->assertEquals('express', $response->json('data.order.shipping_method'));
        } else {
            $this->assertEquals('standard', $response->json('data.order.shipping_method'));
        }

        $response = $this->updateShippingMethod('anya');
        $response->assertStatus(200)
            ->assertJson(['success' => false])
            ->assertJsonStructure([
                'message' => ['shipping_method']
            ]);
    }

    public function test_update_discount_code(): void
    {
        $response = $this->withHeader('x-domain', $this->domain)
            ->post('/v2/public/order/update', [
                'order_token' => OrderTest::ORDER_TOKEN,
                'discount_code' => 'anya_discount_code',
                'ref' => 'apply_discount_code'
            ]);
        $response
            ->assertStatus(200)
            ->assertJson(['success' => false]);

    }

    public function test_get_order(): void
    {
        $response = $this->getTestOrder();

        $response
            ->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'order' => ['id']
                ],
                'message'
            ]);

        // clean up, remove test order
        self::cleanUp();
    }

    private static function cleanUp(): void
    {
        $order = RegionOrders::onRegion(self::REGION)
            ->where('access_token', OrderTest::ORDER_TOKEN)
            ->withTrashed()
            ->first('id');

        if (!$order) {
            return;
        }

        // delete products and order
        RegionOrderProducts::onRegion(self::REGION)
            ->where('order_id', $order->id)
            ->forceDelete();
        $order->forceDelete();
    }

    protected function updateTip($amount): TestResponse
    {
        return $this->withHeader('x-domain', $this->domain)
            ->post('/v2/public/order/update', [
                'order_token' => OrderTest::ORDER_TOKEN,
                'tip_amount' => $amount
            ]);
    }

    protected function updateInsurance($value): TestResponse
    {
        return $this->withHeader('x-domain', $this->domain)
            ->post('/v2/public/order/update', [
                'order_token' => OrderTest::ORDER_TOKEN,
                'delivery_insurance' => $value
            ]);
    }

    protected function updateShippingMethod($value): TestResponse
    {
        return $this->withHeader('x-domain', $this->domain)
            ->post('/v2/public/order/update', [
                'order_token' => OrderTest::ORDER_TOKEN,
                'shipping_method' => $value
            ]);
    }

    protected function getTestOrder(): TestResponse
    {
        return $this->withHeader('x-domain', $this->domain)
            ->get('/v2/public/order/' . OrderTest::ORDER_TOKEN);
    }

    public function test_get_order_status(): void
    {
        $order = RegionOrders::onRegion(self::REGION)
            ->where('status', OrderStatus::PROCESSING)
            ->first(['access_token']);

        if (!$order) {
            self::fail('No processing order found');
        }

        $response = $this->get('/public/order/' . $order->access_token . '/detail');

        $response
            ->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'order' => ['id'],
                    'fulfillments',
                    'timeframe'
                ],
                'message'
            ]);
    }
}
