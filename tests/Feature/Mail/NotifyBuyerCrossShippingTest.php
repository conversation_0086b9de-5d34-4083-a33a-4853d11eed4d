<?php

namespace Tests\Feature\Mail;

use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class NotifyBuyerCrossShippingTest extends TestCase
{
    /**
     * L<PERSON>y đơn hàng có các lịch sử gắn sup chưa thông báo
     *
     * @return void
     */
    public function test_get_order_has_not_been_notified(): void
    {
        $this->assertTrue(
            Order::loadHasNotBeenNotifiedCrossShipping(5)
                ->every(
                    fn(Order $order) => $order->assign_supplier_histories->some(fn($r) => ! $r->notified)
                )
        );
    }
}
