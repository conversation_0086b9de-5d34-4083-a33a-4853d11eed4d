<?php

namespace Database\Seeders;

use App\Enums\PageTypeEnum;
use App\Models\Page;
use App\Models\Store;
use App\Models\User;
use Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PageTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker\Factory::create();
        $pageNames = ['About', 'Shipping Policy', 'Return Policy', 'Terms of Service', 'Contact Us'];

        $stores = Store::query()
            ->select('id', 'seller_id')
            ->whereIn('seller_id', User::role('seller')->get()->modelKeys()) // Only juno_okyo seller account: replace $sellers if you want to get all sellers
            ->get();

        $dataTemplates = [];

        for ($i = 0; $i < 5; $i++) {
            // Create template system
            $type = 'template';
            $title = $pageNames[$i];
            $store = $faker->randomElement($stores);
            $mainSlug = Str::slug($title);
            $slug = $mainSlug;

            while (
            Page::query()
                ->where([
                    'slug' => $slug,
                    'store_id' => $store->id
                ])
                ->exists()
            ) {
                $slug = $mainSlug . '-' . $faker->lexify('????');
            }

            $dataTemplates[] = [
                'type' => $type,
                'title' => $title,
                'slug' => strtolower($slug),
                'content' => $faker->realText(),
                'status' => $faker->randomElement([0, 1]),
            ];
        }

        Page::query()->insert($dataTemplates);

        $dataPages = [];

        for ($i = 0; $i <= 30; $i++) {
//            $sellers = User::getUserByRoleName('seller', [
//                'select' => ['id', 'name'],
//            ], 15);

            // Create custom page
            $templatePages = Page::query()
                ->where('type', PageTypeEnum::TEMPLATE)
                ->get();

            $pageInfo = $faker->randomElement($templatePages);
            $type = PageTypeEnum::CUSTOM;
            $title = $pageInfo->title;
            $templateId = $pageInfo->id;
            $store = $faker->randomElement($stores);
            $mainSlug = Str::slug($title);
            $slug = $mainSlug;

            while (
            Page::query()
                ->where([
                    'slug' => $slug,
                    'store_id' => $store->id
                ])
                ->exists()
            ) {
                $slug = $mainSlug . '-' . $faker->lexify();
            }

            $dataPages[] = [
                'type' => $type,
                'title' => $title,
                'slug' => strtolower($slug),
                'content' => $faker->realText(),
                'template_id' => $templateId,
                'seller_id' => $store->seller_id,
                'store_id' => $store->id,
                'status' => $faker->randomElement([0, 1]),
            ];
        }

        Page::query()->insert($dataPages);

        // free resources
        unset($dataTemplates, $dataPages);
    }
}
