<?php

namespace Database\Seeders;

use App\Models\StoreProduct;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StoreProductTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = User::query()
            ->has('stores')
            ->has('products')
            ->with(['stores:id,seller_id', 'products:id,seller_id'])
            ->get('id');

        if ($users->count() === 0) {
            return;
        }

        try {
            DB::beginTransaction();

            $stores = [];

            foreach ($users as $user) {
                foreach ($user->stores as $store) {
                    foreach ($user->products as $key => $product) {
                        $stores[] = [
                            'store_id'   => $store->id,
                            'product_id' => $product->id,
                        ];

                        // limit 5 product each store
                        if ($key === 5) {
                            break;
                        }
                    }
                }
            }

            foreach (array_chunk($stores, 1000) as $data) {
                StoreProduct::query()->insertOrIgnore($data);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
        }
    }
}
