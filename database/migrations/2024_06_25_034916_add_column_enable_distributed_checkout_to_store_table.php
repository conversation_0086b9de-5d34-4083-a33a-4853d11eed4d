<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            if (!Schema::hasColumn('store', 'enable_distributed_checkout')) {
                $table->boolean('enable_distributed_checkout')->default(0)->after('enable_search');
            }
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
