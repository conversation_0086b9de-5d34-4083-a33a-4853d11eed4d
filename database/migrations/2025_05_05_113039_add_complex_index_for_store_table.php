<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            $table->index(['stripe_gateway_id', 'paypal_gateway_id', 'status', 'id', 'deleted_at'], 'idx_gateways_status_id_deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
