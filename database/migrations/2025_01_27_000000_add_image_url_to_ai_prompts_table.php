<?php

use App\Enums\PromptTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_prompts', function (Blueprint $table) {
            $table->string('image_url')->nullable()->after('prompt');

            $table->enum('type', PromptTypeEnum::asArray())
                  ->default(PromptTypeEnum::MOCKUP)
                  ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::table('ai_prompts', function (Blueprint $table) {
        //     $table->dropColumn('image_url');

        //     // Revert the enum column to exclude the 'example' value
        //     $oldValues = [PromptTypeEnum::MOCKUP, PromptTypeEnum::DESIGN, PromptTypeEnum::MOCKUP_TEMPLATE];
        //     $table->enum('type', $oldValues)
        //           ->default(PromptTypeEnum::MOCKUP)
        //           ->change();
        // });
    }
};
