<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contest', function (Blueprint $table) {
            $table->boolean('need_join_contest')->default(true)->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
