<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterDropUniqueDomainAndSubDomainInStoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('store', 'domain')) {
            Schema::table(
                'store',
                function (Blueprint $table) {
                    $table->dropUnique('domain');
                }
            );
        }
        if (Schema::hasColumn('store', 'sub_domain')) {
            Schema::table(
                'store',
                function (Blueprint $table) {
                    $table->dropUnique('sub_domain');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
