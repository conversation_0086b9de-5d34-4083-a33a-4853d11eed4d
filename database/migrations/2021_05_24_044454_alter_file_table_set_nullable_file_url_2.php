<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterFileTableSetNullableFileUrl2 extends Migration
{
    private string $table = 'file';
    private string $column = 'file_url_2';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(
            $this->table,
            function (Blueprint $table) {
                $table->string($this->column)->nullable()->after('file_url')->change();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
