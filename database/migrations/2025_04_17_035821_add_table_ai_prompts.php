<?php

use App\Enums\PromptTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_prompts', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary();
            $table->enum('type', PromptTypeEnum::asArray())->default(PromptTypeEnum::MOCKUP)->index();
            $table->longText('prompt');
            $table->bigInteger('seller_id')->default(0)->index();
            $table->bigInteger('campaign_id')->default(0)->index();
            $table->bigInteger('product_id')->default(0)->index();
            $table->timestamps();
            $table->softDeletes();
            $table->index(['seller_id', 'campaign_id']);
            $table->index(['seller_id', 'product_id']);
            $table->index(['seller_id', 'product_id', 'campaign_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
