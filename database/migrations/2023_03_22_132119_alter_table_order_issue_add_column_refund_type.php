<?php

use App\Enums\OrderIssueChargeTypeEnum;
use App\Enums\OrderIssueRefundTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableOrderIssueAddColumnRefundType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(
            'order_issue',
            function (Blueprint $table) {
                //update column charge type
                if (Schema::hasColumn('order_issue', 'charge_type')) {
                    $table->enum('charge_type', OrderIssueChargeTypeEnum::getValues())->change();
                }
                //update column charge amount
                if (Schema::hasColumn('order_issue', 'amount')) {
                    $table->renameColumn('amount', 'charge_amount');
                }
                // add column refund type
                if (!Schema::hasColumn('order_issue', 'refund_type')) {
                    $table->enum('refund_type', OrderIssueRefundTypeEnum::getValues())->nullable()->after('charge_type');
                }
                // add column refund amount
                if (!Schema::hasColumn('order_issue', 'refund_amount')) {
                    $table->double('refund_amount')->after('amount');
                }
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
