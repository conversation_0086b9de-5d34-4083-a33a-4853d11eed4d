<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSellerBillingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seller_billing', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('seller_id')->unsigned()->index('seller_id');
            $table->float('amount', 10,0);
            $table->bigInteger('payment_account_id')->nullable()->index('payment_account_id');
            $table->string('detail');
            $table->float('balance', 10,0);
            $table->enum('type', ['sale','commission','refund','payout','other'])->comment('\'sale\',\'commission\',\'refund\',\'payout\',\'other\'');
            $table->enum('status', ['pending', 'processing', 'completed', 'on hold', 'cancelled'])->comment("pending, processing, completed, on hold, cancelled");
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
