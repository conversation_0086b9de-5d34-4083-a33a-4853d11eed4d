<?php

use App\Enums\UserTopupTransactionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserTopupTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_topup_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->unsignedBigInteger('seller_id');
            $table->double('amount_usd')->default(0)->comment('currency USD');
            $table->double('amount_vnd')->default(0)->comment('Convert amount to VND');
            $table->string('code', 15)->index('topup_transaction_code');
            $table->text('detail')->comment('Sms detail of transaction')->nullable();
            $table->enum('status', UserTopupTransactionStatus::getValues())
                ->comment("'" . implode("','", UserTopupTransactionStatus::getValues()) . "'")
                ->default(UserTopupTransactionStatus::PENDING);
            $table->timestamps();

            $table->foreign('seller_id')
                ->references('id')
                ->on('user')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
