<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentGatewaysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();


            $table->unsignedBigInteger('seller_id')
                ->nullable()
                ->index('seller_id')
                ->comment('Null = system');

            $table->foreign('seller_id')
                ->references('id')
                ->on('user');

            $table->string('name');
            $table->enum('gateway', [
                'paypal',
                'stripe',
                'cod'
            ]);
            $table->json('config');
            $table->boolean('active');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
