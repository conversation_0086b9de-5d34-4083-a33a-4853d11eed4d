<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('singlestore')->table('event_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('total')->nullable();
            $table->index('total');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
