<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'additional_attributes')) {
                $table->json('additional_attributes')->nullable();
            }
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'additional_attributes')) {
                $table->json('additional_attributes')->nullable();
            }
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {}
};
