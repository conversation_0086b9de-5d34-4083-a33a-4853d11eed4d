<?php

use App\Enums\UserRoleEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->enum('role', UserRoleEnum::getValues())
                ->default(UserRoleEnum::CUSTOMER)
                ->change();

            $table->unsignedBigInteger('support_staff_id')
                ->nullable()
                ->after('ref_id');

            $table->foreign('support_staff_id')
                ->references('id')
                ->on('staff');

            $table->string('note')
                ->nullable()
                ->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
