<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\OrderService\Helpers\RegionDbConnectionManager;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (config('app.region') === config('app.region_master')) {
            return;
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'payment_summary')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->text('payment_summary')->nullable()->after('payment_log');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
