<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\OrderService\Helpers\RegionDbConnectionManager;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // trigger migrations
        if (config('app.region') === config('app.region_master')) {
            return;
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order_product', 'combo_id')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order_product', function (Blueprint $table) {
                $table->string('combo_id')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            //
        });
    }
};
