<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\OrderService\Helpers\RegionDbConnectionManager;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (config('app.region') === config('app.region_master')) {
            return;
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'house_number')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->string('house_number', 255)->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'mailbox_number')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->string('mailbox_number', 255)->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'region_synced_at')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->timestamp('region_synced_at')->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'estimate_delivery_date')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->timestamp('estimate_delivery_date')->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'ioss_number')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->string('ioss_number', 50)->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'is_corner_placement')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->boolean('is_corner_placement')->default(0);
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order', 'approved_at')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order', function (Blueprint $table) {
                $table->timestamp('approved_at')->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order_product', 'next_scan_tracking_code_at')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order_product', function (Blueprint $table) {
                $table->timestamp('next_scan_tracking_code_at')->nullable();
            });
        }
        if (!Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order_product', 'at_risk')) {
            Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order_product', function (Blueprint $table) {
                $table->tinyInteger('at_risk')->default(0);
            });
        }
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {}
};
