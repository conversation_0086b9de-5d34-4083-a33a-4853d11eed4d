<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\OrderService\Helpers\RegionDbConnectionManager;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (config('app.region') == config('app.region_master')) return;
        Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->table('order_product', function (Blueprint $table) {
            if (Schema::connection(RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region')))->hasColumn('order_product', 'fulfill_order_id')) {
                $table->string('fulfill_order_id', 255)->nullable(false)->default('')->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
