<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddStoreIdToPaymentGatewaysTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('payment_gateways', 'store_id')) {
            Schema::table(
                'payment_gateways',
                function (Blueprint $table) {
                    $table->unsignedBigInteger('store_id')->nullable()->after('seller_id');
                }
            );
        }
    }

    public function down(): void {}
}
