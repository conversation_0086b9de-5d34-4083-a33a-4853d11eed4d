<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ads_campaign', static function (Blueprint $table) {
            $table->string('utm_medium')->nullable()->after('utm_campaign')->index();
            $table->string('utm_source')->nullable()->after('utm_campaign')->index();
        });
        $this->migrateOldData();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }

    private function migrateOldData(): void
    {
        $ads = \App\Models\AdsCampaign::query()
            ->whereNotNull('extra')
            ->get();
        foreach ($ads as $ad) {
            $extras = json_decode($ad->extra, true, 512, JSON_THROW_ON_ERROR);
            foreach ($extras as $extra) {
                if ($extra['key'] === 'utm_medium') {
                    $ad->utm_medium = $extra['value'];
                }
                if ($extra['key'] === 'utm_source') {
                    $ad->utm_source = $extra['value'];
                }
            }
            $ad->save();
        }
    }
};
