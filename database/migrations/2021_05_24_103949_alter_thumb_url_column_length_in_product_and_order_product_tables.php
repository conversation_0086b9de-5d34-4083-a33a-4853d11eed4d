<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterThumbUrlColumnLengthInProductAndOrderProductTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('product', 'thumb_url')) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->string('thumb_url', 512)->change();
                }
            );
        }
        if (Schema::hasColumn('order_product', 'thumb_url')) {
            Schema::table(
                'order_product',
                function (Blueprint $table) {
                    $table->string('thumb_url', 512)->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
