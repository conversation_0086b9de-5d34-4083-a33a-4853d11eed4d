<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddForeignKeysToSellerCollectionTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('seller_collection');

        Schema::table('seller_collection', function (Blueprint $table) use ($indexesFound) {
            if (!array_key_exists('seller_collection_seller_id_foreign', $indexesFound)) {
                $table->foreign('seller_id')
                    ->references('id')
                    ->on('user')
                    ->onDelete('cascade');
            }
            if (!array_key_exists('seller_collection_collection_id_foreign', $indexesFound)) {
                $table->foreign('collection_id')
                    ->references('id')
                    ->on('collection')
                    ->onDelete('cascade');
            }
        });
    }

    public function down(): void {}
}
