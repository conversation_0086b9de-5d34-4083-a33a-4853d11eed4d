<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexesToOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order_product');

        Schema::table(
            'order_product',
            function (Blueprint $table) use ($indexesFound) {
                if (!array_key_exists("sen_fulfill_status", $indexesFound)) {
                    $table->index(
                        'sen_fulfill_status',
                        'sen_fulfill_status'
                    );
                }
                if (!array_key_exists("personalized", $indexesFound)) {
                    $table->index(
                        'personalized',
                        'personalized'
                    );
                }
                if (!array_key_exists("full_printed", $indexesFound)) {
                    $table->index(
                        'full_printed',
                        'full_printed'
                    );
                }
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
