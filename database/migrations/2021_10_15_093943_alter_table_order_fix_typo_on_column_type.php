<?php

use App\Enums\OrderTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableOrderFixTypoOnColumnType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('order', 'type')) {
            Schema::table('order', function (Blueprint $table) {
                $table->enum('type', OrderTypeEnum::getValues())->change();
            });
        }
    }
}
