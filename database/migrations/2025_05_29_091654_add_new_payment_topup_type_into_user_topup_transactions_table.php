<?php

use App\Enums\UserTopupTransactionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_topup_transactions', function (Blueprint $table) {
            $table->enum('type', UserTopupTransactionType::asArray())->default(UserTopupTransactionType::BANK)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
