<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('supplier', function (Blueprint $table) {
            $table->timestamp('api_holds_until')->index()->after('holds_until')->nullable();
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void{}
};
