<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddUniqueIndexToShippingRuleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('shipping_rule');

        if (!array_key_exists("shipping_rule_unique_product_shipping_location", $indexesFound)) {
            Schema::table(
                'shipping_rule',
                function (Blueprint $table) {
                    $table->unique(
                        ['product_id', 'shipping_method', 'location_code'],
                        'shipping_rule_unique_product_shipping_location'
                    );
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
