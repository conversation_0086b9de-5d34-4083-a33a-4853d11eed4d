<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterConstraintProductVariantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table(
            'product_variant',
            function(Blueprint $table) {
                $sm           = Schema::getConnection()->getDoctrineSchemaManager();
                $indexesFound = $sm->listTableIndexes('product_variant');

                if (array_key_exists("product_variant_key", $indexesFound)) {
                    $table->dropIndex("product_variant_key");
                }
                if (empty($indexesFound['primary'])) {
                    $table->primary(['product_id', 'variant_key']);
                }
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
