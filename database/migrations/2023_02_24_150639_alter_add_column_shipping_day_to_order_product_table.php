<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnShippingDayToOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'shipping_day')) {
            Schema::table('order_product', function (Blueprint $table) {
                if (!Schema::hasColumn('order_product', 'shipping_day')) {
                    $table->float('shipping_day')->nullable()->after('processing_day');
                }
                if (Schema::hasColumn('order_product', 'processing_day')) {
                    $table->float('processing_day')->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
