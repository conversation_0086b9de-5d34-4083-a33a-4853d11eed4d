<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\ProductReviewTypeEnum;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        $sm = Schema::getConnection()->getDoctrineSchemaManager();
        $doctrineTable = $sm->introspectTable('product_reviews');
        Schema::table('product_reviews', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("product_reviews_order_product_id_foreign")) {
                $table->dropForeign('product_reviews_order_product_id_foreign');
            }
        });
        Schema::table('product_reviews', function (Blueprint $table) {
            if (Schema::hasColumn('product_reviews', 'order_id')) {
                $table->bigInteger('order_id')->nullable()->change();
            }
            if (Schema::hasColumn('product_reviews', 'order_product_id')) {
                $table->bigInteger('order_product_id')->nullable()->change();
            }
            if (!Schema::hasColumn('product_reviews', 'customer_name')) {
                $table->string('customer_name')->nullable();
            }
            if (!Schema::hasColumn('product_reviews', 'customer_email')) {
                $table->string('customer_email')->nullable();
            }
            if (!Schema::hasColumn('product_reviews', 'country')) {
                $table->string('country')->nullable();
            }
            if (!Schema::hasColumn('product_reviews', 'type')) {
                $table->string('type')->default(ProductReviewTypeEnum::NORMAL);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_reviews', function (Blueprint $table) {
            $table->dropColumn(['customer_name', 'customer_email', 'country', 'type']);
        });
    }
};
