<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterChangeNameLinkColumnsInSocialFeedsTable extends Migration
{
    public function up()
    {
        Schema::table('social_feeds', function (Blueprint $table) {
            if (Schema::hasColumn('social_feeds', 'link_csv')) {
                $table->dropColumn('link_csv');
            }
            if (Schema::hasColumn('social_feeds', 'link_xml')) {
                $table->dropColumn('link_xml');
            }
            if (!Schema::hasColumn('social_feeds', 'path')) {
                $table->string('path')->nullable()->after('custom_labels');
            }
        });
    }

    public function down(): void {}
}
