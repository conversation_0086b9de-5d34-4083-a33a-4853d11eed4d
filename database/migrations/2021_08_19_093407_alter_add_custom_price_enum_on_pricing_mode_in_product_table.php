<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddCustomPriceEnumOnPricingModeInProductTable extends Migration
{
    public function up()
    {
        if (Schema::hasColumn('product', 'pricing_mode')) {
            Schema::table('product', function (Blueprint $table) {
                $table->enum('pricing_mode', \App\Enums\PricingModeEnum::asArray())
                    ->change();
            });
        }
    }

    public function down(): void {}
}
