<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnsSaleReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('pgsql')->table(
            'sales_reports',
            function (Blueprint $table) {
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'data_key')) {
                    $table->renameColumn('data_key', 'data_index');
                }
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'data_id')) {
                    $table->renameColumn('data_id', 'type_index');
                }
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'visits')) {
                    $table->bigInteger('visits')->unsigned()->change();
                }
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'add_to_carts')) {
                    $table->bigInteger('add_to_carts')->unsigned()->change();
                }
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'checkouts')) {
                    $table->bigInteger('checkouts')->unsigned()->change();
                }
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'orders')) {
                    $table->bigInteger('orders')->unsigned()->change();
                }
                if (Schema::connection('pgsql')->hasColumn('sales_reports', 'items')) {
                    $table->bigInteger('items')->unsigned()->change();
                }
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
