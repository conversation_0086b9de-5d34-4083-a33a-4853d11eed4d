<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStatsOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::connection('pgsql')->hasTable('stats_order')) {
            $this->down();
        }

        Schema::connection('pgsql')->create('stats_order', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('seller_id')->index('stats_order_seller_id')->nullable();
            $table->unsignedBigInteger('store_id')->index('stats_order_store_id')->nullable();
            $table->unsignedBigInteger('campaign_id')->index('stats_order_campaign_id')->nullable();
            $table->unsignedBigInteger('order_id')->index('stats_order_order_id');
            $table->unsignedBigInteger('product_id')->index('stats_order_product_id');
            $table->integer('items', false, true);
            $table->double('sales');
            $table->double('seller_profit');
            $table->string('country', 64)->nullable();
            $table->string('device', 16)->nullable();
            $table->string('device_detail', 128)->nullable();
            $table->timestamp('timestamp')->default(DB::raw('CURRENT_TIMESTAMP'))->index('stats_order_timestamp');
            $table->timestamp('datestamp')->nullable();
            $table->integer('timezone');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('pgsql')->dropIfExists('stats_order');
    }
}
