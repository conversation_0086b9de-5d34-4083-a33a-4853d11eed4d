<?php

use App\Enums\OrderProductFulfillStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUpdateFulfillStatusInOrderProductTable extends Migration
{
    public function up()
    {
        if (Schema::hasColumn('order_product', 'fulfill_status')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->enum('fulfill_status', OrderProductFulfillStatus::asArray())
                    ->comment("'" . implode("','", OrderProductFulfillStatus::getValues()) . "'")
                    ->change();
            });
        }
    }

    public function down(): void {}
}
