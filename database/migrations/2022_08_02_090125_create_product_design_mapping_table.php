<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductDesignMappingTable extends Migration
{
    public function up()
    {
        Schema::create('product_design_mapping', function (Blueprint $table) {
            $table->foreignId('supplier_id')
                ->constrained('supplier')
                ->onDelete('cascade');
            $table->string('print_space');
            $table->foreignId('product_id')
                ->constrained('product')
                ->onDelete('cascade');
            $table->string('width');
            $table->string('height');
            $table->primary(['supplier_id', 'print_space', 'product_id'], 'pk_product_design_mapping');
        });
    }

    public function down(): void {}
}
