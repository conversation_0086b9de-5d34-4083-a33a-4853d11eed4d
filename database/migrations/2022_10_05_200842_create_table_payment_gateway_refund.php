<?php

use App\Enums\PaymentGatewayRefundStatusEnums;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateTablePaymentGatewayRefund extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_gateway_refund', function (Blueprint $table) {
            $table->id();
            $table->integer('payment_gateway_id');
            $table->integer('order_id');
            $table->integer('seller_id');
            $table->integer('store_id');
            $table->integer('staff_id');
            $table->double('refund_amount');
            $table->string('reason');
            $table->longText('log');
            $table->enum('status', PaymentGatewayRefundStatusEnums::getValues());
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
