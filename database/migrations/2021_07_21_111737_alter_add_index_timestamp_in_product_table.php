<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexTimestampInProductTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product');

        Schema::table(
            'product',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('deleted_at_index', $indexesFound)) {
                    $table->index('deleted_at', 'deleted_at_index');
                }
            }
        );
    }

    public function down(): void {}
}
