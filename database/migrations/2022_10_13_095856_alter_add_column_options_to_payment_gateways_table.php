<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnOptionsToPaymentGatewaysTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('payment_gateways', 'options')) {
            Schema::table('payment_gateways', function (Blueprint $table) {
                $table->json('options')
                    ->nullable()
                    ->comment('settings for storefront')
                    ->after('config');
            });
        }
    }

    public function down(): void {}
}
