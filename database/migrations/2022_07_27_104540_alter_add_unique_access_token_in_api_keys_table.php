<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddUniqueAccessTokenInApiKeysTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('api_keys');

        if (!array_key_exists("access_token_unique", $indexesFound)) {
            Schema::table(
                'api_keys',
                function(Blueprint $table) {
                    $table->unique('access_token','access_token_unique');
                }
            );
        }
    }

    public function down(): void {}
}
