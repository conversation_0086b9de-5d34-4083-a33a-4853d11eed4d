<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAssignedSupplierAtColumnToOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'assigned_supplier_at')) {
                $table->timestamp('assigned_supplier_at')->nullable()->after('supplier_name')->index('assigned_supplier_at');
            }
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'assigned_supplier_at')) {
                $table->timestamp('assigned_supplier_at')->nullable()->after('supplier_name')->index('assigned_supplier_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
