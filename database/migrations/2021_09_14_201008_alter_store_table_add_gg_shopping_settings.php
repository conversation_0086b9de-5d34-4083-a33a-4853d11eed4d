<?php

use App\Enums\StoreTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterStoreTableAddGgShoppingSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->enum('store_type', StoreTypeEnum::getValues())
                ->after('total_profit')
                ->default(StoreTypeEnum::NORMAL);
            $table->tinyInteger('show_payment_button')
                ->after('total_profit')
                ->comment('For turn off show payment button at add to cart page (Google Shopping)')
                ->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
