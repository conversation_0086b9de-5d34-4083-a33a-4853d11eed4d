<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('topup_webhook_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->char('type', 20)->index();
            $table->char('payment_site', 20)->nullable()->index();
            $table->char('code', 20)->nullable()->index();
            $table->boolean('re_validate')->index()->default(0);
            $table->json('parse_message')->nullable();
            $table->longText('raw_message')->nullable();
            $table->char('status', 50)->index()->nullable();
            $table->timestamp('created_at')->index('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->index('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
