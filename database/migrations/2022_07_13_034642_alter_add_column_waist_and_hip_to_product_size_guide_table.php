<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnWaistAndHipToProductSizeGuideTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_size_guide', function (Blueprint $table) {
            if (!Schema::hasColumn('product_size_guide', 'hip')) {
                $table->float('hip')->nullable()->after('sleeve');
            }
            if (!Schema::hasColumn('product_size_guide', 'waist')) {
                $table->float('waist')->nullable()->after('sleeve');
            }
            if (Schema::hasColumn('product_size_guide', 'size')) {
                $table->string('size')->nullable()->change();
            }
            if (Schema::hasColumn('product_size_guide', 'length')) {
                $table->float('length')->nullable()->change();
            }
            if (Schema::hasColumn('product_size_guide', 'width')) {
                $table->float('width')->nullable()->change();
            }
            if (Schema::hasColumn('product_size_guide', 'sleeve')) {
                $table->float('sleeve')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
