<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoreNavigationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('store_navigation', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('store_id')->index('store_id');
            $table->string('link_text');
            $table->string('link_url');
            $table->enum('place', [
                'header',
                'footer',
                'side_column'
            ])->comment('\'header\',\'footer\',\'side_column\'');
            $table->smallInteger('position')->nullable()->comment('menu order in number');
            $table->bigInteger('parent_id')->nullable();
            $table->boolean('status')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
