<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('supplier', function (Blueprint $table) {
            if (Schema::hasColumn('supplier', 'location')) {
                $table->string('location', 255)->default(null)->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
