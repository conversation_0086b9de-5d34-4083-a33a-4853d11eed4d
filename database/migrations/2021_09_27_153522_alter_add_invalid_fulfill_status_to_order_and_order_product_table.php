<?php

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddInvalidFulfillStatusToOrderAndOrderProductTable extends Migration
{
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            if (Schema::hasColumn('order', 'fulfill_status')) {
                $table->enum('fulfill_status', OrderFulfillStatus::getValues())
                    ->comment("'" . implode("','", OrderFulfillStatus::getValues()) . "'")
                    ->change();
            }
        });
        Schema::table('order_product', function (Blueprint $table) {
            if (Schema::hasColumn('order_product', 'fulfill_status')) {
                $table->enum('fulfill_status', OrderProductFulfillStatus::getValues())
                    ->comment("'" . implode("','", OrderProductFulfillStatus::getValues()) . "'")
                    ->change();
            }
        });
    }

    public function down(): void {}
}
