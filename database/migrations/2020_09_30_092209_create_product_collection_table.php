<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductCollectionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_collection', function (Blueprint $table) {
            $table->bigInteger('product_id')->index('product_collection_product_id');
            $table->bigInteger('collection_id')->index('product_collection_id');
            $table->unsignedBigInteger('seller_id');
            // $table->primary(['product_id','collection_id']); // chanhdn: remove primary
            $table->foreign('seller_id')->references('id')->on('user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
