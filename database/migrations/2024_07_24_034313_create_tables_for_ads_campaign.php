<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ads_campaign', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->index();
            $table->string('link', 255)->index();
            $table->string('short_link', 50)->index()->nullable();
            $table->string('utm_campaign', 20)->index();
            $table->string('utm_source', 20)->index();
            $table->string('utm_medium', 20)->index();
            $table->integer('staff_id')->index();
            $table->float('bonus_money')->index()->default(0);
            $table->tinyInteger('bonus_tier')->index()->default(0);
            $table->tinyInteger('status')->index()->default(0);
            $table->integer('total_click')->index()->default(0);
            $table->text('extra')->nullable();
            $table->timestamp('expired_at')->nullable()->index();
            $table->timestamps();
        });

        Schema::create('ads_campaign_logs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('ads_campaign_id')->index();
            $table->tinyInteger('action')->index();
            $table->bigInteger('seller_id')->index()->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
