<?php

use App\Enums\NotificationCategoryEnum;
use App\Enums\NotificationChannelEnum;
use App\Enums\NotificationTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNofiticationSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notification_settings', function (Blueprint $table) {
            $table->id();
            $table->integer('seller_id');
            $table->integer('store_id');
            $table->enum('type', [
                NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_1,
                NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_2,
                NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_3
            ]);
            $table->string('subject');
            $table->longText('content')->nullable();
            $table->json('params')->nullable();
            $table->boolean('status')->default(1);
            $table->enum('channel', [
                NotificationChannelEnum::EMAIL,
                NotificationChannelEnum::SMS,
                NotificationChannelEnum::SLACK
            ])->default('email');
            $table->enum('category', [
                NotificationCategoryEnum::CARTS,
                NotificationCategoryEnum::ORDERS,
                NotificationCategoryEnum::CARTS
            ]);
            $table->unsignedMediumInteger('send_after')->nullable();
            $table->enum('send_after_period', ['m', 'h', 'd'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
