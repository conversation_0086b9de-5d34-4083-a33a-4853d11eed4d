<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddCreatedAtIndexToUserTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('user');

        Schema::table(
            'user',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('created_at_index', $indexesFound)) {
                    $table->index('created_at', 'created_at_index');
                }
            }
        );
    }

    public function down(): void {}
}
