<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_image_generations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->text('prompt');
            $table->text('negative_prompt')->nullable();
            $table->string('aspect_ratio');
            $table->string('model');
            $table->string('magic_prompt_option');
            $table->string('style_type')->nullable();
            $table->unsignedInteger('seed')->nullable();
            $table->unsignedTinyInteger('num_images')->default(4);
            $table->json('result_images')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('user')->onDelete('cascade');
            $table->index('user_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('ai_image_generations');
    }
};
