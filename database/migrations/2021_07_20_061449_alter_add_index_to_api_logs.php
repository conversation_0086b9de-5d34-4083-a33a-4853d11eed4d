<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexToApiLogs extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('api_logs');

        Schema::table(
            'api_logs',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('reference_id_index', $indexesFound)) {
                    $table->index('reference_id', 'reference_id_index');
                }
                if (!array_key_exists('reference_type_index', $indexesFound)) {
                    $table->index('reference_type', 'reference_type_index');
                }
                if (!array_key_exists('type_index', $indexesFound)) {
                    $table->index('type', 'type_index');
                }
                if (!array_key_exists('fulfill_order_id_index', $indexesFound)) {
                    $table->index('fulfill_order_id', 'fulfill_order_id_index');
                }
            }
        );
    }

    public function down(): void {}
}
