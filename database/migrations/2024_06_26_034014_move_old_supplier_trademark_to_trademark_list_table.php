<?php

use App\Models\Supplier;
use App\Models\TrademarkList;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trademark_list', function (Blueprint $table) {
            if (Schema::hasColumn('supplier', 'trademark_keywords') && Schema::hasColumn('trademark_list', 'supplier_id')) {
                $suppliers = Supplier::query()
                    ->whereNotNull('trademark_keywords')
                    ->get();

                foreach ($suppliers as $supplier) {
                    if (!empty($supplier->trademark_keywords)) {
                        $trademarkKeywords = explode(',', $supplier->trademark_keywords);
                        TrademarkList::query()
                            ->insert(array_map(function ($keyword) use ($supplier) {
                                return [
                                    'supplier_id' => $supplier->id,
                                    'text' => $keyword,
                                    'block_logo' => 0,
                                    'block_text' => 0,
                                    'accept_logo' => 0,
                                ];
                            }, $trademarkKeywords));
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
