<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('avatar')->nullable()->comment('User profile picture');
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state', 64)->nullable();
            $table->string('postcode', 64)->nullable();
            $table->string('country', 4)->nullable()->comment('The user current country');
            $table->date('birthday')->nullable()->index('birthday');
            $table->string('phone', 16)->nullable();
            $table->string('facebook')->nullable();
            $table->double('balance')->nullable()->default(0)->comment('The user current balance');
            $table->string('timezone')->nullable()->comment('The user timezone');
            $table->float('utc_offset',1,0)->nullable()->comment('The user timezone utc offset');
            $table->string('language', 4)->nullable()->default('en')->comment('The user current language');
            $table->string('currency', 20)->nullable()->default('usd')->comment('The user current currency');
            $table->string('theme', 8)->default('light')->comment('light/dark');
            $table->enum('status', [
                'new',
                'verified',
                'trusted',
                'flagged',
                'soft_blocked',
                'hard_blocked',
                'deleted'
            ])->default('new')->comment('This is status of user');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
