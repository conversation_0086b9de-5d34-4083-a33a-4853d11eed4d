<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableProductUpdateTypeEnums extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('product', 'product_type')) {
            Schema::table('product', function (Blueprint $table) {
                $table->enum('product_type', \App\Enums\ProductType::getValues())->change();
            });
        }
    }
}
