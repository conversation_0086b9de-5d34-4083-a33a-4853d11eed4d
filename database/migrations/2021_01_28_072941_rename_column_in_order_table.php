<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameColumnInOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            if (!Schema::hasColumn('order', 'billing_address')) {
                $table->renameColumn( 'billing_address_id', 'billing_address' );
            }
            if (!Schema::hasColumn('order', 'shipping_address')) {
                $table->renameColumn( 'shipping_address_id', 'shipping_address' );
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
