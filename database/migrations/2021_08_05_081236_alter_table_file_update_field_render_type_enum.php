<?php

use App\Enums\FileRenderType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableFileUpdateFieldRenderTypeEnum extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('file', 'render_type')) {
            Schema::table('file', function (Blueprint $table) {
                $table->enum('render_type', FileRenderType::getValues())->change();
            });
        }
    }
}
