<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            $table->timestamp('first_order_at')->after('is_deleted')->index('first_order_at')->nullable();
            $table->index('sale_expired_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
