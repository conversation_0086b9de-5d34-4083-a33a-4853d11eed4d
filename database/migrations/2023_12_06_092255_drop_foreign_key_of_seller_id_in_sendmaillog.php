<?php

use App\Enums\EnvironmentEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropForeignKeyOfSellerIdInSendmaillog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mail_logs')->table('send_mail_log', function (Blueprint $table) {
            if (!app()->environment(EnvironmentEnum::PRODUCTION)) {
                $table->dropForeign(['seller_id']);
            }
            $table->unsignedBigInteger('seller_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
