<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AtlerAddReferColumnsToApiLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('api_logs', 'reference_id')) {
            Schema::table(
                'api_logs',
                function (Blueprint $table) {
                    $table->unsignedInteger('reference_id')->after('from')->nullable();
                }
            );
        }

        if (!Schema::hasColumn('api_logs', 'reference_type')) {
            Schema::table(
                'api_logs',
                function (Blueprint $table) {
                    $table->string('reference_type')->after('reference_id')->nullable();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
