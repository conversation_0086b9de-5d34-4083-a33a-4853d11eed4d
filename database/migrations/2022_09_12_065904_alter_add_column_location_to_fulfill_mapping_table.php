<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnLocationToFulfillMappingTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('fulfill_mapping', 'location')) {
            Schema::table('fulfill_mapping', static function (Blueprint $table) {
                $table->string('location')->nullable()->after('supplier_id');
            });
        }
    }

    public function down(): void {}
}
