<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContactFormLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_form_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('store_id')->nullable();
            $table->string('store_domain')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('customer_email');
            $table->string('order_number')->nullable();
            $table->string('subject')->nullable();
            $table->text('message')->nullable();
            $table->text('attach_files')->nullable()->comment('url1,url2');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
