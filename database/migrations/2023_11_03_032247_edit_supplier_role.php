<?php

use App\Models\Role;
use Illuminate\Database\Migrations\Migration;

class EditSupplierRole extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Role::query()
            ->where('name', 'Supplier')
            ->where('guard_name', 'supplier')
            ->update(['name' => 'supplier']);
        //run
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
