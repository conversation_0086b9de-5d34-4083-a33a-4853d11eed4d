<?php

use App\Enums\CampaignRenderModeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableProductAddColumnRenderMode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('product', 'render_mode')) {
            Schema::table('product', function (Blueprint $table) {
                $table->enum('render_mode', CampaignRenderModeEnum::getValues())
                    ->default(CampaignRenderModeEnum::NATURE)
                    ->comment('Campaign render mode');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
