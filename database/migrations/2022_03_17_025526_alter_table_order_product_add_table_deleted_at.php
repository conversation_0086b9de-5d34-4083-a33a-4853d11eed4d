<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableOrderProductAddTableDeletedAt extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'deleted_at')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->timestamp('deleted_at')->default(null)->nullable()->after('delivered_at');
            });
        }
    }

    public function down(): void {}
}
