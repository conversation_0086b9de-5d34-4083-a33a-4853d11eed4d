<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('customer')) {
            $query = DB::select("SHOW CREATE TABLE user")[0]->{'Create Table'};
            $query = str_replace('CREATE TABLE `user`', 'CREATE TABLE `customer`', $query);
            $query = preg_replace('/,\s*CONSTRAINT `[^`]+` FOREIGN KEY \([^)]+\) REFERENCES `[^`]+` \([^)]+\)/', '', $query);
            $query = preg_replace('/CONSTRAINT `[^`]+` FOREIGN KEY \([^)]+\) REFERENCES `[^`]+` \([^)]+\),\s*/', '', $query);
            DB::statement($query);
        }
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
