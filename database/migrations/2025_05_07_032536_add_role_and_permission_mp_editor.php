<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $permission = Permission::query()->firstOrCreate([
            'guard_name' => 'admin',
            'name' => 'manage_public_campaign'
        ]);
        $role = Role::query()->firstOrCreate([
            'guard_name' => 'admin',
            'name' => 'MP Editor'
        ]);
        $role->givePermissionTo($permission);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
