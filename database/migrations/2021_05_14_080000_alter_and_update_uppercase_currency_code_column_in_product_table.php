<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAndUpdateUppercaseCurrencyCodeColumnInProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('product', 'currency_code')) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->string('currency_code')->default(\App\Enums\CurrencyEnum::USD)->change();
                }
            );
            \DB::raw('update product set `currency_code` = upper(`currency_code`)');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
