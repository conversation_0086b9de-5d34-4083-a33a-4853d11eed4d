<?php

use App\Models\Permission;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Permission::query()->firstOrCreate([
            'guard_name' => 'admin',
            'name' => 'sale_diff_report'
        ]);
        Permission::query()->firstOrCreate([
            'guard_name' => 'user',
            'name' => 'sale_diff_report'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
