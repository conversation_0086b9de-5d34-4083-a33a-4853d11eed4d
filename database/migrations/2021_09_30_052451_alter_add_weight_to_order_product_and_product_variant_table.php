<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddWeightToOrderProductAndProductVariantTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'weight')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->double('weight')
                ->default(0)
                ->after('price')
                ->comment('pound:lbs');
            });
        }
        if (!Schema::hasColumn('product_variant', 'weight')) {
            Schema::table('product_variant', function (Blueprint $table) {
                $table->double('weight')
                ->default(0)
                ->after('old_price')
                ->comment('pound:lbs');
            });
        }
    }

    public function down(): void {}
}
