<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUpsellTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('upsell', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('upsell_collection_id')->nullable();
            $table->string('upsell_campaign_ids')->nullable();
            $table->enum('type', [
                'related',
                'cart',
                'post_sale'
            ])->default('related');
            $table->foreign('campaign_id')->references('id')->on('product');
            $table->foreign('upsell_collection_id')->references('id')->on('collection');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
