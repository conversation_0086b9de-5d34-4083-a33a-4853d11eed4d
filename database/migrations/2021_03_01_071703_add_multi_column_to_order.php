<?php

use App\Enums\OrderFulfillStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMultiColumnToOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->string('customer_name')
                ->after('customer_id')
                ->nullable();
            $table->string('customer_email')
                ->after('customer_name')
                ->nullable();
            $table->string('customer_phone')
                ->after('customer_email')
                ->nullable();
            $table->string('address')
                ->after('stats_status')
                ->nullable();
            $table->string('address_2')
                ->after('address')
                ->nullable();
            $table->string('city')
                ->after('address_2')
                ->nullable();
            $table->string('state')
                ->after('city')
                ->nullable();
            $table->string('postcode')
                ->after('state')
                ->nullable();
            $table->text('order_note')
                ->after('postcode')
                ->nullable();
            $table->string('store_name')
                ->after('store_id')
                ->nullable();
            $table->string('store_domain')
                ->after('store_name')
                ->nullable();
            $table->bigInteger('company_id')
                ->after('id')
                ->nullable();
            $table->string('payment_method')
                ->after('payment_gateway_id')
                ->nullable();
            $table->string('transaction_id')
                ->after('payment_method')
                ->nullable();
            $table->double('payment_fee')
                ->after('transaction_id')
                ->nullable();
            $table->double('processing_fee')
                ->after('payment_fee')
                ->nullable();
            $table->bigInteger('export_id')
                ->after('device_detail')
                ->nullable();
            $table->timestamp('exported_at')
                ->after('export_id')
                ->nullable();
            $table->enum( 'fulfill_status', OrderFulfillStatus::getValues() )
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
