<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleReportsTable extends Migration
{
    public function up()
    {
        Schema::create('sale_reports', function (Blueprint $table) {
            $table->id();
            $table->timestamp('time')->index();
            $table->string('type');
            $table->double('value');
            $table->unique(['type', 'time']);
        });
    }

    public function down(): void {}
}
