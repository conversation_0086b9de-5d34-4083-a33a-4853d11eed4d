<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('order', 'region_synced_at')) {
            Schema::table('order', function (Blueprint $table) {
                $table->timestamp('region_synced_at')->nullable()->after('sync_at')->index('region_synced_at');
            });
        }
        if (!Schema::connection('singlestore')->hasColumn('order', 'region_synced_at')) {
            Schema::connection('singlestore')->table('order', function (Blueprint $table) {
                $table->timestamp('region_synced_at')->nullable()->after('sync_at')->index('region_synced_at');
            });
        }
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
