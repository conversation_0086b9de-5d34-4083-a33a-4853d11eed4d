<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductVariantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_variant', function (Blueprint $table) {
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('campaign_id')->nullable();
            $table->string('variant_key')->index('product_variant_key');
            $table->tinyInteger('out_of_stock')->nullable()->default(0);
            $table->double('adjust_price')->nullable()->default(0);
            $table->double('price')->nullable()->default(0);
            $table->integer('quantity')->nullable()->default(1);
            $table->tinyInteger('check_quantity')->nullable()->default(0);
            // Add foreign key
            $table->foreign('product_id')->references('id')->on('product');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
