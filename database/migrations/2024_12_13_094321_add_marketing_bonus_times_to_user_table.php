<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            $table->tinyInteger('marketing_bonus_times')->default(0)->index('marketing_bonus_times')->after('bonus_times')->comment("1: Email Verified 2: Active Account 3: Sale Account");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
