<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'dynamic_base_cost_index')) {
                $table->double('dynamic_base_cost_index')->after('base_cost')->nullable(false)->default(0);
            }
        });

        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'dynamic_base_cost_index')) {
                $table->double('dynamic_base_cost_index')->after('base_cost')->nullable(false)->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
