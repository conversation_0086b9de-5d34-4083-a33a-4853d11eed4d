<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SinglestoreNewAddShippingLabelToOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('singlestore')->table('order', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order', 'shipping_label')) {
                $table->string('shipping_label')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
