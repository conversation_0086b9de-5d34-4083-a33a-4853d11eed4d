<?php

use App\Enums\ProductReviewAllowSharingEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeTypeOfAllowSharingFieldFromBooleanToEnumProductReviewTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_reviews', function (Blueprint $table) {
            $table->enum('allow_sharing', ProductReviewAllowSharingEnum::getValues())
                ->default(ProductReviewAllowSharingEnum::SHARE_REVIEW)
                ->comment('deny: Deny - share_review: Only share text review - share_campaign: Includes text, url and assets')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
