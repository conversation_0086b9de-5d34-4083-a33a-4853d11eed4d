<?php

use App\Enums\StorefrontStyleEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnStyleToStoreTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('store', 'style')) {
            Schema::table('store', function (Blueprint $table) {
                $table->string('style', 32)->default(StorefrontStyleEnum::DEFAULT);
            });
        }
    }

    public function down(): void {}
}
