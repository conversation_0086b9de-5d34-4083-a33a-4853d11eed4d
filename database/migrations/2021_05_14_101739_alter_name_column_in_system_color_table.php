<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterNameColumnInSystemColorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('system_color');

        if (!array_key_exists("name_unique", $indexesFound)) {
            Schema::table(
                'system_color',
                function (Blueprint $table) {
                    $table->unique(
                        'name',
                        'name_unique'
                    );
                }
            );
        }

        if (array_key_exists("color_name", $indexesFound)) {
            Schema::table(
                'system_color',
                function (Blueprint $table) {
                    $table->dropIndex('color_name');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
