<?php

use App\Enums\OrderSenFulfillStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCustomGatewayFieldsToOrderProduct extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('sen_fulfill_status', OrderSenFulfillStatus::asArray())
                ->default(OrderSenFulfillStatus::YES)
                ->comment(implode(',', OrderSenFulfillStatus::getKeys()))
                ->after('fulfill_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
