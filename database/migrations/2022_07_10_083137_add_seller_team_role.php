<?php

use App\Enums\SellerTeamRoleEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSellerTeamRole extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('seller_team', function (Blueprint $table) {
            $table->enum('role', SellerTeamRoleEnum::getValues())
                ->comment("'" . implode("','", SellerTeamRoleEnum::getValues()) . "'")
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
