<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SinglestoreModifyColumnFulfillFbaByOfOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (Schema::connection('singlestore')->hasColumn('order_product', 'fulfill_fba_by')) {
                $table->dropColumn('fulfill_fba_by');
            }
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->string('fulfill_fba_by')->index('fulfill_fba_by')->nullable();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
