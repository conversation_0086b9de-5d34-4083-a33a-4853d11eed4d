<?php

use App\Enums\FileTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableFileUpdateColumnTypeEnumValues extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('file', 'type')) {
            Schema::table('file', function (Blueprint $table) {
                $table->enum('type', FileTypeEnum::getValues())->change();
            });
        }
    }
}
