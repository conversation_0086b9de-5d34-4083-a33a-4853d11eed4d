<?php

use App\Enums\ProductReviewRequestStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddReviewRequestStatusColumnToOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->enum('review_request_status', [
                ProductReviewRequestStatusEnum::PENDING,
                ProductReviewRequestStatusEnum::PROCESSING,
                ProductReviewRequestStatusEnum::COMPLETED
            ])->default(ProductReviewRequestStatusEnum::PENDING);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
