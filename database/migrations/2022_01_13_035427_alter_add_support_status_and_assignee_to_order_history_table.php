<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddSupportStatusAndAssigneeToOrderHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('order_history', 'support_status')) {
            Schema::table('order_history', function (Blueprint $table) {
                $table->string('support_status')
                    ->nullable()
                    ->after('fulfill_status');
            });
        }
        if (!Schema::hasColumn('order_history', 'assignee')) {
            Schema::table('order_history', function (Blueprint $table) {
                $table->string('assignee')
                    ->nullable()
                    ->after('fulfill_status');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
