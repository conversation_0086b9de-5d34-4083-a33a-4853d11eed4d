<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterDropMethodAndAddNameToApiLogs extends Migration
{
    public function up()
    {
        if (Schema::hasColumn('api_logs', 'method')) {
            Schema::table('api_logs', function (Blueprint $table) {
                $table->dropColumn('method');
            });
        }
        if (!Schema::hasColumn('api_logs', 'name')) {
            Schema::table('api_logs', function (Blueprint $table) {
                $table->string('name')
                    ->nullable()
                    ->index()
                    ->after('id');
            });
        }
    }

    public function down(): void {}
}
