<?php

use App\Enums\AccessExternalApiType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateApiKeysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('reference_id')->index('api_keys_referenceId')->comment('Reference id of supplier, seller or store');
            $table->enum('type', AccessExternalApiType::getValues())->default(AccessExternalApiType::SUPPLIER)->index('api_keys_type');
            $table->string('access_token');
            $table->bigInteger('user_id')->nullable()->index('api_key_for_user_id')->comment('ID for seller');
            $table->bigInteger('limit')->default(0);
            $table->timestamp('expired')->default(DB::raw('CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
