<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
           $table->enum('campaign_type', ProductSystemTypeEnum::asArray())->nullable()->index();
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->string('campaign_type')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
