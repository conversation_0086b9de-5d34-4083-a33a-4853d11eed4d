<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddFulfilledAtToOrderTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order', 'fulfilled_at')) {
            Schema::table(
                'order',
                function (Blueprint $table) {
                    $table->timestamp('fulfilled_at')->nullable()->after('updated_at');
                }
            );
        }
    }

    public function down(): void {}
}
