<?php

use App\Enums\SellerBillingType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTypesForSellerBillingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('seller_billing', function (Blueprint $table) {
            $table->enum('type', SellerBillingType::getValues())
                ->comment("'" . implode("','", SellerBillingType::getValues()) . "'")
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
