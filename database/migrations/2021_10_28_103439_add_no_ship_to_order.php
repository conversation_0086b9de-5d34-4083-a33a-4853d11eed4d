<?php

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNoShipToOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->enum('fulfill_status', OrderFulfillStatus::getValues())
                ->comment("'" . implode("','", OrderFulfillStatus::getValues()) . "'")
                ->change();
        });
        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('fulfill_status', OrderProductFulfillStatus::getValues())
                ->comment("'" . implode("','", OrderProductFulfillStatus::getValues()) . "'")
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
