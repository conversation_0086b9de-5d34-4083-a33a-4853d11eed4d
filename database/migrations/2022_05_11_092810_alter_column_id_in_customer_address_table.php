<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnIdInCustomerAddressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customer_address', function (Blueprint $table) {
            $table->string('id')->change();
        });

        Schema::table('order', function (Blueprint $table) {
            if (Schema::hasColumn('order', 'billing_address')) {
                $table->string('billing_address')->change();
            }
            if (Schema::hasColumn('order', 'shipping_address')) {
                $table->string('shipping_address')->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
