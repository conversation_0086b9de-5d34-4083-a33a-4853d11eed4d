<?php

namespace Database\Factories;

use App\Models\Campaign;
use App\Models\Collection;
use App\Models\ProductCollection;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductCollectionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductCollection::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $campaignId = Campaign::query()
            ->inRandomOrder()
            ->value('id');

        $collectionId = Collection::query()
            ->inRandomOrder()
            ->value('id');

        $sellerId = User::role('seller')
            ->inRandomOrder()
            ->value('id');

        return [
            'product_id' => $campaignId,
            'collection_id' => $collectionId,
            'seller_id' => $sellerId
        ];
    }
}
