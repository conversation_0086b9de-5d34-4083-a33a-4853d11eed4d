<?php

namespace Database\Factories;

use App\Enums\FulfillmentStatusEnum;
use App\Models\Fulfillment;
use App\Models\Order;
use App\Models\Supplier;
use Illuminate\Database\Eloquent\Factories\Factory;

class FulfillmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Fulfillment::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $order = $this->faker->randomElement(
            Order::query()
                ->select('id', 'seller_id', 'store_id')
                ->limit(5)
                ->get()
                ->toArray()
        );

        $supplier = $this->faker->randomElement(
            Supplier::query()
                ->select('id', 'name')
                ->limit(10)
                ->get()
                ->toArray()
        );

        $status = FulfillmentStatusEnum::getValues();
        $carriers = ['USPS', 'EMS', 'DHL', 'CNP', 'VNP'];

        return [
            'order_id' => $order['id'],
            'seller_id' => $order['seller_id'],
            'store_id' => $order['store_id'],
            'supplier_id' => $supplier['id'],
            'items_quantity' => $this->faker->numberBetween(1, 5),
            'supplier_name' => $supplier['name'],
            'shipping_carrier' => $this->faker->randomElement($carriers),
            'tracking_number' => $this->faker->bankAccountNumber,
            'status' => $this->faker->randomElement($status)
        ];
    }
}
