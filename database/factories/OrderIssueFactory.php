<?php

namespace Database\Factories;

use App\Enums\OrderIssueCategoryEnum;
use App\Enums\OrderIssueChargeTypeEnum;
use App\Enums\OrderIssueCustomerRequestEnum;
use App\Enums\OrderIssueRefundTypeEnum;
use App\Enums\OrderIssueStatusEnum;
use App\Models\Category;
use App\Models\Order;
use App\Models\Supplier;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderIssueFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $order = Order::query()->select(['id', 'seller_id'])->whereNotNull('order_number')->limit(10)->orderByDesc('created_at')->get()->random();
        $supplier = Supplier::query()->select('id')->limit(10)->get()->random();
        $productCategory = Category::query()->select('name')->limit(10)->get()->random();

        return [
            'order_id' => $order->id,
            'seller_id' => $order->seller_id,
            'issue_category' => OrderIssueCategoryEnum::getRandomValue(),
            'customer_request' => OrderIssueCustomerRequestEnum::getRandomValue(),
            'charge_type' => OrderIssueChargeTypeEnum::getRandomValue(),
            'refund_type' => OrderIssueRefundTypeEnum::getRandomValue(),
            'charge_amount' => $this->faker->randomFloat(2, 50, 200),
            'refund_amount' => $this->faker->randomFloat(2, 50, 200),
            'detail' => $this->faker->text(100),
            'supplier_id' => $supplier->id,
            'product_category' => $productCategory->name,
            'status' => OrderIssueStatusEnum::getRandomValue(),
            'created_by' => 3,
        ];
    }
}
