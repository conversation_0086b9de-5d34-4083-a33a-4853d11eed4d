<?php

namespace Database\Factories;

use App\Enums\NotificationTypeEnum;
use App\Models\NotificationSetting;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationSettingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = NotificationSetting::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $types = [
            NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_1,
            NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_2,
            NotificationTypeEnum::ABANDONED_CHECKOUT_EMAIL_3
        ];
        $type = $this->faker->randomElement($types);
        $sendAfter = $this->faker->randomElement([
            daysToSeconds(1),
            daysToSeconds(3),
            daysToSeconds(5)
        ]);

        return [
            'type' => $type,
            'subject' => $this->faker->text(),
            'content' => $this->faker->realText(),
            'status' => 1,
            'channel' => 'email',
            'category' => 'carts',
            'send_after' => $sendAfter,
            'send_after_period' => 'd'
        ];
    }
}
