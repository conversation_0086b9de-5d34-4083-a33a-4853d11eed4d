<?php

namespace Database\Factories;

use App\Enums\OrderFulfillStatus;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrderProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $images = [
            'pt/mockup/1/5j15oggkjfOMe2uUf0iMW5TpVfLFKRXmB7rDkDlB.jpeg',
            'pt/mockup/1/G1iLDS8Lz7QFoF069WWDALS8qRP04ecBCyxEmyQU.jpeg',
            'pt/mockup/1/o9L6deLVC4Dq2Pzeq7p81WUKDf5pbkwM7SJj2BmA.jpeg',
            'pt/mockup/1/rPKYSB4hwfpySQOBX7nGo7rPe3XMD4lD0Jeyu0ko.jpeg',
            'pt/mockup/1/NaeeKNqdSG1SPYTQ486fsmljp1i0uijhRNbrYgpE.jpeg',
            'pt/mockup/1/OuQjtcaE02MuO5yD2Ttim5reUgi0rIlDFPImqlFf.jpeg',
            'pt/mockup/1/wMcWKQE2PkuaVlsRfsaTEgAQDbqxr9fMu44PWocS.jpeg',
            'pt/mockup/1/f4UGFsZiMQVNjmepNhwZagAbcROquju05c3w27Wz.jpeg',
        ];

        $options = [
            '{"size": "s", "color": "carolina blue"}',
            '{"size": "m", "color": "berry"}',
            '{"size": "l", "color": "red"}',
        ];

        $order = Order::query()
            ->inRandomOrder()
            ->select(['id', 'seller_id'])
            ->first();

        if (is_null($order)) {
            throw new \RuntimeException('Cannot get order.');
        }

        $orderId = $order->id;
        $sellerId = $order->seller_id;

        $product = Product::query()
            ->select('id', 'name', 'campaign_id')
            ->where('seller_id', $sellerId)
            ->has('campaign')
            ->with('campaign:id,name')
            ->inRandomOrder()
            ->first();

        if (is_null($product)) {
            throw new \RuntimeException('Cannot get product.');
        }

        $fulfillStatus = OrderFulfillStatus::getValues();

        $price = $this->faker->randomFloat(2, 10, 20);
        $quantity = $this->faker->numberBetween(1, 10);
        $totalAmount = $price * $quantity;
        $fulfilledQuantity = $this->faker->numberBetween(0, $quantity);
        $fulfillStatus = $this->faker->randomElement($fulfillStatus);

        if ($fulfilledQuantity > 0) {
            $fulfillStatus = OrderFulfillStatus::PARTIALLY_FULFILLED;
        }

        if ($fulfilledQuantity === $quantity) {
            $fulfillStatus = OrderFulfillStatus::FULFILLED;
        }

        return [
            'order_id' => $orderId,
            'product_id' => $product->id,
            'product_name' => $product->name,
            'campaign_id' => $product->campaign->id,
            'campaign_title' => $product->campaign->name,
            'thumb_url' => $this->faker->randomElement($images),
            'options' => $this->faker->randomElement($options),
            'cost' => $this->faker->randomFloat(2, 10, 20),
            'base_cost' => $this->faker->randomFloat(2, 10, 20),
            'price' => $price,
            'quantity' => $quantity,
            'total_amount' => $totalAmount,
            'shipping_cost' => $this->faker->randomFloat(2, 10, 20),
            'discount_amount' => $this->faker->randomFloat(2, 10, 20),
            'profit' => $this->faker->randomFloat(2, 10, 20),
            'seller_profit' => $this->faker->randomFloat(2, 10, 20),
            'upsell_status' => $this->faker->randomElement([0, 1]),
            'fulfilled_quantity' => $fulfilledQuantity,
            'fulfill_status' => $fulfillStatus,
            'sku' => $this->faker->slug,
        ];
    }
}
