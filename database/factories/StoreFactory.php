<?php

namespace Database\Factories;

use App\Models\Store;
use App\Models\SystemTimeZone;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class StoreFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Store::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $sellerIds = User::query()
            ->select('id')
            ->where('id', '>', 1)
            ->role('seller')
            ->limit(2)
            ->get()
            ->pluck('id')
            ->toArray();

        $timezone = $this->faker->randomElement(SystemTimeZone::all('timezone_name', 'utc_offset'));

        return [
            'name' => $this->faker->name,
            'sub_domain' => $this->faker->unique()->domainWord,
            'seller_id' => $this->faker->randomElement($sellerIds),
            'theme' => 'default',
            'email' => $this->faker->email,
            'phone' => $this->faker->e164PhoneNumber,
            'address' => $this->faker->address,
            'private_store' => $this->faker->numberBetween(0, 1),
            'timezone' => $timezone->timezone_name,
            'utc_offset' => $timezone->utc_offset,
            'domain' => $this->faker->unique()->domainName,
            'status' => 'active',
        ];
    }
}
