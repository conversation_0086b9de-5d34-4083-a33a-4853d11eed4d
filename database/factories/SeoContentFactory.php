<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class SeoContentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $html = '<p>Hello World!</p><p>Some initial <strong>bold</strong> text</p>';
        return [
            'type' => $this->faker->unique()->domainWord,
            'title' => $this->faker->paragraph(),
            'description' => $html,
            'content' => $html,
            'keywords' => json_encode([
                [
                    'name' => 'name',
                    'value' => $this->faker->word
                ],
                [
                    'name' => 'style',
                    'value' => $this->faker->word
                ],
                [
                    'name' => 'product',
                    'value' => $this->faker->word
                ],
            ])
        ];
    }
}
