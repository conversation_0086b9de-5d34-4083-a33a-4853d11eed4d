<?php

namespace Database\Factories;

use App\Enums\FeaturedInfoStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class FeaturedInfoFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $status = FeaturedInfoStatusEnum::getValues();

        return [
            'subject' => $this->faker->text(100),
            'description' => $this->faker->text,
            'short_desc' => $this->faker->text(10),
            'banner_url' => $this->faker->imageUrl,
            'banner_url_2' => $this->faker->imageUrl,
            'article_url' => $this->faker->url,
            'staff_id' => 3, //huannh
            'status' => $status[rand(0,1)],
            'expired_at' => $this->faker->dateTimeBetween('+1 week', '+1 month'),
        ];
    }
}
