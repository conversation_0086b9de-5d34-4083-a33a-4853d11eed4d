<?php

namespace Database\Factories;

use App\Enums\TrackingStatusEnum;
use App\Models\Order;
use App\Models\TrackingStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

class TrackingStatusFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = TrackingStatus::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $status = TrackingStatusEnum::asArray();
        $orderNumber = Order::inRandomOrder()->value('order_number');
        return [
            'tracking_code' => $this->faker->bothify('?####??#######'),
            'order_number' => $orderNumber,
            'status' => $this->faker->randomElement($status),
        ];
    }
}
