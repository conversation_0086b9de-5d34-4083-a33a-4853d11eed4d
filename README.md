# Senprints Backend APIs

### <PERSON><PERSON><PERSON> cầu hệ thống
 - Docker & Docker Compose ([tải xuống](https://www.docker.com/products/docker-desktop))

### Cài đặt
 - docker-compose -f docker-compose.local.yml up -d --build
 - Tạo file .env với nội dung như .env.example, sửa lại cấu hình .env cho phù hợp.
 - docker exec -it [service name] sh (dùng lệnh này để truy cập vào container), ví dụ: `docker exec -it app sh`
 - Sau khi đã truy cập container thì dùng lệnh: php artisan key:generate và php artisan jwt:secret
 - Truy cập http://localhost:6868

### Database

 - Tải xuống tại [đây](https://drive.google.com/drive/folders/1faP5-vnWVQWqmU25xmpy_oOTGVurky-6?usp=drive_link) c<PERSON><PERSON> bạ<PERSON> có thể import để test.
 - <PERSON><PERSON> kết nối vào môi trường dev thì có thể lấy thông tin từ link [này](https://wiki.senprints.net/doc/development-account-FpTN3j2ysV).

### Lưu ý
 - Xem thêm tài liệu tại [đây](https://wiki.senprints.net/doc/dev-onboarding-rVDayyqawC).
