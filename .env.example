APP_NAME=Senprints
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dev_example
DB_USERNAME=dev_example
DB_PASSWORD=dev_example

DB_HOST_PGSQL=127.0.0.1
DB_PORT_PGSQL=5432
DB_DATABASE_PGSQL=dev_example
DB_USERNAME_PGSQL=dev_example
DB_PASSWORD_PGSQL=dev_example

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=
JWT_TTL=63113851
JWT_EXTERNAL_SECRET=
JWT_EXTERNAL_TTL=63113851
LOG_SLACK_WEBHOOK_URL=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=
AWS_URL=

SCOUT_DRIVER=elastic
SCOUT_ELASTIC_DOCUMENT_REFRESH=wait_for
SCOUT_ELASTIC_INDEXER=bulk
ELATICSEARCH_HOST=127.0.0.1
ELATICSEARCH_PORT=9200
ELATICSEARCH_SCHEME=http
ELATICSEARCH_PATH=
ELATICSEARCH_USER=
ELATICSEARCH_PASS=
ELATICSEARCH_INDEX=products

ELATICSEARCH2_HOST=127.0.0.1
ELATICSEARCH2_PORT=9200
ELATICSEARCH2_SCHEME=http
ELATICSEARCH2_PATH=
ELATICSEARCH2_USER=
ELATICSEARCH2_PASS=
ELATICSEARCH2_INDEX=products_sharding

EMAIL_QUEUE_API_KEY=
EMAIL_QUEUE_BASE_URL=

SCHEDULE_ENABLED=false

FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
FACEBOOK_REDIRECT_URI=${APP_URL}

STRIPE_WEBHOOK_ENDPOINT_SECRET=
STRIPE_API_KEY=

RENDER_CANVAS_2D_URL=
RENDER_CANVAS_3D_URL=
CLEAR_CACHE_NODE_URL=

GENERATE_SSL_CUSTOM_DOMAIN_API=
BASE_IMG_URL=https://img.senprints.net

TINEYE_API_KEY=

17TRACK_API_ENDPOINT=
17TRACK_SECRET_KEY=

SHOPIFY_API_KEY=
SHOPIFY_API_SECRET=
SHOPIFY_SCOPES=write_products,write_customers,read_all_orders,write_orders,write_assigned_fulfillment_orders,write_merchant_managed_fulfillment_orders
SHOPIFY_HOST=

SINGLESTORE_DB_HOST=
SINGLESTORE_DB_PORT=
SINGLESTORE_DB_DATABASE=
SINGLESTORE_DB_USERNAME=
SINGLESTORE_DB_PASSWORD=
SINGLESTORE_DB_SOCKET=
