{"info": {"_postman_id": "9ae76666-12c5-4919-9dc1-6af1e49cd03f", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "15762427"}, "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"thangvt\",\r\n    \"password\": \"thangvt@230103\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/seller-api/login?username={{username}}&password={{password}}", "host": ["{{url}}"], "path": ["seller-api", "login"], "query": [{"key": "username", "value": "{{username}}"}, {"key": "password", "value": "{{password}}"}]}}, "response": []}, {"name": "<PERSON><PERSON>d", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\": \"final11\",\r\n    \"buyer_first_name\": \"<PERSON>\",\r\n    \"buyer_last_name\": \"<PERSON>\",\r\n    \"buyer_email\": \"<EMAIL>\",\r\n    \"buyer_phone\": \"\",\r\n    \"buyer_address1\": \"6445 <PERSON>c<PERSON><PERSON>ey <PERSON>\",\r\n    \"buyer_address2\": \"\",\r\n    \"buyer_city\": \"EDINA\",\r\n    \"buyer_province_code\": \"OK\",\r\n    \"buyer_zip\": \"55439\",\r\n    \"buyer_country_code\": \"US\",\r\n    \"shipment\": \"1\",\r\n    \"products\": [\r\n        {\r\n            \"variant_id\": \"12101\",\r\n            \"printer_design_front_url\": \"https://s3.mangoteeprints.com/products/36d70f6a-2507-4231-a4ba-e3236c59e496.png\",\r\n            \"printer_design_back_url\": \"\",\r\n            \"mockup_front_url\": \"https://i.etsystatic.com/43741108/r/il/f13ae4/5067869038/il_794xN.5067869038_kn52.jpg\",\r\n            \"mockup_back_url\": \"\",\r\n            \"quantity\": 1,\r\n            \"note\": \"\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/seller-api/orders/shirt-add", "host": ["{{url}}"], "path": ["seller-api", "orders", "shirt-add"]}}, "response": []}, {"name": "Detail Order", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/seller-api/orders/JOCEQ2PGL", "host": ["{{url}}"], "path": ["seller-api", "orders", "JOCEQ2PGL"]}}, "response": []}, {"name": "Cancel order", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"orderCodeList\": [\r\n        149508\r\n    ],\r\n    \"rejectNote\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/seller-api/orders/seller-reject", "host": ["{{url}}"], "path": ["seller-api", "orders", "seller-reject"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "url", "value": "https://seller.flashship.net"}, {"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}