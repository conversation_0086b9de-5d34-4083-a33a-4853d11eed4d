<?php

return [
    'host' => env('PSD_RENDER_WORKER_HOST', '**************'),
    'port' => env('PSD_RENDER_WORKER_PORT', '11672'),
    'user' => env('PSD_RENDER_WORKER_USER', 'dev'),
    'password' => env('PSD_RENDER_WORKER_PASSWORD', 'dev123'),
    'vhost' => env('PSD_RENDER_WORKER_VHOST', 'vhost'),
    'queue' => env('PSD_RENDER_WORKER_QUEUE', 'render_mockup_normal_first'),
    'exchange' => env('PSD_RENDER_WORKER_EXCHANGE', 'pod_log_center'),
    'result_host' => env('PSD_RENDER_WORKER_BUCKET', 'sendev'), //bucket
    'result_disk' => env('PSD_RENDER_WORKER_DISK', 'sendev') //disk
];

