version: '3.7'
services:
  app:
    image: senprints_apis_app
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: senprints_apis_app
    restart: unless-stopped
    volumes:
      - ./:/var/www/app
      - /var/www/app/vendor
    networks:
      - spappnetwork
  server:
    image: nginx:alpine
    container_name: senprints_apis_server
    restart: unless-stopped
    working_dir: /var/www/app
    ports:
      - "6868:80"
    volumes:
      - ./:/var/www/app
      - ./docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - spappnetwork
networks:
  spappnetwork:
    driver: overlay
